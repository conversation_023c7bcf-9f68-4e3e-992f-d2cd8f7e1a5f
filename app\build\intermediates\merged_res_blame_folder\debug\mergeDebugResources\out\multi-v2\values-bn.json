{"logs": [{"outputFile": "io.github.simplenote.app-mergeDebugResources-66:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8f6b7cfc7dddc58c7ce7adfd537c1781\\transformed\\material-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "10634", "endColumns": "89", "endOffsets": "10719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250d8cc481173cc0c0524c32c74c00eb\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,357,447,545,631,710,816,903,992,1070,1151,1234,1320,1396,1473,1549,1624,1692", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "268,352,442,540,626,705,811,898,987,1065,1146,1229,1315,1391,1468,1544,1619,1687,1805"}, "to": {"startLines": "37,38,39,40,41,42,43,102,103,104,105,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3637,3731,3815,3905,4003,4089,4168,10724,10811,10900,10978,11146,11229,11315,11391,11468,11645,11720,11788", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "3726,3810,3900,3998,4084,4163,4269,10806,10895,10973,11054,11224,11310,11386,11463,11539,11715,11783,11901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8734d346b2e96ba5091fe03752072eab\\transformed\\core-1.16.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "30,31,32,33,34,35,36,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2908,3007,3109,3211,3314,3415,3517,11544", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3002,3104,3206,3309,3410,3512,3632,11640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a52cd15d91b3b91eba876f6457455010\\transformed\\appcompat-1.7.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,11059", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,11141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d61245d3231dfae84075323e154f2e0\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4771,4862,4952,5055,5135,5220,5321,5425,5518,5623,5710,5816,5915,6023,6141,6221,6321", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4766,4857,4947,5050,5130,5215,5316,5420,5513,5618,5705,5811,5910,6018,6136,6216,6316,6410"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4274,4412,4549,4668,4802,4919,5018,5134,5276,5397,5539,5624,5730,5824,5925,6054,6183,6294,6423,6550,6680,6860,6982,7102,7224,7355,7450,7545,7678,7825,7922,8027,8137,8264,8396,8503,8604,8681,8784,8884,8990,9081,9171,9274,9354,9439,9540,9644,9737,9842,9929,10035,10134,10242,10360,10440,10540", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "4407,4544,4663,4797,4914,5013,5129,5271,5392,5534,5619,5725,5819,5920,6049,6178,6289,6418,6545,6675,6855,6977,7097,7219,7350,7445,7540,7673,7820,7917,8022,8132,8259,8391,8498,8599,8676,8779,8879,8985,9076,9166,9269,9349,9434,9535,9639,9732,9837,9924,10030,10129,10237,10355,10435,10535,10629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96ccc887d394ddaa1dd6d0e3b9de1bba\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "29,116,117", "startColumns": "4,4,4", "startOffsets": "2835,11906,11991", "endColumns": "72,84,84", "endOffsets": "2903,11986,12071"}}]}]}