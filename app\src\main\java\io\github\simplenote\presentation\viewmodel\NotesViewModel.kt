package io.github.simplenote.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import arrow.core.Either
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.usecase.DeleteNoteUseCase
import io.github.simplenote.domain.usecase.GetNotesUseCase
import io.github.simplenote.domain.usecase.UpdateNoteUseCase
import io.github.simplenote.presentation.model.NavigationEvent
import io.github.simplenote.presentation.model.NotesUiEvent
import io.github.simplenote.presentation.model.NotesUiState
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job

/**
 * ViewModel for the notes list screen.
 * Manages the state of notes list, search, sorting, and color selection.
 */
class NotesViewModel(
    private val getNotesUseCase: GetNotesUseCase,
    private val updateNoteUseCase: UpdateNoteUseCase,
    private val deleteNoteUseCase: DeleteNoteUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(NotesUiState())
    val uiState: StateFlow<NotesUiState> = _uiState.asStateFlow()

    private var searchJob: Job? = null
    
    private val _navigationEvents = MutableSharedFlow<NavigationEvent>()
    val navigationEvents: SharedFlow<NavigationEvent> = _navigationEvents.asSharedFlow()
    
    init {
        loadNotes()
    }
    
    fun onEvent(event: NotesUiEvent) {
        when (event) {
            is NotesUiEvent.LoadNotes -> loadNotes()
            is NotesUiEvent.CreateNewNote -> navigateToNoteEditor()
            is NotesUiEvent.SelectNote -> navigateToNoteEditor(event.noteId)
            is NotesUiEvent.DeleteNote -> showDeleteConfirmation(event.note)
            is NotesUiEvent.SearchNotes -> searchNotes(event.query)
            is NotesUiEvent.UpdateSearchFocus -> updateSearchFocus(event.isFocused)
            is NotesUiEvent.UpdateSearchBarVisibility -> updateSearchBarVisibility(event.isVisible)
            is NotesUiEvent.ChangeSortOrder -> changeSortOrder(event.sortBy)
            is NotesUiEvent.ShowColorBottomSheet -> showColorBottomSheet()
            is NotesUiEvent.HideColorBottomSheet -> hideColorBottomSheet()
            is NotesUiEvent.ChangeNoteColor -> changeNoteColor(event.noteId, event.color)
            is NotesUiEvent.ClearError -> clearError()
        }
    }
    
    private fun loadNotes() {
        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
        
        val sortBy = when (_uiState.value.sortBy) {
            NotesUiState.SortBy.UPDATED_DATE -> GetNotesUseCase.SortBy.UPDATED_DATE
            NotesUiState.SortBy.CREATED_DATE -> GetNotesUseCase.SortBy.CREATED_DATE
            NotesUiState.SortBy.TITLE -> GetNotesUseCase.SortBy.TITLE
        }
        
        getNotesUseCase(sortBy)
            .onEach { result ->
                when (result) {
                    is Either.Left -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.value
                        )
                    }
                    is Either.Right -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            notes = result.value.toImmutableList(),
                            error = null
                        )
                    }
                }
            }
            .launchIn(viewModelScope)
    }
    
    private fun searchNotes(query: String) {
        // Cancel previous search job if exists
        searchJob?.cancel()

        // Update search query immediately for responsive UI
        _uiState.value = _uiState.value.copy(
            searchQuery = query,
            isLoading = query.isNotBlank(),
            error = null
        )

        if (query.isBlank()) {
            // If query is empty, load all notes
            loadNotes()
            return
        }

        searchJob = getNotesUseCase.searchNotes(query)
            .onEach { result ->
                when (result) {
                    is Either.Left -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.value
                        )
                    }
                    is Either.Right -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            notes = result.value.toImmutableList(),
                            error = null
                        )
                    }
                }
            }
            .launchIn(viewModelScope)
    }
    
    private fun changeSortOrder(sortBy: NotesUiState.SortBy) {
        _uiState.value = _uiState.value.copy(sortBy = sortBy)
        loadNotes()
    }
    
    private fun showColorBottomSheet() {
        _uiState.value = _uiState.value.copy(isColorBottomSheetVisible = true)
    }
    
    private fun hideColorBottomSheet() {
        _uiState.value = _uiState.value.copy(
            isColorBottomSheetVisible = false,
            selectedNoteId = null
        )
    }
    
    private fun changeNoteColor(noteId: Long, color: NoteColor) {
        viewModelScope.launch {
            val result = updateNoteUseCase.updateColor(noteId, color)
            when (result) {
                is Either.Left -> {
                    _uiState.value = _uiState.value.copy(error = result.value)
                }
                is Either.Right -> {
                    hideColorBottomSheet()
                }
            }
        }
    }
    
    private fun navigateToNoteEditor(noteId: Long? = null) {
        viewModelScope.launch {
            _navigationEvents.emit(NavigationEvent.NavigateToNoteEditor(noteId))
        }
    }
    
    private fun showDeleteConfirmation(note: Note) {
        viewModelScope.launch {
            _navigationEvents.emit(NavigationEvent.ShowDeleteConfirmation(note))
        }
    }
    
    fun deleteNote(note: Note) {
        viewModelScope.launch {
            val result = deleteNoteUseCase(note)
            when (result) {
                is Either.Left -> {
                    _uiState.value = _uiState.value.copy(error = result.value)
                }
                is Either.Right -> {
                    // Note deleted successfully, list will update automatically via Flow
                }
            }
        }
    }
    
    fun selectNoteForColorChange(noteId: Long) {
        _uiState.value = _uiState.value.copy(
            selectedNoteId = noteId,
            isColorBottomSheetVisible = true
        )
    }
    
    private fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    private fun updateSearchFocus(isFocused: Boolean) {
        _uiState.value = _uiState.value.copy(isSearchFocused = isFocused)
    }
    
    private fun updateSearchBarVisibility(isVisible: Boolean) {
        _uiState.value = _uiState.value.copy(isSearchBarVisible = isVisible)
    }
}
