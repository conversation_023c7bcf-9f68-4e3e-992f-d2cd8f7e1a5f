package io.github.simplenote.domain.repository

import arrow.core.Either
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.model.NoteError
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for Note operations.
 * Defines the contract for data access operations using functional error handling with Arrow.
 */
interface NoteRepository {
    
    /**
     * Get all notes as a reactive stream
     */
    fun getAllNotes(): Flow<Either<NoteError, List<Note>>>
    
    /**
     * Get all notes ordered by created date
     */
    fun getAllNotesByCreatedDate(): Flow<Either<NoteError, List<Note>>>
    
    /**
     * Get all notes ordered by title
     */
    fun getAllNotesByTitle(): Flow<Either<NoteError, List<Note>>>
    
    /**
     * Get a specific note by ID
     */
    suspend fun getNoteById(id: Long): Either<NoteError, Note>
    
    /**
     * Search notes by query string
     */
    fun searchNotes(query: String): Flow<Either<NoteError, List<Note>>>
    
    /**
     * Get notes filtered by color
     */
    fun getNotesByColor(color: NoteColor): Flow<Either<NoteError, List<Note>>>
    
    /**
     * Create a new note
     */
    suspend fun createNote(note: Note): Either<NoteError, Note>
    
    /**
     * Update an existing note
     */
    suspend fun updateNote(note: Note): Either<NoteError, Note>
    
    /**
     * Update note color only
     */
    suspend fun updateNoteColor(id: Long, color: NoteColor): Either<NoteError, Unit>
    
    /**
     * Delete a note
     */
    suspend fun deleteNote(note: Note): Either<NoteError, Unit>
    
    /**
     * Delete a note by ID
     */
    suspend fun deleteNoteById(id: Long): Either<NoteError, Unit>
    
    /**
     * Delete all notes
     */
    suspend fun deleteAllNotes(): Either<NoteError, Unit>
    
    /**
     * Get the total count of notes
     */
    suspend fun getNotesCount(): Either<NoteError, Int>
}
