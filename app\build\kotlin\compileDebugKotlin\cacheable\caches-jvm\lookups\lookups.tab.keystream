  Activity android.app  Application android.app  
AppNavigation android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  SimpleNOTETheme android.app.Activity  Surface android.app.Activity  collectAsStateWithLifecycle android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  getValue android.app.Activity  inject android.app.Activity  io android.app.Activity  launch android.app.Activity  lifecycleScope android.app.Activity  onCreate android.app.Activity  populateSampleDataUseCase android.app.Activity  provideDelegate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  Level android.app.Application  androidContext android.app.Application  
androidLogger android.app.Application  
appModules android.app.Application  onCreate android.app.Application  	startKoin android.app.Application  Context android.content  
AppNavigation android.content.Context  Level android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  SimpleNOTETheme android.content.Context  Surface android.content.Context  androidContext android.content.Context  
androidLogger android.content.Context  
appModules android.content.Context  collectAsStateWithLifecycle android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  getValue android.content.Context  inject android.content.Context  io android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  populateSampleDataUseCase android.content.Context  preferencesDataStore android.content.Context  provideDelegate android.content.Context  
setContent android.content.Context  	startKoin android.content.Context  
AppNavigation android.content.ContextWrapper  Level android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  SimpleNOTETheme android.content.ContextWrapper  Surface android.content.ContextWrapper  androidContext android.content.ContextWrapper  
androidLogger android.content.ContextWrapper  
appModules android.content.ContextWrapper  collectAsStateWithLifecycle android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  getValue android.content.ContextWrapper  inject android.content.ContextWrapper  io android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  populateSampleDataUseCase android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
setContent android.content.ContextWrapper  	startKoin android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  getLong android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  getLong android.os.Bundle  let android.os.Bundle  View android.view  Window android.view  
AppNavigation  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  SimpleNOTETheme  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  collectAsStateWithLifecycle  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  inject  android.view.ContextThemeWrapper  io  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  populateSampleDataUseCase  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  isInEditMode android.view.View  statusBarColor android.view.Window  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
AppNavigation #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  GetThemeSettingsUseCase #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  PopulateSampleDataUseCase #androidx.activity.ComponentActivity  SimpleNOTETheme #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  collectAsStateWithLifecycle #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  inject #androidx.activity.ComponentActivity  io #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  populateSampleDataUseCase #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
AppNavigation -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  SimpleNOTETheme -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  collectAsStateWithLifecycle -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  inject -androidx.activity.ComponentActivity.Companion  io -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  lifecycleScope -androidx.activity.ComponentActivity.Companion  populateSampleDataUseCase -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  AnimatedVisibility androidx.compose.animation  AnimatedVisibilityScope androidx.compose.animation  EnterTransition androidx.compose.animation  ExitTransition androidx.compose.animation  animateColorAsState androidx.compose.animation  expandHorizontally androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  shrinkHorizontally androidx.compose.animation  AppDestination /androidx.compose.animation.AnimatedContentScope  NoteEditorScreen /androidx.compose.animation.AnimatedContentScope  NotesScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  let /androidx.compose.animation.AnimatedContentScope  takeIf /androidx.compose.animation.AnimatedContentScope  Clear 2androidx.compose.animation.AnimatedVisibilityScope  HapticFeedbackType 2androidx.compose.animation.AnimatedVisibilityScope  Icon 2androidx.compose.animation.AnimatedVisibilityScope  
IconButton 2androidx.compose.animation.AnimatedVisibilityScope  Icons 2androidx.compose.animation.AnimatedVisibilityScope  
MaterialTheme 2androidx.compose.animation.AnimatedVisibilityScope  Modifier 2androidx.compose.animation.AnimatedVisibilityScope  dp 2androidx.compose.animation.AnimatedVisibilityScope  size 2androidx.compose.animation.AnimatedVisibilityScope  plus *androidx.compose.animation.EnterTransition  plus )androidx.compose.animation.ExitTransition  	TweenSpec androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  tween androidx.compose.animation.core  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  detectDragGestures $androidx.compose.foundation.gestures  MutableInteractionSource 'androidx.compose.foundation.interaction  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  WindowInsets "androidx.compose.foundation.layout  asPaddingValues "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  navigationBars "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  safeDrawing "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
statusBars "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  windowInsetsPadding "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AnimatedVisibility +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  BasicTextField +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Check +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Clear +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
EmptyState +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  HapticFeedbackType +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  	ImeAction +androidx.compose.foundation.layout.BoxScope  	IntOffset +androidx.compose.foundation.layout.BoxScope  IntegratedSearchBar +androidx.compose.foundation.layout.BoxScope  KeyboardActions +androidx.compose.foundation.layout.BoxScope  KeyboardOptions +androidx.compose.foundation.layout.BoxScope  KeyboardType +androidx.compose.foundation.layout.BoxScope  LazyVerticalStaggeredGrid +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Menu +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  NoteItem +androidx.compose.foundation.layout.BoxScope  NotesUiEvent +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  Palette +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Search +androidx.compose.foundation.layout.BoxScope  Settings +androidx.compose.foundation.layout.BoxScope  
SolidColor +androidx.compose.foundation.layout.BoxScope  StaggeredGridCells +androidx.compose.foundation.layout.BoxScope  StaggeredGridItemSpan +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  WindowInsets +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  expandHorizontally +androidx.compose.foundation.layout.BoxScope  fadeIn +androidx.compose.foundation.layout.BoxScope  fadeOut +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  focusRequester +androidx.compose.foundation.layout.BoxScope  getContrastColor +androidx.compose.foundation.layout.BoxScope  isBlank +androidx.compose.foundation.layout.BoxScope  isEmpty +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  items +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  onFocusChanged +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  
roundToInt +androidx.compose.foundation.layout.BoxScope  shrinkHorizontally +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  
statusBars +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  windowInsetsPadding +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  ColorPalette .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
EmptyState .androidx.compose.foundation.layout.ColumnScope  Error .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	IntOffset .androidx.compose.foundation.layout.ColumnScope  IntegratedSearchBar .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Note .androidx.compose.foundation.layout.ColumnScope  NoteEditorUiEvent .androidx.compose.foundation.layout.ColumnScope  NotesUiEvent .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  Role .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  	ThemeMode .androidx.compose.foundation.layout.ColumnScope  WindowInsets .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  
formatDate .androidx.compose.foundation.layout.ColumnScope  
getErrorTitle .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  isBlank .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  offset .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  
roundToInt .androidx.compose.foundation.layout.ColumnScope  
selectable .androidx.compose.foundation.layout.ColumnScope  selectableGroup .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  
statusBars .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  windowInsetsPadding .androidx.compose.foundation.layout.ColumnScope  calculateBottomPadding 0androidx.compose.foundation.layout.PaddingValues  	Alignment +androidx.compose.foundation.layout.RowScope  AnimatedVisibility +androidx.compose.foundation.layout.RowScope  Archive +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  BasicTextField +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Clear +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  HapticFeedbackType +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  	ImeAction +androidx.compose.foundation.layout.RowScope  KeyboardActions +androidx.compose.foundation.layout.RowScope  KeyboardOptions +androidx.compose.foundation.layout.RowScope  KeyboardType +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Menu +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NoteEditorUiEvent +androidx.compose.foundation.layout.RowScope  
Notifications +androidx.compose.foundation.layout.RowScope  Palette +androidx.compose.foundation.layout.RowScope  PushPin +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Search +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  
SolidColor +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  align +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  expandHorizontally +androidx.compose.foundation.layout.RowScope  fadeIn +androidx.compose.foundation.layout.RowScope  fadeOut +androidx.compose.foundation.layout.RowScope  fillMaxWidth +androidx.compose.foundation.layout.RowScope  focusRequester +androidx.compose.foundation.layout.RowScope  
formatDate +androidx.compose.foundation.layout.RowScope  isEmpty +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  onFocusChanged +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  shrinkHorizontally +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  	Companion /androidx.compose.foundation.layout.WindowInsets  asPaddingValues /androidx.compose.foundation.layout.WindowInsets  navigationBars /androidx.compose.foundation.layout.WindowInsets  
statusBars /androidx.compose.foundation.layout.WindowInsets  navigationBars 9androidx.compose.foundation.layout.WindowInsets.Companion  
statusBars 9androidx.compose.foundation.layout.WindowInsets.Companion  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  
LazyGridState %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  rememberLazyGridState %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  	ColorItem 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	ColorItem 3androidx.compose.foundation.lazy.grid.LazyGridScope  	NoteColor 3androidx.compose.foundation.lazy.grid.LazyGridScope  getAllColors 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  firstVisibleItemIndex 3androidx.compose.foundation.lazy.grid.LazyGridState  firstVisibleItemScrollOffset 3androidx.compose.foundation.lazy.grid.LazyGridState  LazyStaggeredGridItemScope .androidx.compose.foundation.lazy.staggeredgrid  LazyStaggeredGridScope .androidx.compose.foundation.lazy.staggeredgrid  LazyStaggeredGridState .androidx.compose.foundation.lazy.staggeredgrid  LazyVerticalStaggeredGrid .androidx.compose.foundation.lazy.staggeredgrid  StaggeredGridCells .androidx.compose.foundation.lazy.staggeredgrid  StaggeredGridItemSpan .androidx.compose.foundation.lazy.staggeredgrid  items .androidx.compose.foundation.lazy.staggeredgrid  rememberLazyStaggeredGridState .androidx.compose.foundation.lazy.staggeredgrid  Box Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  IntegratedSearchBar Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  Modifier Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  NoteItem Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  NotesUiEvent Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  WindowInsets Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  dp Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  fillMaxWidth Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  padding Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  
statusBars Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  windowInsetsPadding Iandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope  Box Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  IntegratedSearchBar Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  Modifier Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  NoteItem Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  NotesUiEvent Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  StaggeredGridItemSpan Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  WindowInsets Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  dp Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  fillMaxWidth Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  item Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  items Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  padding Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  
statusBars Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  windowInsetsPadding Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridScope  firstVisibleItemIndex Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState  firstVisibleItemScrollOffset Eandroidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState  Fixed Aandroidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells  	Companion Dandroidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan  FullLine Dandroidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan  FullLine Nandroidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan.Companion  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  BasicTextField  androidx.compose.foundation.text  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  HapticFeedbackType 4androidx.compose.foundation.text.KeyboardActionScope  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Outlined %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  Outlined 2androidx.compose.material.icons.Icons.AutoMirrored  	ArrowBack 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Note 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  	ArrowBack ;androidx.compose.material.icons.Icons.AutoMirrored.Outlined  Add ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  Clear ,androidx.compose.material.icons.Icons.Filled  Error ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Archive .androidx.compose.material.icons.Icons.Outlined  Delete .androidx.compose.material.icons.Icons.Outlined  Menu .androidx.compose.material.icons.Icons.Outlined  
Notifications .androidx.compose.material.icons.Icons.Outlined  Palette .androidx.compose.material.icons.Icons.Outlined  PushPin .androidx.compose.material.icons.Icons.Outlined  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Note 3androidx.compose.material.icons.automirrored.filled  	ArrowBack 5androidx.compose.material.icons.automirrored.outlined  Add &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Error &androidx.compose.material.icons.filled  Palette &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Archive (androidx.compose.material.icons.outlined  Delete (androidx.compose.material.icons.outlined  Menu (androidx.compose.material.icons.outlined  
Notifications (androidx.compose.material.icons.outlined  Palette (androidx.compose.material.icons.outlined  PushPin (androidx.compose.material.icons.outlined  AlertDialog androidx.compose.material3  BottomAppBar androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  
MaterialTheme androidx.compose.material3  ModalBottomSheet androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  RadioButton androidx.compose.material3  Scaffold androidx.compose.material3  
SheetState androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  SnackbarResult androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  rememberModalBottomSheetState androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  showSnackbar ,androidx.compose.material3.SnackbarHostState  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
Composable androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
SideEffect androidx.compose.runtime  State androidx.compose.runtime  derivedStateOf androidx.compose.runtime  getValue androidx.compose.runtime  mutableFloatStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  focusRequester androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  onFocusChanged androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  
selectable androidx.compose.ui.Modifier  selectableGroup androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  windowInsetsPadding androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  focusRequester &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  selectableGroup &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  
FocusState androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  onFocusChanged androidx.compose.ui.focus  requestFocus (androidx.compose.ui.focus.FocusRequester  	isFocused $androidx.compose.ui.focus.FocusState  Offset androidx.compose.ui.geometry  Color androidx.compose.ui.graphics  
SolidColor androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  blue "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  green "androidx.compose.ui.graphics.Color  red "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  HapticFeedback "androidx.compose.ui.hapticfeedback  HapticFeedbackType "androidx.compose.ui.hapticfeedback  performHapticFeedback 1androidx.compose.ui.hapticfeedback.HapticFeedback  	Companion 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  TextHandleMove 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  TextHandleMove ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  nestedScroll &androidx.compose.ui.input.nestedscroll  pointerInput !androidx.compose.ui.input.pointer  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  SoftwareKeyboardController androidx.compose.ui.platform  hide 7androidx.compose.ui.platform.SoftwareKeyboardController  Role androidx.compose.ui.semantics  	Companion "androidx.compose.ui.semantics.Role  RadioButton "androidx.compose.ui.semantics.Role  RadioButton ,androidx.compose.ui.semantics.Role.Companion  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Search (androidx.compose.ui.text.input.ImeAction  Search 2androidx.compose.ui.text.input.ImeAction.Companion  	Companion +androidx.compose.ui.text.input.KeyboardType  Text +androidx.compose.ui.text.input.KeyboardType  Text 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  	Companion +androidx.compose.ui.text.style.TextOverflow  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  Preview #androidx.compose.ui.tooling.preview  PreviewLightDark #androidx.compose.ui.tooling.preview  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  	IntOffset androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  	IntOffset  androidx.compose.ui.unit.Density  dp  androidx.compose.ui.unit.Density  
roundToInt  androidx.compose.ui.unit.Density  toPx  androidx.compose.ui.unit.Density  plus androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  
AppNavigation #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  GetThemeSettingsUseCase #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  PopulateSampleDataUseCase #androidx.core.app.ComponentActivity  SimpleNOTETheme #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  collectAsStateWithLifecycle #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  inject #androidx.core.app.ComponentActivity  io #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  populateSampleDataUseCase #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  LifecycleCoroutineScope androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  collectAsStateWithLifecycle androidx.lifecycle.compose  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  AppDestination #androidx.navigation.NavGraphBuilder  NoteEditorScreen #androidx.navigation.NavGraphBuilder  NotesScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  let #androidx.navigation.NavGraphBuilder  takeIf #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
ColumnInfo 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  EntityDeleteOrUpdateAdapter 
androidx.room  EntityInsertAdapter 
androidx.room  Insert 
androidx.room  InvalidationTracker 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  Update 
androidx.room  handle )androidx.room.EntityDeleteOrUpdateAdapter  insertAndReturnId !androidx.room.EntityInsertAdapter  insertAndReturnIdsList !androidx.room.EntityInsertAdapter  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Array androidx.room.RoomDatabase  AutoMigrationSpec androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  InvalidationTracker androidx.room.RoomDatabase  KClass androidx.room.RoomDatabase  Lazy androidx.room.RoomDatabase  List androidx.room.RoomDatabase  Map androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  MutableList androidx.room.RoomDatabase  
MutableMap androidx.room.RoomDatabase  
MutableSet androidx.room.RoomDatabase  NoteDao androidx.room.RoomDatabase  NoteDao_Impl androidx.room.RoomDatabase  NoteDatabase androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  RoomOpenDelegate androidx.room.RoomDatabase  SQLiteConnection androidx.room.RoomDatabase  Set androidx.room.RoomDatabase  String androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  	TableInfo androidx.room.RoomDatabase  arrayOf androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  dropFtsSyncTriggers androidx.room.RoomDatabase  execSQL androidx.room.RoomDatabase  getRequiredConverters androidx.room.RoomDatabase  internalInitInvalidationTracker androidx.room.RoomDatabase  java androidx.room.RoomDatabase  lazy androidx.room.RoomDatabase  
mutableListOf androidx.room.RoomDatabase  mutableMapOf androidx.room.RoomDatabase  mutableSetOf androidx.room.RoomDatabase  performClear androidx.room.RoomDatabase  read androidx.room.RoomDatabase  
trimMargin androidx.room.RoomDatabase  
addMigrations "androidx.room.RoomDatabase.Builder  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  InvalidationTracker $androidx.room.RoomDatabase.Companion  NoteDao $androidx.room.RoomDatabase.Companion  NoteDao_Impl $androidx.room.RoomDatabase.Companion  NoteDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  RoomOpenDelegate $androidx.room.RoomDatabase.Companion  	TableInfo $androidx.room.RoomDatabase.Companion  arrayOf $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  dropFtsSyncTriggers $androidx.room.RoomDatabase.Companion  execSQL $androidx.room.RoomDatabase.Companion  getRequiredConverters $androidx.room.RoomDatabase.Companion  internalInitInvalidationTracker $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  lazy $androidx.room.RoomDatabase.Companion  
mutableListOf $androidx.room.RoomDatabase.Companion  mutableMapOf $androidx.room.RoomDatabase.Companion  mutableSetOf $androidx.room.RoomDatabase.Companion  read $androidx.room.RoomDatabase.Companion  
trimMargin $androidx.room.RoomDatabase.Companion  ValidationResult +androidx.room.RoomDatabase.RoomOpenDelegate  Column $androidx.room.RoomDatabase.TableInfo  
ForeignKey $androidx.room.RoomDatabase.TableInfo  Index $androidx.room.RoomDatabase.TableInfo  
MutableMap androidx.room.RoomOpenDelegate  
MutableSet androidx.room.RoomOpenDelegate  RoomOpenDelegate androidx.room.RoomOpenDelegate  SQLiteConnection androidx.room.RoomOpenDelegate  String androidx.room.RoomOpenDelegate  	TableInfo androidx.room.RoomOpenDelegate  ValidationResult androidx.room.RoomOpenDelegate  dropFtsSyncTriggers androidx.room.RoomOpenDelegate  execSQL androidx.room.RoomOpenDelegate  internalInitInvalidationTracker androidx.room.RoomOpenDelegate  mutableMapOf androidx.room.RoomOpenDelegate  mutableSetOf androidx.room.RoomOpenDelegate  read androidx.room.RoomOpenDelegate  
trimMargin androidx.room.RoomOpenDelegate  ValidationResult /androidx.room.RoomOpenDelegate.RoomOpenDelegate  Column (androidx.room.RoomOpenDelegate.TableInfo  
ForeignKey (androidx.room.RoomOpenDelegate.TableInfo  Index (androidx.room.RoomOpenDelegate.TableInfo  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  getTotalChangedRows androidx.room.util  performSuspending androidx.room.util  CREATED_FROM_ENTITY androidx.room.util.TableInfo  Column androidx.room.util.TableInfo  	Companion androidx.room.util.TableInfo  
ForeignKey androidx.room.util.TableInfo  Index androidx.room.util.TableInfo  equals androidx.room.util.TableInfo  CREATED_FROM_ENTITY &androidx.room.util.TableInfo.Companion  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  execSQL  androidx.sqlite.SQLiteConnection  prepare  androidx.sqlite.SQLiteConnection  bindLong androidx.sqlite.SQLiteStatement  bindText androidx.sqlite.SQLiteStatement  close androidx.sqlite.SQLiteStatement  getLong androidx.sqlite.SQLiteStatement  getText androidx.sqlite.SQLiteStatement  step androidx.sqlite.SQLiteStatement  SupportSQLiteDatabase androidx.sqlite.db  Either 
arrow.core  left 
arrow.core  	Companion arrow.core.Either  Left arrow.core.Either  Right arrow.core.Either  value arrow.core.Either.Left  value arrow.core.Either.Right  
AppNavigation io.github.simplenote  Application io.github.simplenote  Bundle io.github.simplenote  ComponentActivity io.github.simplenote  GetThemeSettingsUseCase io.github.simplenote  Level io.github.simplenote  MainActivity io.github.simplenote  
MaterialTheme io.github.simplenote  Modifier io.github.simplenote  PopulateSampleDataUseCase io.github.simplenote  SimpleNOTETheme io.github.simplenote  SimpleNoteApplication io.github.simplenote  Surface io.github.simplenote  
appModules io.github.simplenote  collectAsStateWithLifecycle io.github.simplenote  fillMaxSize io.github.simplenote  getValue io.github.simplenote  io io.github.simplenote  launch io.github.simplenote  populateSampleDataUseCase io.github.simplenote  provideDelegate io.github.simplenote  	startKoin io.github.simplenote  
AppNavigation !io.github.simplenote.MainActivity  
MaterialTheme !io.github.simplenote.MainActivity  Modifier !io.github.simplenote.MainActivity  SimpleNOTETheme !io.github.simplenote.MainActivity  Surface !io.github.simplenote.MainActivity  collectAsStateWithLifecycle !io.github.simplenote.MainActivity  enableEdgeToEdge !io.github.simplenote.MainActivity  fillMaxSize !io.github.simplenote.MainActivity  getThemeSettingsUseCase !io.github.simplenote.MainActivity  getValue !io.github.simplenote.MainActivity  inject !io.github.simplenote.MainActivity  io !io.github.simplenote.MainActivity  launch !io.github.simplenote.MainActivity  lifecycleScope !io.github.simplenote.MainActivity  populateSampleDataUseCase !io.github.simplenote.MainActivity  provideDelegate !io.github.simplenote.MainActivity  
setContent !io.github.simplenote.MainActivity  Level *io.github.simplenote.SimpleNoteApplication  androidContext *io.github.simplenote.SimpleNoteApplication  
androidLogger *io.github.simplenote.SimpleNoteApplication  
appModules *io.github.simplenote.SimpleNoteApplication  	startKoin *io.github.simplenote.SimpleNoteApplication  Dao #io.github.simplenote.data.local.dao  Delete #io.github.simplenote.data.local.dao  EntityDeleteOrUpdateAdapter #io.github.simplenote.data.local.dao  EntityInsertAdapter #io.github.simplenote.data.local.dao  Flow #io.github.simplenote.data.local.dao  	Generated #io.github.simplenote.data.local.dao  Insert #io.github.simplenote.data.local.dao  Int #io.github.simplenote.data.local.dao  KClass #io.github.simplenote.data.local.dao  List #io.github.simplenote.data.local.dao  Long #io.github.simplenote.data.local.dao  MutableList #io.github.simplenote.data.local.dao  NoteDao #io.github.simplenote.data.local.dao  NoteDao_Impl #io.github.simplenote.data.local.dao  
NoteEntity #io.github.simplenote.data.local.dao  OnConflictStrategy #io.github.simplenote.data.local.dao  Query #io.github.simplenote.data.local.dao  RoomDatabase #io.github.simplenote.data.local.dao  SQLiteStatement #io.github.simplenote.data.local.dao  String #io.github.simplenote.data.local.dao  Suppress #io.github.simplenote.data.local.dao  Update #io.github.simplenote.data.local.dao  arrayOf #io.github.simplenote.data.local.dao  
createFlow #io.github.simplenote.data.local.dao  	emptyList #io.github.simplenote.data.local.dao  getColumnIndexOrThrow #io.github.simplenote.data.local.dao  getTotalChangedRows #io.github.simplenote.data.local.dao  
mutableListOf #io.github.simplenote.data.local.dao  performSuspending #io.github.simplenote.data.local.dao  
plusAssign #io.github.simplenote.data.local.dao  
trimMargin #io.github.simplenote.data.local.dao  OnConflictStrategy +io.github.simplenote.data.local.dao.NoteDao  deleteAllNotes +io.github.simplenote.data.local.dao.NoteDao  
deleteNote +io.github.simplenote.data.local.dao.NoteDao  deleteNoteById +io.github.simplenote.data.local.dao.NoteDao  getAllNotes +io.github.simplenote.data.local.dao.NoteDao  getAllNotesByCreatedDate +io.github.simplenote.data.local.dao.NoteDao  getAllNotesByTitle +io.github.simplenote.data.local.dao.NoteDao  getNoteById +io.github.simplenote.data.local.dao.NoteDao  getNotesByColor +io.github.simplenote.data.local.dao.NoteDao  
getNotesCount +io.github.simplenote.data.local.dao.NoteDao  
insertNote +io.github.simplenote.data.local.dao.NoteDao  searchNotes +io.github.simplenote.data.local.dao.NoteDao  
updateNote +io.github.simplenote.data.local.dao.NoteDao  updateNoteColor +io.github.simplenote.data.local.dao.NoteDao  	Companion 0io.github.simplenote.data.local.dao.NoteDao_Impl  EntityDeleteOrUpdateAdapter 0io.github.simplenote.data.local.dao.NoteDao_Impl  EntityInsertAdapter 0io.github.simplenote.data.local.dao.NoteDao_Impl  Flow 0io.github.simplenote.data.local.dao.NoteDao_Impl  Int 0io.github.simplenote.data.local.dao.NoteDao_Impl  KClass 0io.github.simplenote.data.local.dao.NoteDao_Impl  List 0io.github.simplenote.data.local.dao.NoteDao_Impl  Long 0io.github.simplenote.data.local.dao.NoteDao_Impl  MutableList 0io.github.simplenote.data.local.dao.NoteDao_Impl  
NoteEntity 0io.github.simplenote.data.local.dao.NoteDao_Impl  RoomDatabase 0io.github.simplenote.data.local.dao.NoteDao_Impl  SQLiteStatement 0io.github.simplenote.data.local.dao.NoteDao_Impl  String 0io.github.simplenote.data.local.dao.NoteDao_Impl  __db 0io.github.simplenote.data.local.dao.NoteDao_Impl  __deleteAdapterOfNoteEntity 0io.github.simplenote.data.local.dao.NoteDao_Impl  __insertAdapterOfNoteEntity 0io.github.simplenote.data.local.dao.NoteDao_Impl  __updateAdapterOfNoteEntity 0io.github.simplenote.data.local.dao.NoteDao_Impl  arrayOf 0io.github.simplenote.data.local.dao.NoteDao_Impl  
createFlow 0io.github.simplenote.data.local.dao.NoteDao_Impl  	emptyList 0io.github.simplenote.data.local.dao.NoteDao_Impl  getColumnIndexOrThrow 0io.github.simplenote.data.local.dao.NoteDao_Impl  getRequiredConverters 0io.github.simplenote.data.local.dao.NoteDao_Impl  getTotalChangedRows 0io.github.simplenote.data.local.dao.NoteDao_Impl  
mutableListOf 0io.github.simplenote.data.local.dao.NoteDao_Impl  performSuspending 0io.github.simplenote.data.local.dao.NoteDao_Impl  
plusAssign 0io.github.simplenote.data.local.dao.NoteDao_Impl  
trimMargin 0io.github.simplenote.data.local.dao.NoteDao_Impl  
NoteEntity :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  arrayOf :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  
createFlow :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  	emptyList :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  getColumnIndexOrThrow :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  getRequiredConverters :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  getTotalChangedRows :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  
mutableListOf :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  performSuspending :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  
plusAssign :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  
trimMargin :io.github.simplenote.data.local.dao.NoteDao_Impl.Companion  Array (io.github.simplenote.data.local.database  AutoMigrationSpec (io.github.simplenote.data.local.database  Context (io.github.simplenote.data.local.database  Database (io.github.simplenote.data.local.database  	Generated (io.github.simplenote.data.local.database  InvalidationTracker (io.github.simplenote.data.local.database  KClass (io.github.simplenote.data.local.database  Lazy (io.github.simplenote.data.local.database  List (io.github.simplenote.data.local.database  Map (io.github.simplenote.data.local.database  	Migration (io.github.simplenote.data.local.database  MutableList (io.github.simplenote.data.local.database  
MutableMap (io.github.simplenote.data.local.database  
MutableSet (io.github.simplenote.data.local.database  NoteDao (io.github.simplenote.data.local.database  NoteDao_Impl (io.github.simplenote.data.local.database  NoteDatabase (io.github.simplenote.data.local.database  NoteDatabase_Impl (io.github.simplenote.data.local.database  
NoteEntity (io.github.simplenote.data.local.database  Room (io.github.simplenote.data.local.database  RoomDatabase (io.github.simplenote.data.local.database  RoomOpenDelegate (io.github.simplenote.data.local.database  SQLiteConnection (io.github.simplenote.data.local.database  Set (io.github.simplenote.data.local.database  String (io.github.simplenote.data.local.database  SupportSQLiteDatabase (io.github.simplenote.data.local.database  Suppress (io.github.simplenote.data.local.database  	TableInfo (io.github.simplenote.data.local.database  arrayOf (io.github.simplenote.data.local.database  databaseBuilder (io.github.simplenote.data.local.database  dropFtsSyncTriggers (io.github.simplenote.data.local.database  execSQL (io.github.simplenote.data.local.database  getRequiredConverters (io.github.simplenote.data.local.database  internalInitInvalidationTracker (io.github.simplenote.data.local.database  java (io.github.simplenote.data.local.database  lazy (io.github.simplenote.data.local.database  
mutableListOf (io.github.simplenote.data.local.database  mutableMapOf (io.github.simplenote.data.local.database  mutableSetOf (io.github.simplenote.data.local.database  read (io.github.simplenote.data.local.database  
trimMargin (io.github.simplenote.data.local.database  Array 5io.github.simplenote.data.local.database.NoteDatabase  AutoMigrationSpec 5io.github.simplenote.data.local.database.NoteDatabase  	Companion 5io.github.simplenote.data.local.database.NoteDatabase  Context 5io.github.simplenote.data.local.database.NoteDatabase  
DATABASE_NAME 5io.github.simplenote.data.local.database.NoteDatabase  InvalidationTracker 5io.github.simplenote.data.local.database.NoteDatabase  KClass 5io.github.simplenote.data.local.database.NoteDatabase  Lazy 5io.github.simplenote.data.local.database.NoteDatabase  List 5io.github.simplenote.data.local.database.NoteDatabase  Map 5io.github.simplenote.data.local.database.NoteDatabase  	Migration 5io.github.simplenote.data.local.database.NoteDatabase  MutableList 5io.github.simplenote.data.local.database.NoteDatabase  
MutableMap 5io.github.simplenote.data.local.database.NoteDatabase  
MutableSet 5io.github.simplenote.data.local.database.NoteDatabase  NoteDao 5io.github.simplenote.data.local.database.NoteDatabase  NoteDao_Impl 5io.github.simplenote.data.local.database.NoteDatabase  NoteDatabase 5io.github.simplenote.data.local.database.NoteDatabase  Room 5io.github.simplenote.data.local.database.NoteDatabase  RoomOpenDelegate 5io.github.simplenote.data.local.database.NoteDatabase  SQLiteConnection 5io.github.simplenote.data.local.database.NoteDatabase  Set 5io.github.simplenote.data.local.database.NoteDatabase  String 5io.github.simplenote.data.local.database.NoteDatabase  SupportSQLiteDatabase 5io.github.simplenote.data.local.database.NoteDatabase  	TableInfo 5io.github.simplenote.data.local.database.NoteDatabase  arrayOf 5io.github.simplenote.data.local.database.NoteDatabase  create 5io.github.simplenote.data.local.database.NoteDatabase  databaseBuilder 5io.github.simplenote.data.local.database.NoteDatabase  dropFtsSyncTriggers 5io.github.simplenote.data.local.database.NoteDatabase  execSQL 5io.github.simplenote.data.local.database.NoteDatabase  getAllMigrations 5io.github.simplenote.data.local.database.NoteDatabase  getRequiredConverters 5io.github.simplenote.data.local.database.NoteDatabase  internalInitInvalidationTracker 5io.github.simplenote.data.local.database.NoteDatabase  java 5io.github.simplenote.data.local.database.NoteDatabase  lazy 5io.github.simplenote.data.local.database.NoteDatabase  
mutableListOf 5io.github.simplenote.data.local.database.NoteDatabase  mutableMapOf 5io.github.simplenote.data.local.database.NoteDatabase  mutableSetOf 5io.github.simplenote.data.local.database.NoteDatabase  noteDao 5io.github.simplenote.data.local.database.NoteDatabase  performClear 5io.github.simplenote.data.local.database.NoteDatabase  read 5io.github.simplenote.data.local.database.NoteDatabase  
trimMargin 5io.github.simplenote.data.local.database.NoteDatabase  
DATABASE_NAME ?io.github.simplenote.data.local.database.NoteDatabase.Companion  InvalidationTracker ?io.github.simplenote.data.local.database.NoteDatabase.Companion  NoteDao ?io.github.simplenote.data.local.database.NoteDatabase.Companion  NoteDao_Impl ?io.github.simplenote.data.local.database.NoteDatabase.Companion  NoteDatabase ?io.github.simplenote.data.local.database.NoteDatabase.Companion  Room ?io.github.simplenote.data.local.database.NoteDatabase.Companion  RoomOpenDelegate ?io.github.simplenote.data.local.database.NoteDatabase.Companion  	TableInfo ?io.github.simplenote.data.local.database.NoteDatabase.Companion  arrayOf ?io.github.simplenote.data.local.database.NoteDatabase.Companion  create ?io.github.simplenote.data.local.database.NoteDatabase.Companion  databaseBuilder ?io.github.simplenote.data.local.database.NoteDatabase.Companion  dropFtsSyncTriggers ?io.github.simplenote.data.local.database.NoteDatabase.Companion  execSQL ?io.github.simplenote.data.local.database.NoteDatabase.Companion  getAllMigrations ?io.github.simplenote.data.local.database.NoteDatabase.Companion  getRequiredConverters ?io.github.simplenote.data.local.database.NoteDatabase.Companion  internalInitInvalidationTracker ?io.github.simplenote.data.local.database.NoteDatabase.Companion  java ?io.github.simplenote.data.local.database.NoteDatabase.Companion  lazy ?io.github.simplenote.data.local.database.NoteDatabase.Companion  
mutableListOf ?io.github.simplenote.data.local.database.NoteDatabase.Companion  mutableMapOf ?io.github.simplenote.data.local.database.NoteDatabase.Companion  mutableSetOf ?io.github.simplenote.data.local.database.NoteDatabase.Companion  read ?io.github.simplenote.data.local.database.NoteDatabase.Companion  
trimMargin ?io.github.simplenote.data.local.database.NoteDatabase.Companion  ValidationResult Fio.github.simplenote.data.local.database.NoteDatabase.RoomOpenDelegate  Column ?io.github.simplenote.data.local.database.NoteDatabase.TableInfo  
ForeignKey ?io.github.simplenote.data.local.database.NoteDatabase.TableInfo  Index ?io.github.simplenote.data.local.database.NoteDatabase.TableInfo  InvalidationTracker :io.github.simplenote.data.local.database.NoteDatabase_Impl  NoteDao :io.github.simplenote.data.local.database.NoteDatabase_Impl  NoteDao_Impl :io.github.simplenote.data.local.database.NoteDatabase_Impl  RoomOpenDelegate :io.github.simplenote.data.local.database.NoteDatabase_Impl  	TableInfo :io.github.simplenote.data.local.database.NoteDatabase_Impl  _noteDao :io.github.simplenote.data.local.database.NoteDatabase_Impl  dropFtsSyncTriggers :io.github.simplenote.data.local.database.NoteDatabase_Impl  execSQL :io.github.simplenote.data.local.database.NoteDatabase_Impl  getRequiredConverters :io.github.simplenote.data.local.database.NoteDatabase_Impl  internalInitInvalidationTracker :io.github.simplenote.data.local.database.NoteDatabase_Impl  lazy :io.github.simplenote.data.local.database.NoteDatabase_Impl  
mutableListOf :io.github.simplenote.data.local.database.NoteDatabase_Impl  mutableMapOf :io.github.simplenote.data.local.database.NoteDatabase_Impl  mutableSetOf :io.github.simplenote.data.local.database.NoteDatabase_Impl  read :io.github.simplenote.data.local.database.NoteDatabase_Impl  
trimMargin :io.github.simplenote.data.local.database.NoteDatabase_Impl  ValidationResult 9io.github.simplenote.data.local.database.RoomOpenDelegate  Column 2io.github.simplenote.data.local.database.TableInfo  
ForeignKey 2io.github.simplenote.data.local.database.TableInfo  Index 2io.github.simplenote.data.local.database.TableInfo  
ColumnInfo &io.github.simplenote.data.local.entity  Entity &io.github.simplenote.data.local.entity  Instant &io.github.simplenote.data.local.entity  Int &io.github.simplenote.data.local.entity  Long &io.github.simplenote.data.local.entity  Note &io.github.simplenote.data.local.entity  	NoteColor &io.github.simplenote.data.local.entity  
NoteEntity &io.github.simplenote.data.local.entity  
PrimaryKey &io.github.simplenote.data.local.entity  String &io.github.simplenote.data.local.entity  fromEpochMilliseconds &io.github.simplenote.data.local.entity  fromId &io.github.simplenote.data.local.entity  toDomain &io.github.simplenote.data.local.entity  toEntity &io.github.simplenote.data.local.entity  Instant 1io.github.simplenote.data.local.entity.NoteEntity  Note 1io.github.simplenote.data.local.entity.NoteEntity  	NoteColor 1io.github.simplenote.data.local.entity.NoteEntity  colorId 1io.github.simplenote.data.local.entity.NoteEntity  content 1io.github.simplenote.data.local.entity.NoteEntity  	createdAt 1io.github.simplenote.data.local.entity.NoteEntity  fromEpochMilliseconds 1io.github.simplenote.data.local.entity.NoteEntity  fromId 1io.github.simplenote.data.local.entity.NoteEntity  id 1io.github.simplenote.data.local.entity.NoteEntity  title 1io.github.simplenote.data.local.entity.NoteEntity  toDomain 1io.github.simplenote.data.local.entity.NoteEntity  	updatedAt 1io.github.simplenote.data.local.entity.NoteEntity  Boolean %io.github.simplenote.data.preferences  	DataStore %io.github.simplenote.data.preferences  Flow %io.github.simplenote.data.preferences  Preferences %io.github.simplenote.data.preferences  THEME_MODE_KEY %io.github.simplenote.data.preferences  	ThemeMode %io.github.simplenote.data.preferences  ThemePreferences %io.github.simplenote.data.preferences  
ThemeSettings %io.github.simplenote.data.preferences  USE_DYNAMIC_THEME_KEY %io.github.simplenote.data.preferences  booleanPreferencesKey %io.github.simplenote.data.preferences  edit %io.github.simplenote.data.preferences  
fromString %io.github.simplenote.data.preferences  map %io.github.simplenote.data.preferences  stringPreferencesKey %io.github.simplenote.data.preferences  Boolean 6io.github.simplenote.data.preferences.ThemePreferences  	DataStore 6io.github.simplenote.data.preferences.ThemePreferences  Flow 6io.github.simplenote.data.preferences.ThemePreferences  Preferences 6io.github.simplenote.data.preferences.ThemePreferences  THEME_MODE_KEY 6io.github.simplenote.data.preferences.ThemePreferences  	ThemeMode 6io.github.simplenote.data.preferences.ThemePreferences  
ThemeSettings 6io.github.simplenote.data.preferences.ThemePreferences  USE_DYNAMIC_THEME_KEY 6io.github.simplenote.data.preferences.ThemePreferences  booleanPreferencesKey 6io.github.simplenote.data.preferences.ThemePreferences  	dataStore 6io.github.simplenote.data.preferences.ThemePreferences  edit 6io.github.simplenote.data.preferences.ThemePreferences  
fromString 6io.github.simplenote.data.preferences.ThemePreferences  map 6io.github.simplenote.data.preferences.ThemePreferences  stringPreferencesKey 6io.github.simplenote.data.preferences.ThemePreferences  
themeSettings 6io.github.simplenote.data.preferences.ThemePreferences  updateThemeMode 6io.github.simplenote.data.preferences.ThemePreferences  updateThemeSettings 6io.github.simplenote.data.preferences.ThemePreferences  updateUseDynamicTheme 6io.github.simplenote.data.preferences.ThemePreferences  THEME_MODE_KEY @io.github.simplenote.data.preferences.ThemePreferences.Companion  	ThemeMode @io.github.simplenote.data.preferences.ThemePreferences.Companion  
ThemeSettings @io.github.simplenote.data.preferences.ThemePreferences.Companion  USE_DYNAMIC_THEME_KEY @io.github.simplenote.data.preferences.ThemePreferences.Companion  booleanPreferencesKey @io.github.simplenote.data.preferences.ThemePreferences.Companion  edit @io.github.simplenote.data.preferences.ThemePreferences.Companion  
fromString @io.github.simplenote.data.preferences.ThemePreferences.Companion  map @io.github.simplenote.data.preferences.ThemePreferences.Companion  stringPreferencesKey @io.github.simplenote.data.preferences.ThemePreferences.Companion  Clock $io.github.simplenote.data.repository  Either $io.github.simplenote.data.repository  	Exception $io.github.simplenote.data.repository  Flow $io.github.simplenote.data.repository  Int $io.github.simplenote.data.repository  List $io.github.simplenote.data.repository  Long $io.github.simplenote.data.repository  Note $io.github.simplenote.data.repository  	NoteColor $io.github.simplenote.data.repository  NoteDao $io.github.simplenote.data.repository  	NoteError $io.github.simplenote.data.repository  NoteRepository $io.github.simplenote.data.repository  NoteRepositoryImpl $io.github.simplenote.data.repository  String $io.github.simplenote.data.repository  Unit $io.github.simplenote.data.repository  catch $io.github.simplenote.data.repository  map $io.github.simplenote.data.repository  now $io.github.simplenote.data.repository  toDomain $io.github.simplenote.data.repository  toEntity $io.github.simplenote.data.repository  Clock 7io.github.simplenote.data.repository.NoteRepositoryImpl  Either 7io.github.simplenote.data.repository.NoteRepositoryImpl  	NoteError 7io.github.simplenote.data.repository.NoteRepositoryImpl  Unit 7io.github.simplenote.data.repository.NoteRepositoryImpl  catch 7io.github.simplenote.data.repository.NoteRepositoryImpl  map 7io.github.simplenote.data.repository.NoteRepositoryImpl  noteDao 7io.github.simplenote.data.repository.NoteRepositoryImpl  now 7io.github.simplenote.data.repository.NoteRepositoryImpl  toDomain 7io.github.simplenote.data.repository.NoteRepositoryImpl  toEntity 7io.github.simplenote.data.repository.NoteRepositoryImpl  Clock io.github.simplenote.data.util  Int io.github.simplenote.data.util  List io.github.simplenote.data.util  Note io.github.simplenote.data.util  	NoteColor io.github.simplenote.data.util  Random io.github.simplenote.data.util  SampleDataGenerator io.github.simplenote.data.util  create io.github.simplenote.data.util  listOf io.github.simplenote.data.util  
mutableListOf io.github.simplenote.data.util  nextBoolean io.github.simplenote.data.util  nextInt io.github.simplenote.data.util  now io.github.simplenote.data.util  repeat io.github.simplenote.data.util  sortedByDescending io.github.simplenote.data.util  toList io.github.simplenote.data.util  
trimMargin io.github.simplenote.data.util  Clock 2io.github.simplenote.data.util.SampleDataGenerator  Note 2io.github.simplenote.data.util.SampleDataGenerator  	NoteColor 2io.github.simplenote.data.util.SampleDataGenerator  Random 2io.github.simplenote.data.util.SampleDataGenerator  create 2io.github.simplenote.data.util.SampleDataGenerator  days 2io.github.simplenote.data.util.SampleDataGenerator  generateSampleNotes 2io.github.simplenote.data.util.SampleDataGenerator  hours 2io.github.simplenote.data.util.SampleDataGenerator  listOf 2io.github.simplenote.data.util.SampleDataGenerator  
mutableListOf 2io.github.simplenote.data.util.SampleDataGenerator  nextBoolean 2io.github.simplenote.data.util.SampleDataGenerator  nextInt 2io.github.simplenote.data.util.SampleDataGenerator  now 2io.github.simplenote.data.util.SampleDataGenerator  repeat 2io.github.simplenote.data.util.SampleDataGenerator  sampleContents 2io.github.simplenote.data.util.SampleDataGenerator  sampleTitles 2io.github.simplenote.data.util.SampleDataGenerator  sortedByDescending 2io.github.simplenote.data.util.SampleDataGenerator  toList 2io.github.simplenote.data.util.SampleDataGenerator  
trimMargin 2io.github.simplenote.data.util.SampleDataGenerator  CreateNoteUseCase io.github.simplenote.di  	DataStore io.github.simplenote.di  DeleteNoteUseCase io.github.simplenote.di  GetNotesUseCase io.github.simplenote.di  GetThemeSettingsUseCase io.github.simplenote.di  NoteDatabase io.github.simplenote.di  NoteEditorViewModel io.github.simplenote.di  NoteRepository io.github.simplenote.di  NoteRepositoryImpl io.github.simplenote.di  NotesViewModel io.github.simplenote.di  PopulateSampleDataUseCase io.github.simplenote.di  Preferences io.github.simplenote.di  SettingsViewModel io.github.simplenote.di  ThemePreferences io.github.simplenote.di  UpdateNoteUseCase io.github.simplenote.di  UpdateThemeSettingsUseCase io.github.simplenote.di  android io.github.simplenote.di  
appModules io.github.simplenote.di  create io.github.simplenote.di  databaseModule io.github.simplenote.di  listOf io.github.simplenote.di  preferencesDataStore io.github.simplenote.di  preferencesModule io.github.simplenote.di  provideDelegate io.github.simplenote.di  repositoryModule io.github.simplenote.di  
useCaseModule io.github.simplenote.di  viewModelModule io.github.simplenote.di  content io.github.simplenote.di.android  Context 'io.github.simplenote.di.android.content  AppError !io.github.simplenote.domain.model  Boolean !io.github.simplenote.domain.model  Color !io.github.simplenote.domain.model  
DatabaseError !io.github.simplenote.domain.model  Instant !io.github.simplenote.domain.model  Int !io.github.simplenote.domain.model  List !io.github.simplenote.domain.model  Long !io.github.simplenote.domain.model  Note !io.github.simplenote.domain.model  	NoteColor !io.github.simplenote.domain.model  	NoteError !io.github.simplenote.domain.model  NoteValidation !io.github.simplenote.domain.model  SYSTEM !io.github.simplenote.domain.model  String !io.github.simplenote.domain.model  	ThemeMode !io.github.simplenote.domain.model  
ThemeSettings !io.github.simplenote.domain.model  ValidationError !io.github.simplenote.domain.model  YELLOW !io.github.simplenote.domain.model  entries !io.github.simplenote.domain.model  find !io.github.simplenote.domain.model  isBlank !io.github.simplenote.domain.model  let !io.github.simplenote.domain.model  take !io.github.simplenote.domain.model  toList !io.github.simplenote.domain.model  Boolean &io.github.simplenote.domain.model.Note  	Companion &io.github.simplenote.domain.model.Note  Instant &io.github.simplenote.domain.model.Note  Long &io.github.simplenote.domain.model.Note  Note &io.github.simplenote.domain.model.Note  	NoteColor &io.github.simplenote.domain.model.Note  
NoteEntity &io.github.simplenote.domain.model.Note  String &io.github.simplenote.domain.model.Note  color &io.github.simplenote.domain.model.Note  content &io.github.simplenote.domain.model.Note  copy &io.github.simplenote.domain.model.Note  create &io.github.simplenote.domain.model.Note  	createdAt &io.github.simplenote.domain.model.Note  id &io.github.simplenote.domain.model.Note  isBlank &io.github.simplenote.domain.model.Note  let &io.github.simplenote.domain.model.Note  preview &io.github.simplenote.domain.model.Note  take &io.github.simplenote.domain.model.Note  title &io.github.simplenote.domain.model.Note  toEntity &io.github.simplenote.domain.model.Note  update &io.github.simplenote.domain.model.Note  	updatedAt &io.github.simplenote.domain.model.Note  Note 0io.github.simplenote.domain.model.Note.Companion  	NoteColor 0io.github.simplenote.domain.model.Note.Companion  create 0io.github.simplenote.domain.model.Note.Companion  isBlank 0io.github.simplenote.domain.model.Note.Companion  let 0io.github.simplenote.domain.model.Note.Companion  take 0io.github.simplenote.domain.model.Note.Companion  BLUE +io.github.simplenote.domain.model.NoteColor  Color +io.github.simplenote.domain.model.NoteColor  	Companion +io.github.simplenote.domain.model.NoteColor  DEFAULT +io.github.simplenote.domain.model.NoteColor  Int +io.github.simplenote.domain.model.NoteColor  List +io.github.simplenote.domain.model.NoteColor  	NoteColor +io.github.simplenote.domain.model.NoteColor  String +io.github.simplenote.domain.model.NoteColor  YELLOW +io.github.simplenote.domain.model.NoteColor  color +io.github.simplenote.domain.model.NoteColor  entries +io.github.simplenote.domain.model.NoteColor  find +io.github.simplenote.domain.model.NoteColor  fromId +io.github.simplenote.domain.model.NoteColor  getAllColors +io.github.simplenote.domain.model.NoteColor  id +io.github.simplenote.domain.model.NoteColor  toList +io.github.simplenote.domain.model.NoteColor  Color 5io.github.simplenote.domain.model.NoteColor.Companion  DEFAULT 5io.github.simplenote.domain.model.NoteColor.Companion  YELLOW 5io.github.simplenote.domain.model.NoteColor.Companion  entries 5io.github.simplenote.domain.model.NoteColor.Companion  find 5io.github.simplenote.domain.model.NoteColor.Companion  fromId 5io.github.simplenote.domain.model.NoteColor.Companion  getAllColors 5io.github.simplenote.domain.model.NoteColor.Companion  toList 5io.github.simplenote.domain.model.NoteColor.Companion  AppError +io.github.simplenote.domain.model.NoteError  
DatabaseError +io.github.simplenote.domain.model.NoteError  	NoteError +io.github.simplenote.domain.model.NoteError  String +io.github.simplenote.domain.model.NoteError  ValidationError +io.github.simplenote.domain.model.NoteError  let +io.github.simplenote.domain.model.NoteError  message +io.github.simplenote.domain.model.NoteError  AppError 4io.github.simplenote.domain.model.NoteError.AppError  NetworkUnavailable 4io.github.simplenote.domain.model.NoteError.AppError  StorageUnavailable 4io.github.simplenote.domain.model.NoteError.AppError  String 4io.github.simplenote.domain.model.NoteError.AppError  Unknown 4io.github.simplenote.domain.model.NoteError.AppError  
DatabaseError 9io.github.simplenote.domain.model.NoteError.DatabaseError  DeleteFailed 9io.github.simplenote.domain.model.NoteError.DatabaseError  InsertFailed 9io.github.simplenote.domain.model.NoteError.DatabaseError  NotFound 9io.github.simplenote.domain.model.NoteError.DatabaseError  QueryFailed 9io.github.simplenote.domain.model.NoteError.DatabaseError  String 9io.github.simplenote.domain.model.NoteError.DatabaseError  UpdateFailed 9io.github.simplenote.domain.model.NoteError.DatabaseError  ContentTooLong ;io.github.simplenote.domain.model.NoteError.ValidationError  EmptyContent ;io.github.simplenote.domain.model.NoteError.ValidationError  InvalidColor ;io.github.simplenote.domain.model.NoteError.ValidationError  String ;io.github.simplenote.domain.model.NoteError.ValidationError  TitleTooLong ;io.github.simplenote.domain.model.NoteError.ValidationError  ValidationError ;io.github.simplenote.domain.model.NoteError.ValidationError  left ;io.github.simplenote.domain.model.NoteError.ValidationError  MAX_CONTENT_LENGTH 0io.github.simplenote.domain.model.NoteValidation  MAX_TITLE_LENGTH 0io.github.simplenote.domain.model.NoteValidation  	Companion +io.github.simplenote.domain.model.ThemeMode  DARK +io.github.simplenote.domain.model.ThemeMode  DEFAULT +io.github.simplenote.domain.model.ThemeMode  LIGHT +io.github.simplenote.domain.model.ThemeMode  SYSTEM +io.github.simplenote.domain.model.ThemeMode  String +io.github.simplenote.domain.model.ThemeMode  	ThemeMode +io.github.simplenote.domain.model.ThemeMode  displayName +io.github.simplenote.domain.model.ThemeMode  entries +io.github.simplenote.domain.model.ThemeMode  find +io.github.simplenote.domain.model.ThemeMode  
fromString +io.github.simplenote.domain.model.ThemeMode  name +io.github.simplenote.domain.model.ThemeMode  DEFAULT 5io.github.simplenote.domain.model.ThemeMode.Companion  SYSTEM 5io.github.simplenote.domain.model.ThemeMode.Companion  entries 5io.github.simplenote.domain.model.ThemeMode.Companion  find 5io.github.simplenote.domain.model.ThemeMode.Companion  
fromString 5io.github.simplenote.domain.model.ThemeMode.Companion  Boolean /io.github.simplenote.domain.model.ThemeSettings  	Companion /io.github.simplenote.domain.model.ThemeSettings  DEFAULT /io.github.simplenote.domain.model.ThemeSettings  	ThemeMode /io.github.simplenote.domain.model.ThemeSettings  
ThemeSettings /io.github.simplenote.domain.model.ThemeSettings  	themeMode /io.github.simplenote.domain.model.ThemeSettings  useDynamicTheme /io.github.simplenote.domain.model.ThemeSettings  DEFAULT 9io.github.simplenote.domain.model.ThemeSettings.Companion  	ThemeMode 9io.github.simplenote.domain.model.ThemeSettings.Companion  
ThemeSettings 9io.github.simplenote.domain.model.ThemeSettings.Companion  Either &io.github.simplenote.domain.repository  Flow &io.github.simplenote.domain.repository  Int &io.github.simplenote.domain.repository  List &io.github.simplenote.domain.repository  Long &io.github.simplenote.domain.repository  Note &io.github.simplenote.domain.repository  	NoteColor &io.github.simplenote.domain.repository  	NoteError &io.github.simplenote.domain.repository  NoteRepository &io.github.simplenote.domain.repository  String &io.github.simplenote.domain.repository  Unit &io.github.simplenote.domain.repository  
createNote 5io.github.simplenote.domain.repository.NoteRepository  deleteAllNotes 5io.github.simplenote.domain.repository.NoteRepository  
deleteNote 5io.github.simplenote.domain.repository.NoteRepository  deleteNoteById 5io.github.simplenote.domain.repository.NoteRepository  getAllNotes 5io.github.simplenote.domain.repository.NoteRepository  getAllNotesByCreatedDate 5io.github.simplenote.domain.repository.NoteRepository  getAllNotesByTitle 5io.github.simplenote.domain.repository.NoteRepository  getNoteById 5io.github.simplenote.domain.repository.NoteRepository  getNotesByColor 5io.github.simplenote.domain.repository.NoteRepository  
getNotesCount 5io.github.simplenote.domain.repository.NoteRepository  searchNotes 5io.github.simplenote.domain.repository.NoteRepository  
updateNote 5io.github.simplenote.domain.repository.NoteRepository  updateNoteColor 5io.github.simplenote.domain.repository.NoteRepository  Boolean #io.github.simplenote.domain.usecase  Clock #io.github.simplenote.domain.usecase  CreateNoteUseCase #io.github.simplenote.domain.usecase  DeleteNoteUseCase #io.github.simplenote.domain.usecase  Either #io.github.simplenote.domain.usecase  	Exception #io.github.simplenote.domain.usecase  Flow #io.github.simplenote.domain.usecase  GetNotesUseCase #io.github.simplenote.domain.usecase  GetThemeSettingsUseCase #io.github.simplenote.domain.usecase  Int #io.github.simplenote.domain.usecase  List #io.github.simplenote.domain.usecase  Long #io.github.simplenote.domain.usecase  Note #io.github.simplenote.domain.usecase  	NoteColor #io.github.simplenote.domain.usecase  	NoteError #io.github.simplenote.domain.usecase  NoteRepository #io.github.simplenote.domain.usecase  NoteValidation #io.github.simplenote.domain.usecase  PopulateSampleDataUseCase #io.github.simplenote.domain.usecase  SampleDataGenerator #io.github.simplenote.domain.usecase  SortBy #io.github.simplenote.domain.usecase  String #io.github.simplenote.domain.usecase  	ThemeMode #io.github.simplenote.domain.usecase  ThemePreferences #io.github.simplenote.domain.usecase  
ThemeSettings #io.github.simplenote.domain.usecase  Unit #io.github.simplenote.domain.usecase  UpdateNoteUseCase #io.github.simplenote.domain.usecase  UpdateThemeSettingsUseCase #io.github.simplenote.domain.usecase  create #io.github.simplenote.domain.usecase  first #io.github.simplenote.domain.usecase  forEach #io.github.simplenote.domain.usecase  generateSampleNotes #io.github.simplenote.domain.usecase  isBlank #io.github.simplenote.domain.usecase  left #io.github.simplenote.domain.usecase  now #io.github.simplenote.domain.usecase  trim #io.github.simplenote.domain.usecase  Clock 5io.github.simplenote.domain.usecase.CreateNoteUseCase  Note 5io.github.simplenote.domain.usecase.CreateNoteUseCase  	NoteColor 5io.github.simplenote.domain.usecase.CreateNoteUseCase  	NoteError 5io.github.simplenote.domain.usecase.CreateNoteUseCase  NoteValidation 5io.github.simplenote.domain.usecase.CreateNoteUseCase  create 5io.github.simplenote.domain.usecase.CreateNoteUseCase  invoke 5io.github.simplenote.domain.usecase.CreateNoteUseCase  isBlank 5io.github.simplenote.domain.usecase.CreateNoteUseCase  left 5io.github.simplenote.domain.usecase.CreateNoteUseCase  now 5io.github.simplenote.domain.usecase.CreateNoteUseCase  
repository 5io.github.simplenote.domain.usecase.CreateNoteUseCase  trim 5io.github.simplenote.domain.usecase.CreateNoteUseCase  validateNoteInput 5io.github.simplenote.domain.usecase.CreateNoteUseCase  Either 5io.github.simplenote.domain.usecase.DeleteNoteUseCase  	NoteError 5io.github.simplenote.domain.usecase.DeleteNoteUseCase  invoke 5io.github.simplenote.domain.usecase.DeleteNoteUseCase  
repository 5io.github.simplenote.domain.usecase.DeleteNoteUseCase  Left *io.github.simplenote.domain.usecase.Either  Right *io.github.simplenote.domain.usecase.Either  Either 3io.github.simplenote.domain.usecase.GetNotesUseCase  Flow 3io.github.simplenote.domain.usecase.GetNotesUseCase  List 3io.github.simplenote.domain.usecase.GetNotesUseCase  Long 3io.github.simplenote.domain.usecase.GetNotesUseCase  Note 3io.github.simplenote.domain.usecase.GetNotesUseCase  	NoteColor 3io.github.simplenote.domain.usecase.GetNotesUseCase  	NoteError 3io.github.simplenote.domain.usecase.GetNotesUseCase  NoteRepository 3io.github.simplenote.domain.usecase.GetNotesUseCase  SortBy 3io.github.simplenote.domain.usecase.GetNotesUseCase  String 3io.github.simplenote.domain.usecase.GetNotesUseCase  getNoteById 3io.github.simplenote.domain.usecase.GetNotesUseCase  invoke 3io.github.simplenote.domain.usecase.GetNotesUseCase  isBlank 3io.github.simplenote.domain.usecase.GetNotesUseCase  
repository 3io.github.simplenote.domain.usecase.GetNotesUseCase  searchNotes 3io.github.simplenote.domain.usecase.GetNotesUseCase  trim 3io.github.simplenote.domain.usecase.GetNotesUseCase  CREATED_DATE :io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy  TITLE :io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy  UPDATED_DATE :io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy  invoke ;io.github.simplenote.domain.usecase.GetThemeSettingsUseCase  themePreferences ;io.github.simplenote.domain.usecase.GetThemeSettingsUseCase  ValidationError -io.github.simplenote.domain.usecase.NoteError  Either =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  	NoteError =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  SampleDataGenerator =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  Unit =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  first =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  generateSampleNotes =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  invoke =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  
repository =io.github.simplenote.domain.usecase.PopulateSampleDataUseCase  Clock 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  	NoteError 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  NoteValidation 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  invoke 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  isBlank 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  left 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  now 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  
repository 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  trim 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  updateColor 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  validateNoteInput 5io.github.simplenote.domain.usecase.UpdateNoteUseCase  themePreferences >io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase  updateThemeMode >io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase  updateUseDynamicTheme >io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase  AppDestination io.github.simplenote.navigation  
AppNavigation io.github.simplenote.navigation  
Composable io.github.simplenote.navigation  Long io.github.simplenote.navigation  Modifier io.github.simplenote.navigation  NavHostController io.github.simplenote.navigation  NoteEditorScreen io.github.simplenote.navigation  NotesScreen io.github.simplenote.navigation  Serializable io.github.simplenote.navigation  SettingsScreen io.github.simplenote.navigation  let io.github.simplenote.navigation  takeIf io.github.simplenote.navigation  AppDestination .io.github.simplenote.navigation.AppDestination  	Companion .io.github.simplenote.navigation.AppDestination  Long .io.github.simplenote.navigation.AppDestination  
NoteEditor .io.github.simplenote.navigation.AppDestination  Notes .io.github.simplenote.navigation.AppDestination  Serializable .io.github.simplenote.navigation.AppDestination  Settings .io.github.simplenote.navigation.AppDestination  Long 9io.github.simplenote.navigation.AppDestination.NoteEditor  noteId 9io.github.simplenote.navigation.AppDestination.NoteEditor  	Alignment ,io.github.simplenote.presentation.components  Arrangement ,io.github.simplenote.presentation.components  BasicTextField ,io.github.simplenote.presentation.components  Boolean ,io.github.simplenote.presentation.components  Box ,io.github.simplenote.presentation.components  Button ,io.github.simplenote.presentation.components  CardDefaults ,io.github.simplenote.presentation.components  CircleShape ,io.github.simplenote.presentation.components  CircularProgressIndicator ,io.github.simplenote.presentation.components  Clock ,io.github.simplenote.presentation.components  Color ,io.github.simplenote.presentation.components  ColorBottomSheet ,io.github.simplenote.presentation.components  ColorBottomSheetPreview ,io.github.simplenote.presentation.components  	ColorItem ,io.github.simplenote.presentation.components  ColorPalette ,io.github.simplenote.presentation.components  ColorPalettePreview ,io.github.simplenote.presentation.components  Column ,io.github.simplenote.presentation.components  
Composable ,io.github.simplenote.presentation.components  DeleteConfirmationDialog ,io.github.simplenote.presentation.components  DeleteConfirmationDialogPreview ,io.github.simplenote.presentation.components  
EmptyState ,io.github.simplenote.presentation.components  EmptyStatePreview ,io.github.simplenote.presentation.components  EnhancedSearchBar ,io.github.simplenote.presentation.components  EnhancedSearchBarPreview ,io.github.simplenote.presentation.components  
ErrorState ,io.github.simplenote.presentation.components  ErrorStatePreview ,io.github.simplenote.presentation.components  	Exception ,io.github.simplenote.presentation.components  ExperimentalMaterial3Api ,io.github.simplenote.presentation.components  
FontWeight ,io.github.simplenote.presentation.components  	GridCells ,io.github.simplenote.presentation.components  HapticFeedbackType ,io.github.simplenote.presentation.components  Icon ,io.github.simplenote.presentation.components  
IconButton ,io.github.simplenote.presentation.components  Icons ,io.github.simplenote.presentation.components  	ImeAction ,io.github.simplenote.presentation.components  	IntOffset ,io.github.simplenote.presentation.components  IntegratedSearchBar ,io.github.simplenote.presentation.components  IntegratedSearchBarPreview ,io.github.simplenote.presentation.components  KeyboardActions ,io.github.simplenote.presentation.components  KeyboardOptions ,io.github.simplenote.presentation.components  KeyboardType ,io.github.simplenote.presentation.components  
LazyGridState ,io.github.simplenote.presentation.components  LazyStaggeredGridState ,io.github.simplenote.presentation.components  List ,io.github.simplenote.presentation.components  LoadingState ,io.github.simplenote.presentation.components  LoadingStatePreview ,io.github.simplenote.presentation.components  
MaterialTheme ,io.github.simplenote.presentation.components  Modifier ,io.github.simplenote.presentation.components  Note ,io.github.simplenote.presentation.components  	NoteColor ,io.github.simplenote.presentation.components  	NoteError ,io.github.simplenote.presentation.components  NoteItem ,io.github.simplenote.presentation.components  NoteItemPreview ,io.github.simplenote.presentation.components  NoteItemPreview2 ,io.github.simplenote.presentation.components  OptIn ,io.github.simplenote.presentation.components  Preview ,io.github.simplenote.presentation.components  PreviewLightDark ,io.github.simplenote.presentation.components  RoundedCornerShape ,io.github.simplenote.presentation.components  Row ,io.github.simplenote.presentation.components  SampleKeepBottomSheetRight ,io.github.simplenote.presentation.components  ScrollableSearchBarTopAppBar ,io.github.simplenote.presentation.components  SimpleScrollableSearchBarLayout ,io.github.simplenote.presentation.components  
SolidColor ,io.github.simplenote.presentation.components  Spacer ,io.github.simplenote.presentation.components  String ,io.github.simplenote.presentation.components  Text ,io.github.simplenote.presentation.components  	TextAlign ,io.github.simplenote.presentation.components  TextOverflow ,io.github.simplenote.presentation.components  Unit ,io.github.simplenote.presentation.components  WindowInsets ,io.github.simplenote.presentation.components  align ,io.github.simplenote.presentation.components  androidx ,io.github.simplenote.presentation.components  
background ,io.github.simplenote.presentation.components  
cardColors ,io.github.simplenote.presentation.components  	clickable ,io.github.simplenote.presentation.components  clip ,io.github.simplenote.presentation.components  create ,io.github.simplenote.presentation.components  expandHorizontally ,io.github.simplenote.presentation.components  fadeIn ,io.github.simplenote.presentation.components  fadeOut ,io.github.simplenote.presentation.components  fillMaxSize ,io.github.simplenote.presentation.components  fillMaxWidth ,io.github.simplenote.presentation.components  focusRequester ,io.github.simplenote.presentation.components  forEach ,io.github.simplenote.presentation.components  
formatDate ,io.github.simplenote.presentation.components  getAllColors ,io.github.simplenote.presentation.components  getContrastColor ,io.github.simplenote.presentation.components  
getErrorTitle ,io.github.simplenote.presentation.components  height ,io.github.simplenote.presentation.components  isEmpty ,io.github.simplenote.presentation.components  
isNotBlank ,io.github.simplenote.presentation.components  
isNotEmpty ,io.github.simplenote.presentation.components  now ,io.github.simplenote.presentation.components  offset ,io.github.simplenote.presentation.components  onFocusChanged ,io.github.simplenote.presentation.components  padding ,io.github.simplenote.presentation.components  provideDelegate ,io.github.simplenote.presentation.components  
roundToInt ,io.github.simplenote.presentation.components  shrinkHorizontally ,io.github.simplenote.presentation.components  size ,io.github.simplenote.presentation.components  spacedBy ,io.github.simplenote.presentation.components  substringBefore ,io.github.simplenote.presentation.components  weight ,io.github.simplenote.presentation.components  windowInsetsPadding ,io.github.simplenote.presentation.components  with ,io.github.simplenote.presentation.components  AppError 6io.github.simplenote.presentation.components.NoteError  
DatabaseError 6io.github.simplenote.presentation.components.NoteError  ValidationError 6io.github.simplenote.presentation.components.NoteError  NetworkUnavailable ?io.github.simplenote.presentation.components.NoteError.AppError  StorageUnavailable ?io.github.simplenote.presentation.components.NoteError.AppError  Unknown ?io.github.simplenote.presentation.components.NoteError.AppError  Boolean 'io.github.simplenote.presentation.model  
ImmutableList 'io.github.simplenote.presentation.model  Long 'io.github.simplenote.presentation.model  NavigationEvent 'io.github.simplenote.presentation.model  Note 'io.github.simplenote.presentation.model  	NoteColor 'io.github.simplenote.presentation.model  NoteEditorUiEvent 'io.github.simplenote.presentation.model  NoteEditorUiState 'io.github.simplenote.presentation.model  	NoteError 'io.github.simplenote.presentation.model  NotesUiEvent 'io.github.simplenote.presentation.model  NotesUiState 'io.github.simplenote.presentation.model  SortBy 'io.github.simplenote.presentation.model  String 'io.github.simplenote.presentation.model  
isNotBlank 'io.github.simplenote.presentation.model  Long 7io.github.simplenote.presentation.model.NavigationEvent  NavigateBack 7io.github.simplenote.presentation.model.NavigationEvent  NavigateToNoteEditor 7io.github.simplenote.presentation.model.NavigationEvent  NavigationEvent 7io.github.simplenote.presentation.model.NavigationEvent  Note 7io.github.simplenote.presentation.model.NavigationEvent  	NoteError 7io.github.simplenote.presentation.model.NavigationEvent  ShowDeleteConfirmation 7io.github.simplenote.presentation.model.NavigationEvent  	ShowError 7io.github.simplenote.presentation.model.NavigationEvent  noteId Lio.github.simplenote.presentation.model.NavigationEvent.NavigateToNoteEditor  note Nio.github.simplenote.presentation.model.NavigationEvent.ShowDeleteConfirmation  error Aio.github.simplenote.presentation.model.NavigationEvent.ShowError  ChangeColor 9io.github.simplenote.presentation.model.NoteEditorUiEvent  
ClearError 9io.github.simplenote.presentation.model.NoteEditorUiEvent  
DeleteNote 9io.github.simplenote.presentation.model.NoteEditorUiEvent  LoadNote 9io.github.simplenote.presentation.model.NoteEditorUiEvent  Long 9io.github.simplenote.presentation.model.NoteEditorUiEvent  NavigateBack 9io.github.simplenote.presentation.model.NoteEditorUiEvent  	NoteColor 9io.github.simplenote.presentation.model.NoteEditorUiEvent  NoteEditorUiEvent 9io.github.simplenote.presentation.model.NoteEditorUiEvent  SaveNote 9io.github.simplenote.presentation.model.NoteEditorUiEvent  String 9io.github.simplenote.presentation.model.NoteEditorUiEvent  
UpdateContent 9io.github.simplenote.presentation.model.NoteEditorUiEvent  UpdateTitle 9io.github.simplenote.presentation.model.NoteEditorUiEvent  color Eio.github.simplenote.presentation.model.NoteEditorUiEvent.ChangeColor  noteId Bio.github.simplenote.presentation.model.NoteEditorUiEvent.LoadNote  content Gio.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateContent  title Eio.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateTitle  canSave 9io.github.simplenote.presentation.model.NoteEditorUiState  content 9io.github.simplenote.presentation.model.NoteEditorUiState  copy 9io.github.simplenote.presentation.model.NoteEditorUiState  error 9io.github.simplenote.presentation.model.NoteEditorUiState  hasUnsavedChanges 9io.github.simplenote.presentation.model.NoteEditorUiState  	isLoading 9io.github.simplenote.presentation.model.NoteEditorUiState  	isNewNote 9io.github.simplenote.presentation.model.NoteEditorUiState  
isNotBlank 9io.github.simplenote.presentation.model.NoteEditorUiState  isSaving 9io.github.simplenote.presentation.model.NoteEditorUiState  note 9io.github.simplenote.presentation.model.NoteEditorUiState  
selectedColor 9io.github.simplenote.presentation.model.NoteEditorUiState  title 9io.github.simplenote.presentation.model.NoteEditorUiState  ChangeNoteColor 4io.github.simplenote.presentation.model.NotesUiEvent  ChangeSortOrder 4io.github.simplenote.presentation.model.NotesUiEvent  
ClearError 4io.github.simplenote.presentation.model.NotesUiEvent  
CreateNewNote 4io.github.simplenote.presentation.model.NotesUiEvent  
DeleteNote 4io.github.simplenote.presentation.model.NotesUiEvent  HideColorBottomSheet 4io.github.simplenote.presentation.model.NotesUiEvent  	LoadNotes 4io.github.simplenote.presentation.model.NotesUiEvent  Long 4io.github.simplenote.presentation.model.NotesUiEvent  Note 4io.github.simplenote.presentation.model.NotesUiEvent  	NoteColor 4io.github.simplenote.presentation.model.NotesUiEvent  NotesUiEvent 4io.github.simplenote.presentation.model.NotesUiEvent  NotesUiState 4io.github.simplenote.presentation.model.NotesUiEvent  SearchNotes 4io.github.simplenote.presentation.model.NotesUiEvent  
SelectNote 4io.github.simplenote.presentation.model.NotesUiEvent  ShowColorBottomSheet 4io.github.simplenote.presentation.model.NotesUiEvent  String 4io.github.simplenote.presentation.model.NotesUiEvent  color Dio.github.simplenote.presentation.model.NotesUiEvent.ChangeNoteColor  noteId Dio.github.simplenote.presentation.model.NotesUiEvent.ChangeNoteColor  sortBy Dio.github.simplenote.presentation.model.NotesUiEvent.ChangeSortOrder  note ?io.github.simplenote.presentation.model.NotesUiEvent.DeleteNote  SortBy Aio.github.simplenote.presentation.model.NotesUiEvent.NotesUiState  query @io.github.simplenote.presentation.model.NotesUiEvent.SearchNotes  noteId ?io.github.simplenote.presentation.model.NotesUiEvent.SelectNote  Boolean 4io.github.simplenote.presentation.model.NotesUiState  
ImmutableList 4io.github.simplenote.presentation.model.NotesUiState  Long 4io.github.simplenote.presentation.model.NotesUiState  Note 4io.github.simplenote.presentation.model.NotesUiState  	NoteError 4io.github.simplenote.presentation.model.NotesUiState  SortBy 4io.github.simplenote.presentation.model.NotesUiState  String 4io.github.simplenote.presentation.model.NotesUiState  copy 4io.github.simplenote.presentation.model.NotesUiState  error 4io.github.simplenote.presentation.model.NotesUiState  	isLoading 4io.github.simplenote.presentation.model.NotesUiState  notes 4io.github.simplenote.presentation.model.NotesUiState  persistentListOf 4io.github.simplenote.presentation.model.NotesUiState  sortBy 4io.github.simplenote.presentation.model.NotesUiState  CREATED_DATE ;io.github.simplenote.presentation.model.NotesUiState.SortBy  TITLE ;io.github.simplenote.presentation.model.NotesUiState.SortBy  UPDATED_DATE ;io.github.simplenote.presentation.model.NotesUiState.SortBy  	Alignment (io.github.simplenote.presentation.screen  Arrangement (io.github.simplenote.presentation.screen  Box (io.github.simplenote.presentation.screen  Card (io.github.simplenote.presentation.screen  CardDefaults (io.github.simplenote.presentation.screen  CircleShape (io.github.simplenote.presentation.screen  CircularProgressIndicator (io.github.simplenote.presentation.screen  Color (io.github.simplenote.presentation.screen  Column (io.github.simplenote.presentation.screen  
Composable (io.github.simplenote.presentation.screen  
EmptyState (io.github.simplenote.presentation.screen  	Exception (io.github.simplenote.presentation.screen  ExperimentalMaterial3Api (io.github.simplenote.presentation.screen  
FontWeight (io.github.simplenote.presentation.screen  Icon (io.github.simplenote.presentation.screen  
IconButton (io.github.simplenote.presentation.screen  Icons (io.github.simplenote.presentation.screen  IntegratedSearchBar (io.github.simplenote.presentation.screen  LazyVerticalStaggeredGrid (io.github.simplenote.presentation.screen  Long (io.github.simplenote.presentation.screen  
MaterialTheme (io.github.simplenote.presentation.screen  Modifier (io.github.simplenote.presentation.screen  NavigationEvent (io.github.simplenote.presentation.screen  Note (io.github.simplenote.presentation.screen  NoteEditorScreen (io.github.simplenote.presentation.screen  NoteEditorScreenPreview (io.github.simplenote.presentation.screen  NoteEditorUiEvent (io.github.simplenote.presentation.screen  NoteEditorViewModel (io.github.simplenote.presentation.screen  NoteItem (io.github.simplenote.presentation.screen  NotesScreen (io.github.simplenote.presentation.screen  NotesScreenPreview (io.github.simplenote.presentation.screen  NotesUiEvent (io.github.simplenote.presentation.screen  NotesViewModel (io.github.simplenote.presentation.screen  OptIn (io.github.simplenote.presentation.screen  OutlinedTextField (io.github.simplenote.presentation.screen  OutlinedTextFieldDefaults (io.github.simplenote.presentation.screen  
PaddingValues (io.github.simplenote.presentation.screen  Preview (io.github.simplenote.presentation.screen  RadioButton (io.github.simplenote.presentation.screen  Role (io.github.simplenote.presentation.screen  Row (io.github.simplenote.presentation.screen  SettingsScreen (io.github.simplenote.presentation.screen  SettingsScreenPreview (io.github.simplenote.presentation.screen  SettingsViewModel (io.github.simplenote.presentation.screen  Spacer (io.github.simplenote.presentation.screen  StaggeredGridCells (io.github.simplenote.presentation.screen  StaggeredGridItemSpan (io.github.simplenote.presentation.screen  String (io.github.simplenote.presentation.screen  Switch (io.github.simplenote.presentation.screen  Text (io.github.simplenote.presentation.screen  	ThemeMode (io.github.simplenote.presentation.screen  TopAppBarDefaults (io.github.simplenote.presentation.screen  Unit (io.github.simplenote.presentation.screen  WindowInsets (io.github.simplenote.presentation.screen  align (io.github.simplenote.presentation.screen  androidx (io.github.simplenote.presentation.screen  
background (io.github.simplenote.presentation.screen  
cardColors (io.github.simplenote.presentation.screen  clip (io.github.simplenote.presentation.screen  colors (io.github.simplenote.presentation.screen  delay (io.github.simplenote.presentation.screen  fillMaxSize (io.github.simplenote.presentation.screen  fillMaxWidth (io.github.simplenote.presentation.screen  forEach (io.github.simplenote.presentation.screen  
formatDate (io.github.simplenote.presentation.screen  height (io.github.simplenote.presentation.screen  isBlank (io.github.simplenote.presentation.screen  
isNotEmpty (io.github.simplenote.presentation.screen  let (io.github.simplenote.presentation.screen  listOf (io.github.simplenote.presentation.screen  padding (io.github.simplenote.presentation.screen  provideDelegate (io.github.simplenote.presentation.screen  
selectable (io.github.simplenote.presentation.screen  selectableGroup (io.github.simplenote.presentation.screen  size (io.github.simplenote.presentation.screen  spacedBy (io.github.simplenote.presentation.screen  substringBefore (io.github.simplenote.presentation.screen  topAppBarColors (io.github.simplenote.presentation.screen  weight (io.github.simplenote.presentation.screen  width (io.github.simplenote.presentation.screen  windowInsetsPadding (io.github.simplenote.presentation.screen  NavigateBack 8io.github.simplenote.presentation.screen.NavigationEvent  NavigateToNoteEditor 8io.github.simplenote.presentation.screen.NavigationEvent  ShowDeleteConfirmation 8io.github.simplenote.presentation.screen.NavigationEvent  	ShowError 8io.github.simplenote.presentation.screen.NavigationEvent  Boolean +io.github.simplenote.presentation.viewmodel  CreateNoteUseCase +io.github.simplenote.presentation.viewmodel  DeleteNoteUseCase +io.github.simplenote.presentation.viewmodel  Either +io.github.simplenote.presentation.viewmodel  GetNotesUseCase +io.github.simplenote.presentation.viewmodel  GetThemeSettingsUseCase +io.github.simplenote.presentation.viewmodel  Job +io.github.simplenote.presentation.viewmodel  Long +io.github.simplenote.presentation.viewmodel  MutableSharedFlow +io.github.simplenote.presentation.viewmodel  MutableStateFlow +io.github.simplenote.presentation.viewmodel  NavigationEvent +io.github.simplenote.presentation.viewmodel  Note +io.github.simplenote.presentation.viewmodel  	NoteColor +io.github.simplenote.presentation.viewmodel  NoteEditorUiEvent +io.github.simplenote.presentation.viewmodel  NoteEditorUiState +io.github.simplenote.presentation.viewmodel  NoteEditorViewModel +io.github.simplenote.presentation.viewmodel  NotesUiEvent +io.github.simplenote.presentation.viewmodel  NotesUiState +io.github.simplenote.presentation.viewmodel  NotesViewModel +io.github.simplenote.presentation.viewmodel  SettingsViewModel +io.github.simplenote.presentation.viewmodel  
SharedFlow +io.github.simplenote.presentation.viewmodel  	StateFlow +io.github.simplenote.presentation.viewmodel  String +io.github.simplenote.presentation.viewmodel  	ThemeMode +io.github.simplenote.presentation.viewmodel  
ThemeSettings +io.github.simplenote.presentation.viewmodel  UpdateNoteUseCase +io.github.simplenote.presentation.viewmodel  UpdateThemeSettingsUseCase +io.github.simplenote.presentation.viewmodel  	ViewModel +io.github.simplenote.presentation.viewmodel  WhileSubscribed +io.github.simplenote.presentation.viewmodel  _navigationEvents +io.github.simplenote.presentation.viewmodel  _uiState +io.github.simplenote.presentation.viewmodel  asSharedFlow +io.github.simplenote.presentation.viewmodel  asStateFlow +io.github.simplenote.presentation.viewmodel  createNoteUseCase +io.github.simplenote.presentation.viewmodel  deleteNoteUseCase +io.github.simplenote.presentation.viewmodel  getNotesUseCase +io.github.simplenote.presentation.viewmodel  hideColorBottomSheet +io.github.simplenote.presentation.viewmodel  io +io.github.simplenote.presentation.viewmodel  
isNotBlank +io.github.simplenote.presentation.viewmodel  kotlinx +io.github.simplenote.presentation.viewmodel  launch +io.github.simplenote.presentation.viewmodel  launchIn +io.github.simplenote.presentation.viewmodel  let +io.github.simplenote.presentation.viewmodel  onEach +io.github.simplenote.presentation.viewmodel  stateIn +io.github.simplenote.presentation.viewmodel  toImmutableList +io.github.simplenote.presentation.viewmodel  updateNoteUseCase +io.github.simplenote.presentation.viewmodel  updateThemeSettingsUseCase +io.github.simplenote.presentation.viewmodel  Left 2io.github.simplenote.presentation.viewmodel.Either  Right 2io.github.simplenote.presentation.viewmodel.Either  ChangeColor =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  
ClearError =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  
DeleteNote =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  LoadNote =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  NavigateBack =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  SaveNote =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  
UpdateContent =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  UpdateTitle =io.github.simplenote.presentation.viewmodel.NoteEditorUiEvent  Either ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  MutableSharedFlow ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  MutableStateFlow ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  NavigationEvent ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  	NoteColor ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  NoteEditorUiState ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  _navigationEvents ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  _uiState ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  asSharedFlow ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  asStateFlow ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  changeColor ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  
clearError ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  
confirmDelete ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  createNoteUseCase ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  
deleteNote ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  deleteNoteUseCase ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  getNotesUseCase ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  io ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  launch ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  let ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  loadNote ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  navigateBack ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  navigationEvents ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  onEvent ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  saveNote ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  uiState ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  
updateContent ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  updateNoteUseCase ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  updateTitle ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  viewModelScope ?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel  ChangeNoteColor 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  ChangeSortOrder 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  
ClearError 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  
CreateNewNote 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  
DeleteNote 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  HideColorBottomSheet 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  	LoadNotes 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  SearchNotes 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  
SelectNote 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  ShowColorBottomSheet 8io.github.simplenote.presentation.viewmodel.NotesUiEvent  SortBy 8io.github.simplenote.presentation.viewmodel.NotesUiState  GetNotesUseCase :io.github.simplenote.presentation.viewmodel.NotesViewModel  MutableSharedFlow :io.github.simplenote.presentation.viewmodel.NotesViewModel  MutableStateFlow :io.github.simplenote.presentation.viewmodel.NotesViewModel  NavigationEvent :io.github.simplenote.presentation.viewmodel.NotesViewModel  NotesUiState :io.github.simplenote.presentation.viewmodel.NotesViewModel  _navigationEvents :io.github.simplenote.presentation.viewmodel.NotesViewModel  _uiState :io.github.simplenote.presentation.viewmodel.NotesViewModel  asSharedFlow :io.github.simplenote.presentation.viewmodel.NotesViewModel  asStateFlow :io.github.simplenote.presentation.viewmodel.NotesViewModel  changeNoteColor :io.github.simplenote.presentation.viewmodel.NotesViewModel  changeSortOrder :io.github.simplenote.presentation.viewmodel.NotesViewModel  
clearError :io.github.simplenote.presentation.viewmodel.NotesViewModel  
deleteNote :io.github.simplenote.presentation.viewmodel.NotesViewModel  deleteNoteUseCase :io.github.simplenote.presentation.viewmodel.NotesViewModel  getNotesUseCase :io.github.simplenote.presentation.viewmodel.NotesViewModel  hideColorBottomSheet :io.github.simplenote.presentation.viewmodel.NotesViewModel  
isNotBlank :io.github.simplenote.presentation.viewmodel.NotesViewModel  launch :io.github.simplenote.presentation.viewmodel.NotesViewModel  launchIn :io.github.simplenote.presentation.viewmodel.NotesViewModel  	loadNotes :io.github.simplenote.presentation.viewmodel.NotesViewModel  navigateToNoteEditor :io.github.simplenote.presentation.viewmodel.NotesViewModel  navigationEvents :io.github.simplenote.presentation.viewmodel.NotesViewModel  onEach :io.github.simplenote.presentation.viewmodel.NotesViewModel  onEvent :io.github.simplenote.presentation.viewmodel.NotesViewModel  	searchJob :io.github.simplenote.presentation.viewmodel.NotesViewModel  searchNotes :io.github.simplenote.presentation.viewmodel.NotesViewModel  showColorBottomSheet :io.github.simplenote.presentation.viewmodel.NotesViewModel  showDeleteConfirmation :io.github.simplenote.presentation.viewmodel.NotesViewModel  toImmutableList :io.github.simplenote.presentation.viewmodel.NotesViewModel  uiState :io.github.simplenote.presentation.viewmodel.NotesViewModel  updateNoteUseCase :io.github.simplenote.presentation.viewmodel.NotesViewModel  viewModelScope :io.github.simplenote.presentation.viewmodel.NotesViewModel  
ThemeSettings =io.github.simplenote.presentation.viewmodel.SettingsViewModel  WhileSubscribed =io.github.simplenote.presentation.viewmodel.SettingsViewModel  getThemeSettingsUseCase =io.github.simplenote.presentation.viewmodel.SettingsViewModel  kotlinx =io.github.simplenote.presentation.viewmodel.SettingsViewModel  launch =io.github.simplenote.presentation.viewmodel.SettingsViewModel  stateIn =io.github.simplenote.presentation.viewmodel.SettingsViewModel  
themeSettings =io.github.simplenote.presentation.viewmodel.SettingsViewModel  updateThemeMode =io.github.simplenote.presentation.viewmodel.SettingsViewModel  updateThemeSettingsUseCase =io.github.simplenote.presentation.viewmodel.SettingsViewModel  updateUseDynamicTheme =io.github.simplenote.presentation.viewmodel.SettingsViewModel  viewModelScope =io.github.simplenote.presentation.viewmodel.SettingsViewModel  Activity io.github.simplenote.ui.theme  Boolean io.github.simplenote.ui.theme  Build io.github.simplenote.ui.theme  
Composable io.github.simplenote.ui.theme  DarkColorScheme io.github.simplenote.ui.theme  
FontFamily io.github.simplenote.ui.theme  
FontWeight io.github.simplenote.ui.theme  LightColorScheme io.github.simplenote.ui.theme  Pink40 io.github.simplenote.ui.theme  Pink80 io.github.simplenote.ui.theme  Purple40 io.github.simplenote.ui.theme  Purple80 io.github.simplenote.ui.theme  PurpleGrey40 io.github.simplenote.ui.theme  PurpleGrey80 io.github.simplenote.ui.theme  SimpleNOTETheme io.github.simplenote.ui.theme  	ThemeMode io.github.simplenote.ui.theme  
Typography io.github.simplenote.ui.theme  Unit io.github.simplenote.ui.theme  WindowCompat io.github.simplenote.ui.theme  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  	Generated javax.annotation.processing  Array kotlin  CharSequence kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  Lazy kotlin  Long kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  String kotlin  Suppress kotlin  	Throwable kotlin  arrayOf kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  repeat kotlin  takeIf kotlin  toList kotlin  with kotlin  not kotlin.Boolean  isEmpty kotlin.CharSequence  	compareTo 
kotlin.Double  plus 
kotlin.Double  sp 
kotlin.Double  times 
kotlin.Double  Color kotlin.Enum  	Companion kotlin.Enum  Int kotlin.Enum  List kotlin.Enum  	NoteColor kotlin.Enum  SYSTEM kotlin.Enum  String kotlin.Enum  	ThemeMode kotlin.Enum  YELLOW kotlin.Enum  entries kotlin.Enum  find kotlin.Enum  toList kotlin.Enum  Color kotlin.Enum.Companion  SYSTEM kotlin.Enum.Companion  YELLOW kotlin.Enum.Companion  entries kotlin.Enum.Companion  find kotlin.Enum.Companion  toList kotlin.Enum.Companion  
roundToInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  days 
kotlin.Int  hours 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rem 
kotlin.Int  toLong 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  value kotlin.Lazy  	compareTo kotlin.Long  takeIf kotlin.Long  toInt kotlin.Long  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  substringBefore 
kotlin.String  take 
kotlin.String  trim 
kotlin.String  
trimMargin 
kotlin.String  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  	emptyList kotlin.collections  find kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  
plusAssign kotlin.collections  sortedByDescending kotlin.collections  take kotlin.collections  toList kotlin.collections  get kotlin.collections.List  isEmpty kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  toImmutableList kotlin.collections.List  add kotlin.collections.MutableList  sortedByDescending kotlin.collections.MutableList  put kotlin.collections.MutableMap  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  EnumEntries kotlin.enums  find kotlin.enums.EnumEntries  toList kotlin.enums.EnumEntries  java 
kotlin.jvm  
roundToInt kotlin.math  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  Random 
kotlin.random  Default kotlin.random.Random  nextBoolean kotlin.random.Random  nextInt kotlin.random.Random  nextBoolean kotlin.random.Random.Default  nextInt kotlin.random.Random.Default  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  map kotlin.sequences  sortedByDescending kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  find kotlin.text  forEach kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  repeat kotlin.text  substringBefore kotlin.text  take kotlin.text  toList kotlin.text  trim kotlin.text  
trimMargin kotlin.text  Duration kotlin.time  days kotlin.time.Duration.Companion  hours kotlin.time.Duration.Companion  
ImmutableList kotlinx.collections.immutable  PersistentList kotlinx.collections.immutable  persistentListOf kotlinx.collections.immutable  toImmutableList kotlinx.collections.immutable  isEmpty +kotlinx.collections.immutable.ImmutableList  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  Either !kotlinx.coroutines.CoroutineScope  NavigationEvent !kotlinx.coroutines.CoroutineScope  NoteEditorUiEvent !kotlinx.coroutines.CoroutineScope  NotesUiEvent !kotlinx.coroutines.CoroutineScope  _navigationEvents !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  createNoteUseCase !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  deleteNoteUseCase !kotlinx.coroutines.CoroutineScope  getNotesUseCase !kotlinx.coroutines.CoroutineScope  hideColorBottomSheet !kotlinx.coroutines.CoroutineScope  io !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  populateSampleDataUseCase !kotlinx.coroutines.CoroutineScope  updateNoteUseCase !kotlinx.coroutines.CoroutineScope  updateThemeSettingsUseCase !kotlinx.coroutines.CoroutineScope  cancel kotlinx.coroutines.Job  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableSharedFlow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  
SharedFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asSharedFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  first kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow  map kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  catch kotlinx.coroutines.flow.Flow  collectAsStateWithLifecycle kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  launchIn kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  onEach kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  Either %kotlinx.coroutines.flow.FlowCollector  	NoteError %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  asSharedFlow )kotlinx.coroutines.flow.MutableSharedFlow  emit )kotlinx.coroutines.flow.MutableSharedFlow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collect "kotlinx.coroutines.flow.SharedFlow  	Companion &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  Clock kotlinx.datetime  Instant kotlinx.datetime  	Companion kotlinx.datetime.Clock  System kotlinx.datetime.Clock  now kotlinx.datetime.Clock.System  	Companion kotlinx.datetime.Instant  fromEpochMilliseconds kotlinx.datetime.Instant  minus kotlinx.datetime.Instant  toEpochMilliseconds kotlinx.datetime.Instant  toString kotlinx.datetime.Instant  fromEpochMilliseconds "kotlinx.datetime.Instant.Companion  Serializable kotlinx.serialization  inject org.koin.android.ext.android  androidContext org.koin.android.ext.koin  
androidLogger org.koin.android.ext.koin  
koinViewModel org.koin.androidx.compose  KoinApplication 
org.koin.core  Level org.koin.core.KoinApplication  androidContext org.koin.core.KoinApplication  
androidLogger org.koin.core.KoinApplication  
appModules org.koin.core.KoinApplication  modules org.koin.core.KoinApplication  	startKoin org.koin.core.context  KoinDefinition org.koin.core.definition  Level org.koin.core.logger  ERROR org.koin.core.logger.Level  Module org.koin.core.module  CreateNoteUseCase org.koin.core.module.Module  DeleteNoteUseCase org.koin.core.module.Module  GetNotesUseCase org.koin.core.module.Module  GetThemeSettingsUseCase org.koin.core.module.Module  NoteDatabase org.koin.core.module.Module  NoteEditorViewModel org.koin.core.module.Module  NoteRepositoryImpl org.koin.core.module.Module  NotesViewModel org.koin.core.module.Module  PopulateSampleDataUseCase org.koin.core.module.Module  SettingsViewModel org.koin.core.module.Module  ThemePreferences org.koin.core.module.Module  UpdateNoteUseCase org.koin.core.module.Module  UpdateThemeSettingsUseCase org.koin.core.module.Module  androidContext org.koin.core.module.Module  create org.koin.core.module.Module  factory org.koin.core.module.Module  preferencesDataStore org.koin.core.module.Module  single org.koin.core.module.Module  	viewModel org.koin.core.module.Module  	viewModel org.koin.core.module.dsl  ParametersHolder org.koin.core.parameter  Scope org.koin.core.scope  CreateNoteUseCase org.koin.core.scope.Scope  DeleteNoteUseCase org.koin.core.scope.Scope  GetNotesUseCase org.koin.core.scope.Scope  GetThemeSettingsUseCase org.koin.core.scope.Scope  NoteDatabase org.koin.core.scope.Scope  NoteEditorViewModel org.koin.core.scope.Scope  NoteRepositoryImpl org.koin.core.scope.Scope  NotesViewModel org.koin.core.scope.Scope  PopulateSampleDataUseCase org.koin.core.scope.Scope  SettingsViewModel org.koin.core.scope.Scope  ThemePreferences org.koin.core.scope.Scope  UpdateNoteUseCase org.koin.core.scope.Scope  UpdateThemeSettingsUseCase org.koin.core.scope.Scope  androidContext org.koin.core.scope.Scope  create org.koin.core.scope.Scope  get org.koin.core.scope.Scope  preferencesDataStore org.koin.core.scope.Scope  module org.koin.dsl                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   