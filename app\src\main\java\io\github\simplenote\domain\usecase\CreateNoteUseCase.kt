package io.github.simplenote.domain.usecase

import arrow.core.Either
import arrow.core.left
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.model.NoteError
import io.github.simplenote.domain.model.NoteValidation
import io.github.simplenote.domain.repository.NoteRepository
import kotlinx.datetime.Clock

/**
 * Use case for creating new notes with validation.
 */
class CreateNoteUseCase(
    private val repository: NoteRepository
) {

    /**
     * Create a new note with validation
     */
    suspend operator fun invoke(
        title: String,
        content: String,
        color: NoteColor = NoteColor.DEFAULT
    ): Either<NoteError, Note> {

        // Validate input
        val validationResult = validateNoteInput(title, content)
        if (validationResult != null) {
            return validationResult.left()
        }

        // Create note with current timestamp
        val currentTime = Clock.System.now()
        val note = Note.create(
            title = title.trim(),
            content = content.trim(),
            color = color,
            timestamp = currentTime
        )

        // Save to repository
        return repository.createNote(note)
    }

    /**
     * Create a note from existing note (for duplication)
     */
    suspend fun createFromExisting(
        existingNote: Note,
        newTitle: String = existingNote.title,
        newContent: String = existingNote.content,
        newColor: NoteColor = existingNote.color
    ): Either<NoteError, Note> {
        return invoke(newTitle, newContent, newColor)
    }

    /**
     * Validate note input data
     */
    private fun validateNoteInput(title: String, content: String): NoteError.ValidationError? {
        return when {
            title.isBlank() && content.isBlank() ->
                NoteError.ValidationError.EmptyContent

            title.length > NoteValidation.MAX_TITLE_LENGTH ->
                NoteError.ValidationError.TitleTooLong

            content.length > NoteValidation.MAX_CONTENT_LENGTH ->
                NoteError.ValidationError.ContentTooLong

            else -> null
        }
    }
}
