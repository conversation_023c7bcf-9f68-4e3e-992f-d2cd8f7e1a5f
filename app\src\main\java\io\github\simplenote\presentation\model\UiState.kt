package io.github.simplenote.presentation.model

import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.model.NoteError
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

/**
 * UI state for the notes list screen
 */
data class NotesUiState(
    val notes: ImmutableList<Note> = persistentListOf(),
    val isLoading: Boolean = false,
    val error: NoteError? = null,
    val searchQuery: String = "",
    val selectedNoteId: Long? = null,
    val isColorBottomSheetVisible: Boolean = false,
    val sortBy: SortBy = SortBy.UPDATED_DATE
) {
    enum class SortBy {
        UPDATED_DATE,
        CREATED_DATE,
        TITLE
    }
}

/**
 * UI state for the note editor screen
 */
data class NoteEditorUiState(
    val note: Note? = null,
    val title: String = "",
    val content: String = "",
    val selectedColor: NoteColor = NoteColor.DEFAULT,
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val error: NoteError? = null,
    val hasUnsavedChanges: Boolean = false,
    val isNewNote: Boolean = true
) {
    val canSave: Boolean
        get() = !isSaving && (title.isNotBlank() || content.isNotBlank()) && hasUnsavedChanges
}

/**
 * UI events for the notes screen
 */
sealed class NotesUiEvent {
    data object LoadNotes : NotesUiEvent()
    data object CreateNewNote : NotesUiEvent()
    data class SelectNote(val noteId: Long) : NotesUiEvent()
    data class DeleteNote(val note: Note) : NotesUiEvent()
    data class SearchNotes(val query: String) : NotesUiEvent()
    data class ChangeSortOrder(val sortBy: NotesUiState.SortBy) : NotesUiEvent()
    data object ShowColorBottomSheet : NotesUiEvent()
    data object HideColorBottomSheet : NotesUiEvent()
    data class ChangeNoteColor(val noteId: Long, val color: NoteColor) : NotesUiEvent()
    data object ClearError : NotesUiEvent()
}

/**
 * UI events for the note editor screen
 */
sealed class NoteEditorUiEvent {
    data class LoadNote(val noteId: Long) : NoteEditorUiEvent()
    data class UpdateTitle(val title: String) : NoteEditorUiEvent()
    data class UpdateContent(val content: String) : NoteEditorUiEvent()
    data class ChangeColor(val color: NoteColor) : NoteEditorUiEvent()
    data object SaveNote : NoteEditorUiEvent()
    data object DeleteNote : NoteEditorUiEvent()
    data object NavigateBack : NoteEditorUiEvent()
    data object ClearError : NoteEditorUiEvent()
}

/**
 * Navigation events
 */
sealed class NavigationEvent {
    data object NavigateBack : NavigationEvent()
    data class NavigateToNoteEditor(val noteId: Long? = null) : NavigationEvent()
    data class ShowDeleteConfirmation(val note: Note) : NavigationEvent()
    data class ShowError(val error: NoteError) : NavigationEvent()
}
