package io.github.simplenote.data.preferences

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import io.github.simplenote.domain.model.ThemeMode
import io.github.simplenote.domain.model.ThemeSettings
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * DataStore implementation for theme preferences.
 */
class ThemePreferences(
    private val dataStore: DataStore<Preferences>
) {
    
    companion object {
        private val THEME_MODE_KEY = stringPreferencesKey("theme_mode")
        private val USE_DYNAMIC_THEME_KEY = booleanPreferencesKey("use_dynamic_theme")
    }
    
    /**
     * Get theme settings as a Flow
     */
    val themeSettings: Flow<ThemeSettings> = dataStore.data.map { preferences ->
        ThemeSettings(
            themeMode = ThemeMode.fromString(
                preferences[THEME_MODE_KEY] ?: ThemeMode.DEFAULT.name
            ),
            useDynamicTheme = preferences[USE_DYNAMIC_THEME_KEY] ?: true
        )
    }
    
    /**
     * Update theme mode
     */
    suspend fun updateThemeMode(themeMode: ThemeMode) {
        dataStore.edit { preferences ->
            preferences[THEME_MODE_KEY] = themeMode.name
        }
    }
    
    /**
     * Update dynamic theme setting
     */
    suspend fun updateUseDynamicTheme(useDynamicTheme: Boolean) {
        dataStore.edit { preferences ->
            preferences[USE_DYNAMIC_THEME_KEY] = useDynamicTheme
        }
    }
    
    /**
     * Update all theme settings
     */
    suspend fun updateThemeSettings(themeSettings: ThemeSettings) {
        dataStore.edit { preferences ->
            preferences[THEME_MODE_KEY] = themeSettings.themeMode.name
            preferences[USE_DYNAMIC_THEME_KEY] = themeSettings.useDynamicTheme
        }
    }
}
