io.github.simplenote:xml/data_extraction_rules = 0x7f100001
io.github.simplenote:styleable/ViewBackgroundHelper = 0x7f0f0035
io.github.simplenote:styleable/TextAppearance = 0x7f0f0032
io.github.simplenote:styleable/StateListDrawableItem = 0x7f0f0030
io.github.simplenote:styleable/StateListDrawable = 0x7f0f002f
io.github.simplenote:styleable/Spinner = 0x7f0f002e
io.github.simplenote:styleable/NavInclude = 0x7f0f0028
io.github.simplenote:styleable/NavHost = 0x7f0f0027
io.github.simplenote:styleable/NavGraphNavigator = 0x7f0f0026
io.github.simplenote:styleable/ListPopupWindow = 0x7f0f001f
io.github.simplenote:styleable/LinearLayoutCompat_Layout = 0x7f0f001e
io.github.simplenote:styleable/GradientColor = 0x7f0f001b
io.github.simplenote:styleable/FragmentContainerView = 0x7f0f001a
io.github.simplenote:styleable/Fragment = 0x7f0f0019
io.github.simplenote:styleable/FontFamily = 0x7f0f0017
io.github.simplenote:styleable/DrawerArrowToggle = 0x7f0f0016
io.github.simplenote:styleable/ColorStateListItem = 0x7f0f0014
io.github.simplenote:styleable/CheckedTextView = 0x7f0f0013
io.github.simplenote:styleable/Capability = 0x7f0f0012
io.github.simplenote:styleable/ButtonBarLayout = 0x7f0f0011
io.github.simplenote:styleable/AppCompatTheme = 0x7f0f0010
io.github.simplenote:styleable/AppCompatImageView = 0x7f0f000c
io.github.simplenote:styleable/AppCompatEmojiHelper = 0x7f0f000b
io.github.simplenote:styleable/AnimatedStateListDrawableTransition = 0x7f0f000a
io.github.simplenote:styleable/AnimatedStateListDrawableItem = 0x7f0f0009
io.github.simplenote:styleable/AnimatedStateListDrawableCompat = 0x7f0f0008
io.github.simplenote:styleable/ActivityNavigator = 0x7f0f0006
io.github.simplenote:styleable/ActionMode = 0x7f0f0004
io.github.simplenote:styleable/ActionMenuView = 0x7f0f0003
io.github.simplenote:styleable/ActionMenuItemView = 0x7f0f0002
io.github.simplenote:styleable/ActionBar = 0x7f0f0000
io.github.simplenote:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0e0169
io.github.simplenote:style/Widget.AppCompat.Spinner.DropDown = 0x7f0e0165
io.github.simplenote:style/Widget.AppCompat.RatingBar.Small = 0x7f0e015f
io.github.simplenote:style/Widget.AppCompat.ProgressBar = 0x7f0e015b
io.github.simplenote:style/Widget.AppCompat.PopupMenu = 0x7f0e0158
io.github.simplenote:style/Widget.AppCompat.ListView.DropDown = 0x7f0e0156
io.github.simplenote:style/Widget.AppCompat.ListView = 0x7f0e0155
io.github.simplenote:xml/backup_rules = 0x7f100000
io.github.simplenote:style/Widget.AppCompat.ListPopupWindow = 0x7f0e0154
io.github.simplenote:style/Widget.AppCompat.ListMenuView = 0x7f0e0153
io.github.simplenote:styleable/PopupWindowBackgroundState = 0x7f0f002b
io.github.simplenote:style/Widget.AppCompat.Light.SearchView = 0x7f0e0151
io.github.simplenote:style/Widget.AppCompat.Light.PopupMenu = 0x7f0e014f
io.github.simplenote:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0e014b
io.github.simplenote:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0e014a
io.github.simplenote:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0e0149
io.github.simplenote:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0e0147
io.github.simplenote:style/Widget.AppCompat.Light.ActionButton = 0x7f0e0146
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0e0144
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0e0143
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0e0142
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0e0141
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0e013f
io.github.simplenote:style/Widget.AppCompat.ImageButton = 0x7f0e013c
io.github.simplenote:style/Widget.AppCompat.EditText = 0x7f0e013b
io.github.simplenote:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0e0135
io.github.simplenote:style/Widget.AppCompat.Button.Small = 0x7f0e0133
io.github.simplenote:style/Widget.AppCompat.Button.Colored = 0x7f0e0132
io.github.simplenote:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0e0131
io.github.simplenote:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0e0130
io.github.simplenote:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0e012d
io.github.simplenote:style/Widget.AppCompat.ActionMode = 0x7f0e012b
io.github.simplenote:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0e0129
io.github.simplenote:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0e014c
io.github.simplenote:style/Widget.AppCompat.ActionBar.Solid = 0x7f0e0124
io.github.simplenote:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0e0121
io.github.simplenote:style/ThemeOverlay.AppCompat.DayNight = 0x7f0e011e
io.github.simplenote:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0e011d
io.github.simplenote:style/ThemeOverlay.AppCompat = 0x7f0e011a
io.github.simplenote:style/Theme.AppCompat.NoActionBar = 0x7f0e0115
io.github.simplenote:style/Theme.AppCompat.Light.NoActionBar = 0x7f0e0114
io.github.simplenote:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0e0112
io.github.simplenote:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0e010b
io.github.simplenote:style/Theme.AppCompat.Dialog.Alert = 0x7f0e010a
io.github.simplenote:style/Theme.AppCompat.Dialog = 0x7f0e0109
io.github.simplenote:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0e0108
io.github.simplenote:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0e0107
io.github.simplenote:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0e0106
io.github.simplenote:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0e015c
io.github.simplenote:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0e0103
io.github.simplenote:style/Theme.AppCompat.DayNight = 0x7f0e0102
io.github.simplenote:style/Theme.AppCompat = 0x7f0e0100
io.github.simplenote:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0e00ff
io.github.simplenote:style/TextAppearance.Compat.Notification.Title = 0x7f0e00fc
io.github.simplenote:style/TextAppearance.Compat.Notification.Line2 = 0x7f0e00fa
io.github.simplenote:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0e00f6
io.github.simplenote:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0e00ef
io.github.simplenote:style/TextAppearance.AppCompat.Widget.Button = 0x7f0e00ee
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0e00ed
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0e00ec
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0e00eb
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0e00ea
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0e00e8
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0e00e6
io.github.simplenote:styleable/NavAction = 0x7f0f0023
io.github.simplenote:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0e00e3
io.github.simplenote:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0e00e1
io.github.simplenote:style/TextAppearance.AppCompat.Small = 0x7f0e00de
io.github.simplenote:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0e00dd
io.github.simplenote:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0e00dc
io.github.simplenote:style/TextAppearance.AppCompat.Menu = 0x7f0e00db
io.github.simplenote:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0e00da
io.github.simplenote:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0e00d7
io.github.simplenote:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0e00d6
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0e00e5
io.github.simplenote:style/TextAppearance.AppCompat.Display2 = 0x7f0e00ce
io.github.simplenote:style/TextAppearance.AppCompat.Button = 0x7f0e00cb
io.github.simplenote:style/TextAppearance.AppCompat = 0x7f0e00c8
io.github.simplenote:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0e00c6
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0e00c5
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0e00c4
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0e00c3
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0e00c2
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0e00c1
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0e00bf
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0e00be
io.github.simplenote:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0e00fd
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0e00bd
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0e00bc
io.github.simplenote:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0e00b7
io.github.simplenote:style/Platform.V25.AppCompat.Light = 0x7f0e00b5
io.github.simplenote:style/Platform.V25.AppCompat = 0x7f0e00b4
io.github.simplenote:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0e00b0
io.github.simplenote:style/Widget.AppCompat.Spinner = 0x7f0e0164
io.github.simplenote:style/Platform.ThemeOverlay.AppCompat = 0x7f0e00af
io.github.simplenote:style/FloatingDialogWindowTheme = 0x7f0e00ac
io.github.simplenote:style/FloatingDialogTheme = 0x7f0e00ab
io.github.simplenote:style/EdgeToEdgeFloatingDialogWindowTheme = 0x7f0e00aa
io.github.simplenote:style/Base.v27.Theme.SplashScreen.Light = 0x7f0e00a7
io.github.simplenote:style/Base.v27.Theme.SplashScreen = 0x7f0e00a6
io.github.simplenote:style/Base.v21.Theme.SplashScreen = 0x7f0e00a4
io.github.simplenote:style/Base.Widget.AppCompat.Toolbar = 0x7f0e00a2
io.github.simplenote:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0e00a1
io.github.simplenote:style/Base.Widget.AppCompat.Spinner = 0x7f0e009e
io.github.simplenote:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0e0099
io.github.simplenote:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0e0098
io.github.simplenote:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0e0096
io.github.simplenote:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0e0093
io.github.simplenote:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0e0091
io.github.simplenote:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0e008e
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0e00c0
io.github.simplenote:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0e008a
io.github.simplenote:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0e0088
io.github.simplenote:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0e0087
io.github.simplenote:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0e008c
io.github.simplenote:style/Base.Widget.AppCompat.EditText = 0x7f0e0083
io.github.simplenote:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0e0080
io.github.simplenote:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0e007f
io.github.simplenote:style/Theme.AppCompat.Light = 0x7f0e010e
io.github.simplenote:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0e007d
io.github.simplenote:style/Base.Widget.AppCompat.ButtonBar = 0x7f0e007b
io.github.simplenote:style/Base.Widget.AppCompat.Button.Small = 0x7f0e007a
io.github.simplenote:style/Base.Widget.AppCompat.Button.Colored = 0x7f0e0079
io.github.simplenote:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0e0078
io.github.simplenote:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0e0077
io.github.simplenote:style/Base.Widget.AppCompat.ActionMode = 0x7f0e0072
io.github.simplenote:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0e014d
io.github.simplenote:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0e0071
io.github.simplenote:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0e0070
io.github.simplenote:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0e006d
io.github.simplenote:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0e006c
io.github.simplenote:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0e0163
io.github.simplenote:style/Base.Widget.AppCompat.ActionBar = 0x7f0e006a
io.github.simplenote:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0e0069
io.github.simplenote:style/Base.V7.Widget.AppCompat.EditText = 0x7f0e0068
io.github.simplenote:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0e0066
io.github.simplenote:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0e0065
io.github.simplenote:style/Base.V7.Theme.AppCompat.Light = 0x7f0e0064
io.github.simplenote:style/Base.V7.Theme.AppCompat = 0x7f0e0062
io.github.simplenote:style/Theme.SplashScreen.IconBackground = 0x7f0e0119
io.github.simplenote:style/Base.V26.Theme.AppCompat = 0x7f0e005d
io.github.simplenote:style/Base.V23.Theme.AppCompat = 0x7f0e005b
io.github.simplenote:style/Base.V22.Theme.AppCompat.Light = 0x7f0e005a
io.github.simplenote:style/Base.V22.Theme.AppCompat = 0x7f0e0059
io.github.simplenote:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0e0058
io.github.simplenote:style/Base.V21.Theme.AppCompat.Light = 0x7f0e0056
io.github.simplenote:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0e0053
io.github.simplenote:styleable/ActivityChooserView = 0x7f0f0005
io.github.simplenote:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0e0051
io.github.simplenote:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0e0050
io.github.simplenote:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0e004f
io.github.simplenote:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0e004e
io.github.simplenote:style/Base.Theme.SplashScreen.DayNight = 0x7f0e004b
io.github.simplenote:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0e0045
io.github.simplenote:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0e0044
io.github.simplenote:style/TextAppearance.AppCompat.Display1 = 0x7f0e00cd
io.github.simplenote:style/Base.Theme.AppCompat.Light = 0x7f0e0043
io.github.simplenote:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0e003f
io.github.simplenote:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0e012a
io.github.simplenote:style/Base.Theme.AppCompat.Dialog = 0x7f0e003e
io.github.simplenote:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0e003b
io.github.simplenote:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0e003a
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0e0038
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0e0031
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0e002c
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0e002b
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0e002a
io.github.simplenote:style/Base.V26.Theme.AppCompat.Light = 0x7f0e005e
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0e0028
io.github.simplenote:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0e0023
io.github.simplenote:style/TextAppearance.AppCompat.Body2 = 0x7f0e00ca
io.github.simplenote:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0e0022
io.github.simplenote:style/Base.TextAppearance.AppCompat.Small = 0x7f0e0021
io.github.simplenote:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0e0020
io.github.simplenote:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0e001f
io.github.simplenote:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0e001e
io.github.simplenote:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0e001a
io.github.simplenote:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0e0018
io.github.simplenote:style/Base.TextAppearance.AppCompat.Large = 0x7f0e0017
io.github.simplenote:style/Widget.AppCompat.RatingBar = 0x7f0e015d
io.github.simplenote:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0e0047
io.github.simplenote:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0e0013
io.github.simplenote:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0e0012
io.github.simplenote:style/Base.TextAppearance.AppCompat.Button = 0x7f0e000f
io.github.simplenote:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0e000b
io.github.simplenote:style/Base.DialogWindowTitle.AppCompat = 0x7f0e000a
io.github.simplenote:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0e0148
io.github.simplenote:style/Base.AlertDialog.AppCompat.Light = 0x7f0e0006
io.github.simplenote:style/Base.AlertDialog.AppCompat = 0x7f0e0005
io.github.simplenote:style/Animation.AppCompat.Tooltip = 0x7f0e0004
io.github.simplenote:style/Animation.AppCompat.Dialog = 0x7f0e0002
io.github.simplenote:string/tooltip_description = 0x7f0d0074
io.github.simplenote:style/Base.TextAppearance.AppCompat = 0x7f0e000c
io.github.simplenote:string/tab = 0x7f0d0072
io.github.simplenote:string/switch_role = 0x7f0d0071
io.github.simplenote:string/state_on = 0x7f0d006f
io.github.simplenote:string/state_off = 0x7f0d006e
io.github.simplenote:string/state_empty = 0x7f0d006d
io.github.simplenote:string/snackbar_pane_title = 0x7f0d006c
io.github.simplenote:string/selected = 0x7f0d006b
io.github.simplenote:string/range_start = 0x7f0d0069
io.github.simplenote:string/range_end = 0x7f0d0068
io.github.simplenote:string/m3c_tooltip_pane_description = 0x7f0d0064
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0e0034
io.github.simplenote:string/m3c_tooltip_long_press_label = 0x7f0d0063
io.github.simplenote:string/m3c_time_picker_pm = 0x7f0d0062
io.github.simplenote:string/m3c_time_picker_period_toggle_description = 0x7f0d0061
io.github.simplenote:styleable/Toolbar = 0x7f0f0033
io.github.simplenote:string/m3c_time_picker_minute_selection = 0x7f0d005e
io.github.simplenote:string/m3c_time_picker_minute = 0x7f0d005d
io.github.simplenote:string/m3c_time_picker_hour_text_field = 0x7f0d005c
io.github.simplenote:string/m3c_time_picker_hour_suffix = 0x7f0d005b
io.github.simplenote:string/m3c_time_picker_hour_selection = 0x7f0d005a
io.github.simplenote:string/m3c_time_picker_hour_24h_suffix = 0x7f0d0059
io.github.simplenote:styleable/GradientColorItem = 0x7f0f001c
io.github.simplenote:string/m3c_time_picker_am = 0x7f0d0057
io.github.simplenote:string/m3c_dropdown_menu_collapsed = 0x7f0d0051
io.github.simplenote:string/m3c_dialog = 0x7f0d0050
io.github.simplenote:string/m3c_date_range_picker_title = 0x7f0d004f
io.github.simplenote:string/m3c_date_range_picker_end_headline = 0x7f0d004b
io.github.simplenote:string/m3c_date_range_picker_day_in_range = 0x7f0d004a
io.github.simplenote:string/m3c_date_range_input_title = 0x7f0d0049
io.github.simplenote:string/m3c_date_range_input_invalid_range_input = 0x7f0d0048
io.github.simplenote:string/m3c_date_picker_year_picker_pane_title = 0x7f0d0047
io.github.simplenote:string/m3c_date_picker_today_description = 0x7f0d0046
io.github.simplenote:string/m3c_date_picker_title = 0x7f0d0045
io.github.simplenote:string/m3c_date_picker_switch_to_previous_month = 0x7f0d0043
io.github.simplenote:string/m3c_date_picker_switch_to_input_mode = 0x7f0d0041
io.github.simplenote:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0e0161
io.github.simplenote:string/m3c_date_picker_switch_to_day_selection = 0x7f0d0040
io.github.simplenote:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0e00f7
io.github.simplenote:string/m3c_date_picker_scroll_to_later_years = 0x7f0d003e
io.github.simplenote:style/TextAppearance.AppCompat.Medium = 0x7f0e00d9
io.github.simplenote:string/m3c_date_picker_no_selection_description = 0x7f0d003c
io.github.simplenote:string/m3c_date_picker_headline = 0x7f0d0039
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0e00ba
io.github.simplenote:string/m3c_date_input_no_input_description = 0x7f0d0037
io.github.simplenote:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0e00d5
io.github.simplenote:string/m3c_date_input_invalid_year_range = 0x7f0d0035
io.github.simplenote:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0e0138
io.github.simplenote:string/m3c_date_input_invalid_for_pattern = 0x7f0d0033
io.github.simplenote:string/m3c_date_input_headline_description = 0x7f0d0032
io.github.simplenote:string/m3c_date_input_headline = 0x7f0d0031
io.github.simplenote:string/m3c_bottom_sheet_pane_title = 0x7f0d0030
io.github.simplenote:string/m3c_bottom_sheet_drag_handle_description = 0x7f0d002e
io.github.simplenote:string/m3c_date_picker_scroll_to_earlier_years = 0x7f0d003d
io.github.simplenote:string/m3c_bottom_sheet_dismiss_description = 0x7f0d002d
io.github.simplenote:string/dropdown_menu = 0x7f0d0029
io.github.simplenote:string/default_popup_window_title = 0x7f0d0028
io.github.simplenote:string/default_error_message = 0x7f0d0027
io.github.simplenote:styleable/AppCompatSeekBar = 0x7f0f000d
io.github.simplenote:string/call_notification_screening_text = 0x7f0d0024
io.github.simplenote:style/ThemeOverlay.AppCompat.Dialog = 0x7f0e0120
io.github.simplenote:string/call_notification_incoming_text = 0x7f0d0022
io.github.simplenote:string/call_notification_hang_up_action = 0x7f0d0021
io.github.simplenote:string/androidx_startup = 0x7f0d001b
io.github.simplenote:string/abc_toolbar_collapse_description = 0x7f0d001a
io.github.simplenote:string/abc_shareactionprovider_share_with_application = 0x7f0d0019
io.github.simplenote:string/abc_searchview_description_voice = 0x7f0d0017
io.github.simplenote:string/abc_searchview_description_search = 0x7f0d0015
io.github.simplenote:string/abc_searchview_description_clear = 0x7f0d0013
io.github.simplenote:string/abc_prepend_shortcut_label = 0x7f0d0011
io.github.simplenote:string/abc_menu_sym_shortcut_label = 0x7f0d0010
io.github.simplenote:string/abc_menu_space_shortcut_label = 0x7f0d000f
io.github.simplenote:string/abc_menu_shift_shortcut_label = 0x7f0d000e
io.github.simplenote:string/abc_menu_function_shortcut_label = 0x7f0d000c
io.github.simplenote:string/abc_menu_enter_shortcut_label = 0x7f0d000b
io.github.simplenote:string/abc_menu_delete_shortcut_label = 0x7f0d000a
io.github.simplenote:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0e001c
io.github.simplenote:string/abc_menu_alt_shortcut_label = 0x7f0d0008
io.github.simplenote:style/Base.Animation.AppCompat.Dialog = 0x7f0e0007
io.github.simplenote:string/abc_activity_chooser_view_see_all = 0x7f0d0004
io.github.simplenote:styleable/CompoundButton = 0x7f0f0015
io.github.simplenote:style/Base.Widget.AppCompat.ImageButton = 0x7f0e0084
io.github.simplenote:string/abc_action_menu_overflow_description = 0x7f0d0002
io.github.simplenote:string/abc_action_bar_up_description = 0x7f0d0001
io.github.simplenote:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0e0063
io.github.simplenote:string/abc_action_bar_home_description = 0x7f0d0000
io.github.simplenote:mipmap/ic_launcher_round = 0x7f0c0001
io.github.simplenote:layout/splash_screen_view = 0x7f0b0028
io.github.simplenote:layout/support_simple_spinner_dropdown_item = 0x7f0b0029
io.github.simplenote:layout/select_dialog_multichoice_material = 0x7f0b0026
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0e0032
io.github.simplenote:layout/select_dialog_item_material = 0x7f0b0025
io.github.simplenote:style/Widget.AppCompat.PopupWindow = 0x7f0e015a
io.github.simplenote:layout/notification_template_icon_group = 0x7f0b0022
io.github.simplenote:layout/notification_template_custom_big = 0x7f0b0021
io.github.simplenote:layout/notification_action_tombstone = 0x7f0b0020
io.github.simplenote:layout/notification_action = 0x7f0b001f
io.github.simplenote:layout/abc_tooltip = 0x7f0b001b
io.github.simplenote:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0e00fe
io.github.simplenote:layout/abc_search_view = 0x7f0b0019
io.github.simplenote:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
io.github.simplenote:layout/abc_screen_toolbar = 0x7f0b0017
io.github.simplenote:style/Base.Widget.AppCompat.Button = 0x7f0e0075
io.github.simplenote:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0e006e
io.github.simplenote:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
io.github.simplenote:layout/abc_screen_simple = 0x7f0b0015
io.github.simplenote:layout/abc_screen_content_include = 0x7f0b0014
io.github.simplenote:layout/abc_popup_menu_item_layout = 0x7f0b0013
io.github.simplenote:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
io.github.simplenote:layout/abc_list_menu_item_radio = 0x7f0b0011
io.github.simplenote:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0e0159
io.github.simplenote:layout/abc_list_menu_item_checkbox = 0x7f0b000e
io.github.simplenote:layout/notification_template_part_chronometer = 0x7f0b0023
io.github.simplenote:layout/abc_expanded_menu_layout = 0x7f0b000d
io.github.simplenote:style/Widget.AppCompat.SearchView = 0x7f0e0160
io.github.simplenote:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
io.github.simplenote:layout/abc_action_menu_layout = 0x7f0b0003
io.github.simplenote:layout/abc_action_bar_title_item = 0x7f0b0000
io.github.simplenote:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0e010f
io.github.simplenote:interpolator/fast_out_slow_in = 0x7f0a0006
io.github.simplenote:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
io.github.simplenote:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
io.github.simplenote:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
io.github.simplenote:integer/default_icon_animation_duration = 0x7f090004
io.github.simplenote:integer/abc_config_activityDefaultDur = 0x7f090000
io.github.simplenote:id/wrapped_composition_tag = 0x7f0800c1
io.github.simplenote:id/wrap_content = 0x7f0800c0
io.github.simplenote:id/visible_removing_fragment_view_tag = 0x7f0800be
io.github.simplenote:id/view_tree_saved_state_registry_owner = 0x7f0800bc
io.github.simplenote:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0800bb
io.github.simplenote:id/up = 0x7f0800b7
io.github.simplenote:id/uniform = 0x7f0800b6
io.github.simplenote:id/topPanel = 0x7f0800b4
io.github.simplenote:id/top = 0x7f0800b3
io.github.simplenote:id/title_template = 0x7f0800b2
io.github.simplenote:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0e0073
io.github.simplenote:id/time = 0x7f0800af
io.github.simplenote:id/tag_window_insets_animation_callback = 0x7f0800aa
io.github.simplenote:id/tag_screen_reader_focusable = 0x7f0800a4
io.github.simplenote:string/m3c_date_picker_navigate_to_year_description = 0x7f0d003b
io.github.simplenote:id/tag_on_receive_content_mime_types = 0x7f0800a3
io.github.simplenote:string/in_progress = 0x7f0d002a
io.github.simplenote:id/tag_accessibility_pane_title = 0x7f08009f
io.github.simplenote:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0e011b
io.github.simplenote:layout/abc_action_menu_item_layout = 0x7f0b0002
io.github.simplenote:integer/cancel_button_image_alpha = 0x7f090002
io.github.simplenote:id/tag_accessibility_heading = 0x7f08009e
io.github.simplenote:id/tag_accessibility_clickable_spans = 0x7f08009d
io.github.simplenote:string/abc_capital_on = 0x7f0d0007
io.github.simplenote:id/tag_accessibility_actions = 0x7f08009c
io.github.simplenote:id/tabMode = 0x7f08009b
io.github.simplenote:id/submit_area = 0x7f08009a
io.github.simplenote:id/submenuarrow = 0x7f080099
io.github.simplenote:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
io.github.simplenote:id/src_over = 0x7f080098
io.github.simplenote:id/src_in = 0x7f080097
io.github.simplenote:id/src_atop = 0x7f080096
io.github.simplenote:id/split_action_bar = 0x7f080095
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0e00bb
io.github.simplenote:id/splashscreen_icon_view = 0x7f080094
io.github.simplenote:id/spacer = 0x7f080092
io.github.simplenote:id/showTitle = 0x7f080091
io.github.simplenote:styleable/NavDeepLink = 0x7f0f0025
io.github.simplenote:id/showCustom = 0x7f08008f
io.github.simplenote:styleable/NavArgument = 0x7f0f0024
io.github.simplenote:string/search_menu_title = 0x7f0d006a
io.github.simplenote:id/shortcut = 0x7f08008e
io.github.simplenote:id/select_dialog_listview = 0x7f08008d
io.github.simplenote:id/search_plate = 0x7f08008a
io.github.simplenote:styleable/Navigator = 0x7f0f0029
io.github.simplenote:id/search_mag_icon = 0x7f080089
io.github.simplenote:id/search_go_btn = 0x7f080088
io.github.simplenote:id/search_button = 0x7f080085
io.github.simplenote:id/search_bar = 0x7f080084
io.github.simplenote:id/search_badge = 0x7f080083
io.github.simplenote:style/ThemeOverlay.AppCompat.Dark = 0x7f0e011c
io.github.simplenote:layout/ime_base_split_test_activity = 0x7f0b001d
io.github.simplenote:id/scrollView = 0x7f080082
io.github.simplenote:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0e0040
io.github.simplenote:id/scrollIndicatorUp = 0x7f080081
io.github.simplenote:id/scrollIndicatorDown = 0x7f080080
io.github.simplenote:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0e0027
io.github.simplenote:id/screen = 0x7f08007f
io.github.simplenote:styleable/AppCompatTextView = 0x7f0f000f
io.github.simplenote:style/Widget.AppCompat.ActionBar.TabView = 0x7f0e0127
io.github.simplenote:id/right_side = 0x7f08007e
io.github.simplenote:string/mc2_snackbar_pane_title = 0x7f0d0065
io.github.simplenote:id/report_drawn = 0x7f08007c
io.github.simplenote:id/progress_horizontal = 0x7f08007a
io.github.simplenote:id/progress_circular = 0x7f080079
io.github.simplenote:id/pooling_container_listener_holder_tag = 0x7f080078
io.github.simplenote:id/tag_unhandled_key_listeners = 0x7f0800a9
io.github.simplenote:id/tag_transition_group = 0x7f0800a7
io.github.simplenote:id/parentPanel = 0x7f080077
io.github.simplenote:id/on = 0x7f080076
io.github.simplenote:style/Base.Widget.AppCompat.PopupWindow = 0x7f0e0094
io.github.simplenote:id/off = 0x7f080075
io.github.simplenote:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0e0039
io.github.simplenote:string/m3c_bottom_sheet_expand_description = 0x7f0d002f
io.github.simplenote:id/notification_main_column_container = 0x7f080074
io.github.simplenote:id/notification_background = 0x7f080072
io.github.simplenote:id/view_tree_lifecycle_owner = 0x7f0800ba
io.github.simplenote:id/normal = 0x7f080071
io.github.simplenote:style/Widget.AppCompat.TextView = 0x7f0e0168
io.github.simplenote:id/never = 0x7f08006f
io.github.simplenote:id/nav_controller_view_tag = 0x7f08006e
io.github.simplenote:id/multiply = 0x7f08006d
io.github.simplenote:id/message = 0x7f08006b
io.github.simplenote:id/ifRoom = 0x7f080061
io.github.simplenote:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0e009f
io.github.simplenote:id/forever = 0x7f080057
io.github.simplenote:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0e0048
io.github.simplenote:id/expanded_menu = 0x7f080056
io.github.simplenote:id/end = 0x7f080054
io.github.simplenote:attr/titleMarginTop = 0x7f03011f
io.github.simplenote:id/edit_text_id = 0x7f080053
io.github.simplenote:id/default_activity_button = 0x7f08004f
io.github.simplenote:id/decor_content_parent = 0x7f08004e
io.github.simplenote:id/contentPanel = 0x7f08004b
io.github.simplenote:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
io.github.simplenote:id/consume_window_insets_tag = 0x7f080049
io.github.simplenote:string/m3c_date_picker_switch_to_next_month = 0x7f0d0042
io.github.simplenote:id/checked = 0x7f080045
io.github.simplenote:style/Base.TextAppearance.AppCompat.Menu = 0x7f0e001d
io.github.simplenote:id/line3 = 0x7f080068
io.github.simplenote:id/checkbox = 0x7f080044
io.github.simplenote:id/center_vertical = 0x7f080043
io.github.simplenote:id/buttonPanel = 0x7f080042
io.github.simplenote:id/bottom = 0x7f080041
io.github.simplenote:id/beginning = 0x7f08003f
io.github.simplenote:id/androidx_compose_ui_view_composition_context = 0x7f08003d
io.github.simplenote:attr/actionBarItemBackground = 0x7f030002
io.github.simplenote:id/always = 0x7f08003c
io.github.simplenote:id/alertTitle = 0x7f08003b
io.github.simplenote:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0e007e
io.github.simplenote:id/actions = 0x7f080038
io.github.simplenote:id/action_mode_close_button = 0x7f080036
io.github.simplenote:style/Base.Widget.AppCompat.PopupMenu = 0x7f0e0092
io.github.simplenote:id/action_mode_bar_stub = 0x7f080035
io.github.simplenote:attr/titleTextColor = 0x7f030122
io.github.simplenote:id/action_menu_presenter = 0x7f080033
io.github.simplenote:style/TextAppearance.AppCompat.Subhead = 0x7f0e00e0
io.github.simplenote:drawable/abc_list_selector_disabled_holo_dark = 0x7f07002d
io.github.simplenote:id/action_menu_divider = 0x7f080032
io.github.simplenote:attr/drawableLeftCompat = 0x7f030075
io.github.simplenote:drawable/abc_list_selector_background_transition_holo_dark = 0x7f07002b
io.github.simplenote:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
io.github.simplenote:id/action_context_bar = 0x7f08002f
io.github.simplenote:id/accessibility_custom_action_25 = 0x7f080019
io.github.simplenote:id/action_bar_title = 0x7f08002d
io.github.simplenote:id/action_bar_subtitle = 0x7f08002c
io.github.simplenote:attr/backgroundTint = 0x7f030038
io.github.simplenote:id/action_bar_spinner = 0x7f08002b
io.github.simplenote:attr/popExitAnim = 0x7f0300d0
io.github.simplenote:id/action_bar_container = 0x7f080029
io.github.simplenote:attr/iconTintMode = 0x7f03009e
io.github.simplenote:attr/buttonStyle = 0x7f030045
io.github.simplenote:id/action_image = 0x7f080031
io.github.simplenote:id/accessibility_custom_action_7 = 0x7f080024
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0e0035
io.github.simplenote:id/accessibility_custom_action_4 = 0x7f080021
io.github.simplenote:string/m3c_dropdown_menu_expanded = 0x7f0d0052
io.github.simplenote:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070006
io.github.simplenote:id/accessibility_custom_action_31 = 0x7f080020
io.github.simplenote:attr/autoSizeMinTextSize = 0x7f030031
io.github.simplenote:id/accessibility_custom_action_24 = 0x7f080018
io.github.simplenote:string/abc_action_mode_done = 0x7f0d0003
io.github.simplenote:attr/buttonTint = 0x7f030047
io.github.simplenote:id/accessibility_custom_action_22 = 0x7f080016
io.github.simplenote:id/accessibility_custom_action_2 = 0x7f080013
io.github.simplenote:style/TextAppearance.AppCompat.Title = 0x7f0e00e2
io.github.simplenote:id/accessibility_custom_action_16 = 0x7f08000f
io.github.simplenote:id/accessibility_custom_action_13 = 0x7f08000c
io.github.simplenote:dimen/abc_text_size_menu_header_material = 0x7f06004a
io.github.simplenote:id/accessibility_custom_action_11 = 0x7f08000a
io.github.simplenote:dimen/abc_control_corner_material = 0x7f060018
io.github.simplenote:id/accessibility_custom_action_10 = 0x7f080009
io.github.simplenote:style/Base.Widget.AppCompat.SearchView = 0x7f0e009a
io.github.simplenote:id/accessibility_custom_action_1 = 0x7f080008
io.github.simplenote:id/accessibility_action_clickable_span = 0x7f080006
io.github.simplenote:id/SYM = 0x7f080005
io.github.simplenote:layout/abc_cascading_menu_item_layout = 0x7f0b000b
io.github.simplenote:id/hide_in_inspector_tag = 0x7f08005c
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0e0033
io.github.simplenote:id/META = 0x7f080003
io.github.simplenote:attr/actionModeCutDrawable = 0x7f030016
io.github.simplenote:attr/windowFixedWidthMajor = 0x7f030135
io.github.simplenote:id/FUNCTION = 0x7f080002
io.github.simplenote:integer/status_bar_notification_info_maxnum = 0x7f090006
io.github.simplenote:attr/maxButtonHeight = 0x7f0300bb
io.github.simplenote:id/accessibility_custom_action_6 = 0x7f080023
io.github.simplenote:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0e0014
io.github.simplenote:drawable/tooltip_frame_dark = 0x7f070071
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0e0145
io.github.simplenote:drawable/notification_template_icon_low_bg = 0x7f07006d
io.github.simplenote:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0e0137
io.github.simplenote:attr/textAppearanceSearchResultSubtitle = 0x7f03010a
io.github.simplenote:drawable/notification_template_icon_bg = 0x7f07006c
io.github.simplenote:id/useLogo = 0x7f0800b8
io.github.simplenote:drawable/notification_oversize_large_icon_bg = 0x7f07006b
io.github.simplenote:drawable/notification_bg_normal_pressed = 0x7f070069
io.github.simplenote:drawable/notification_bg_normal = 0x7f070068
io.github.simplenote:attr/fontProviderPackage = 0x7f03008f
io.github.simplenote:id/line1 = 0x7f080067
io.github.simplenote:style/Base.TextAppearance.AppCompat.Caption = 0x7f0e0010
io.github.simplenote:attr/windowMinWidthMajor = 0x7f030137
io.github.simplenote:drawable/notification_bg_low_pressed = 0x7f070067
io.github.simplenote:id/ALT = 0x7f080000
io.github.simplenote:attr/defaultQueryHint = 0x7f030069
io.github.simplenote:drawable/notification_action_background = 0x7f070063
io.github.simplenote:drawable/ic_call_decline_low = 0x7f07005f
io.github.simplenote:id/content = 0x7f08004a
io.github.simplenote:id/special_effects_controller_view_tag = 0x7f080093
io.github.simplenote:attr/actionModeCopyDrawable = 0x7f030015
io.github.simplenote:drawable/ic_call_decline = 0x7f07005e
io.github.simplenote:id/activity_chooser_view_content = 0x7f080039
io.github.simplenote:drawable/ic_call_answer_video_low = 0x7f07005d
io.github.simplenote:id/accessibility_custom_action_30 = 0x7f08001f
io.github.simplenote:drawable/ic_call_answer = 0x7f07005a
io.github.simplenote:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070051
io.github.simplenote:attr/editTextBackground = 0x7f03007f
io.github.simplenote:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f070055
io.github.simplenote:anim/abc_fade_in = 0x7f010000
io.github.simplenote:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070053
io.github.simplenote:drawable/notification_bg = 0x7f070064
io.github.simplenote:drawable/abc_textfield_search_material = 0x7f07004e
io.github.simplenote:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f07004d
io.github.simplenote:drawable/compat_splash_screen = 0x7f070058
io.github.simplenote:attr/fontFamily = 0x7f030089
io.github.simplenote:drawable/abc_textfield_default_mtrl_alpha = 0x7f07004b
io.github.simplenote:style/Theme.AppCompat.CompactMenu = 0x7f0e0101
io.github.simplenote:drawable/abc_text_select_handle_right_mtrl = 0x7f070049
io.github.simplenote:layout/notification_template_part_time = 0x7f0b0024
io.github.simplenote:drawable/abc_text_select_handle_left_mtrl = 0x7f070047
io.github.simplenote:integer/abc_config_activityShortDur = 0x7f090001
io.github.simplenote:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070045
io.github.simplenote:drawable/abc_star_black_48dp = 0x7f070040
io.github.simplenote:attr/alpha = 0x7f03002a
io.github.simplenote:drawable/abc_spinner_mtrl_am_alpha = 0x7f07003e
io.github.simplenote:styleable/MenuView = 0x7f0f0022
io.github.simplenote:drawable/abc_seekbar_tick_mark_material = 0x7f07003c
io.github.simplenote:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0e006b
io.github.simplenote:color/abc_tint_switch_track = 0x7f050018
io.github.simplenote:drawable/abc_seekbar_thumb_material = 0x7f07003b
io.github.simplenote:color/dim_foreground_material_light = 0x7f05002f
io.github.simplenote:drawable/abc_scrubber_track_mtrl_alpha = 0x7f07003a
io.github.simplenote:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070037
io.github.simplenote:dimen/hint_alpha_material_light = 0x7f06005e
io.github.simplenote:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f070036
io.github.simplenote:style/Base.V23.Theme.AppCompat.Light = 0x7f0e005c
io.github.simplenote:drawable/abc_ratingbar_indicator_material = 0x7f070033
io.github.simplenote:color/teal_200 = 0x7f05005b
io.github.simplenote:attr/subtitleTextStyle = 0x7f0300fd
io.github.simplenote:drawable/abc_list_selector_holo_light = 0x7f070030
io.github.simplenote:string/close_sheet = 0x7f0d0026
io.github.simplenote:drawable/abc_list_selector_holo_dark = 0x7f07002f
io.github.simplenote:string/call_notification_answer_video_action = 0x7f0d001f
io.github.simplenote:drawable/abc_list_selector_disabled_holo_light = 0x7f07002e
io.github.simplenote:dimen/compat_button_inset_vertical_material = 0x7f060052
io.github.simplenote:drawable/abc_list_selector_background_transition_holo_light = 0x7f07002c
io.github.simplenote:integer/m3c_window_layout_in_display_cutout_mode = 0x7f090005
io.github.simplenote:id/textSpacerNoButtons = 0x7f0800ad
io.github.simplenote:attr/argType = 0x7f03002c
io.github.simplenote:drawable/abc_list_pressed_holo_dark = 0x7f070029
io.github.simplenote:drawable/abc_ic_search_api_material = 0x7f070021
io.github.simplenote:drawable/abc_ic_clear_material = 0x7f070018
io.github.simplenote:drawable/abc_text_select_handle_middle_mtrl = 0x7f070048
io.github.simplenote:style/DialogWindowTheme = 0x7f0e00a8
io.github.simplenote:drawable/abc_dialog_material_background = 0x7f070014
io.github.simplenote:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070012
io.github.simplenote:string/m3c_snackbar_dismiss = 0x7f0d0055
io.github.simplenote:dimen/abc_list_item_height_material = 0x7f060031
io.github.simplenote:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f07000e
io.github.simplenote:drawable/abc_btn_radio_material = 0x7f07000a
io.github.simplenote:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f060072
io.github.simplenote:drawable/abc_btn_colored_material = 0x7f070008
io.github.simplenote:drawable/abc_btn_check_material_anim = 0x7f070005
io.github.simplenote:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0e0042
io.github.simplenote:drawable/abc_btn_check_material = 0x7f070004
io.github.simplenote:layout/abc_list_menu_item_icon = 0x7f0b000f
io.github.simplenote:id/group_divider = 0x7f080059
io.github.simplenote:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0e016b
io.github.simplenote:attr/panelMenuListWidth = 0x7f0300ce
io.github.simplenote:dimen/tooltip_y_offset_non_touch = 0x7f06007d
io.github.simplenote:dimen/tooltip_vertical_padding = 0x7f06007c
io.github.simplenote:dimen/hint_alpha_material_dark = 0x7f06005d
io.github.simplenote:dimen/tooltip_precise_anchor_threshold = 0x7f06007b
io.github.simplenote:dimen/tooltip_precise_anchor_extra_offset = 0x7f06007a
io.github.simplenote:dimen/tooltip_margin = 0x7f060079
io.github.simplenote:styleable/SearchView = 0x7f0f002d
io.github.simplenote:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0e0113
io.github.simplenote:dimen/tooltip_corner_radius = 0x7f060077
io.github.simplenote:attr/drawableSize = 0x7f030077
io.github.simplenote:dimen/splashscreen_icon_size_no_background = 0x7f060075
io.github.simplenote:attr/titleMarginEnd = 0x7f03011d
io.github.simplenote:dimen/splashscreen_icon_size = 0x7f060074
io.github.simplenote:id/homeAsUp = 0x7f08005e
io.github.simplenote:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f060073
io.github.simplenote:id/compose_view_saveable_id_tag = 0x7f080048
io.github.simplenote:dimen/splashscreen_icon_mask_size_no_background = 0x7f060070
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0e002f
io.github.simplenote:dimen/notification_small_icon_size_as_large = 0x7f06006c
io.github.simplenote:drawable/abc_ic_menu_overflow_material = 0x7f07001d
io.github.simplenote:drawable/abc_ratingbar_small_material = 0x7f070035
io.github.simplenote:color/primary_dark_material_light = 0x7f050045
io.github.simplenote:dimen/notification_right_side_padding_top = 0x7f06006a
io.github.simplenote:dimen/notification_right_icon_size = 0x7f060069
io.github.simplenote:dimen/notification_media_narrow_margin = 0x7f060068
io.github.simplenote:attr/customNavigationLayout = 0x7f030066
io.github.simplenote:id/italic = 0x7f080066
io.github.simplenote:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0e0076
io.github.simplenote:dimen/abc_text_size_caption_material = 0x7f060042
io.github.simplenote:dimen/notification_action_icon_size = 0x7f060061
io.github.simplenote:attr/lastBaselineToBottomHeight = 0x7f0300a6
io.github.simplenote:id/blocking = 0x7f080040
io.github.simplenote:attr/panelBackground = 0x7f0300cc
io.github.simplenote:dimen/hint_pressed_alpha_material_dark = 0x7f06005f
io.github.simplenote:id/dialog_button = 0x7f080050
io.github.simplenote:dimen/highlight_alpha_material_light = 0x7f06005c
io.github.simplenote:styleable/ActionBarLayout = 0x7f0f0001
io.github.simplenote:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
io.github.simplenote:dimen/highlight_alpha_material_colored = 0x7f06005a
io.github.simplenote:dimen/compat_button_inset_horizontal_material = 0x7f060051
io.github.simplenote:dimen/disabled_alpha_material_light = 0x7f060059
io.github.simplenote:dimen/disabled_alpha_material_dark = 0x7f060058
io.github.simplenote:dimen/compat_notification_large_icon_max_height = 0x7f060056
io.github.simplenote:id/notification_main_column = 0x7f080073
io.github.simplenote:drawable/tooltip_frame_light = 0x7f070072
io.github.simplenote:color/background_material_dark = 0x7f05001f
io.github.simplenote:dimen/compat_button_padding_vertical_material = 0x7f060054
io.github.simplenote:id/action_mode_bar = 0x7f080034
io.github.simplenote:dimen/abc_text_size_title_material = 0x7f06004f
io.github.simplenote:style/Widget.Compat.NotificationActionText = 0x7f0e016d
io.github.simplenote:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0e0081
io.github.simplenote:layout/ime_secondary_split_test_activity = 0x7f0b001e
io.github.simplenote:attr/subtitle = 0x7f0300fa
io.github.simplenote:color/error_color_material_dark = 0x7f050030
io.github.simplenote:dimen/abc_text_size_small_material = 0x7f06004c
io.github.simplenote:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0e0089
io.github.simplenote:dimen/abc_text_size_menu_material = 0x7f06004b
io.github.simplenote:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f07001f
io.github.simplenote:drawable/ic_call_answer_video = 0x7f07005c
io.github.simplenote:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0e000e
io.github.simplenote:color/vector_tint_theme_color = 0x7f050060
io.github.simplenote:dimen/abc_text_size_medium_material = 0x7f060049
io.github.simplenote:style/Widget.AppCompat.Spinner.Underlined = 0x7f0e0167
io.github.simplenote:dimen/abc_text_size_headline_material = 0x7f060047
io.github.simplenote:mipmap/ic_launcher = 0x7f0c0000
io.github.simplenote:dimen/abc_text_size_display_2_material = 0x7f060044
io.github.simplenote:dimen/abc_text_size_button_material = 0x7f060041
io.github.simplenote:attr/windowNoTitle = 0x7f030139
io.github.simplenote:dimen/abc_text_size_body_1_material = 0x7f06003f
io.github.simplenote:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0e0085
io.github.simplenote:attr/numericModifiers = 0x7f0300c6
io.github.simplenote:id/accessibility_custom_action_26 = 0x7f08001a
io.github.simplenote:dimen/abc_switch_padding = 0x7f06003e
io.github.simplenote:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0e005f
io.github.simplenote:id/action_bar_activity_content = 0x7f080028
io.github.simplenote:id/is_pooling_container_tag = 0x7f080065
io.github.simplenote:attr/listChoiceBackgroundIndicator = 0x7f0300aa
io.github.simplenote:attr/tintMode = 0x7f030119
io.github.simplenote:dimen/abc_star_medium = 0x7f06003c
io.github.simplenote:attr/colorControlNormal = 0x7f030058
io.github.simplenote:dimen/abc_star_big = 0x7f06003b
io.github.simplenote:attr/alphabeticModifiers = 0x7f03002b
io.github.simplenote:color/material_grey_600 = 0x7f05003e
io.github.simplenote:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
io.github.simplenote:dimen/abc_search_view_preferred_height = 0x7f060036
io.github.simplenote:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
io.github.simplenote:attr/ratingBarStyleIndicator = 0x7f0300e0
io.github.simplenote:id/accessibility_custom_action_17 = 0x7f080010
io.github.simplenote:dimen/abc_list_item_height_small_material = 0x7f060032
io.github.simplenote:id/icon = 0x7f08005f
io.github.simplenote:string/m3c_date_picker_switch_to_calendar_mode = 0x7f0d003f
io.github.simplenote:attr/actionBarTabStyle = 0x7f030008
io.github.simplenote:attr/iconifiedByDefault = 0x7f03009f
io.github.simplenote:dimen/abc_edit_text_inset_top_material = 0x7f06002e
io.github.simplenote:id/chronometer = 0x7f080046
io.github.simplenote:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
io.github.simplenote:style/Base.v21.Theme.SplashScreen.Light = 0x7f0e00a5
io.github.simplenote:attr/windowSplashScreenBackground = 0x7f03013c
io.github.simplenote:dimen/abc_dropdownitem_icon_width = 0x7f060029
io.github.simplenote:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f07001e
io.github.simplenote:id/add = 0x7f08003a
io.github.simplenote:dimen/abc_text_size_display_4_material = 0x7f060046
io.github.simplenote:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0e0111
io.github.simplenote:attr/tint = 0x7f030118
io.github.simplenote:dimen/abc_disabled_alpha_material_dark = 0x7f060027
io.github.simplenote:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001b
io.github.simplenote:dimen/abc_dialog_padding_top_material = 0x7f060025
io.github.simplenote:dimen/abc_dialog_padding_material = 0x7f060024
io.github.simplenote:dimen/abc_dialog_min_width_minor = 0x7f060023
io.github.simplenote:attr/logoDescription = 0x7f0300ba
io.github.simplenote:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f070017
io.github.simplenote:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0e0125
io.github.simplenote:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
io.github.simplenote:drawable/abc_cab_background_top_material = 0x7f070011
io.github.simplenote:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
io.github.simplenote:attr/textAppearanceListItemSecondary = 0x7f030107
io.github.simplenote:dimen/tooltip_horizontal_padding = 0x7f060078
io.github.simplenote:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
io.github.simplenote:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
io.github.simplenote:dimen/abc_dialog_fixed_width_major = 0x7f06001e
io.github.simplenote:attr/viewInflaterClass = 0x7f03012e
io.github.simplenote:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
io.github.simplenote:dimen/abc_text_size_display_1_material = 0x7f060043
io.github.simplenote:color/material_grey_50 = 0x7f05003d
io.github.simplenote:dimen/abc_dialog_corner_radius_material = 0x7f06001b
io.github.simplenote:string/m3c_dropdown_menu_toggle = 0x7f0d0053
io.github.simplenote:id/image = 0x7f080062
io.github.simplenote:animator/fragment_close_enter = 0x7f020000
io.github.simplenote:dimen/compat_notification_large_icon_max_width = 0x7f060057
io.github.simplenote:style/Widget.AppCompat.ButtonBar = 0x7f0e0134
io.github.simplenote:layout/abc_list_menu_item_layout = 0x7f0b0010
io.github.simplenote:dimen/abc_control_padding_material = 0x7f06001a
io.github.simplenote:drawable/abc_cab_background_internal_bg = 0x7f070010
io.github.simplenote:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0e009b
io.github.simplenote:dimen/abc_button_padding_vertical_material = 0x7f060015
io.github.simplenote:id/accessibility_custom_action_9 = 0x7f080026
io.github.simplenote:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
io.github.simplenote:attr/showDividers = 0x7f0300ec
io.github.simplenote:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070001
io.github.simplenote:dimen/abc_button_inset_vertical_material = 0x7f060013
io.github.simplenote:id/middle = 0x7f08006c
io.github.simplenote:dimen/abc_button_inset_horizontal_material = 0x7f060012
io.github.simplenote:attr/paddingEnd = 0x7f0300c9
io.github.simplenote:id/accessibility_custom_action_0 = 0x7f080007
io.github.simplenote:dimen/abc_action_button_min_height_material = 0x7f06000d
io.github.simplenote:string/autofill = 0x7f0d001d
io.github.simplenote:layout/abc_action_bar_up_container = 0x7f0b0001
io.github.simplenote:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
io.github.simplenote:dimen/abc_action_bar_stacked_max_height = 0x7f060009
io.github.simplenote:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
io.github.simplenote:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
io.github.simplenote:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0e00f0
io.github.simplenote:drawable/abc_switch_thumb_material = 0x7f070042
io.github.simplenote:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
io.github.simplenote:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0e0049
io.github.simplenote:dimen/abc_action_bar_default_height_material = 0x7f060002
io.github.simplenote:dimen/notification_big_circle_margin = 0x7f060063
io.github.simplenote:dimen/abc_action_bar_content_inset_material = 0x7f060000
io.github.simplenote:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0e0019
io.github.simplenote:color/vector_tint_color = 0x7f05005f
io.github.simplenote:color/tooltip_background_light = 0x7f05005e
io.github.simplenote:dimen/abc_text_size_large_material = 0x7f060048
io.github.simplenote:attr/thumbTintMode = 0x7f030114
io.github.simplenote:color/switch_thumb_normal_material_light = 0x7f05005a
io.github.simplenote:id/tag_on_receive_content_listener = 0x7f0800a2
io.github.simplenote:color/switch_thumb_normal_material_dark = 0x7f050059
io.github.simplenote:styleable/MenuGroup = 0x7f0f0020
io.github.simplenote:style/Base.Theme.SplashScreen = 0x7f0e004a
io.github.simplenote:attr/actionBarStyle = 0x7f030006
io.github.simplenote:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070038
io.github.simplenote:attr/editTextColor = 0x7f030080
io.github.simplenote:dimen/abc_progress_bar_height_material = 0x7f060035
io.github.simplenote:dimen/abc_star_small = 0x7f06003d
io.github.simplenote:id/accessibility_custom_action_27 = 0x7f08001b
io.github.simplenote:dimen/abc_action_button_min_width_material = 0x7f06000e
io.github.simplenote:style/TextAppearance.AppCompat.Caption = 0x7f0e00cc
io.github.simplenote:color/switch_thumb_material_light = 0x7f050058
io.github.simplenote:color/switch_thumb_material_dark = 0x7f050057
io.github.simplenote:color/switch_thumb_disabled_material_light = 0x7f050056
io.github.simplenote:color/secondary_text_disabled_material_dark = 0x7f050053
io.github.simplenote:drawable/notification_icon_background = 0x7f07006a
io.github.simplenote:color/secondary_text_default_material_light = 0x7f050052
io.github.simplenote:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0e00d8
io.github.simplenote:color/ripple_material_light = 0x7f050050
io.github.simplenote:drawable/abc_ratingbar_material = 0x7f070034
io.github.simplenote:string/m3c_bottom_sheet_collapse_description = 0x7f0d002c
io.github.simplenote:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
io.github.simplenote:color/purple_700 = 0x7f05004e
io.github.simplenote:styleable/ViewStubCompat = 0x7f0f0036
io.github.simplenote:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070039
io.github.simplenote:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
io.github.simplenote:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0e009d
io.github.simplenote:style/AlertDialog.AppCompat.Light = 0x7f0e0001
io.github.simplenote:color/purple_200 = 0x7f05004c
io.github.simplenote:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0e00b1
io.github.simplenote:string/abc_menu_meta_shortcut_label = 0x7f0d000d
io.github.simplenote:id/accessibility_custom_action_23 = 0x7f080017
io.github.simplenote:id/custom = 0x7f08004c
io.github.simplenote:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
io.github.simplenote:attr/dialogPreferredPadding = 0x7f03006c
io.github.simplenote:color/primary_text_disabled_material_dark = 0x7f05004a
io.github.simplenote:id/view_tree_disjoint_parent = 0x7f0800b9
io.github.simplenote:color/primary_text_default_material_light = 0x7f050049
io.github.simplenote:color/primary_text_default_material_dark = 0x7f050048
io.github.simplenote:attr/activityChooserViewStyle = 0x7f030024
io.github.simplenote:id/accessibility_custom_action_19 = 0x7f080012
io.github.simplenote:color/primary_material_light = 0x7f050047
io.github.simplenote:style/Base.TextAppearance.AppCompat.Headline = 0x7f0e0015
io.github.simplenote:color/call_notification_decline_color = 0x7f05002b
io.github.simplenote:drawable/btn_radio_off_mtrl = 0x7f070054
io.github.simplenote:style/Widget.AppCompat.Button.Borderless = 0x7f0e012f
io.github.simplenote:dimen/abc_alert_dialog_button_dimen = 0x7f060011
io.github.simplenote:color/switch_thumb_disabled_material_dark = 0x7f050055
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0e013e
io.github.simplenote:color/notification_icon_bg_color = 0x7f050043
io.github.simplenote:color/material_grey_900 = 0x7f050041
io.github.simplenote:string/call_notification_answer_action = 0x7f0d001e
io.github.simplenote:drawable/test_level_drawable = 0x7f070070
io.github.simplenote:styleable/AlertDialog = 0x7f0f0007
io.github.simplenote:color/material_grey_850 = 0x7f050040
io.github.simplenote:drawable/abc_star_half_black_48dp = 0x7f070041
io.github.simplenote:color/material_grey_800 = 0x7f05003f
io.github.simplenote:string/app_name = 0x7f0d001c
io.github.simplenote:id/action_text = 0x7f080037
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0e0036
io.github.simplenote:dimen/tooltip_y_offset_touch = 0x7f06007e
io.github.simplenote:style/Base.Theme.AppCompat = 0x7f0e003c
io.github.simplenote:color/material_grey_300 = 0x7f05003c
io.github.simplenote:color/material_deep_teal_500 = 0x7f05003a
io.github.simplenote:attr/theme = 0x7f030110
io.github.simplenote:attr/windowSplashScreenAnimatedIcon = 0x7f03013a
io.github.simplenote:dimen/abc_control_inset_material = 0x7f060019
io.github.simplenote:string/m3c_time_picker_minute_text_field = 0x7f0d0060
io.github.simplenote:color/material_blue_grey_950 = 0x7f050038
io.github.simplenote:style/Base.Animation.AppCompat.DropDownUp = 0x7f0e0008
io.github.simplenote:dimen/compat_control_corner_material = 0x7f060055
io.github.simplenote:dimen/notification_content_margin_start = 0x7f060064
io.github.simplenote:attr/actionModeStyle = 0x7f03001d
io.github.simplenote:color/material_blue_grey_900 = 0x7f050037
io.github.simplenote:string/template_percent = 0x7f0d0073
io.github.simplenote:id/fragment_container_view_tag = 0x7f080058
io.github.simplenote:attr/emojiCompatEnabled = 0x7f030083
io.github.simplenote:color/material_blue_grey_800 = 0x7f050036
io.github.simplenote:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0e0166
io.github.simplenote:style/Widget.AppCompat.SeekBar = 0x7f0e0162
io.github.simplenote:string/m3c_date_range_picker_scroll_to_previous_month = 0x7f0d004d
io.github.simplenote:drawable/btn_radio_on_mtrl = 0x7f070056
io.github.simplenote:color/foreground_material_dark = 0x7f050032
io.github.simplenote:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
io.github.simplenote:color/error_color_material_light = 0x7f050031
io.github.simplenote:style/Platform.V21.AppCompat = 0x7f0e00b2
io.github.simplenote:attr/shortcutMatchRequired = 0x7f0300ea
io.github.simplenote:color/dim_foreground_material_dark = 0x7f05002e
io.github.simplenote:style/EdgeToEdgeFloatingDialogTheme = 0x7f0e00a9
io.github.simplenote:attr/expandActivityOverflowButtonDrawable = 0x7f030086
io.github.simplenote:color/dim_foreground_disabled_material_light = 0x7f05002d
io.github.simplenote:string/m3c_search_bar_search = 0x7f0d0054
io.github.simplenote:color/call_notification_answer_color = 0x7f05002a
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0e00b9
io.github.simplenote:string/not_selected = 0x7f0d0067
io.github.simplenote:attr/layout = 0x7f0300a8
io.github.simplenote:color/button_material_dark = 0x7f050028
io.github.simplenote:color/primary_material_dark = 0x7f050046
io.github.simplenote:attr/buttonBarNegativeButtonStyle = 0x7f03003d
io.github.simplenote:bool/abc_action_bar_embed_tabs = 0x7f040000
io.github.simplenote:color/bright_foreground_material_light = 0x7f050027
io.github.simplenote:attr/elevation = 0x7f030082
io.github.simplenote:color/bright_foreground_disabled_material_light = 0x7f050023
io.github.simplenote:color/background_floating_material_dark = 0x7f05001d
io.github.simplenote:attr/tooltipForegroundColor = 0x7f030126
io.github.simplenote:style/Widget.AppCompat.Toolbar = 0x7f0e016a
io.github.simplenote:style/Theme.AppCompat.Empty = 0x7f0e010d
io.github.simplenote:style/TextAppearance.Compat.Notification = 0x7f0e00f8
io.github.simplenote:attr/closeItemLayout = 0x7f03004f
io.github.simplenote:animator/fragment_close_exit = 0x7f020001
io.github.simplenote:color/accent_material_light = 0x7f05001a
io.github.simplenote:attr/popEnterAnim = 0x7f0300cf
io.github.simplenote:style/Base.Theme.SplashScreen.Light = 0x7f0e004c
io.github.simplenote:color/bright_foreground_inverse_material_light = 0x7f050025
io.github.simplenote:color/accent_material_dark = 0x7f050019
io.github.simplenote:style/Base.V28.Theme.AppCompat.Light = 0x7f0e0061
io.github.simplenote:attr/subtitleTextAppearance = 0x7f0300fb
io.github.simplenote:dimen/abc_search_view_preferred_width = 0x7f060037
io.github.simplenote:color/abc_tint_seek_thumb = 0x7f050016
io.github.simplenote:color/abc_tint_default = 0x7f050014
io.github.simplenote:anim/abc_popup_exit = 0x7f010004
io.github.simplenote:color/abc_tint_btn_checkable = 0x7f050013
io.github.simplenote:attr/thumbTint = 0x7f030113
io.github.simplenote:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f070007
io.github.simplenote:style/AlertDialog.AppCompat = 0x7f0e0000
io.github.simplenote:color/abc_secondary_text_material_light = 0x7f050012
io.github.simplenote:attr/alertDialogCenterButtons = 0x7f030026
io.github.simplenote:color/background_floating_material_light = 0x7f05001e
io.github.simplenote:string/abc_activitychooserview_choose_application = 0x7f0d0005
io.github.simplenote:attr/windowSplashScreenAnimationDuration = 0x7f03013b
io.github.simplenote:color/abc_decor_view_status_guard = 0x7f050005
io.github.simplenote:color/abc_search_url_text_selected = 0x7f050010
io.github.simplenote:drawable/abc_list_longpressed_holo = 0x7f070028
io.github.simplenote:id/accessibility_custom_action_3 = 0x7f08001e
io.github.simplenote:drawable/abc_item_background_holo_light = 0x7f070024
io.github.simplenote:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0e0090
io.github.simplenote:id/withText = 0x7f0800bf
io.github.simplenote:attr/windowActionBarOverlay = 0x7f030131
io.github.simplenote:color/abc_primary_text_disable_only_material_light = 0x7f05000a
io.github.simplenote:color/androidx_core_ripple_material_light = 0x7f05001b
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0e0140
io.github.simplenote:color/abc_hint_foreground_material_dark = 0x7f050007
io.github.simplenote:attr/drawableTopCompat = 0x7f03007b
io.github.simplenote:color/abc_primary_text_material_light = 0x7f05000c
io.github.simplenote:drawable/abc_ic_go_search_api_material = 0x7f07001a
io.github.simplenote:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0e0026
io.github.simplenote:attr/actionModeCloseButtonStyle = 0x7f030012
io.github.simplenote:attr/dividerPadding = 0x7f030071
io.github.simplenote:color/abc_decor_view_status_guard_light = 0x7f050006
io.github.simplenote:style/TextAppearance.AppCompat.Display3 = 0x7f0e00cf
io.github.simplenote:dimen/notification_top_pad_large_text = 0x7f06006f
io.github.simplenote:dimen/notification_top_pad = 0x7f06006e
io.github.simplenote:color/abc_color_highlight_material = 0x7f050004
io.github.simplenote:color/abc_background_cache_hint_selector_material_light = 0x7f050001
io.github.simplenote:style/Base.Widget.AppCompat.ProgressBar = 0x7f0e0095
io.github.simplenote:attr/itemPadding = 0x7f0300a4
io.github.simplenote:id/accessibility_custom_action_15 = 0x7f08000e
io.github.simplenote:drawable/notification_bg_low_normal = 0x7f070066
io.github.simplenote:styleable/SwitchCompat = 0x7f0f0031
io.github.simplenote:attr/icon = 0x7f03009c
io.github.simplenote:attr/listDividerAlertDialog = 0x7f0300ad
io.github.simplenote:attr/showTitle = 0x7f0300ee
io.github.simplenote:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
io.github.simplenote:attr/actionButtonStyle = 0x7f03000c
io.github.simplenote:attr/contentInsetLeft = 0x7f030061
io.github.simplenote:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070057
io.github.simplenote:attr/backgroundTintMode = 0x7f030039
io.github.simplenote:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
io.github.simplenote:attr/windowMinWidthMinor = 0x7f030138
io.github.simplenote:attr/windowSplashScreenIconBackgroundColor = 0x7f03013d
io.github.simplenote:id/action_divider = 0x7f080030
io.github.simplenote:color/teal_700 = 0x7f05005c
io.github.simplenote:drawable/abc_vector_test = 0x7f07004f
io.github.simplenote:attr/windowActionBar = 0x7f030130
io.github.simplenote:drawable/abc_seekbar_track_material = 0x7f07003d
io.github.simplenote:style/Widget.AppCompat.ActionButton = 0x7f0e0128
io.github.simplenote:attr/splashScreenIconSize = 0x7f0300f3
io.github.simplenote:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0e0052
io.github.simplenote:layout/abc_action_mode_close_item_material = 0x7f0b0005
io.github.simplenote:id/expand_activities_button = 0x7f080055
io.github.simplenote:attr/textAppearancePopupMenuHeader = 0x7f030109
io.github.simplenote:attr/windowFixedWidthMinor = 0x7f030136
io.github.simplenote:attr/actionModeShareDrawable = 0x7f03001b
io.github.simplenote:dimen/compat_button_padding_horizontal_material = 0x7f060053
io.github.simplenote:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
io.github.simplenote:id/accessibility_custom_action_28 = 0x7f08001c
io.github.simplenote:attr/windowFixedHeightMinor = 0x7f030134
io.github.simplenote:color/ripple_material_dark = 0x7f05004f
io.github.simplenote:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
io.github.simplenote:id/search_voice_btn = 0x7f08008c
io.github.simplenote:drawable/abc_list_divider_material = 0x7f070025
io.github.simplenote:id/hide_ime_id = 0x7f08005b
io.github.simplenote:attr/voiceIcon = 0x7f03012f
io.github.simplenote:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0e014e
io.github.simplenote:id/collapseActionView = 0x7f080047
io.github.simplenote:drawable/abc_ic_ab_back_material = 0x7f070016
io.github.simplenote:attr/trackTint = 0x7f03012a
io.github.simplenote:drawable/ic_launcher_foreground = 0x7f070061
io.github.simplenote:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
io.github.simplenote:attr/tooltipFrameBackground = 0x7f030127
io.github.simplenote:attr/windowActionModeOverlay = 0x7f030132
io.github.simplenote:drawable/ic_launcher_background = 0x7f070060
io.github.simplenote:styleable/LinearLayoutCompat = 0x7f0f001d
io.github.simplenote:attr/toolbarStyle = 0x7f030125
io.github.simplenote:id/search_edit_frame = 0x7f080087
io.github.simplenote:attr/titleTextStyle = 0x7f030123
io.github.simplenote:attr/colorPrimaryDark = 0x7f03005b
io.github.simplenote:attr/listPreferredItemPaddingLeft = 0x7f0300b6
io.github.simplenote:color/abc_primary_text_material_dark = 0x7f05000b
io.github.simplenote:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
io.github.simplenote:attr/fontVariationSettings = 0x7f030093
io.github.simplenote:attr/selectableItemBackground = 0x7f0300e8
io.github.simplenote:attr/titleMarginBottom = 0x7f03011c
io.github.simplenote:color/dim_foreground_disabled_material_dark = 0x7f05002c
io.github.simplenote:layout/abc_alert_dialog_material = 0x7f0b0009
io.github.simplenote:dimen/hint_pressed_alpha_material_light = 0x7f060060
io.github.simplenote:anim/abc_slide_in_bottom = 0x7f010006
io.github.simplenote:attr/tickMarkTintMode = 0x7f030117
io.github.simplenote:color/highlighted_text_material_dark = 0x7f050034
io.github.simplenote:attr/tickMarkTint = 0x7f030116
io.github.simplenote:attr/nullable = 0x7f0300c5
io.github.simplenote:attr/textLocale = 0x7f03010f
io.github.simplenote:attr/textAppearanceSearchResultTitle = 0x7f03010b
io.github.simplenote:drawable/abc_text_cursor_material = 0x7f070046
io.github.simplenote:attr/title = 0x7f03011a
io.github.simplenote:color/primary_dark_material_dark = 0x7f050044
io.github.simplenote:color/abc_primary_text_disable_only_material_dark = 0x7f050009
io.github.simplenote:attr/textAppearanceListItem = 0x7f030106
io.github.simplenote:id/accessibility_custom_action_5 = 0x7f080022
io.github.simplenote:attr/hideOnContentScroll = 0x7f030099
io.github.simplenote:attr/actionBarTabTextStyle = 0x7f030009
io.github.simplenote:color/background_material_light = 0x7f050020
io.github.simplenote:attr/tickMark = 0x7f030115
io.github.simplenote:attr/switchPadding = 0x7f030100
io.github.simplenote:style/Base.Widget.AppCompat.ListMenuView = 0x7f0e008d
io.github.simplenote:drawable/abc_list_pressed_holo_light = 0x7f07002a
io.github.simplenote:layout/abc_dialog_title_material = 0x7f0b000c
io.github.simplenote:color/abc_secondary_text_material_dark = 0x7f050011
io.github.simplenote:attr/allowStacking = 0x7f030029
io.github.simplenote:attr/dataPattern = 0x7f030068
io.github.simplenote:attr/restoreState = 0x7f0300e2
io.github.simplenote:attr/startDestination = 0x7f0300f6
io.github.simplenote:attr/srcCompat = 0x7f0300f5
io.github.simplenote:drawable/notification_bg_low = 0x7f070065
io.github.simplenote:attr/autoSizePresetSizes = 0x7f030032
io.github.simplenote:attr/iconTint = 0x7f03009d
io.github.simplenote:attr/lStar = 0x7f0300a5
io.github.simplenote:attr/splitTrack = 0x7f0300f4
io.github.simplenote:drawable/notify_panel_notification_icon_bg = 0x7f07006f
io.github.simplenote:id/tag_compat_insets_dispatch = 0x7f0800a0
io.github.simplenote:attr/uri = 0x7f03012d
io.github.simplenote:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0e011f
io.github.simplenote:id/listMode = 0x7f080069
io.github.simplenote:styleable/MenuItem = 0x7f0f0021
io.github.simplenote:layout/abc_alert_dialog_title_material = 0x7f0b000a
io.github.simplenote:drawable/abc_btn_default_mtrl_shape = 0x7f070009
io.github.simplenote:string/abc_searchview_description_submit = 0x7f0d0016
io.github.simplenote:attr/listChoiceIndicatorSingleAnimated = 0x7f0300ac
io.github.simplenote:attr/actionModePasteDrawable = 0x7f030018
io.github.simplenote:attr/actionBarDivider = 0x7f030001
io.github.simplenote:style/TextAppearance.Compat.Notification.Info = 0x7f0e00f9
io.github.simplenote:attr/spinnerDropDownItemStyle = 0x7f0300f1
io.github.simplenote:style/Base.TextAppearance.AppCompat.Medium = 0x7f0e001b
io.github.simplenote:dimen/notification_action_text_size = 0x7f060062
io.github.simplenote:dimen/abc_seekbar_track_background_height_material = 0x7f060038
io.github.simplenote:attr/spinBars = 0x7f0300f0
io.github.simplenote:attr/popUpToSaveState = 0x7f0300d3
io.github.simplenote:id/accessibility_custom_action_14 = 0x7f08000d
io.github.simplenote:drawable/$ic_launcher_foreground__0 = 0x7f070000
io.github.simplenote:style/Base.Theme.AppCompat.CompactMenu = 0x7f0e003d
io.github.simplenote:style/Base.TextAppearance.AppCompat.Title = 0x7f0e0025
io.github.simplenote:animator/fragment_open_exit = 0x7f020005
io.github.simplenote:attr/titleTextAppearance = 0x7f030121
io.github.simplenote:dimen/abc_config_prefDialogWidth = 0x7f060017
io.github.simplenote:attr/multiChoiceItemLayout = 0x7f0300bf
io.github.simplenote:attr/singleChoiceItemLayout = 0x7f0300ef
io.github.simplenote:attr/ttcIndex = 0x7f03012c
io.github.simplenote:styleable/PopupWindow = 0x7f0f002a
io.github.simplenote:attr/showAsAction = 0x7f0300eb
io.github.simplenote:color/abc_search_url_text = 0x7f05000d
io.github.simplenote:attr/checkMarkTint = 0x7f03004a
io.github.simplenote:attr/textAppearanceListItemSmall = 0x7f030108
io.github.simplenote:style/Platform.AppCompat.Light = 0x7f0e00ae
io.github.simplenote:attr/subMenuArrow = 0x7f0300f8
io.github.simplenote:attr/actionBarPopupTheme = 0x7f030003
io.github.simplenote:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0e0082
io.github.simplenote:layout/abc_action_mode_bar = 0x7f0b0004
io.github.simplenote:attr/selectableItemBackgroundBorderless = 0x7f0300e9
io.github.simplenote:color/foreground_material_light = 0x7f050033
io.github.simplenote:attr/seekBarStyle = 0x7f0300e7
io.github.simplenote:id/customPanel = 0x7f08004d
io.github.simplenote:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0e000d
io.github.simplenote:attr/textAppearanceLargePopupMenu = 0x7f030105
io.github.simplenote:id/accessibility_custom_action_20 = 0x7f080014
io.github.simplenote:dimen/abc_list_item_height_large_material = 0x7f060030
io.github.simplenote:attr/searchViewStyle = 0x7f0300e6
io.github.simplenote:style/ThemeOverlay.AppCompat.Light = 0x7f0e0122
io.github.simplenote:style/Theme.AppCompat.DialogWhenLarge = 0x7f0e010c
io.github.simplenote:drawable/abc_list_divider_mtrl_alpha = 0x7f070026
io.github.simplenote:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0e0055
io.github.simplenote:string/indeterminate = 0x7f0d002b
io.github.simplenote:attr/buttonStyleSmall = 0x7f030046
io.github.simplenote:attr/searchHintIcon = 0x7f0300e4
io.github.simplenote:drawable/abc_textfield_activated_mtrl_alpha = 0x7f07004a
io.github.simplenote:attr/windowFixedHeightMajor = 0x7f030133
io.github.simplenote:attr/route = 0x7f0300e3
io.github.simplenote:id/list_item = 0x7f08006a
io.github.simplenote:attr/colorControlHighlight = 0x7f030057
io.github.simplenote:attr/progressBarPadding = 0x7f0300d9
io.github.simplenote:attr/dropDownListViewStyle = 0x7f03007d
io.github.simplenote:attr/radioButtonStyle = 0x7f0300de
io.github.simplenote:style/Base.Widget.AppCompat.TextView = 0x7f0e00a0
io.github.simplenote:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0e0046
io.github.simplenote:attr/queryPatterns = 0x7f0300dd
io.github.simplenote:attr/textAppearanceSmallPopupMenu = 0x7f03010c
io.github.simplenote:attr/state_above_anchor = 0x7f0300f7
io.github.simplenote:string/abc_search_hint = 0x7f0d0012
io.github.simplenote:drawable/icon_background = 0x7f070062
io.github.simplenote:attr/progressBarStyle = 0x7f0300da
io.github.simplenote:attr/goIcon = 0x7f030096
io.github.simplenote:attr/titleMargin = 0x7f03011b
io.github.simplenote:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
io.github.simplenote:attr/postSplashScreenTheme = 0x7f0300d7
io.github.simplenote:attr/popupWindowStyle = 0x7f0300d6
io.github.simplenote:style/Widget.AppCompat.Button = 0x7f0e012e
io.github.simplenote:attr/popupMenuStyle = 0x7f0300d4
io.github.simplenote:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0e00c7
io.github.simplenote:attr/popUpToInclusive = 0x7f0300d2
io.github.simplenote:attr/listItemLayout = 0x7f0300ae
io.github.simplenote:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0e00d4
io.github.simplenote:attr/popUpTo = 0x7f0300d1
io.github.simplenote:style/Widget.AppCompat.ActionBar = 0x7f0e0123
io.github.simplenote:attr/fontProviderAuthority = 0x7f03008a
io.github.simplenote:drawable/btn_checkbox_unchecked_mtrl = 0x7f070052
io.github.simplenote:attr/panelMenuListTheme = 0x7f0300cd
io.github.simplenote:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07001c
io.github.simplenote:attr/paddingStart = 0x7f0300ca
io.github.simplenote:color/material_deep_teal_200 = 0x7f050039
io.github.simplenote:style/Theme.AppCompat.Light.Dialog = 0x7f0e0110
io.github.simplenote:attr/actionModePopupWindowStyle = 0x7f030019
io.github.simplenote:attr/navigationMode = 0x7f0300c3
io.github.simplenote:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0e007c
io.github.simplenote:attr/navigationContentDescription = 0x7f0300c1
io.github.simplenote:attr/menu = 0x7f0300bd
io.github.simplenote:attr/measureWithLargestChild = 0x7f0300bc
io.github.simplenote:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0e00f4
io.github.simplenote:attr/listPreferredItemPaddingStart = 0x7f0300b8
io.github.simplenote:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
io.github.simplenote:string/m3c_date_picker_switch_to_year_selection = 0x7f0d0044
io.github.simplenote:drawable/abc_item_background_holo_dark = 0x7f070023
io.github.simplenote:attr/listPreferredItemPaddingRight = 0x7f0300b7
io.github.simplenote:attr/listPreferredItemPaddingEnd = 0x7f0300b5
io.github.simplenote:id/SHIFT = 0x7f080004
io.github.simplenote:string/m3c_date_picker_headline_description = 0x7f0d003a
io.github.simplenote:drawable/abc_btn_radio_material_anim = 0x7f07000b
io.github.simplenote:string/abc_searchview_description_query = 0x7f0d0014
io.github.simplenote:id/showHome = 0x7f080090
io.github.simplenote:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07000c
io.github.simplenote:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
io.github.simplenote:attr/listPreferredItemHeightLarge = 0x7f0300b3
io.github.simplenote:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
io.github.simplenote:attr/contentInsetEnd = 0x7f03005f
io.github.simplenote:attr/listPreferredItemHeight = 0x7f0300b2
io.github.simplenote:style/Base.ThemeOverlay.AppCompat = 0x7f0e004d
io.github.simplenote:id/text = 0x7f0800ab
io.github.simplenote:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
io.github.simplenote:attr/listPopupWindowStyle = 0x7f0300b1
io.github.simplenote:drawable/abc_list_focused_holo = 0x7f070027
io.github.simplenote:dimen/splashscreen_icon_mask_size_with_background = 0x7f060071
io.github.simplenote:attr/listLayout = 0x7f0300af
io.github.simplenote:anim/abc_slide_in_top = 0x7f010007
io.github.simplenote:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
io.github.simplenote:dimen/abc_dialog_min_width_major = 0x7f060022
io.github.simplenote:attr/nestedScrollViewStyle = 0x7f0300c4
io.github.simplenote:id/action_bar = 0x7f080027
io.github.simplenote:style/TextAppearance.Compat.Notification.Time = 0x7f0e00fb
io.github.simplenote:dimen/abc_disabled_alpha_material_light = 0x7f060028
io.github.simplenote:drawable/compat_splash_screen_no_icon_background = 0x7f070059
io.github.simplenote:attr/listChoiceIndicatorMultipleAnimated = 0x7f0300ab
io.github.simplenote:style/Base.Widget.AppCompat.RatingBar = 0x7f0e0097
io.github.simplenote:attr/lineHeight = 0x7f0300a9
io.github.simplenote:anim/abc_tooltip_enter = 0x7f01000a
io.github.simplenote:attr/paddingTopNoTitle = 0x7f0300cb
io.github.simplenote:integer/config_tooltipAnimTime = 0x7f090003
io.github.simplenote:id/unchecked = 0x7f0800b5
io.github.simplenote:anim/abc_tooltip_exit = 0x7f01000b
io.github.simplenote:attr/isLightTheme = 0x7f0300a3
io.github.simplenote:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0e013a
io.github.simplenote:id/tag_on_apply_window_listener = 0x7f0800a1
io.github.simplenote:attr/subtitleTextColor = 0x7f0300fc
io.github.simplenote:style/Widget.AppCompat.Light.ActionBar = 0x7f0e013d
io.github.simplenote:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0e00df
io.github.simplenote:id/title = 0x7f0800b0
io.github.simplenote:attr/firstBaselineToTopHeight = 0x7f030087
io.github.simplenote:attr/autoSizeTextType = 0x7f030034
io.github.simplenote:attr/initialActivityCount = 0x7f0300a2
io.github.simplenote:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0e0067
io.github.simplenote:dimen/notification_subtext_size = 0x7f06006d
io.github.simplenote:string/m3c_date_range_picker_scroll_to_next_month = 0x7f0d004c
io.github.simplenote:dimen/abc_panel_menu_list_width = 0x7f060034
io.github.simplenote:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0e0152
io.github.simplenote:attr/actionOverflowMenuStyle = 0x7f030021
io.github.simplenote:color/white = 0x7f050061
io.github.simplenote:attr/toolbarNavigationButtonStyle = 0x7f030124
io.github.simplenote:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0e0086
io.github.simplenote:attr/navGraph = 0x7f0300c0
io.github.simplenote:attr/colorAccent = 0x7f030053
io.github.simplenote:attr/autoSizeMaxTextSize = 0x7f030030
io.github.simplenote:attr/drawableEndCompat = 0x7f030074
io.github.simplenote:dimen/abc_button_padding_horizontal_material = 0x7f060014
io.github.simplenote:attr/imageButtonStyle = 0x7f0300a0
io.github.simplenote:attr/checkMarkCompat = 0x7f030049
io.github.simplenote:dimen/highlight_alpha_material_dark = 0x7f06005b
io.github.simplenote:attr/arrowHeadLength = 0x7f03002d
io.github.simplenote:attr/launchSingleTop = 0x7f0300a7
io.github.simplenote:attr/mimeType = 0x7f0300be
io.github.simplenote:style/Platform.V21.AppCompat.Light = 0x7f0e00b3
io.github.simplenote:drawable/abc_action_bar_item_background_material = 0x7f070002
io.github.simplenote:attr/thickness = 0x7f030111
io.github.simplenote:attr/actionModeFindDrawable = 0x7f030017
io.github.simplenote:style/Theme.AppCompat.DayNight.Dialog = 0x7f0e0104
io.github.simplenote:drawable/abc_tab_indicator_material = 0x7f070044
io.github.simplenote:attr/gapBetweenBars = 0x7f030095
io.github.simplenote:attr/actionModeCloseContentDescription = 0x7f030013
io.github.simplenote:string/m3c_suggestions_available = 0x7f0d0056
io.github.simplenote:layout/select_dialog_singlechoice_material = 0x7f0b0027
io.github.simplenote:attr/fontWeight = 0x7f030094
io.github.simplenote:attr/actionBarSize = 0x7f030004
io.github.simplenote:attr/checkboxStyle = 0x7f03004c
io.github.simplenote:attr/height = 0x7f030098
io.github.simplenote:attr/alertDialogTheme = 0x7f030028
io.github.simplenote:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0e015e
io.github.simplenote:attr/fontProviderFetchTimeout = 0x7f03008e
io.github.simplenote:attr/ratingBarStyle = 0x7f0300df
io.github.simplenote:style/Platform.Widget.AppCompat.Spinner = 0x7f0e00b6
io.github.simplenote:id/accessibility_custom_action_18 = 0x7f080011
io.github.simplenote:attr/switchMinWidth = 0x7f0300ff
io.github.simplenote:attr/fontProviderFetchStrategy = 0x7f03008d
io.github.simplenote:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070020
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0e0037
io.github.simplenote:color/abc_search_url_text_normal = 0x7f05000e
io.github.simplenote:attr/fontProviderFallbackQuery = 0x7f03008c
io.github.simplenote:attr/homeLayout = 0x7f03009b
io.github.simplenote:color/secondary_text_disabled_material_light = 0x7f050054
io.github.simplenote:id/textSpacerNoTitle = 0x7f0800ae
io.github.simplenote:color/tooltip_background_dark = 0x7f05005d
io.github.simplenote:attr/fontProviderCerts = 0x7f03008b
io.github.simplenote:attr/enterAnim = 0x7f030084
io.github.simplenote:string/call_notification_decline_action = 0x7f0d0020
io.github.simplenote:attr/track = 0x7f030129
io.github.simplenote:attr/textAllCaps = 0x7f030104
io.github.simplenote:color/black = 0x7f050021
io.github.simplenote:attr/closeIcon = 0x7f03004e
io.github.simplenote:string/abc_capital_off = 0x7f0d0006
io.github.simplenote:id/view_tree_view_model_store_owner = 0x7f0800bd
io.github.simplenote:attr/indeterminateProgressStyle = 0x7f0300a1
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0e002e
io.github.simplenote:attr/switchTextAppearance = 0x7f030102
io.github.simplenote:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0e00f2
io.github.simplenote:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f07000d
io.github.simplenote:style/Base.Animation.AppCompat.Tooltip = 0x7f0e0009
io.github.simplenote:attr/targetPackage = 0x7f030103
io.github.simplenote:style/TextAppearance.AppCompat.Headline = 0x7f0e00d1
io.github.simplenote:style/Base.Widget.AppCompat.ListView = 0x7f0e008f
io.github.simplenote:dimen/abc_dialog_title_divider_material = 0x7f060026
io.github.simplenote:style/TextAppearance.AppCompat.Display4 = 0x7f0e00d0
io.github.simplenote:attr/drawableTintMode = 0x7f03007a
io.github.simplenote:id/edit_query = 0x7f080052
io.github.simplenote:dimen/abc_text_size_title_material_toolbar = 0x7f060050
io.github.simplenote:attr/drawerArrowStyle = 0x7f03007c
io.github.simplenote:attr/queryHint = 0x7f0300dc
io.github.simplenote:id/tag_state_description = 0x7f0800a5
io.github.simplenote:attr/overlapAnchor = 0x7f0300c7
io.github.simplenote:attr/exitAnim = 0x7f030085
io.github.simplenote:style/TextAppearance.AppCompat.Tooltip = 0x7f0e00e4
io.github.simplenote:attr/drawableBottomCompat = 0x7f030073
io.github.simplenote:id/accessibility_custom_action_8 = 0x7f080025
io.github.simplenote:attr/contentInsetStartWithNavigation = 0x7f030064
io.github.simplenote:attr/popupTheme = 0x7f0300d5
io.github.simplenote:string/tooltip_label = 0x7f0d0075
io.github.simplenote:attr/action = 0x7f030000
io.github.simplenote:attr/actionViewClass = 0x7f030023
io.github.simplenote:attr/dividerVertical = 0x7f030072
io.github.simplenote:id/action_bar_root = 0x7f08002a
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0e002d
io.github.simplenote:attr/divider = 0x7f03006f
io.github.simplenote:styleable/AppCompatTextHelper = 0x7f0f000e
io.github.simplenote:drawable/abc_switch_track_mtrl_alpha = 0x7f070043
io.github.simplenote:attr/titleMargins = 0x7f030120
io.github.simplenote:attr/displayOptions = 0x7f03006e
io.github.simplenote:string/close_drawer = 0x7f0d0025
io.github.simplenote:id/search_src_text = 0x7f08008b
io.github.simplenote:id/icon_group = 0x7f080060
io.github.simplenote:attr/dialogTheme = 0x7f03006d
io.github.simplenote:dimen/abc_text_size_subhead_material = 0x7f06004d
io.github.simplenote:attr/buttonTintMode = 0x7f030048
io.github.simplenote:drawable/btn_checkbox_checked_mtrl = 0x7f070050
io.github.simplenote:attr/dialogCornerRadius = 0x7f03006b
io.github.simplenote:attr/drawableStartCompat = 0x7f030078
io.github.simplenote:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0e00a3
io.github.simplenote:string/m3c_date_input_label = 0x7f0d0036
io.github.simplenote:attr/destination = 0x7f03006a
io.github.simplenote:attr/fontStyle = 0x7f030092
io.github.simplenote:attr/queryBackground = 0x7f0300db
io.github.simplenote:layout/abc_activity_chooser_view = 0x7f0b0006
io.github.simplenote:attr/controlBackground = 0x7f030065
io.github.simplenote:attr/dividerHorizontal = 0x7f030070
io.github.simplenote:attr/logo = 0x7f0300b9
io.github.simplenote:attr/contentInsetStart = 0x7f030063
io.github.simplenote:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0e0057
io.github.simplenote:attr/contentInsetEndWithActions = 0x7f030060
io.github.simplenote:dimen/abc_text_size_body_2_material = 0x7f060040
io.github.simplenote:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
io.github.simplenote:string/m3c_time_picker_minute_suffix = 0x7f0d005f
io.github.simplenote:attr/actionMenuTextAppearance = 0x7f03000f
io.github.simplenote:attr/paddingBottomNoButtons = 0x7f0300c8
io.github.simplenote:string/m3c_date_range_picker_start_headline = 0x7f0d004e
io.github.simplenote:id/right_icon = 0x7f08007d
io.github.simplenote:attr/colorControlActivated = 0x7f030056
io.github.simplenote:dimen/notification_small_icon_background_padding = 0x7f06006b
io.github.simplenote:attr/colorBackgroundFloating = 0x7f030054
io.github.simplenote:attr/color = 0x7f030052
io.github.simplenote:styleable/FontFamilyFont = 0x7f0f0018
io.github.simplenote:color/primary_text_disabled_material_light = 0x7f05004b
io.github.simplenote:color/abc_hint_foreground_material_light = 0x7f050008
io.github.simplenote:attr/collapseIcon = 0x7f030051
io.github.simplenote:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0e00f3
io.github.simplenote:attr/collapseContentDescription = 0x7f030050
io.github.simplenote:attr/checkMarkTintMode = 0x7f03004b
io.github.simplenote:attr/dropdownListPreferredItemHeight = 0x7f03007e
io.github.simplenote:string/call_notification_ongoing_text = 0x7f0d0023
io.github.simplenote:color/purple_500 = 0x7f05004d
io.github.simplenote:attr/colorError = 0x7f030059
io.github.simplenote:id/tag_system_bar_state_monitor = 0x7f0800a6
io.github.simplenote:attr/textColorSearchUrl = 0x7f03010e
io.github.simplenote:id/action_container = 0x7f08002e
io.github.simplenote:styleable/RecycleListView = 0x7f0f002c
io.github.simplenote:anim/abc_slide_out_bottom = 0x7f010008
io.github.simplenote:attr/actionModeSelectAllDrawable = 0x7f03001a
io.github.simplenote:attr/fontProviderSystemFontFamily = 0x7f030091
io.github.simplenote:attr/showText = 0x7f0300ed
io.github.simplenote:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0e0011
io.github.simplenote:attr/alertDialogButtonGroupStyle = 0x7f030025
io.github.simplenote:attr/font = 0x7f030088
io.github.simplenote:attr/buttonPanelSideLayout = 0x7f030044
io.github.simplenote:attr/thumbTextPadding = 0x7f030112
io.github.simplenote:attr/buttonIconDimen = 0x7f030043
io.github.simplenote:style/Base.V21.Theme.AppCompat = 0x7f0e0054
io.github.simplenote:dimen/notification_main_column_padding_top = 0x7f060067
io.github.simplenote:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0e00b8
io.github.simplenote:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
io.github.simplenote:id/radio = 0x7f08007b
io.github.simplenote:attr/buttonGravity = 0x7f030042
io.github.simplenote:string/status_bar_notification_info_overflow = 0x7f0d0070
io.github.simplenote:drawable/notification_tile_bg = 0x7f07006e
io.github.simplenote:attr/colorButtonNormal = 0x7f030055
io.github.simplenote:attr/actionOverflowButtonStyle = 0x7f030020
io.github.simplenote:attr/buttonBarStyle = 0x7f030040
io.github.simplenote:attr/background = 0x7f030035
io.github.simplenote:color/button_material_light = 0x7f050029
io.github.simplenote:color/abc_search_url_text_pressed = 0x7f05000f
io.github.simplenote:anim/abc_grow_fade_in_from_bottom = 0x7f010002
io.github.simplenote:attr/actionBarTabBarStyle = 0x7f030007
io.github.simplenote:attr/actionModeTheme = 0x7f03001e
io.github.simplenote:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
io.github.simplenote:attr/contentInsetRight = 0x7f030062
io.github.simplenote:attr/actionBarTheme = 0x7f03000a
io.github.simplenote:attr/listPreferredItemHeightSmall = 0x7f0300b4
io.github.simplenote:attr/arrowShaftLength = 0x7f03002e
io.github.simplenote:animator/fragment_fade_exit = 0x7f020003
io.github.simplenote:id/accessibility_custom_action_29 = 0x7f08001d
io.github.simplenote:attr/preserveIconSpacing = 0x7f0300d8
io.github.simplenote:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0e0024
io.github.simplenote:attr/homeAsUpIndicator = 0x7f03009a
io.github.simplenote:style/TextAppearance.AppCompat.Body1 = 0x7f0e00c9
io.github.simplenote:attr/graph = 0x7f030097
io.github.simplenote:dimen/abc_dialog_fixed_height_major = 0x7f06001c
io.github.simplenote:attr/autoCompleteTextViewStyle = 0x7f03002f
io.github.simplenote:anim/abc_fade_out = 0x7f010001
io.github.simplenote:attr/actionBarWidgetTheme = 0x7f03000b
io.github.simplenote:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0e0074
io.github.simplenote:drawable/abc_edit_text_material = 0x7f070015
io.github.simplenote:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0e008b
io.github.simplenote:attr/alertDialogStyle = 0x7f030027
io.github.simplenote:drawable/abc_btn_borderless_material = 0x7f070003
io.github.simplenote:anim/abc_slide_out_top = 0x7f010009
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0e00e9
io.github.simplenote:id/disableHome = 0x7f080051
io.github.simplenote:attr/actionModeCloseDrawable = 0x7f030014
io.github.simplenote:attr/barLength = 0x7f03003a
io.github.simplenote:attr/actionProviderClass = 0x7f030022
io.github.simplenote:attr/buttonBarButtonStyle = 0x7f03003c
io.github.simplenote:string/m3c_time_picker_hour = 0x7f0d0058
io.github.simplenote:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
io.github.simplenote:dimen/notification_large_icon_height = 0x7f060065
io.github.simplenote:dimen/abc_action_bar_elevation_material = 0x7f060005
io.github.simplenote:dimen/notification_large_icon_width = 0x7f060066
io.github.simplenote:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0e0136
io.github.simplenote:style/Widget.AppCompat.ActionBar.TabText = 0x7f0e0126
io.github.simplenote:attr/tooltipText = 0x7f030128
io.github.simplenote:attr/switchStyle = 0x7f030101
io.github.simplenote:style/Animation.AppCompat.DropDownUp = 0x7f0e0003
io.github.simplenote:color/abc_btn_colored_borderless_text_material = 0x7f050002
io.github.simplenote:color/highlighted_text_material_light = 0x7f050035
io.github.simplenote:style/Base.Widget.AppCompat.SeekBar = 0x7f0e009c
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0e0029
io.github.simplenote:color/bright_foreground_inverse_material_dark = 0x7f050024
io.github.simplenote:attr/contentDescription = 0x7f03005e
io.github.simplenote:style/Base.V28.Theme.AppCompat = 0x7f0e0060
io.github.simplenote:attr/actionLayout = 0x7f03000e
io.github.simplenote:attr/actionModeWebSearchDrawable = 0x7f03001f
io.github.simplenote:color/bright_foreground_material_dark = 0x7f050026
io.github.simplenote:string/m3c_date_input_title = 0x7f0d0038
io.github.simplenote:color/notification_action_color_filter = 0x7f050042
io.github.simplenote:style/Theme.SplashScreen = 0x7f0e0117
io.github.simplenote:attr/borderlessButtonStyle = 0x7f03003b
io.github.simplenote:id/async = 0x7f08003e
io.github.simplenote:style/TextAppearance.AppCompat.Large = 0x7f0e00d3
io.github.simplenote:drawable/abc_popup_background_mtrl_mult = 0x7f070032
io.github.simplenote:attr/actionModeSplitBackground = 0x7f03001c
io.github.simplenote:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070031
io.github.simplenote:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f07000f
io.github.simplenote:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0e0139
io.github.simplenote:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0e0105
io.github.simplenote:attr/autoSizeStepGranularity = 0x7f030033
io.github.simplenote:style/Widget.AppCompat.ActivityChooserView = 0x7f0e012c
io.github.simplenote:drawable/abc_spinner_textfield_background_material = 0x7f07003f
io.github.simplenote:layout/custom_dialog = 0x7f0b001c
io.github.simplenote:color/secondary_text_default_material_dark = 0x7f050051
io.github.simplenote:attr/actionModeBackground = 0x7f030011
io.github.simplenote:attr/navigationIcon = 0x7f0300c2
io.github.simplenote:attr/drawableRightCompat = 0x7f030076
io.github.simplenote:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
io.github.simplenote:dimen/splashscreen_icon_size_with_background = 0x7f060076
io.github.simplenote:attr/trackTintMode = 0x7f03012b
io.github.simplenote:attr/listMenuViewStyle = 0x7f0300b0
io.github.simplenote:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
io.github.simplenote:attr/buttonBarPositiveButtonStyle = 0x7f03003f
io.github.simplenote:style/Base.Widget.AppCompat.ActionButton = 0x7f0e006f
io.github.simplenote:attr/actionBarSplitStyle = 0x7f030005
io.github.simplenote:anim/abc_popup_enter = 0x7f010003
io.github.simplenote:dimen/abc_text_size_display_3_material = 0x7f060045
io.github.simplenote:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
io.github.simplenote:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0e00e7
io.github.simplenote:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0e0016
io.github.simplenote:id/tag_unhandled_key_event_manager = 0x7f0800a8
io.github.simplenote:dimen/abc_floating_window_z = 0x7f06002f
io.github.simplenote:attr/ratingBarStyleSmall = 0x7f0300e1
io.github.simplenote:style/Widget.Compat.NotificationActionContainer = 0x7f0e016c
io.github.simplenote:id/accessibility_custom_action_21 = 0x7f080015
io.github.simplenote:color/abc_tint_edittext = 0x7f050015
io.github.simplenote:style/Platform.AppCompat = 0x7f0e00ad
io.github.simplenote:attr/editTextStyle = 0x7f030081
io.github.simplenote:id/CTRL = 0x7f080001
io.github.simplenote:animator/fragment_open_enter = 0x7f020004
io.github.simplenote:attr/actionMenuTextColor = 0x7f030010
io.github.simplenote:drawable/abc_control_background_material = 0x7f070013
io.github.simplenote:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0e0150
io.github.simplenote:id/text2 = 0x7f0800ac
io.github.simplenote:attr/drawableTint = 0x7f030079
io.github.simplenote:animator/fragment_fade_enter = 0x7f020002
io.github.simplenote:attr/checkedTextViewStyle = 0x7f03004d
io.github.simplenote:color/bright_foreground_disabled_material_dark = 0x7f050022
io.github.simplenote:style/Theme.SplashScreen.Common = 0x7f0e0118
io.github.simplenote:attr/textColorAlertDialogListItem = 0x7f03010d
io.github.simplenote:anim/fragment_fast_out_extra_slow_in = 0x7f010018
io.github.simplenote:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0e00f5
io.github.simplenote:id/none = 0x7f080070
io.github.simplenote:attr/buttonBarNeutralButtonStyle = 0x7f03003e
io.github.simplenote:attr/backgroundSplit = 0x7f030036
io.github.simplenote:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f07004c
io.github.simplenote:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
io.github.simplenote:attr/spinnerStyle = 0x7f0300f2
io.github.simplenote:id/home = 0x7f08005d
io.github.simplenote:string/abc_shareactionprovider_share_with = 0x7f0d0018
io.github.simplenote:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
io.github.simplenote:drawable/ic_call_answer_low = 0x7f07005b
io.github.simplenote:attr/actionDropDownStyle = 0x7f03000d
io.github.simplenote:styleable/View = 0x7f0f0034
io.github.simplenote:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0e00f1
io.github.simplenote:attr/suggestionRowLayout = 0x7f0300fe
io.github.simplenote:id/inspection_slot_table_set = 0x7f080064
io.github.simplenote:style/TextAppearance.AppCompat.Inverse = 0x7f0e00d2
io.github.simplenote:attr/colorSwitchThumbNormal = 0x7f03005c
io.github.simplenote:attr/searchIcon = 0x7f0300e5
io.github.simplenote:color/abc_tint_spinner = 0x7f050017
io.github.simplenote:style/Theme.SimpleNOTE = 0x7f0e0116
io.github.simplenote:drawable/abc_ic_voice_search_api_material = 0x7f070022
io.github.simplenote:attr/commitIcon = 0x7f03005d
io.github.simplenote:color/material_grey_100 = 0x7f05003b
io.github.simplenote:attr/data = 0x7f030067
io.github.simplenote:id/hide_graphics_layer_in_inspector_tag = 0x7f08005a
io.github.simplenote:id/search_close_btn = 0x7f080086
io.github.simplenote:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070019
io.github.simplenote:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
io.github.simplenote:attr/backgroundStacked = 0x7f030037
io.github.simplenote:attr/buttonCompat = 0x7f030041
io.github.simplenote:attr/fontProviderQuery = 0x7f030090
io.github.simplenote:attr/colorPrimary = 0x7f03005a
io.github.simplenote:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0e0030
io.github.simplenote:id/titleDividerNoCustom = 0x7f0800b1
io.github.simplenote:id/info = 0x7f080063
io.github.simplenote:color/abc_btn_colored_text_material = 0x7f050003
io.github.simplenote:string/abc_menu_ctrl_shortcut_label = 0x7f0d0009
io.github.simplenote:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
io.github.simplenote:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0e0041
io.github.simplenote:string/m3c_date_input_invalid_not_allowed = 0x7f0d0034
io.github.simplenote:layout/abc_select_dialog_material = 0x7f0b001a
io.github.simplenote:id/accessibility_custom_action_12 = 0x7f08000b
io.github.simplenote:style/Widget.AppCompat.ListView.Menu = 0x7f0e0157
io.github.simplenote:string/navigation_menu = 0x7f0d0066
io.github.simplenote:attr/titleMarginStart = 0x7f03011e
io.github.simplenote:attr/submitBackground = 0x7f0300f9
