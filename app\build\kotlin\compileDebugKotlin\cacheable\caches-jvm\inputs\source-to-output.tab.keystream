   X a p p / b u i l d / g e n e r a t e d / k s p / d e b u g / k o t l i n / i o / g i t h u b / s i m p l e n o t e / d a t a / l o c a l / d a o / N o t e D a o _ I m p l . k t   b a p p / b u i l d / g e n e r a t e d / k s p / d e b u g / k o t l i n / i o / g i t h u b / s i m p l e n o t e / d a t a / l o c a l / d a t a b a s e / N o t e D a t a b a s e _ I m p l . k t   6 a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / M a i n A c t i v i t y . k t   ? a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / S i m p l e N o t e A p p l i c a t i o n . k t   @ a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d a t a / l o c a l / d a o / N o t e D a o . k t   J a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d a t a / l o c a l / d a t a b a s e / N o t e D a t a b a s e . k t   F a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d a t a / l o c a l / e n t i t y / N o t e E n t i t y . k t   K a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d a t a / p r e f e r e n c e s / T h e m e P r e f e r e n c e s . k t   L a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d a t a / r e p o s i t o r y / N o t e R e p o s i t o r y I m p l . k t   G a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d a t a / u t i l / S a m p l e D a t a G e n e r a t o r . k t   6 a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d i / A p p M o d u l e . k t   ; a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d i / D a t a b a s e M o d u l e . k t   > a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d i / P r e f e r e n c e s M o d u l e . k t   = a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d i / R e p o s i t o r y M o d u l e . k t   : a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d i / U s e C a s e M o d u l e . k t   < a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d i / V i e w M o d e l M o d u l e . k t   ; a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / m o d e l / N o t e . k t   @ a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / m o d e l / N o t e C o l o r . k t   @ a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / m o d e l / N o t e E r r o r . k t   @ a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / m o d e l / T h e m e M o d e . k t   D a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / m o d e l / T h e m e S e t t i n g s . k t   J a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / r e p o s i t o r y / N o t e R e p o s i t o r y . k t   J a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / u s e c a s e / C r e a t e N o t e U s e C a s e . k t   J a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / u s e c a s e / D e l e t e N o t e U s e C a s e . k t   H a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / u s e c a s e / G e t N o t e s U s e C a s e . k t   P a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / u s e c a s e / G e t T h e m e S e t t i n g s U s e C a s e . k t   R a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / u s e c a s e / P o p u l a t e S a m p l e D a t a U s e C a s e . k t   J a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / u s e c a s e / U p d a t e N o t e U s e C a s e . k t   S a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / d o m a i n / u s e c a s e / U p d a t e T h e m e S e t t i n g s U s e C a s e . k t   C a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / n a v i g a t i o n / A p p D e s t i n a t i o n . k t   B a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / n a v i g a t i o n / A p p N a v i g a t i o n . k t   R a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / C o l o r B o t t o m S h e e t . k t   N a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / C o l o r P a l e t t e . k t   Z a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / D e l e t e C o n f i r m a t i o n D i a l o g . k t   L a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / E m p t y S t a t e . k t   S a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / E n h a n c e d S e a r c h B a r . k t   L a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / E r r o r S t a t e . k t   U a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / I n t e g r a t e d S e a r c h B a r . k t   N a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / L o a d i n g S t a t e . k t   J a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / N o t e I t e m . k t   [ a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / c o m p o n e n t s / S c r o l l a b l e S e a r c h B a r L a y o u t . k t   D a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / m o d e l / U i S t a t e . k t   N a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / s c r e e n / N o t e E d i t o r S c r e e n . k t   I a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / s c r e e n / N o t e s S c r e e n . k t   L a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / s c r e e n / S e t t i n g s S c r e e n . k t   T a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / v i e w m o d e l / N o t e E d i t o r V i e w M o d e l . k t   O a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / v i e w m o d e l / N o t e s V i e w M o d e l . k t   R a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / p r e s e n t a t i o n / v i e w m o d e l / S e t t i n g s V i e w M o d e l . k t   8 a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / u i / t h e m e / C o l o r . k t   8 a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / u i / t h e m e / T h e m e . k t   7 a p p / s r c / m a i n / j a v a / i o / g i t h u b / s i m p l e n o t e / u i / t h e m e / T y p e . k t                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    