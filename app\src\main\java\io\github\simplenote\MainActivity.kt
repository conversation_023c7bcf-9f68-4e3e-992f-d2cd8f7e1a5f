package io.github.simplenote

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import io.github.simplenote.domain.usecase.GetThemeSettingsUseCase
import io.github.simplenote.domain.usecase.PopulateSampleDataUseCase
import io.github.simplenote.navigation.AppNavigation
import io.github.simplenote.ui.theme.SimpleNOTETheme
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject

/**
 * Main activity for the SimpleNote application.
 */
class MainActivity : ComponentActivity() {

    private val getThemeSettingsUseCase: GetThemeSettingsUseCase by inject()
    private val populateSampleDataUseCase: PopulateSampleDataUseCase by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        // Populate sample data on first launch
        lifecycleScope.launch {
            populateSampleDataUseCase()
        }

        setContent {
            val themeSettings by getThemeSettingsUseCase().collectAsStateWithLifecycle(
                initialValue = io.github.simplenote.domain.model.ThemeSettings.DEFAULT
            )

            SimpleNOTETheme(
                themeMode = themeSettings.themeMode,
                useDynamicTheme = themeSettings.useDynamicTheme
            ) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    AppNavigation()
                }
            }
        }
    }
}