[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 46, "crc": 1570773839}, {"key": "org/jetbrains/annotations/BlockingExecutor.class", "name": "org/jetbrains/annotations/BlockingExecutor.class", "size": 463, "crc": -1238224193}, {"key": "org/jetbrains/annotations/NonBlockingExecutor.class", "name": "org/jetbrains/annotations/NonBlockingExecutor.class", "size": 469, "crc": -275818841}, {"key": "org/jetbrains/annotations/Range.class", "name": "org/jetbrains/annotations/Range.class", "size": 463, "crc": -728701735}, {"key": "org/jetbrains/annotations/UnknownNullability.class", "name": "org/jetbrains/annotations/UnknownNullability.class", "size": 645, "crc": 742646883}, {"key": "org/jetbrains/annotations/Unmodifiable.class", "name": "org/jetbrains/annotations/Unmodifiable.class", "size": 443, "crc": 384520228}, {"key": "org/jetbrains/annotations/UnmodifiableView.class", "name": "org/jetbrains/annotations/UnmodifiableView.class", "size": 451, "crc": 1994218283}, {"key": "org/intellij/lang/annotations/Flow.class", "name": "org/intellij/lang/annotations/Flow.class", "size": 1050, "crc": 1525873546}, {"key": "org/intellij/lang/annotations/Identifier.class", "name": "org/intellij/lang/annotations/Identifier.class", "size": 324, "crc": -2033986748}, {"key": "org/intellij/lang/annotations/JdkConstants$AdjustableOrientation.class", "name": "org/intellij/lang/annotations/JdkConstants$AdjustableOrientation.class", "size": 299, "crc": 1887720386}, {"key": "org/intellij/lang/annotations/JdkConstants$BoxLayoutAxis.class", "name": "org/intellij/lang/annotations/JdkConstants$BoxLayoutAxis.class", "size": 283, "crc": -*********}, {"key": "org/intellij/lang/annotations/JdkConstants$CalendarMonth.class", "name": "org/intellij/lang/annotations/JdkConstants$CalendarMonth.class", "size": 283, "crc": *********}, {"key": "org/intellij/lang/annotations/JdkConstants$CursorType.class", "name": "org/intellij/lang/annotations/JdkConstants$CursorType.class", "size": 277, "crc": 22053300}, {"key": "org/intellij/lang/annotations/JdkConstants$FlowLayoutAlignment.class", "name": "org/intellij/lang/annotations/JdkConstants$FlowLayoutAlignment.class", "size": 295, "crc": *********}, {"key": "org/intellij/lang/annotations/JdkConstants$FontStyle.class", "name": "org/intellij/lang/annotations/JdkConstants$FontStyle.class", "size": 275, "crc": -*********}, {"key": "org/intellij/lang/annotations/JdkConstants$HorizontalAlignment.class", "name": "org/intellij/lang/annotations/JdkConstants$HorizontalAlignment.class", "size": 295, "crc": *********}, {"key": "org/intellij/lang/annotations/JdkConstants$HorizontalScrollBarPolicy.class", "name": "org/intellij/lang/annotations/JdkConstants$HorizontalScrollBarPolicy.class", "size": 307, "crc": -713533423}, {"key": "org/intellij/lang/annotations/JdkConstants$InputEventMask.class", "name": "org/intellij/lang/annotations/JdkConstants$InputEventMask.class", "size": 285, "crc": 1592711045}, {"key": "org/intellij/lang/annotations/JdkConstants$ListSelectionMode.class", "name": "org/intellij/lang/annotations/JdkConstants$ListSelectionMode.class", "size": 291, "crc": 618604480}, {"key": "org/intellij/lang/annotations/JdkConstants$PatternFlags.class", "name": "org/intellij/lang/annotations/JdkConstants$PatternFlags.class", "size": 281, "crc": -1800219836}, {"key": "org/intellij/lang/annotations/JdkConstants$TabLayoutPolicy.class", "name": "org/intellij/lang/annotations/JdkConstants$TabLayoutPolicy.class", "size": 287, "crc": -1210053674}, {"key": "org/intellij/lang/annotations/JdkConstants$TabPlacement.class", "name": "org/intellij/lang/annotations/JdkConstants$TabPlacement.class", "size": 281, "crc": -894472644}, {"key": "org/intellij/lang/annotations/JdkConstants$TitledBorderJustification.class", "name": "org/intellij/lang/annotations/JdkConstants$TitledBorderJustification.class", "size": 307, "crc": -1816001121}, {"key": "org/intellij/lang/annotations/JdkConstants$TitledBorderTitlePosition.class", "name": "org/intellij/lang/annotations/JdkConstants$TitledBorderTitlePosition.class", "size": 307, "crc": -516592885}, {"key": "org/intellij/lang/annotations/JdkConstants$TreeSelectionMode.class", "name": "org/intellij/lang/annotations/JdkConstants$TreeSelectionMode.class", "size": 291, "crc": -1293767413}, {"key": "org/intellij/lang/annotations/JdkConstants$VerticalScrollBarPolicy.class", "name": "org/intellij/lang/annotations/JdkConstants$VerticalScrollBarPolicy.class", "size": 303, "crc": -2033166827}, {"key": "org/intellij/lang/annotations/JdkConstants.class", "name": "org/intellij/lang/annotations/JdkConstants.class", "size": 2148, "crc": 1756827512}, {"key": "org/intellij/lang/annotations/Language.class", "name": "org/intellij/lang/annotations/Language.class", "size": 757, "crc": -390120454}, {"key": "org/intellij/lang/annotations/MagicConstant.class", "name": "org/intellij/lang/annotations/MagicConstant.class", "size": 897, "crc": 2104194554}, {"key": "org/intellij/lang/annotations/Pattern.class", "name": "org/intellij/lang/annotations/Pattern.class", "size": 689, "crc": 2068574124}, {"key": "org/intellij/lang/annotations/PrintFormat.class", "name": "org/intellij/lang/annotations/PrintFormat.class", "size": 356, "crc": 228927008}, {"key": "org/intellij/lang/annotations/PrintFormatPattern.class", "name": "org/intellij/lang/annotations/PrintFormatPattern.class", "size": 964, "crc": -111669936}, {"key": "org/intellij/lang/annotations/RegExp.class", "name": "org/intellij/lang/annotations/RegExp.class", "size": 788, "crc": -612763741}, {"key": "org/intellij/lang/annotations/Subst.class", "name": "org/intellij/lang/annotations/Subst.class", "size": 474, "crc": 321278805}, {"key": "org/jetbrains/annotations/ApiStatus$AvailableSince.class", "name": "org/jetbrains/annotations/ApiStatus$AvailableSince.class", "size": 652, "crc": -1656312619}, {"key": "org/jetbrains/annotations/ApiStatus$Experimental.class", "name": "org/jetbrains/annotations/ApiStatus$Experimental.class", "size": 617, "crc": 2110460216}, {"key": "org/jetbrains/annotations/ApiStatus$Internal.class", "name": "org/jetbrains/annotations/ApiStatus$Internal.class", "size": 609, "crc": 1879671044}, {"key": "org/jetbrains/annotations/ApiStatus$NonExtendable.class", "name": "org/jetbrains/annotations/ApiStatus$NonExtendable.class", "size": 549, "crc": -1635238692}, {"key": "org/jetbrains/annotations/ApiStatus$OverrideOnly.class", "name": "org/jetbrains/annotations/ApiStatus$OverrideOnly.class", "size": 547, "crc": 889285194}, {"key": "org/jetbrains/annotations/ApiStatus$ScheduledForRemoval.class", "name": "org/jetbrains/annotations/ApiStatus$ScheduledForRemoval.class", "size": 706, "crc": 1449314641}, {"key": "org/jetbrains/annotations/ApiStatus.class", "name": "org/jetbrains/annotations/ApiStatus.class", "size": 920, "crc": 1466737865}, {"key": "org/jetbrains/annotations/Async$Execute.class", "name": "org/jetbrains/annotations/Async$Execute.class", "size": 509, "crc": -2054696282}, {"key": "org/jetbrains/annotations/Async$Schedule.class", "name": "org/jetbrains/annotations/Async$Schedule.class", "size": 511, "crc": 1383473229}, {"key": "org/jetbrains/annotations/Async.class", "name": "org/jetbrains/annotations/Async.class", "size": 558, "crc": -299370084}, {"key": "org/jetbrains/annotations/Blocking.class", "name": "org/jetbrains/annotations/Blocking.class", "size": 464, "crc": 2112426468}, {"key": "org/jetbrains/annotations/Contract.class", "name": "org/jetbrains/annotations/Contract.class", "size": 730, "crc": 2047825489}, {"key": "org/jetbrains/annotations/Debug$Renderer.class", "name": "org/jetbrains/annotations/Debug$Renderer.class", "size": 1056, "crc": 487398394}, {"key": "org/jetbrains/annotations/Debug.class", "name": "org/jetbrains/annotations/Debug.class", "size": 495, "crc": -99688080}, {"key": "org/jetbrains/annotations/MustBeInvokedByOverriders.class", "name": "org/jetbrains/annotations/MustBeInvokedByOverriders.class", "size": 467, "crc": 31913704}, {"key": "org/jetbrains/annotations/Nls$Capitalization.class", "name": "org/jetbrains/annotations/Nls$Capitalization.class", "size": 1220, "crc": -508093}, {"key": "org/jetbrains/annotations/Nls.class", "name": "org/jetbrains/annotations/Nls.class", "size": 787, "crc": -167509303}, {"key": "org/jetbrains/annotations/NonBlocking.class", "name": "org/jetbrains/annotations/NonBlocking.class", "size": 470, "crc": 932019776}, {"key": "org/jetbrains/annotations/NonNls.class", "name": "org/jetbrains/annotations/NonNls.class", "size": 524, "crc": 1751553609}, {"key": "org/jetbrains/annotations/NotNull.class", "name": "org/jetbrains/annotations/NotNull.class", "size": 703, "crc": -528523151}, {"key": "org/jetbrains/annotations/Nullable.class", "name": "org/jetbrains/annotations/Nullable.class", "size": 691, "crc": -1186372330}, {"key": "org/jetbrains/annotations/PropertyKey.class", "name": "org/jetbrains/annotations/PropertyKey.class", "size": 668, "crc": -1926574107}, {"key": "org/jetbrains/annotations/TestOnly.class", "name": "org/jetbrains/annotations/TestOnly.class", "size": 477, "crc": -1022613195}, {"key": "org/jetbrains/annotations/VisibleForTesting.class", "name": "org/jetbrains/annotations/VisibleForTesting.class", "size": 495, "crc": -927995176}, {"key": "META-INF/versions/9/module-info.class", "name": "META-INF/versions/9/module-info.class", "size": 276, "crc": -1744023431}]