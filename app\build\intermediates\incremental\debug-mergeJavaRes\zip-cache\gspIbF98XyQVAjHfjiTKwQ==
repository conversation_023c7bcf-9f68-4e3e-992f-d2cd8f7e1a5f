[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$1.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$1.class", "size": 276, "crc": -940893964}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$AtomicHelper.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$AtomicHelper.class", "size": 2208, "crc": 1918496937}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$Cancellation.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$Cancellation.class", "size": 1041, "crc": 886782754}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$Failure$1.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$Failure$1.class", "size": 737, "crc": -471209672}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$Failure.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$Failure.class", "size": 951, "crc": -1760206701}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$Listener.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$Listener.class", "size": 873, "crc": 402316535}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$SafeAtomicHelper.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$SafeAtomicHelper.class", "size": 5564, "crc": -1818562924}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$SetFuture.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$SetFuture.class", "size": 2033, "crc": 286080067}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$SynchronizedHelper.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$SynchronizedHelper.class", "size": 3421, "crc": -1182687467}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture$Waiter.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture$Waiter.class", "size": 1723, "crc": 226188834}, {"key": "androidx/concurrent/futures/AbstractResolvableFuture.class", "name": "androidx/concurrent/futures/AbstractResolvableFuture.class", "size": 18591, "crc": -370636094}, {"key": "androidx/concurrent/futures/CallbackToFutureAdapter$Completer.class", "name": "androidx/concurrent/futures/CallbackToFutureAdapter$Completer.class", "size": 3784, "crc": 2035088105}, {"key": "androidx/concurrent/futures/CallbackToFutureAdapter$FutureGarbageCollectedException.class", "name": "androidx/concurrent/futures/CallbackToFutureAdapter$FutureGarbageCollectedException.class", "size": 706, "crc": -1609752935}, {"key": "androidx/concurrent/futures/CallbackToFutureAdapter$Resolver.class", "name": "androidx/concurrent/futures/CallbackToFutureAdapter$Resolver.class", "size": 831, "crc": 142665041}, {"key": "androidx/concurrent/futures/CallbackToFutureAdapter$SafeFuture$1.class", "name": "androidx/concurrent/futures/CallbackToFutureAdapter$SafeFuture$1.class", "size": 1829, "crc": 555402729}, {"key": "androidx/concurrent/futures/CallbackToFutureAdapter$SafeFuture.class", "name": "androidx/concurrent/futures/CallbackToFutureAdapter$SafeFuture.class", "size": 3934, "crc": 1212043617}, {"key": "androidx/concurrent/futures/CallbackToFutureAdapter.class", "name": "androidx/concurrent/futures/CallbackToFutureAdapter.class", "size": 2370, "crc": 1014408157}, {"key": "androidx/concurrent/futures/DirectExecutor.class", "name": "androidx/concurrent/futures/DirectExecutor.class", "size": 1661, "crc": -2104738324}, {"key": "androidx/concurrent/futures/ResolvableFuture.class", "name": "androidx/concurrent/futures/ResolvableFuture.class", "size": 1867, "crc": -254057077}]