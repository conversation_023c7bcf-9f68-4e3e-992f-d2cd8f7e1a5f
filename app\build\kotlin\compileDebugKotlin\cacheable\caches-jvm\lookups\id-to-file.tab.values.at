/ Header Record For PersistentHashMapValueStorageY Xapp/build/generated/ksp/debug/kotlin/io/github/simplenote/data/local/dao/NoteDao_Impl.ktc bapp/build/generated/ksp/debug/kotlin/io/github/simplenote/data/local/database/NoteDatabase_Impl.kt7 6app/src/main/java/io/github/simplenote/MainActivity.kt@ ?app/src/main/java/io/github/simplenote/SimpleNoteApplication.ktA @app/src/main/java/io/github/simplenote/data/local/dao/NoteDao.ktK Japp/src/main/java/io/github/simplenote/data/local/database/NoteDatabase.ktG Fapp/src/main/java/io/github/simplenote/data/local/entity/NoteEntity.ktL Kapp/src/main/java/io/github/simplenote/data/preferences/ThemePreferences.ktM Lapp/src/main/java/io/github/simplenote/data/repository/NoteRepositoryImpl.ktH Gapp/src/main/java/io/github/simplenote/data/util/SampleDataGenerator.kt7 6app/src/main/java/io/github/simplenote/di/AppModule.kt< ;app/src/main/java/io/github/simplenote/di/DatabaseModule.kt? >app/src/main/java/io/github/simplenote/di/PreferencesModule.kt> =app/src/main/java/io/github/simplenote/di/RepositoryModule.kt; :app/src/main/java/io/github/simplenote/di/UseCaseModule.kt= <app/src/main/java/io/github/simplenote/di/ViewModelModule.kt< ;app/src/main/java/io/github/simplenote/domain/model/Note.ktA @app/src/main/java/io/github/simplenote/domain/model/NoteColor.ktA @app/src/main/java/io/github/simplenote/domain/model/NoteError.ktA @app/src/main/java/io/github/simplenote/domain/model/ThemeMode.ktE Dapp/src/main/java/io/github/simplenote/domain/model/ThemeSettings.ktK Japp/src/main/java/io/github/simplenote/domain/repository/NoteRepository.ktK Japp/src/main/java/io/github/simplenote/domain/usecase/CreateNoteUseCase.ktK Japp/src/main/java/io/github/simplenote/domain/usecase/DeleteNoteUseCase.ktI Happ/src/main/java/io/github/simplenote/domain/usecase/GetNotesUseCase.ktQ Papp/src/main/java/io/github/simplenote/domain/usecase/GetThemeSettingsUseCase.ktS Rapp/src/main/java/io/github/simplenote/domain/usecase/PopulateSampleDataUseCase.ktK Japp/src/main/java/io/github/simplenote/domain/usecase/UpdateNoteUseCase.ktT Sapp/src/main/java/io/github/simplenote/domain/usecase/UpdateThemeSettingsUseCase.ktD Capp/src/main/java/io/github/simplenote/navigation/AppDestination.ktC Bapp/src/main/java/io/github/simplenote/navigation/AppNavigation.ktS Rapp/src/main/java/io/github/simplenote/presentation/components/ColorBottomSheet.ktO Napp/src/main/java/io/github/simplenote/presentation/components/ColorPalette.kt[ Zapp/src/main/java/io/github/simplenote/presentation/components/DeleteConfirmationDialog.ktM Lapp/src/main/java/io/github/simplenote/presentation/components/EmptyState.ktM Lapp/src/main/java/io/github/simplenote/presentation/components/ErrorState.ktV Uapp/src/main/java/io/github/simplenote/presentation/components/GoogleKeepSearchBar.ktO Napp/src/main/java/io/github/simplenote/presentation/components/LoadingState.ktK Japp/src/main/java/io/github/simplenote/presentation/components/NoteItem.ktE Dapp/src/main/java/io/github/simplenote/presentation/model/UiState.ktO Napp/src/main/java/io/github/simplenote/presentation/screen/NoteEditorScreen.ktJ Iapp/src/main/java/io/github/simplenote/presentation/screen/NotesScreen.ktM Lapp/src/main/java/io/github/simplenote/presentation/screen/SettingsScreen.ktU Tapp/src/main/java/io/github/simplenote/presentation/viewmodel/NoteEditorViewModel.ktP Oapp/src/main/java/io/github/simplenote/presentation/viewmodel/NotesViewModel.ktS Rapp/src/main/java/io/github/simplenote/presentation/viewmodel/SettingsViewModel.kt9 8app/src/main/java/io/github/simplenote/ui/theme/Color.kt9 8app/src/main/java/io/github/simplenote/ui/theme/Theme.kt8 7app/src/main/java/io/github/simplenote/ui/theme/Type.ktO Napp/src/main/java/io/github/simplenote/presentation/components/ColorPalette.kt