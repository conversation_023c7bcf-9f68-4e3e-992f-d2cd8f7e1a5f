[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/stately-concurrency.kotlin_module", "name": "META-INF/stately-concurrency.kotlin_module", "size": 238, "crc": 944343538}, {"key": "co/touchlab/stately/concurrency/AtomicBoolean.class", "name": "co/touchlab/stately/concurrency/AtomicBoolean.class", "size": 1713, "crc": 1607369446}, {"key": "co/touchlab/stately/concurrency/AtomicIntActual.class", "name": "co/touchlab/stately/concurrency/AtomicIntActual.class", "size": 498, "crc": -860371526}, {"key": "co/touchlab/stately/concurrency/AtomicIntKt.class", "name": "co/touchlab/stately/concurrency/AtomicIntKt.class", "size": 1163, "crc": -516255384}, {"key": "co/touchlab/stately/concurrency/AtomicLongActual.class", "name": "co/touchlab/stately/concurrency/AtomicLongActual.class", "size": 499, "crc": -1295400541}, {"key": "co/touchlab/stately/concurrency/AtomicLongKt.class", "name": "co/touchlab/stately/concurrency/AtomicLongKt.class", "size": 1154, "crc": 2020164389}, {"key": "co/touchlab/stately/concurrency/AtomicReferenceActual.class", "name": "co/touchlab/stately/concurrency/AtomicReferenceActual.class", "size": 551, "crc": -1808929256}, {"key": "co/touchlab/stately/concurrency/AtomicReferenceKt.class", "name": "co/touchlab/stately/concurrency/AtomicReferenceKt.class", "size": 1481, "crc": 1857375131}, {"key": "co/touchlab/stately/concurrency/LockIntActual.class", "name": "co/touchlab/stately/concurrency/LockIntActual.class", "size": 987, "crc": -1911140698}, {"key": "co/touchlab/stately/concurrency/LockKt.class", "name": "co/touchlab/stately/concurrency/LockKt.class", "size": 1601, "crc": 1169530374}, {"key": "co/touchlab/stately/concurrency/SynchronizableKt.class", "name": "co/touchlab/stately/concurrency/SynchronizableKt.class", "size": 1531, "crc": -1811537095}, {"key": "co/touchlab/stately/concurrency/ThreadLocalJVMKt.class", "name": "co/touchlab/stately/concurrency/ThreadLocalJVMKt.class", "size": 421, "crc": 1540179176}, {"key": "co/touchlab/stately/concurrency/ThreadLocalKt.class", "name": "co/touchlab/stately/concurrency/ThreadLocalKt.class", "size": 1429, "crc": -205128587}, {"key": "co/touchlab/stately/concurrency/ThreadRef.class", "name": "co/touchlab/stately/concurrency/ThreadRef.class", "size": 846, "crc": 2119410225}]