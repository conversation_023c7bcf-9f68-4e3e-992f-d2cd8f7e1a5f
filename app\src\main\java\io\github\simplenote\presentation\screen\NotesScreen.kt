package io.github.simplenote.presentation.screen

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars

import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.foundation.lazy.staggeredgrid.items
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import io.github.simplenote.domain.model.Note
import io.github.simplenote.presentation.components.DeleteConfirmationDialog
import io.github.simplenote.presentation.components.EmptyState
import io.github.simplenote.presentation.components.IntegratedSearchBar
import io.github.simplenote.presentation.components.NoteItem

import io.github.simplenote.presentation.model.NavigationEvent
import io.github.simplenote.presentation.model.NotesUiEvent
import io.github.simplenote.presentation.viewmodel.NotesViewModel
import io.github.simplenote.ui.theme.SimpleNOTETheme
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel

/**
 * Main notes screen displaying the list of notes with search and color selection.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotesScreen(
    modifier: Modifier = Modifier,
    onNavigateToEditor: (Long?) -> Unit,
    onNavigateToSettings: () -> Unit,
    viewModel: NotesViewModel = koinViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val snackbarHostState = remember { SnackbarHostState() }
    var noteToDelete by remember { mutableStateOf<Note?>(null) }
    var searchQuery by remember { mutableStateOf("") }
    var debouncedSearchQuery by remember { mutableStateOf("") }

    // Handle navigation events
    LaunchedEffect(viewModel.navigationEvents) {
        viewModel.navigationEvents.collect { event ->
            when (event) {
                is NavigationEvent.NavigateToNoteEditor -> {
                    onNavigateToEditor(event.noteId)
                }

                is NavigationEvent.ShowDeleteConfirmation -> {
                    noteToDelete = event.note
                }

                is NavigationEvent.ShowError -> {
                    snackbarHostState.showSnackbar(event.error.message)
                }

                else -> {}
            }
        }
    }

    // Handle errors
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(error.message)
            viewModel.onEvent(NotesUiEvent.ClearError)
        }
    }

    // Debounce search query
    LaunchedEffect(searchQuery) {
        delay(400) // 400ms debounce
        debouncedSearchQuery = searchQuery
    }

    // Trigger search when debounced query changes
    LaunchedEffect(debouncedSearchQuery) {
        viewModel.onEvent(NotesUiEvent.SearchNotes(debouncedSearchQuery))
    }

    val gridState = rememberLazyStaggeredGridState()

    Scaffold(
        modifier = modifier.fillMaxSize(),
        containerColor = androidx.compose.ui.graphics.Color.Transparent,
        floatingActionButton = {
            FloatingActionButton(
                modifier = Modifier.padding(
                    bottom = WindowInsets.navigationBars.asPaddingValues()
                        .calculateBottomPadding() +
                            16.dp, // Add extra spacing if needed
                    end = 12.dp
                ),
                onClick = {
                    viewModel.onEvent(NotesUiEvent.CreateNewNote)
                },
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Create new note"
                )
            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        contentWindowInsets = WindowInsets(0, 0, 0, 0), // Reset window insets

    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    // Show search bar and loading indicator
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        // Search bar with status bar padding
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .windowInsetsPadding(WindowInsets.statusBars)
                        ) {
                            IntegratedSearchBar(
                                query = searchQuery,
                                onQueryChange = { query ->
                                    searchQuery = query
                                },
                                onSearch = { query ->
                                    debouncedSearchQuery = query
                                    viewModel.onEvent(NotesUiEvent.SearchNotes(query))
                                },
                                onSettingsClick = onNavigateToSettings,
                                placeholder = "Search notes...",
                                isSearching = searchQuery.isNotEmpty()
                            )
                        }

                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .weight(1f)
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.align(Alignment.Center)
                            )
                        }
                    }
                }

                uiState.notes.isEmpty() -> {
                    // Show search bar and empty state
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        // Search bar with status bar padding
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .windowInsetsPadding(WindowInsets.statusBars)
                        ) {
                            IntegratedSearchBar(
                                query = searchQuery,
                                onQueryChange = { query ->
                                    searchQuery = query
                                },
                                onSearch = { query ->
                                    debouncedSearchQuery = query
                                    viewModel.onEvent(NotesUiEvent.SearchNotes(query))
                                },
                                onSettingsClick = onNavigateToSettings,
                                placeholder = "Search notes...",
                                isSearching = false
                            )
                        }

                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .weight(1f)
                        ) {
                            EmptyState(
                                title = if (searchQuery.isBlank()) "No notes yet" else "No notes found",
                                subtitle = if (searchQuery.isBlank()) {
                                    "Tap the + button to create your first note"
                                } else {
                                    "Try a different search term"
                                }
                            )
                        }
                    }
                }

                else -> {
                    // LazyVerticalStaggeredGrid with search bar as header
                    LazyVerticalStaggeredGrid(
                        columns = StaggeredGridCells.Fixed(2),
                        state = gridState,
                        contentPadding = PaddingValues(
                            start = 16.dp,
                            end = 16.dp,
                            bottom = 16.dp
                        ),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalItemSpacing = 8.dp
                    ) {
                        // Search bar as header item that scrolls with content
                        item(span = StaggeredGridItemSpan.FullLine) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .windowInsetsPadding(WindowInsets.statusBars)
                                    .padding(bottom = 8.dp)
                            ) {
                                IntegratedSearchBar(
                                    query = searchQuery,
                                    onQueryChange = { query ->
                                        searchQuery = query
                                    },
                                    onSearch = { query ->
                                        debouncedSearchQuery = query
                                        viewModel.onEvent(NotesUiEvent.SearchNotes(query))
                                    },
                                    onSettingsClick = onNavigateToSettings,
                                    placeholder = "Search notes...",
                                    isSearching = false
                                )
                            }
                        }

                        // Notes items
                        items(
                            items = uiState.notes,
                            key = { it.id }
                        ) { note ->
                            NoteItem(
                                note = note,
                                onNoteClick = {
                                    viewModel.onEvent(NotesUiEvent.SelectNote(note.id))
                                }
                            )
                        }
                    }
                }
            }
        }

        // Delete confirmation dialog
        noteToDelete?.let { note ->
            DeleteConfirmationDialog(
                note = note,
                onConfirm = {
                    viewModel.deleteNote(note)
                    noteToDelete = null
                },
                onDismiss = {
                    noteToDelete = null
                }
            )
        }
    }

}


@Preview(showBackground = true)
@Composable
fun NotesScreenPreview() {
    SimpleNOTETheme {
        NotesScreen(
            onNavigateToEditor = {},
            onNavigateToSettings = {}
        )
    }
}
