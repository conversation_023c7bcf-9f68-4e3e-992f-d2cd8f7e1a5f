package io.github.simplenote.presentation.screen

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.items
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import io.github.simplenote.domain.model.Note
import io.github.simplenote.presentation.components.DeleteConfirmationDialog
import io.github.simplenote.presentation.components.EmptyState
import io.github.simplenote.presentation.components.GoogleKeepSearchBar
import io.github.simplenote.presentation.components.NoteItem
import io.github.simplenote.presentation.model.NavigationEvent
import io.github.simplenote.presentation.model.NotesUiEvent
import io.github.simplenote.presentation.viewmodel.NotesViewModel
import io.github.simplenote.ui.theme.SimpleNOTETheme
import org.koin.androidx.compose.koinViewModel

/**
 * Main notes screen displaying the list of notes with search and color selection.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotesScreen(
    modifier: Modifier = Modifier,
    onNavigateToEditor: (Long?) -> Unit,
    onNavigateToSettings: () -> Unit,
    viewModel: NotesViewModel = koinViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val snackbarHostState = remember { SnackbarHostState() }
    var noteToDelete by remember { mutableStateOf<Note?>(null) }

    // Handle navigation events
    LaunchedEffect(viewModel.navigationEvents) {
        viewModel.navigationEvents.collect { event ->
            when (event) {
                is NavigationEvent.NavigateToNoteEditor -> {
                    onNavigateToEditor(event.noteId)
                }

                is NavigationEvent.ShowDeleteConfirmation -> {
                    noteToDelete = event.note
                }

                is NavigationEvent.ShowError -> {
                    snackbarHostState.showSnackbar(event.error.message)
                }

                else -> {}
            }
        }
    }

    // Handle errors
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(error.message)
            viewModel.onEvent(NotesUiEvent.ClearError)
        }
    }

    val gridState = rememberLazyStaggeredGridState()

    // Handle scroll-based search bar visibility
    LaunchedEffect(gridState.firstVisibleItemIndex, gridState.firstVisibleItemScrollOffset, uiState.isSearchFocused) {
        val isAtTop = gridState.firstVisibleItemIndex == 0 && gridState.firstVisibleItemScrollOffset <= 50
        val shouldShow = isAtTop || uiState.isSearchFocused
        
        if (uiState.isSearchBarVisible != shouldShow) {
            viewModel.onEvent(NotesUiEvent.UpdateSearchBarVisibility(shouldShow))
        }
    }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        containerColor = androidx.compose.ui.graphics.Color.Transparent,
        floatingActionButton = {
            FloatingActionButton(
                modifier = Modifier.padding(
                    bottom = WindowInsets.navigationBars.asPaddingValues()
                        .calculateBottomPadding() + 16.dp,
                    end = 16.dp
                ),
                onClick = {
                    viewModel.onEvent(NotesUiEvent.CreateNewNote)
                },
                containerColor = MaterialTheme.colorScheme.primary
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Create new note"
                )
            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Main content area
            LazyVerticalStaggeredGrid(
                columns = StaggeredGridCells.Fixed(2),
                state = gridState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(
                    start = 16.dp,
                    end = 16.dp,
                    top = if (uiState.isSearchBarVisible) 120.dp else 16.dp, // Account for search bar height + padding + margin
                    bottom = 100.dp // Account for FAB
                ),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalItemSpacing = 8.dp
            ) {
                when {
                    uiState.isLoading -> {
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(32.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator()
                            }
                        }
                    }
                    
                    uiState.notes.isEmpty() -> {
                        item {
                            Box(
                                modifier = Modifier.fillMaxWidth(),
                                contentAlignment = Alignment.Center
                            ) {
                                EmptyState(
                                    title = if (uiState.searchQuery.isBlank()) "No notes yet" else "No notes found",
                                    subtitle = if (uiState.searchQuery.isBlank()) {
                                        "Tap the + button to create your first note"
                                    } else {
                                        "Try a different search term"
                                    }
                                )
                            }
                        }
                    }
                    
                    else -> {
                        items(
                            items = uiState.notes,
                            key = { it.id }
                        ) { note ->
                            NoteItem(
                                note = note,
                                onNoteClick = {
                                    viewModel.onEvent(NotesUiEvent.SelectNote(note.id))
                                }
                            )
                        }
                    }
                }
            }
            
            // Fixed search bar at top (Google Keep style)
            GoogleKeepSearchBar(
                query = uiState.searchQuery,
                onQueryChange = { query ->
                    viewModel.onEvent(NotesUiEvent.SearchNotes(query))
                },
                onSearch = { query ->
                    viewModel.onEvent(NotesUiEvent.SearchNotes(query))
                },
                onSettingsClick = onNavigateToSettings,
                placeholder = "Search notes...",
                isSearching = uiState.isLoading && uiState.searchQuery.isNotEmpty(),
                isFocused = uiState.isSearchFocused,
                onFocusChange = { focused ->
                    viewModel.onEvent(NotesUiEvent.UpdateSearchFocus(focused))
                    if (focused) {
                        // Always show search bar when focused
                        viewModel.onEvent(NotesUiEvent.UpdateSearchBarVisibility(true))
                    }
                },
                isVisible = uiState.isSearchBarVisible,
                onBackPressed = {
                    // Clear search and remove focus
                    if (uiState.searchQuery.isNotEmpty()) {
                        viewModel.onEvent(NotesUiEvent.SearchNotes(""))
                    }
                    viewModel.onEvent(NotesUiEvent.UpdateSearchFocus(false))
                }
            )
        }

        // Delete confirmation dialog
        noteToDelete?.let { note ->
            DeleteConfirmationDialog(
                note = note,
                onConfirm = {
                    viewModel.deleteNote(note)
                    noteToDelete = null
                },
                onDismiss = {
                    noteToDelete = null
                }
            )
        }
    }

}


@Preview(showBackground = true)
@Composable
fun NotesScreenPreview() {
    SimpleNOTETheme {
        NotesScreen(
            onNavigateToEditor = {},
            onNavigateToSettings = {}
        )
    }
}
