package io.github.simplenote.domain.model

/**
 * Enum representing different theme modes for the application.
 */
enum class ThemeMode(val displayName: String) {
    LIGHT("Light"),
    DARK("Dark"),
    SYSTEM("System");
    
    companion object {
        /**
         * Get ThemeMode from string value, defaults to SYSTEM if not found
         */
        fun fromString(value: String): ThemeMode {
            return entries.find { it.name == value } ?: SYSTEM
        }
        
        /**
         * Default theme mode
         */
        val DEFAULT = SYSTEM
    }
}
