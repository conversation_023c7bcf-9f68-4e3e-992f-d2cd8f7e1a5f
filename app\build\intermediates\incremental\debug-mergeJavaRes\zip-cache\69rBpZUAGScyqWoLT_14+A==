[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 112, "crc": -1936459766}, {"key": "META-INF/kotlinx-serialization-core.kotlin_module", "name": "META-INF/kotlinx-serialization-core.kotlin_module", "size": 844, "crc": 201852746}, {"key": "kotlinx/serialization/BinaryFormat.class", "name": "kotlinx/serialization/BinaryFormat.class", "size": 1306, "crc": 1305807670}, {"key": "kotlinx/serialization/Contextual.class", "name": "kotlinx/serialization/Contextual.class", "size": 828, "crc": 138536713}, {"key": "kotlinx/serialization/ContextualSerializer.class", "name": "kotlinx/serialization/ContextualSerializer.class", "size": 6927, "crc": -1392482188}, {"key": "kotlinx/serialization/DeserializationStrategy.class", "name": "kotlinx/serialization/DeserializationStrategy.class", "size": 1046, "crc": -421650715}, {"key": "kotlinx/serialization/EncodeDefault$Mode.class", "name": "kotlinx/serialization/EncodeDefault$Mode.class", "size": 1983, "crc": -1844382301}, {"key": "kotlinx/serialization/EncodeDefault.class", "name": "kotlinx/serialization/EncodeDefault.class", "size": 1111, "crc": 1449741064}, {"key": "kotlinx/serialization/ExperimentalSerializationApi.class", "name": "kotlinx/serialization/ExperimentalSerializationApi.class", "size": 1117, "crc": -672637049}, {"key": "kotlinx/serialization/InheritableSerialInfo.class", "name": "kotlinx/serialization/InheritableSerialInfo.class", "size": 995, "crc": 2042938367}, {"key": "kotlinx/serialization/InternalSerializationApi.class", "name": "kotlinx/serialization/InternalSerializationApi.class", "size": 1107, "crc": 1594819506}, {"key": "kotlinx/serialization/KSerializer.class", "name": "kotlinx/serialization/KSerializer.class", "size": 1052, "crc": 1634488917}, {"key": "kotlinx/serialization/KeepGeneratedSerializer.class", "name": "kotlinx/serialization/KeepGeneratedSerializer.class", "size": 887, "crc": -2081187949}, {"key": "kotlinx/serialization/MetaSerializable.class", "name": "kotlinx/serialization/MetaSerializable.class", "size": 893, "crc": 1909729130}, {"key": "kotlinx/serialization/MissingFieldException.class", "name": "kotlinx/serialization/MissingFieldException.class", "size": 3256, "crc": 448053490}, {"key": "kotlinx/serialization/Polymorphic.class", "name": "kotlinx/serialization/Polymorphic.class", "size": 756, "crc": 1570797683}, {"key": "kotlinx/serialization/PolymorphicSerializer.class", "name": "kotlinx/serialization/PolymorphicSerializer.class", "size": 6799, "crc": -617966560}, {"key": "kotlinx/serialization/PolymorphicSerializerKt.class", "name": "kotlinx/serialization/PolymorphicSerializerKt.class", "size": 3446, "crc": -241293040}, {"key": "kotlinx/serialization/Required.class", "name": "kotlinx/serialization/Required.class", "size": 750, "crc": -1084715496}, {"key": "kotlinx/serialization/SealedClassSerializer$special$$inlined$groupingBy$1.class", "name": "kotlinx/serialization/SealedClassSerializer$special$$inlined$groupingBy$1.class", "size": 3028, "crc": -881268429}, {"key": "kotlinx/serialization/SealedClassSerializer.class", "name": "kotlinx/serialization/SealedClassSerializer.class", "size": 14107, "crc": 1340028550}, {"key": "kotlinx/serialization/SealedSerializationApi.class", "name": "kotlinx/serialization/SealedSerializationApi.class", "size": 1159, "crc": -1367297019}, {"key": "kotlinx/serialization/SerialFormat.class", "name": "kotlinx/serialization/SerialFormat.class", "size": 660, "crc": -562470073}, {"key": "kotlinx/serialization/SerialFormatKt.class", "name": "kotlinx/serialization/SerialFormatKt.class", "size": 5534, "crc": -1300411230}, {"key": "kotlinx/serialization/SerialInfo.class", "name": "kotlinx/serialization/SerialInfo.class", "size": 973, "crc": 1937174554}, {"key": "kotlinx/serialization/SerialName.class", "name": "kotlinx/serialization/SerialName.class", "size": 889, "crc": -1915907185}, {"key": "kotlinx/serialization/Serializable.class", "name": "kotlinx/serialization/Serializable.class", "size": 1135, "crc": 61070241}, {"key": "kotlinx/serialization/SerializationException.class", "name": "kotlinx/serialization/SerializationException.class", "size": 1321, "crc": -560206063}, {"key": "kotlinx/serialization/SerializationStrategy.class", "name": "kotlinx/serialization/SerializationStrategy.class", "size": 1074, "crc": 2100934112}, {"key": "kotlinx/serialization/Serializer.class", "name": "kotlinx/serialization/Serializer.class", "size": 1105, "crc": 543414893}, {"key": "kotlinx/serialization/SerializersCacheKt.class", "name": "kotlinx/serialization/SerializersCacheKt.class", "size": 8451, "crc": 573220479}, {"key": "kotlinx/serialization/SerializersKt.class", "name": "kotlinx/serialization/SerializersKt.class", "size": 7217, "crc": 726599053}, {"key": "kotlinx/serialization/SerializersKt__SerializersJvmKt.class", "name": "kotlinx/serialization/SerializersKt__SerializersJvmKt.class", "size": 13999, "crc": -1697967954}, {"key": "kotlinx/serialization/SerializersKt__SerializersKt.class", "name": "kotlinx/serialization/SerializersKt__SerializersKt.class", "size": 20970, "crc": -1585188153}, {"key": "kotlinx/serialization/StringFormat.class", "name": "kotlinx/serialization/StringFormat.class", "size": 1365, "crc": -2037620793}, {"key": "kotlinx/serialization/Transient.class", "name": "kotlinx/serialization/Transient.class", "size": 752, "crc": 151066913}, {"key": "kotlinx/serialization/UnknownFieldException.class", "name": "kotlinx/serialization/UnknownFieldException.class", "size": 1262, "crc": 266270558}, {"key": "kotlinx/serialization/UseContextualSerialization.class", "name": "kotlinx/serialization/UseContextualSerialization.class", "size": 988, "crc": 26036025}, {"key": "kotlinx/serialization/UseSerializers.class", "name": "kotlinx/serialization/UseSerializers.class", "size": 1078, "crc": 17469041}, {"key": "kotlinx/serialization/builtins/BuiltinSerializersKt.class", "name": "kotlinx/serialization/builtins/BuiltinSerializersKt.class", "size": 18817, "crc": 740493636}, {"key": "kotlinx/serialization/builtins/InstantComponentSerializer.class", "name": "kotlinx/serialization/builtins/InstantComponentSerializer.class", "size": 8481, "crc": -989683781}, {"key": "kotlinx/serialization/builtins/LongAsStringSerializer.class", "name": "kotlinx/serialization/builtins/LongAsStringSerializer.class", "size": 3202, "crc": 874369048}, {"key": "kotlinx/serialization/descriptors/ClassSerialDescriptorBuilder.class", "name": "kotlinx/serialization/descriptors/ClassSerialDescriptorBuilder.class", "size": 6202, "crc": 877241483}, {"key": "kotlinx/serialization/descriptors/ContextAwareKt.class", "name": "kotlinx/serialization/descriptors/ContextAwareKt.class", "size": 5951, "crc": 621182029}, {"key": "kotlinx/serialization/descriptors/ContextDescriptor.class", "name": "kotlinx/serialization/descriptors/ContextDescriptor.class", "size": 4457, "crc": -641138427}, {"key": "kotlinx/serialization/descriptors/PolymorphicKind$OPEN.class", "name": "kotlinx/serialization/descriptors/PolymorphicKind$OPEN.class", "size": 917, "crc": 1947956514}, {"key": "kotlinx/serialization/descriptors/PolymorphicKind$SEALED.class", "name": "kotlinx/serialization/descriptors/PolymorphicKind$SEALED.class", "size": 923, "crc": -1851800800}, {"key": "kotlinx/serialization/descriptors/PolymorphicKind.class", "name": "kotlinx/serialization/descriptors/PolymorphicKind.class", "size": 1223, "crc": 788408464}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$BOOLEAN.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$BOOLEAN.class", "size": 918, "crc": -230267869}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$BYTE.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$BYTE.class", "size": 909, "crc": 1062075875}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$CHAR.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$CHAR.class", "size": 909, "crc": -1808308564}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$DOUBLE.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$DOUBLE.class", "size": 915, "crc": 1953395710}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$FLOAT.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$FLOAT.class", "size": 912, "crc": 1503953509}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$INT.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$INT.class", "size": 906, "crc": -125798237}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$LONG.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$LONG.class", "size": 909, "crc": -365827594}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$SHORT.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$SHORT.class", "size": 912, "crc": 102440710}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind$STRING.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind$STRING.class", "size": 915, "crc": -533364677}, {"key": "kotlinx/serialization/descriptors/PrimitiveKind.class", "name": "kotlinx/serialization/descriptors/PrimitiveKind.class", "size": 2160, "crc": 604650110}, {"key": "kotlinx/serialization/descriptors/SerialDescriptor$DefaultImpls.class", "name": "kotlinx/serialization/descriptors/SerialDescriptor$DefaultImpls.class", "size": 1321, "crc": 519277080}, {"key": "kotlinx/serialization/descriptors/SerialDescriptor.class", "name": "kotlinx/serialization/descriptors/SerialDescriptor.class", "size": 2670, "crc": 1407892064}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorImpl.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorImpl.class", "size": 11683, "crc": 1706327857}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementDescriptors$1$1.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementDescriptors$1$1.class", "size": 2126, "crc": -789353684}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementNames$1$1.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$elementNames$1$1.class", "size": 1994, "crc": -1304685035}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$1.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$1.class", "size": 2408, "crc": -794452130}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$2.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt$special$$inlined$Iterable$2.class", "size": 2316, "crc": -1639239655}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorKt.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorKt.class", "size": 1906, "crc": -339018703}, {"key": "kotlinx/serialization/descriptors/SerialDescriptorsKt.class", "name": "kotlinx/serialization/descriptors/SerialDescriptorsKt.class", "size": 13037, "crc": -129940529}, {"key": "kotlinx/serialization/descriptors/SerialKind$CONTEXTUAL.class", "name": "kotlinx/serialization/descriptors/SerialKind$CONTEXTUAL.class", "size": 915, "crc": 1848797644}, {"key": "kotlinx/serialization/descriptors/SerialKind$ENUM.class", "name": "kotlinx/serialization/descriptors/SerialKind$ENUM.class", "size": 897, "crc": 1818934578}, {"key": "kotlinx/serialization/descriptors/SerialKind.class", "name": "kotlinx/serialization/descriptors/SerialKind.class", "size": 1899, "crc": -1951124569}, {"key": "kotlinx/serialization/descriptors/StructureKind$CLASS.class", "name": "kotlinx/serialization/descriptors/StructureKind$CLASS.class", "size": 912, "crc": 1785257237}, {"key": "kotlinx/serialization/descriptors/StructureKind$LIST.class", "name": "kotlinx/serialization/descriptors/StructureKind$LIST.class", "size": 909, "crc": 1008747375}, {"key": "kotlinx/serialization/descriptors/StructureKind$MAP.class", "name": "kotlinx/serialization/descriptors/StructureKind$MAP.class", "size": 906, "crc": 1167821584}, {"key": "kotlinx/serialization/descriptors/StructureKind$OBJECT.class", "name": "kotlinx/serialization/descriptors/StructureKind$OBJECT.class", "size": 915, "crc": 1394391267}, {"key": "kotlinx/serialization/descriptors/StructureKind.class", "name": "kotlinx/serialization/descriptors/StructureKind.class", "size": 1432, "crc": 1686357034}, {"key": "kotlinx/serialization/descriptors/WrappedSerialDescriptor.class", "name": "kotlinx/serialization/descriptors/WrappedSerialDescriptor.class", "size": 3929, "crc": -1928933725}, {"key": "kotlinx/serialization/encoding/AbstractDecoder.class", "name": "kotlinx/serialization/encoding/AbstractDecoder.class", "size": 10957, "crc": 1259926589}, {"key": "kotlinx/serialization/encoding/AbstractEncoder.class", "name": "kotlinx/serialization/encoding/AbstractEncoder.class", "size": 9690, "crc": 1657161228}, {"key": "kotlinx/serialization/encoding/ChunkedDecoder.class", "name": "kotlinx/serialization/encoding/ChunkedDecoder.class", "size": 895, "crc": -876746138}, {"key": "kotlinx/serialization/encoding/CompositeDecoder$Companion.class", "name": "kotlinx/serialization/encoding/CompositeDecoder$Companion.class", "size": 905, "crc": 1557747850}, {"key": "kotlinx/serialization/encoding/CompositeDecoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/CompositeDecoder$DefaultImpls.class", "size": 1749, "crc": -1729170313}, {"key": "kotlinx/serialization/encoding/CompositeDecoder.class", "name": "kotlinx/serialization/encoding/CompositeDecoder.class", "size": 5499, "crc": 363952467}, {"key": "kotlinx/serialization/encoding/CompositeEncoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/CompositeEncoder$DefaultImpls.class", "size": 1157, "crc": 2123471562}, {"key": "kotlinx/serialization/encoding/CompositeEncoder.class", "name": "kotlinx/serialization/encoding/CompositeEncoder.class", "size": 4092, "crc": 630993830}, {"key": "kotlinx/serialization/encoding/Decoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/Decoder$DefaultImpls.class", "size": 1537, "crc": 2060040111}, {"key": "kotlinx/serialization/encoding/Decoder.class", "name": "kotlinx/serialization/encoding/Decoder.class", "size": 4615, "crc": 251203891}, {"key": "kotlinx/serialization/encoding/DecodingKt.class", "name": "kotlinx/serialization/encoding/DecodingKt.class", "size": 3367, "crc": 1640509309}, {"key": "kotlinx/serialization/encoding/Encoder$DefaultImpls.class", "name": "kotlinx/serialization/encoding/Encoder$DefaultImpls.class", "size": 2223, "crc": 667244867}, {"key": "kotlinx/serialization/encoding/Encoder.class", "name": "kotlinx/serialization/encoding/Encoder.class", "size": 4703, "crc": -2101871064}, {"key": "kotlinx/serialization/encoding/EncodingKt.class", "name": "kotlinx/serialization/encoding/EncodingKt.class", "size": 5760, "crc": -1536504094}, {"key": "kotlinx/serialization/internal/AbstractCollectionSerializer.class", "name": "kotlinx/serialization/internal/AbstractCollectionSerializer.class", "size": 5562, "crc": -958705010}, {"key": "kotlinx/serialization/internal/AbstractPolymorphicSerializer.class", "name": "kotlinx/serialization/internal/AbstractPolymorphicSerializer.class", "size": 10136, "crc": -1424997669}, {"key": "kotlinx/serialization/internal/AbstractPolymorphicSerializerKt.class", "name": "kotlinx/serialization/internal/AbstractPolymorphicSerializerKt.class", "size": 2685, "crc": -951188807}, {"key": "kotlinx/serialization/internal/ArrayClassDesc.class", "name": "kotlinx/serialization/internal/ArrayClassDesc.class", "size": 1335, "crc": -700480689}, {"key": "kotlinx/serialization/internal/ArrayListClassDesc.class", "name": "kotlinx/serialization/internal/ArrayListClassDesc.class", "size": 1359, "crc": 860839878}, {"key": "kotlinx/serialization/internal/ArrayListSerializer.class", "name": "kotlinx/serialization/internal/ArrayListSerializer.class", "size": 4570, "crc": 2015053633}, {"key": "kotlinx/serialization/internal/BooleanArrayBuilder.class", "name": "kotlinx/serialization/internal/BooleanArrayBuilder.class", "size": 2476, "crc": -1154428280}, {"key": "kotlinx/serialization/internal/BooleanArraySerializer.class", "name": "kotlinx/serialization/internal/BooleanArraySerializer.class", "size": 4624, "crc": 1326472138}, {"key": "kotlinx/serialization/internal/BooleanSerializer.class", "name": "kotlinx/serialization/internal/BooleanSerializer.class", "size": 3015, "crc": 1844467316}, {"key": "kotlinx/serialization/internal/ByteArrayBuilder.class", "name": "kotlinx/serialization/internal/ByteArrayBuilder.class", "size": 2470, "crc": 1193579867}, {"key": "kotlinx/serialization/internal/ByteArraySerializer.class", "name": "kotlinx/serialization/internal/ByteArraySerializer.class", "size": 4596, "crc": -1578767871}, {"key": "kotlinx/serialization/internal/ByteSerializer.class", "name": "kotlinx/serialization/internal/ByteSerializer.class", "size": 2998, "crc": -1081348654}, {"key": "kotlinx/serialization/internal/CacheEntry.class", "name": "kotlinx/serialization/internal/CacheEntry.class", "size": 1073, "crc": -1968705788}, {"key": "kotlinx/serialization/internal/CachedNames.class", "name": "kotlinx/serialization/internal/CachedNames.class", "size": 650, "crc": -9789989}, {"key": "kotlinx/serialization/internal/CachingKt.class", "name": "kotlinx/serialization/internal/CachingKt.class", "size": 2940, "crc": -668306214}, {"key": "kotlinx/serialization/internal/CharArrayBuilder.class", "name": "kotlinx/serialization/internal/CharArrayBuilder.class", "size": 2470, "crc": 1620475162}, {"key": "kotlinx/serialization/internal/CharArraySerializer.class", "name": "kotlinx/serialization/internal/CharArraySerializer.class", "size": 4601, "crc": -813831639}, {"key": "kotlinx/serialization/internal/CharSerializer.class", "name": "kotlinx/serialization/internal/CharSerializer.class", "size": 2996, "crc": 401263668}, {"key": "kotlinx/serialization/internal/ClassValueCache$get$$inlined$getOrSet$1.class", "name": "kotlinx/serialization/internal/ClassValueCache$get$$inlined$getOrSet$1.class", "size": 2161, "crc": -1068745884}, {"key": "kotlinx/serialization/internal/ClassValueCache.class", "name": "kotlinx/serialization/internal/ClassValueCache.class", "size": 4856, "crc": 471701251}, {"key": "kotlinx/serialization/internal/ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1.class", "name": "kotlinx/serialization/internal/ClassValueParametrizedCache$get-gIAlu-s$$inlined$getOrSet$1.class", "size": 1795, "crc": 1571671887}, {"key": "kotlinx/serialization/internal/ClassValueParametrizedCache.class", "name": "kotlinx/serialization/internal/ClassValueParametrizedCache.class", "size": 7989, "crc": -205331773}, {"key": "kotlinx/serialization/internal/ClassValueReferences$getOrSet$2.class", "name": "kotlinx/serialization/internal/ClassValueReferences$getOrSet$2.class", "size": 1516, "crc": -1916996879}, {"key": "kotlinx/serialization/internal/ClassValueReferences.class", "name": "kotlinx/serialization/internal/ClassValueReferences.class", "size": 3515, "crc": 844841091}, {"key": "kotlinx/serialization/internal/CollectionDescriptorsKt.class", "name": "kotlinx/serialization/internal/CollectionDescriptorsKt.class", "size": 1056, "crc": 2128899089}, {"key": "kotlinx/serialization/internal/CollectionLikeSerializer.class", "name": "kotlinx/serialization/internal/CollectionLikeSerializer.class", "size": 6855, "crc": -1020600829}, {"key": "kotlinx/serialization/internal/CollectionSerializer.class", "name": "kotlinx/serialization/internal/CollectionSerializer.class", "size": 2349, "crc": 418940594}, {"key": "kotlinx/serialization/internal/ConcurrentHashMapCache.class", "name": "kotlinx/serialization/internal/ConcurrentHashMapCache.class", "size": 4420, "crc": 305494623}, {"key": "kotlinx/serialization/internal/ConcurrentHashMapParametrizedCache.class", "name": "kotlinx/serialization/internal/ConcurrentHashMapParametrizedCache.class", "size": 7463, "crc": -552928144}, {"key": "kotlinx/serialization/internal/CoreFriendModuleApi.class", "name": "kotlinx/serialization/internal/CoreFriendModuleApi.class", "size": 787, "crc": 140433061}, {"key": "kotlinx/serialization/internal/DoubleArrayBuilder.class", "name": "kotlinx/serialization/internal/DoubleArrayBuilder.class", "size": 2474, "crc": 1193586614}, {"key": "kotlinx/serialization/internal/DoubleArraySerializer.class", "name": "kotlinx/serialization/internal/DoubleArraySerializer.class", "size": 4622, "crc": 626471423}, {"key": "kotlinx/serialization/internal/DoubleSerializer.class", "name": "kotlinx/serialization/internal/DoubleSerializer.class", "size": 3024, "crc": -2146207912}, {"key": "kotlinx/serialization/internal/DurationSerializer.class", "name": "kotlinx/serialization/internal/DurationSerializer.class", "size": 3356, "crc": -247220929}, {"key": "kotlinx/serialization/internal/ElementMarker$Companion.class", "name": "kotlinx/serialization/internal/ElementMarker$Companion.class", "size": 909, "crc": -1422475647}, {"key": "kotlinx/serialization/internal/ElementMarker.class", "name": "kotlinx/serialization/internal/ElementMarker.class", "size": 4136, "crc": -670971809}, {"key": "kotlinx/serialization/internal/EnumDescriptor.class", "name": "kotlinx/serialization/internal/EnumDescriptor.class", "size": 7594, "crc": 947014989}, {"key": "kotlinx/serialization/internal/EnumSerializer.class", "name": "kotlinx/serialization/internal/EnumSerializer.class", "size": 7247, "crc": -576659066}, {"key": "kotlinx/serialization/internal/EnumsKt.class", "name": "kotlinx/serialization/internal/EnumsKt.class", "size": 6448, "crc": -1370325637}, {"key": "kotlinx/serialization/internal/FloatArrayBuilder.class", "name": "kotlinx/serialization/internal/FloatArrayBuilder.class", "size": 2472, "crc": -1166212518}, {"key": "kotlinx/serialization/internal/FloatArraySerializer.class", "name": "kotlinx/serialization/internal/FloatArraySerializer.class", "size": 4609, "crc": -1391619861}, {"key": "kotlinx/serialization/internal/FloatSerializer.class", "name": "kotlinx/serialization/internal/FloatSerializer.class", "size": 3011, "crc": -402009781}, {"key": "kotlinx/serialization/internal/GeneratedSerializer$DefaultImpls.class", "name": "kotlinx/serialization/internal/GeneratedSerializer$DefaultImpls.class", "size": 1054, "crc": -2141594153}, {"key": "kotlinx/serialization/internal/GeneratedSerializer.class", "name": "kotlinx/serialization/internal/GeneratedSerializer.class", "size": 1575, "crc": 1294851763}, {"key": "kotlinx/serialization/internal/HashMapClassDesc.class", "name": "kotlinx/serialization/internal/HashMapClassDesc.class", "size": 1324, "crc": -148561360}, {"key": "kotlinx/serialization/internal/HashMapSerializer.class", "name": "kotlinx/serialization/internal/HashMapSerializer.class", "size": 5997, "crc": 1316014343}, {"key": "kotlinx/serialization/internal/HashSetClassDesc.class", "name": "kotlinx/serialization/internal/HashSetClassDesc.class", "size": 1353, "crc": 967718160}, {"key": "kotlinx/serialization/internal/HashSetSerializer.class", "name": "kotlinx/serialization/internal/HashSetSerializer.class", "size": 4443, "crc": 689214605}, {"key": "kotlinx/serialization/internal/InlineClassDescriptor.class", "name": "kotlinx/serialization/internal/InlineClassDescriptor.class", "size": 3962, "crc": 1537874978}, {"key": "kotlinx/serialization/internal/InlineClassDescriptorKt$InlinePrimitiveDescriptor$1.class", "name": "kotlinx/serialization/internal/InlineClassDescriptorKt$InlinePrimitiveDescriptor$1.class", "size": 2917, "crc": -1677005545}, {"key": "kotlinx/serialization/internal/InlineClassDescriptorKt.class", "name": "kotlinx/serialization/internal/InlineClassDescriptorKt.class", "size": 1768, "crc": -1350454490}, {"key": "kotlinx/serialization/internal/InstantSerializer.class", "name": "kotlinx/serialization/internal/InstantSerializer.class", "size": 3299, "crc": 306200067}, {"key": "kotlinx/serialization/internal/IntArrayBuilder.class", "name": "kotlinx/serialization/internal/IntArrayBuilder.class", "size": 2446, "crc": 699043996}, {"key": "kotlinx/serialization/internal/IntArraySerializer.class", "name": "kotlinx/serialization/internal/IntArraySerializer.class", "size": 4576, "crc": -633809358}, {"key": "kotlinx/serialization/internal/IntSerializer.class", "name": "kotlinx/serialization/internal/IntSerializer.class", "size": 3001, "crc": 929474553}, {"key": "kotlinx/serialization/internal/InternalHexConverter.class", "name": "kotlinx/serialization/internal/InternalHexConverter.class", "size": 4383, "crc": 750773621}, {"key": "kotlinx/serialization/internal/JsonInternalDependenciesKt.class", "name": "kotlinx/serialization/internal/JsonInternalDependenciesKt.class", "size": 1205, "crc": 1859804262}, {"key": "kotlinx/serialization/internal/KTypeWrapper.class", "name": "kotlinx/serialization/internal/KTypeWrapper.class", "size": 3098, "crc": 145824807}, {"key": "kotlinx/serialization/internal/KeyValueSerializer.class", "name": "kotlinx/serialization/internal/KeyValueSerializer.class", "size": 6891, "crc": 1972889417}, {"key": "kotlinx/serialization/internal/LinkedHashMapClassDesc.class", "name": "kotlinx/serialization/internal/LinkedHashMapClassDesc.class", "size": 1342, "crc": 140950531}, {"key": "kotlinx/serialization/internal/LinkedHashMapSerializer.class", "name": "kotlinx/serialization/internal/LinkedHashMapSerializer.class", "size": 6111, "crc": -323130225}, {"key": "kotlinx/serialization/internal/LinkedHashSetClassDesc.class", "name": "kotlinx/serialization/internal/LinkedHashSetClassDesc.class", "size": 1371, "crc": -432388806}, {"key": "kotlinx/serialization/internal/LinkedHashSetSerializer.class", "name": "kotlinx/serialization/internal/LinkedHashSetSerializer.class", "size": 4561, "crc": 1127433912}, {"key": "kotlinx/serialization/internal/ListLikeDescriptor.class", "name": "kotlinx/serialization/internal/ListLikeDescriptor.class", "size": 6146, "crc": 146138068}, {"key": "kotlinx/serialization/internal/LongArrayBuilder.class", "name": "kotlinx/serialization/internal/LongArrayBuilder.class", "size": 2470, "crc": -1429970295}, {"key": "kotlinx/serialization/internal/LongArraySerializer.class", "name": "kotlinx/serialization/internal/LongArraySerializer.class", "size": 4596, "crc": -466523742}, {"key": "kotlinx/serialization/internal/LongSerializer.class", "name": "kotlinx/serialization/internal/LongSerializer.class", "size": 2998, "crc": 195457385}, {"key": "kotlinx/serialization/internal/MapEntrySerializer$MapEntry.class", "name": "kotlinx/serialization/internal/MapEntrySerializer$MapEntry.class", "size": 3625, "crc": -131030463}, {"key": "kotlinx/serialization/internal/MapEntrySerializer.class", "name": "kotlinx/serialization/internal/MapEntrySerializer.class", "size": 5368, "crc": -467801940}, {"key": "kotlinx/serialization/internal/MapLikeDescriptor.class", "name": "kotlinx/serialization/internal/MapLikeDescriptor.class", "size": 6663, "crc": -1261746360}, {"key": "kotlinx/serialization/internal/MapLikeSerializer.class", "name": "kotlinx/serialization/internal/MapLikeSerializer.class", "size": 10242, "crc": -2136207916}, {"key": "kotlinx/serialization/internal/MutableSoftReference.class", "name": "kotlinx/serialization/internal/MutableSoftReference.class", "size": 2209, "crc": -1398827132}, {"key": "kotlinx/serialization/internal/NamedCompanion.class", "name": "kotlinx/serialization/internal/NamedCompanion.class", "size": 886, "crc": 816384341}, {"key": "kotlinx/serialization/internal/NamedValueDecoder.class", "name": "kotlinx/serialization/internal/NamedValueDecoder.class", "size": 3358, "crc": 1989332777}, {"key": "kotlinx/serialization/internal/NamedValueEncoder.class", "name": "kotlinx/serialization/internal/NamedValueEncoder.class", "size": 2760, "crc": -1852716601}, {"key": "kotlinx/serialization/internal/NoOpEncoder.class", "name": "kotlinx/serialization/internal/NoOpEncoder.class", "size": 3105, "crc": -1775905001}, {"key": "kotlinx/serialization/internal/NothingSerialDescriptor.class", "name": "kotlinx/serialization/internal/NothingSerialDescriptor.class", "size": 4098, "crc": 824370517}, {"key": "kotlinx/serialization/internal/NothingSerializer.class", "name": "kotlinx/serialization/internal/NothingSerializer.class", "size": 2629, "crc": 1587977005}, {"key": "kotlinx/serialization/internal/NullableSerializer.class", "name": "kotlinx/serialization/internal/NullableSerializer.class", "size": 3699, "crc": -870847077}, {"key": "kotlinx/serialization/internal/ObjectSerializer.class", "name": "kotlinx/serialization/internal/ObjectSerializer.class", "size": 7840, "crc": -2044343402}, {"key": "kotlinx/serialization/internal/PairSerializer.class", "name": "kotlinx/serialization/internal/PairSerializer.class", "size": 4860, "crc": 967473427}, {"key": "kotlinx/serialization/internal/ParametrizedCacheEntry.class", "name": "kotlinx/serialization/internal/ParametrizedCacheEntry.class", "size": 5607, "crc": -1996348918}, {"key": "kotlinx/serialization/internal/ParametrizedSerializerCache$DefaultImpls.class", "name": "kotlinx/serialization/internal/ParametrizedSerializerCache$DefaultImpls.class", "size": 639, "crc": -1038966437}, {"key": "kotlinx/serialization/internal/ParametrizedSerializerCache.class", "name": "kotlinx/serialization/internal/ParametrizedSerializerCache.class", "size": 1779, "crc": -493066089}, {"key": "kotlinx/serialization/internal/PlatformKt.class", "name": "kotlinx/serialization/internal/PlatformKt.class", "size": 22080, "crc": 1583348276}, {"key": "kotlinx/serialization/internal/Platform_commonKt.class", "name": "kotlinx/serialization/internal/Platform_commonKt.class", "size": 9358, "crc": -756350103}, {"key": "kotlinx/serialization/internal/PluginExceptionsKt.class", "name": "kotlinx/serialization/internal/PluginExceptionsKt.class", "size": 2176, "crc": 883873977}, {"key": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptor.class", "name": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptor.class", "size": 14122, "crc": 964193813}, {"key": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptorKt.class", "name": "kotlinx/serialization/internal/PluginGeneratedSerialDescriptorKt.class", "size": 7464, "crc": -1053316600}, {"key": "kotlinx/serialization/internal/PluginHelperInterfacesKt.class", "name": "kotlinx/serialization/internal/PluginHelperInterfacesKt.class", "size": 815, "crc": -1167894991}, {"key": "kotlinx/serialization/internal/PrimitiveArrayBuilder.class", "name": "kotlinx/serialization/internal/PrimitiveArrayBuilder.class", "size": 1529, "crc": 803810715}, {"key": "kotlinx/serialization/internal/PrimitiveArrayDescriptor.class", "name": "kotlinx/serialization/internal/PrimitiveArrayDescriptor.class", "size": 1638, "crc": -169481562}, {"key": "kotlinx/serialization/internal/PrimitiveArraySerializer.class", "name": "kotlinx/serialization/internal/PrimitiveArraySerializer.class", "size": 7883, "crc": -2077175033}, {"key": "kotlinx/serialization/internal/PrimitiveArraysSerializersKt.class", "name": "kotlinx/serialization/internal/PrimitiveArraysSerializersKt.class", "size": 437, "crc": -1788610261}, {"key": "kotlinx/serialization/internal/PrimitiveSerialDescriptor.class", "name": "kotlinx/serialization/internal/PrimitiveSerialDescriptor.class", "size": 4370, "crc": -1786548298}, {"key": "kotlinx/serialization/internal/PrimitivesKt.class", "name": "kotlinx/serialization/internal/PrimitivesKt.class", "size": 5114, "crc": -1819858758}, {"key": "kotlinx/serialization/internal/ReferenceArraySerializer.class", "name": "kotlinx/serialization/internal/ReferenceArraySerializer.class", "size": 5983, "crc": 1081512891}, {"key": "kotlinx/serialization/internal/SerialDescriptorForNullable.class", "name": "kotlinx/serialization/internal/SerialDescriptorForNullable.class", "size": 4666, "crc": 667477168}, {"key": "kotlinx/serialization/internal/SerializationConstructorMarker.class", "name": "kotlinx/serialization/internal/SerializationConstructorMarker.class", "size": 782, "crc": -307966066}, {"key": "kotlinx/serialization/internal/SerializerCache$DefaultImpls.class", "name": "kotlinx/serialization/internal/SerializerCache$DefaultImpls.class", "size": 1097, "crc": -232969425}, {"key": "kotlinx/serialization/internal/SerializerCache.class", "name": "kotlinx/serialization/internal/SerializerCache.class", "size": 1630, "crc": -840268485}, {"key": "kotlinx/serialization/internal/SerializerFactory.class", "name": "kotlinx/serialization/internal/SerializerFactory.class", "size": 1065, "crc": -1019032972}, {"key": "kotlinx/serialization/internal/ShortArrayBuilder.class", "name": "kotlinx/serialization/internal/ShortArrayBuilder.class", "size": 2472, "crc": -888405850}, {"key": "kotlinx/serialization/internal/ShortArraySerializer.class", "name": "kotlinx/serialization/internal/ShortArraySerializer.class", "size": 4609, "crc": 903424310}, {"key": "kotlinx/serialization/internal/ShortSerializer.class", "name": "kotlinx/serialization/internal/ShortSerializer.class", "size": 3011, "crc": 2029787103}, {"key": "kotlinx/serialization/internal/StringSerializer.class", "name": "kotlinx/serialization/internal/StringSerializer.class", "size": 2999, "crc": 1403975797}, {"key": "kotlinx/serialization/internal/SuppressAnimalSniffer.class", "name": "kotlinx/serialization/internal/SuppressAnimalSniffer.class", "size": 851, "crc": 745747274}, {"key": "kotlinx/serialization/internal/TaggedDecoder.class", "name": "kotlinx/serialization/internal/TaggedDecoder.class", "size": 17595, "crc": -582689944}, {"key": "kotlinx/serialization/internal/TaggedEncoder.class", "name": "kotlinx/serialization/internal/TaggedEncoder.class", "size": 15182, "crc": -85573978}, {"key": "kotlinx/serialization/internal/TripleSerializer.class", "name": "kotlinx/serialization/internal/TripleSerializer.class", "size": 8106, "crc": 22050414}, {"key": "kotlinx/serialization/internal/TuplesKt.class", "name": "kotlinx/serialization/internal/TuplesKt.class", "size": 918, "crc": 831359372}, {"key": "kotlinx/serialization/internal/UByteArrayBuilder.class", "name": "kotlinx/serialization/internal/UByteArrayBuilder.class", "size": 3038, "crc": -1977675374}, {"key": "kotlinx/serialization/internal/UByteArraySerializer.class", "name": "kotlinx/serialization/internal/UByteArraySerializer.class", "size": 5497, "crc": 238944338}, {"key": "kotlinx/serialization/internal/UByteSerializer.class", "name": "kotlinx/serialization/internal/UByteSerializer.class", "size": 3395, "crc": 451607151}, {"key": "kotlinx/serialization/internal/UIntArrayBuilder.class", "name": "kotlinx/serialization/internal/UIntArrayBuilder.class", "size": 3020, "crc": -541767805}, {"key": "kotlinx/serialization/internal/UIntArraySerializer.class", "name": "kotlinx/serialization/internal/UIntArraySerializer.class", "size": 5489, "crc": 458728261}, {"key": "kotlinx/serialization/internal/UIntSerializer.class", "name": "kotlinx/serialization/internal/UIntSerializer.class", "size": 3383, "crc": 74500025}, {"key": "kotlinx/serialization/internal/ULongArrayBuilder.class", "name": "kotlinx/serialization/internal/ULongArrayBuilder.class", "size": 3038, "crc": -1600738949}, {"key": "kotlinx/serialization/internal/ULongArraySerializer.class", "name": "kotlinx/serialization/internal/ULongArraySerializer.class", "size": 5497, "crc": -460073385}, {"key": "kotlinx/serialization/internal/ULongSerializer.class", "name": "kotlinx/serialization/internal/ULongSerializer.class", "size": 3395, "crc": 1040141327}, {"key": "kotlinx/serialization/internal/UShortArrayBuilder.class", "name": "kotlinx/serialization/internal/UShortArrayBuilder.class", "size": 3045, "crc": -706030373}, {"key": "kotlinx/serialization/internal/UShortArraySerializer.class", "name": "kotlinx/serialization/internal/UShortArraySerializer.class", "size": 5517, "crc": 1961334783}, {"key": "kotlinx/serialization/internal/UShortSerializer.class", "name": "kotlinx/serialization/internal/UShortSerializer.class", "size": 3407, "crc": 269835735}, {"key": "kotlinx/serialization/internal/UnitSerializer.class", "name": "kotlinx/serialization/internal/UnitSerializer.class", "size": 2579, "crc": 772363715}, {"key": "kotlinx/serialization/internal/UuidSerializer.class", "name": "kotlinx/serialization/internal/UuidSerializer.class", "size": 3232, "crc": **********}, {"key": "kotlinx/serialization/modules/ContextualProvider$Argless.class", "name": "kotlinx/serialization/modules/ContextualProvider$Argless.class", "size": 2338, "crc": -454291997}, {"key": "kotlinx/serialization/modules/ContextualProvider$WithTypeArguments.class", "name": "kotlinx/serialization/modules/ContextualProvider$WithTypeArguments.class", "size": 2465, "crc": -803636207}, {"key": "kotlinx/serialization/modules/ContextualProvider.class", "name": "kotlinx/serialization/modules/ContextualProvider.class", "size": 1574, "crc": **********}, {"key": "kotlinx/serialization/modules/PolymorphicModuleBuilder.class", "name": "kotlinx/serialization/modules/PolymorphicModuleBuilder.class", "size": 7592, "crc": 51147149}, {"key": "kotlinx/serialization/modules/PolymorphicModuleBuilderKt.class", "name": "kotlinx/serialization/modules/PolymorphicModuleBuilderKt.class", "size": 2183, "crc": 34240111}, {"key": "kotlinx/serialization/modules/SerialModuleImpl.class", "name": "kotlinx/serialization/modules/SerialModuleImpl.class", "size": 11751, "crc": 423740733}, {"key": "kotlinx/serialization/modules/SerializerAlreadyRegisteredException.class", "name": "kotlinx/serialization/modules/SerializerAlreadyRegisteredException.class", "size": 1672, "crc": **********}, {"key": "kotlinx/serialization/modules/SerializersModule.class", "name": "kotlinx/serialization/modules/SerializersModule.class", "size": 4106, "crc": -**********}, {"key": "kotlinx/serialization/modules/SerializersModuleBuilder.class", "name": "kotlinx/serialization/modules/SerializersModuleBuilder.class", "size": 14532, "crc": -282644137}, {"key": "kotlinx/serialization/modules/SerializersModuleBuildersKt$polymorphic$1.class", "name": "kotlinx/serialization/modules/SerializersModuleBuildersKt$polymorphic$1.class", "size": 2238, "crc": 674103950}, {"key": "kotlinx/serialization/modules/SerializersModuleBuildersKt.class", "name": "kotlinx/serialization/modules/SerializersModuleBuildersKt.class", "size": 6166, "crc": -24337800}, {"key": "kotlinx/serialization/modules/SerializersModuleCollector$DefaultImpls.class", "name": "kotlinx/serialization/modules/SerializersModuleCollector$DefaultImpls.class", "size": 2295, "crc": 1765751950}, {"key": "kotlinx/serialization/modules/SerializersModuleCollector.class", "name": "kotlinx/serialization/modules/SerializersModuleCollector.class", "size": 5033, "crc": -1120725730}, {"key": "kotlinx/serialization/modules/SerializersModuleKt$overwriteWith$1$1.class", "name": "kotlinx/serialization/modules/SerializersModuleKt$overwriteWith$1$1.class", "size": 5230, "crc": 1382499451}, {"key": "kotlinx/serialization/modules/SerializersModuleKt.class", "name": "kotlinx/serialization/modules/SerializersModuleKt.class", "size": 4481, "crc": 26713173}, {"key": "META-INF/proguard/kotlinx-serialization-common.pro", "name": "META-INF/proguard/kotlinx-serialization-common.pro", "size": 2116, "crc": 60593265}, {"key": "META-INF/com.android.tools/proguard/kotlinx-serialization-common.pro", "name": "META-INF/com.android.tools/proguard/kotlinx-serialization-common.pro", "size": 2116, "crc": 60593265}, {"key": "META-INF/com.android.tools/r8/kotlinx-serialization-common.pro", "name": "META-INF/com.android.tools/r8/kotlinx-serialization-common.pro", "size": 2116, "crc": 60593265}, {"key": "META-INF/com.android.tools/r8/kotlinx-serialization-r8.pro", "name": "META-INF/com.android.tools/r8/kotlinx-serialization-r8.pro", "size": 1124, "crc": -22045854}, {"key": "META-INF/versions/9/module-info.class", "name": "META-INF/versions/9/module-info.class", "size": 435, "crc": -921077919}]