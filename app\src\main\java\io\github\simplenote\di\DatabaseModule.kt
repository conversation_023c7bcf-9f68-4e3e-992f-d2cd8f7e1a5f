package io.github.simplenote.di

import io.github.simplenote.data.local.database.NoteDatabase
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module

/**
 * Koin module for database dependencies.
 * Provides Room database and DAO instances.
 */
val databaseModule = module {
    
    // Room Database - Singleton
    single<NoteDatabase> {
        NoteDatabase.create(androidContext())
    }
    
    // Note DAO - Singleton
    single {
        get<NoteDatabase>().noteDao()
    }
}
