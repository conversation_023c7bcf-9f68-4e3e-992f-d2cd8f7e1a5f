  Activity android.app  Context android.content  Build 
android.os  BackHandler androidx.activity.compose  AnimatedContent androidx.compose.animation  AnimatedVisibility androidx.compose.animation  animateColorAsState androidx.compose.animation  expandHorizontally androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  shrinkHorizontally androidx.compose.animation  slideInHorizontally androidx.compose.animation  slideInVertically androidx.compose.animation  slideOutHorizontally androidx.compose.animation  slideOutVertically androidx.compose.animation  togetherWith androidx.compose.animation  animateFloatAsState androidx.compose.animation.core  tween androidx.compose.animation.core  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  MutableInteractionSource 'androidx.compose.foundation.interaction  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  WindowInsets "androidx.compose.foundation.layout  asPaddingValues "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  navigationBars "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  safeDrawing "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
statusBars "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  windowInsetsPadding "androidx.compose.foundation.layout  	GridCells %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  LazyVerticalStaggeredGrid .androidx.compose.foundation.lazy.staggeredgrid  StaggeredGridCells .androidx.compose.foundation.lazy.staggeredgrid  items .androidx.compose.foundation.lazy.staggeredgrid  rememberLazyStaggeredGridState .androidx.compose.foundation.lazy.staggeredgrid  
selectable %androidx.compose.foundation.selection  selectableGroup %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  BasicTextField  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Note 3androidx.compose.material.icons.automirrored.filled  	ArrowBack 5androidx.compose.material.icons.automirrored.outlined  Add &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  Error &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Archive (androidx.compose.material.icons.outlined  Delete (androidx.compose.material.icons.outlined  Menu (androidx.compose.material.icons.outlined  
Notifications (androidx.compose.material.icons.outlined  Palette (androidx.compose.material.icons.outlined  PushPin (androidx.compose.material.icons.outlined  AlertDialog androidx.compose.material3  BottomAppBar androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  
MaterialTheme androidx.compose.material3  ModalBottomSheet androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  RadioButton androidx.compose.material3  Scaffold androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  rememberModalBottomSheetState androidx.compose.material3  
Composable androidx.compose.runtime  DisposableEffect androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
SideEffect androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  zIndex androidx.compose.ui  clip androidx.compose.ui.draw  shadow androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  onFocusChanged androidx.compose.ui.focus  Color androidx.compose.ui.graphics  
SolidColor androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  HapticFeedbackType "androidx.compose.ui.hapticfeedback  LocalContext androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  Role androidx.compose.ui.semantics  
FontWeight androidx.compose.ui.text.font  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  PreviewLightDark #androidx.compose.ui.tooling.preview  dp androidx.compose.ui.unit  WindowCompat androidx.core.view  	DataStore androidx.datastore.core  Preferences #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  	Lifecycle androidx.lifecycle  LifecycleEventObserver androidx.lifecycle  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  LocalLifecycleOwner androidx.lifecycle.compose  collectAsStateWithLifecycle androidx.lifecycle.compose  NavHostController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
ColumnInfo 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  EntityDeleteOrUpdateAdapter 
androidx.room  EntityInsertAdapter 
androidx.room  Insert 
androidx.room  InvalidationTracker 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Array androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  NoteDao androidx.room.RoomDatabase  NoteDatabase androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  getTotalChangedRows androidx.room.util  performSuspending androidx.room.util  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  SupportSQLiteDatabase androidx.sqlite.db  Either 
arrow.core  left 
arrow.core  Dao #io.github.simplenote.data.local.dao  Delete #io.github.simplenote.data.local.dao  Flow #io.github.simplenote.data.local.dao  	Generated #io.github.simplenote.data.local.dao  Insert #io.github.simplenote.data.local.dao  Int #io.github.simplenote.data.local.dao  List #io.github.simplenote.data.local.dao  Long #io.github.simplenote.data.local.dao  NoteDao #io.github.simplenote.data.local.dao  NoteDao_Impl #io.github.simplenote.data.local.dao  
NoteEntity #io.github.simplenote.data.local.dao  OnConflictStrategy #io.github.simplenote.data.local.dao  Query #io.github.simplenote.data.local.dao  String #io.github.simplenote.data.local.dao  Suppress #io.github.simplenote.data.local.dao  Update #io.github.simplenote.data.local.dao  OnConflictStrategy +io.github.simplenote.data.local.dao.NoteDao  Array (io.github.simplenote.data.local.database  Context (io.github.simplenote.data.local.database  Database (io.github.simplenote.data.local.database  	Generated (io.github.simplenote.data.local.database  	Migration (io.github.simplenote.data.local.database  NoteDao (io.github.simplenote.data.local.database  NoteDatabase (io.github.simplenote.data.local.database  
NoteEntity (io.github.simplenote.data.local.database  RoomDatabase (io.github.simplenote.data.local.database  SupportSQLiteDatabase (io.github.simplenote.data.local.database  Suppress (io.github.simplenote.data.local.database  Array 5io.github.simplenote.data.local.database.NoteDatabase  Context 5io.github.simplenote.data.local.database.NoteDatabase  	Migration 5io.github.simplenote.data.local.database.NoteDatabase  NoteDao 5io.github.simplenote.data.local.database.NoteDatabase  NoteDatabase 5io.github.simplenote.data.local.database.NoteDatabase  SupportSQLiteDatabase 5io.github.simplenote.data.local.database.NoteDatabase  
ColumnInfo &io.github.simplenote.data.local.entity  Entity &io.github.simplenote.data.local.entity  Int &io.github.simplenote.data.local.entity  Long &io.github.simplenote.data.local.entity  
NoteEntity &io.github.simplenote.data.local.entity  
PrimaryKey &io.github.simplenote.data.local.entity  String &io.github.simplenote.data.local.entity  toDomain &io.github.simplenote.data.local.entity  toEntity &io.github.simplenote.data.local.entity  Boolean %io.github.simplenote.data.preferences  	DataStore %io.github.simplenote.data.preferences  Flow %io.github.simplenote.data.preferences  Preferences %io.github.simplenote.data.preferences  	ThemeMode %io.github.simplenote.data.preferences  ThemePreferences %io.github.simplenote.data.preferences  
ThemeSettings %io.github.simplenote.data.preferences  Boolean 6io.github.simplenote.data.preferences.ThemePreferences  	DataStore 6io.github.simplenote.data.preferences.ThemePreferences  Flow 6io.github.simplenote.data.preferences.ThemePreferences  Preferences 6io.github.simplenote.data.preferences.ThemePreferences  	ThemeMode 6io.github.simplenote.data.preferences.ThemePreferences  
ThemeSettings 6io.github.simplenote.data.preferences.ThemePreferences  Either $io.github.simplenote.data.repository  Flow $io.github.simplenote.data.repository  Int $io.github.simplenote.data.repository  List $io.github.simplenote.data.repository  Long $io.github.simplenote.data.repository  Note $io.github.simplenote.data.repository  	NoteColor $io.github.simplenote.data.repository  NoteDao $io.github.simplenote.data.repository  	NoteError $io.github.simplenote.data.repository  NoteRepository $io.github.simplenote.data.repository  String $io.github.simplenote.data.repository  Unit $io.github.simplenote.data.repository  SampleDataGenerator io.github.simplenote.data.util  AppError !io.github.simplenote.domain.model  Boolean !io.github.simplenote.domain.model  Color !io.github.simplenote.domain.model  
DatabaseError !io.github.simplenote.domain.model  Instant !io.github.simplenote.domain.model  Int !io.github.simplenote.domain.model  Long !io.github.simplenote.domain.model  Note !io.github.simplenote.domain.model  	NoteColor !io.github.simplenote.domain.model  	NoteError !io.github.simplenote.domain.model  NoteValidation !io.github.simplenote.domain.model  String !io.github.simplenote.domain.model  	ThemeMode !io.github.simplenote.domain.model  
ThemeSettings !io.github.simplenote.domain.model  Boolean &io.github.simplenote.domain.model.Note  Instant &io.github.simplenote.domain.model.Note  Long &io.github.simplenote.domain.model.Note  Note &io.github.simplenote.domain.model.Note  	NoteColor &io.github.simplenote.domain.model.Note  String &io.github.simplenote.domain.model.Note  Color +io.github.simplenote.domain.model.NoteColor  Int +io.github.simplenote.domain.model.NoteColor  String +io.github.simplenote.domain.model.NoteColor  AppError +io.github.simplenote.domain.model.NoteError  
DatabaseError +io.github.simplenote.domain.model.NoteError  	NoteError +io.github.simplenote.domain.model.NoteError  String +io.github.simplenote.domain.model.NoteError  ValidationError +io.github.simplenote.domain.model.NoteError  AppError 4io.github.simplenote.domain.model.NoteError.AppError  String 4io.github.simplenote.domain.model.NoteError.AppError  
DatabaseError 9io.github.simplenote.domain.model.NoteError.DatabaseError  String 9io.github.simplenote.domain.model.NoteError.DatabaseError  String +io.github.simplenote.domain.model.ThemeMode  Boolean /io.github.simplenote.domain.model.ThemeSettings  	ThemeMode /io.github.simplenote.domain.model.ThemeSettings  Either &io.github.simplenote.domain.repository  Flow &io.github.simplenote.domain.repository  Int &io.github.simplenote.domain.repository  List &io.github.simplenote.domain.repository  Long &io.github.simplenote.domain.repository  Note &io.github.simplenote.domain.repository  	NoteColor &io.github.simplenote.domain.repository  	NoteError &io.github.simplenote.domain.repository  NoteRepository &io.github.simplenote.domain.repository  String &io.github.simplenote.domain.repository  Unit &io.github.simplenote.domain.repository  Boolean #io.github.simplenote.domain.usecase  CreateNoteUseCase #io.github.simplenote.domain.usecase  DeleteNoteUseCase #io.github.simplenote.domain.usecase  Either #io.github.simplenote.domain.usecase  Flow #io.github.simplenote.domain.usecase  GetNotesUseCase #io.github.simplenote.domain.usecase  GetThemeSettingsUseCase #io.github.simplenote.domain.usecase  Int #io.github.simplenote.domain.usecase  List #io.github.simplenote.domain.usecase  Long #io.github.simplenote.domain.usecase  Note #io.github.simplenote.domain.usecase  	NoteColor #io.github.simplenote.domain.usecase  	NoteError #io.github.simplenote.domain.usecase  NoteRepository #io.github.simplenote.domain.usecase  SortBy #io.github.simplenote.domain.usecase  String #io.github.simplenote.domain.usecase  	ThemeMode #io.github.simplenote.domain.usecase  ThemePreferences #io.github.simplenote.domain.usecase  
ThemeSettings #io.github.simplenote.domain.usecase  Unit #io.github.simplenote.domain.usecase  UpdateNoteUseCase #io.github.simplenote.domain.usecase  UpdateThemeSettingsUseCase #io.github.simplenote.domain.usecase  Either 3io.github.simplenote.domain.usecase.GetNotesUseCase  Flow 3io.github.simplenote.domain.usecase.GetNotesUseCase  List 3io.github.simplenote.domain.usecase.GetNotesUseCase  Long 3io.github.simplenote.domain.usecase.GetNotesUseCase  Note 3io.github.simplenote.domain.usecase.GetNotesUseCase  	NoteColor 3io.github.simplenote.domain.usecase.GetNotesUseCase  	NoteError 3io.github.simplenote.domain.usecase.GetNotesUseCase  NoteRepository 3io.github.simplenote.domain.usecase.GetNotesUseCase  SortBy 3io.github.simplenote.domain.usecase.GetNotesUseCase  String 3io.github.simplenote.domain.usecase.GetNotesUseCase  ValidationError -io.github.simplenote.domain.usecase.NoteError  AppDestination io.github.simplenote.navigation  
Composable io.github.simplenote.navigation  Long io.github.simplenote.navigation  Modifier io.github.simplenote.navigation  NavHostController io.github.simplenote.navigation  Serializable io.github.simplenote.navigation  AppDestination .io.github.simplenote.navigation.AppDestination  Long .io.github.simplenote.navigation.AppDestination  Serializable .io.github.simplenote.navigation.AppDestination  Boolean ,io.github.simplenote.presentation.components  ColorBottomSheet ,io.github.simplenote.presentation.components  
Composable ,io.github.simplenote.presentation.components  DeleteConfirmationDialog ,io.github.simplenote.presentation.components  
EmptyState ,io.github.simplenote.presentation.components  GoogleKeepSearchBar ,io.github.simplenote.presentation.components  List ,io.github.simplenote.presentation.components  Modifier ,io.github.simplenote.presentation.components  Note ,io.github.simplenote.presentation.components  	NoteColor ,io.github.simplenote.presentation.components  	NoteError ,io.github.simplenote.presentation.components  NoteItem ,io.github.simplenote.presentation.components  OptIn ,io.github.simplenote.presentation.components  Preview ,io.github.simplenote.presentation.components  PreviewLightDark ,io.github.simplenote.presentation.components  SampleKeepBottomSheetRight ,io.github.simplenote.presentation.components  String ,io.github.simplenote.presentation.components  Unit ,io.github.simplenote.presentation.components  Boolean 'io.github.simplenote.presentation.model  
ImmutableList 'io.github.simplenote.presentation.model  Long 'io.github.simplenote.presentation.model  NavigationEvent 'io.github.simplenote.presentation.model  Note 'io.github.simplenote.presentation.model  	NoteColor 'io.github.simplenote.presentation.model  NoteEditorUiEvent 'io.github.simplenote.presentation.model  NoteEditorUiState 'io.github.simplenote.presentation.model  	NoteError 'io.github.simplenote.presentation.model  NotesUiEvent 'io.github.simplenote.presentation.model  NotesUiState 'io.github.simplenote.presentation.model  SortBy 'io.github.simplenote.presentation.model  String 'io.github.simplenote.presentation.model  Long 7io.github.simplenote.presentation.model.NavigationEvent  NavigationEvent 7io.github.simplenote.presentation.model.NavigationEvent  Note 7io.github.simplenote.presentation.model.NavigationEvent  	NoteError 7io.github.simplenote.presentation.model.NavigationEvent  Long 9io.github.simplenote.presentation.model.NoteEditorUiEvent  	NoteColor 9io.github.simplenote.presentation.model.NoteEditorUiEvent  NoteEditorUiEvent 9io.github.simplenote.presentation.model.NoteEditorUiEvent  String 9io.github.simplenote.presentation.model.NoteEditorUiEvent  Boolean 4io.github.simplenote.presentation.model.NotesUiEvent  Long 4io.github.simplenote.presentation.model.NotesUiEvent  Note 4io.github.simplenote.presentation.model.NotesUiEvent  	NoteColor 4io.github.simplenote.presentation.model.NotesUiEvent  NotesUiEvent 4io.github.simplenote.presentation.model.NotesUiEvent  NotesUiState 4io.github.simplenote.presentation.model.NotesUiEvent  String 4io.github.simplenote.presentation.model.NotesUiEvent  SortBy Aio.github.simplenote.presentation.model.NotesUiEvent.NotesUiState  Boolean 4io.github.simplenote.presentation.model.NotesUiState  
ImmutableList 4io.github.simplenote.presentation.model.NotesUiState  Long 4io.github.simplenote.presentation.model.NotesUiState  Note 4io.github.simplenote.presentation.model.NotesUiState  	NoteError 4io.github.simplenote.presentation.model.NotesUiState  SortBy 4io.github.simplenote.presentation.model.NotesUiState  String 4io.github.simplenote.presentation.model.NotesUiState  
Composable (io.github.simplenote.presentation.screen  Long (io.github.simplenote.presentation.screen  Modifier (io.github.simplenote.presentation.screen  NoteEditorScreen (io.github.simplenote.presentation.screen  NoteEditorViewModel (io.github.simplenote.presentation.screen  NotesScreen (io.github.simplenote.presentation.screen  NotesViewModel (io.github.simplenote.presentation.screen  OptIn (io.github.simplenote.presentation.screen  Preview (io.github.simplenote.presentation.screen  SettingsScreen (io.github.simplenote.presentation.screen  SettingsViewModel (io.github.simplenote.presentation.screen  Unit (io.github.simplenote.presentation.screen  Boolean +io.github.simplenote.presentation.viewmodel  CreateNoteUseCase +io.github.simplenote.presentation.viewmodel  DeleteNoteUseCase +io.github.simplenote.presentation.viewmodel  GetNotesUseCase +io.github.simplenote.presentation.viewmodel  GetThemeSettingsUseCase +io.github.simplenote.presentation.viewmodel  Job +io.github.simplenote.presentation.viewmodel  Long +io.github.simplenote.presentation.viewmodel  NavigationEvent +io.github.simplenote.presentation.viewmodel  Note +io.github.simplenote.presentation.viewmodel  	NoteColor +io.github.simplenote.presentation.viewmodel  NoteEditorUiEvent +io.github.simplenote.presentation.viewmodel  NoteEditorUiState +io.github.simplenote.presentation.viewmodel  NoteEditorViewModel +io.github.simplenote.presentation.viewmodel  NotesUiEvent +io.github.simplenote.presentation.viewmodel  NotesUiState +io.github.simplenote.presentation.viewmodel  NotesViewModel +io.github.simplenote.presentation.viewmodel  SettingsViewModel +io.github.simplenote.presentation.viewmodel  
SharedFlow +io.github.simplenote.presentation.viewmodel  	StateFlow +io.github.simplenote.presentation.viewmodel  String +io.github.simplenote.presentation.viewmodel  	ThemeMode +io.github.simplenote.presentation.viewmodel  
ThemeSettings +io.github.simplenote.presentation.viewmodel  UpdateNoteUseCase +io.github.simplenote.presentation.viewmodel  UpdateThemeSettingsUseCase +io.github.simplenote.presentation.viewmodel  	ViewModel +io.github.simplenote.presentation.viewmodel  SortBy 8io.github.simplenote.presentation.viewmodel.NotesUiState  Boolean io.github.simplenote.ui.theme  
Composable io.github.simplenote.ui.theme  SimpleNOTETheme io.github.simplenote.ui.theme  	ThemeMode io.github.simplenote.ui.theme  Unit io.github.simplenote.ui.theme  	Generated javax.annotation.processing  Any kotlin  Array kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  Lazy kotlin  Long kotlin  OptIn kotlin  String kotlin  Suppress kotlin  Color kotlin.Enum  Int kotlin.Enum  String kotlin.Enum  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  KClass kotlin.reflect  
ImmutableList kotlinx.collections.immutable  persistentListOf kotlinx.collections.immutable  toImmutableList kotlinx.collections.immutable  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableSharedFlow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  
SharedFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asSharedFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  first kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow  map kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  Clock kotlinx.datetime  Instant kotlinx.datetime  Serializable kotlinx.serialization  
koinViewModel org.koin.androidx.compose  aspectRatio "androidx.compose.foundation.layout                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                