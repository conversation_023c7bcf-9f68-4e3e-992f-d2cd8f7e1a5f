package io.github.simplenote.domain.model

/**
 * Sealed class representing all possible errors in the Note domain.
 * Used with Arrow's Either for functional error handling.
 */
sealed class NoteError(val message: String) {
    
    /**
     * Database-related errors
     */
    sealed class DatabaseError(message: String) : NoteError(message) {
        data object NotFound : DatabaseError("Note not found")
        data class InsertFailed(val cause: String) : DatabaseError("Failed to insert note: $cause")
        data class UpdateFailed(val cause: String) : DatabaseError("Failed to update note: $cause")
        data class DeleteFailed(val cause: String) : DatabaseError("Failed to delete note: $cause")
        data class QueryFailed(val cause: String) : DatabaseError("Failed to query notes: $cause")
    }
    
    /**
     * Validation errors
     */
    sealed class ValidationError(message: String) : NoteError(message) {
        data object EmptyTitle : ValidationError("Note title cannot be empty")
        data object EmptyContent : ValidationError("Note content cannot be empty")
        data object TitleTooLong : ValidationError("Note title is too long (max 200 characters)")
        data object ContentTooLong : ValidationError("Note content is too long (max 10000 characters)")
        data object InvalidColor : ValidationError("Invalid note color")
    }
    
    /**
     * General application errors
     */
    sealed class AppError(message: String) : NoteError(message) {
        data class Unknown(val cause: String) : AppError("Unknown error: $cause")
        data object NetworkUnavailable : AppError("Network is unavailable")
        data object StorageUnavailable : AppError("Storage is unavailable")
    }
}

/**
 * Validation constants
 */
object NoteValidation {
    const val MAX_TITLE_LENGTH = 200
    const val MAX_CONTENT_LENGTH = 10000
}
