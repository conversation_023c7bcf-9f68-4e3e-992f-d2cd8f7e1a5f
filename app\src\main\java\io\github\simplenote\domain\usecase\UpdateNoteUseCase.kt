package io.github.simplenote.domain.usecase

import arrow.core.Either
import arrow.core.left
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.model.NoteError
import io.github.simplenote.domain.model.NoteValidation
import io.github.simplenote.domain.repository.NoteRepository
import kotlinx.datetime.Clock

/**
 * Use case for updating existing notes with validation.
 */
class UpdateNoteUseCase(
    private val repository: NoteRepository
) {
    
    /**
     * Update an existing note with validation
     */
    suspend operator fun invoke(
        note: Note,
        newTitle: String = note.title,
        newContent: String = note.content,
        newColor: NoteColor = note.color
    ): Either<NoteError, Note> {
        
        // Validate input
        val validationResult = validateNoteInput(newTitle, newContent)
        if (validationResult != null) {
            return validationResult.left()
        }
        
        // Update note with current timestamp
        val currentTime = Clock.System.now()
        val updatedNote = note.update(
            title = newTitle.trim(),
            content = newContent.trim(),
            color = newColor,
            timestamp = currentTime
        )
        
        // Save to repository
        return repository.updateNote(updatedNote)
    }
    
    /**
     * Update only the color of a note
     */
    suspend fun updateColor(noteId: Long, color: NoteColor): Either<NoteError, Unit> {
        return repository.updateNoteColor(noteId, color)
    }
    
    /**
     * Update only the title of a note
     */
    suspend fun updateTitle(note: Note, newTitle: String): Either<NoteError, Note> {
        return invoke(note, newTitle = newTitle)
    }
    
    /**
     * Update only the content of a note
     */
    suspend fun updateContent(note: Note, newContent: String): Either<NoteError, Note> {
        return invoke(note, newContent = newContent)
    }
    
    /**
     * Validate note input data
     */
    private fun validateNoteInput(title: String, content: String): NoteError.ValidationError? {
        return when {
            title.isBlank() && content.isBlank() -> 
                NoteError.ValidationError.EmptyContent
            
            title.length > NoteValidation.MAX_TITLE_LENGTH -> 
                NoteError.ValidationError.TitleTooLong
            
            content.length > NoteValidation.MAX_CONTENT_LENGTH -> 
                NoteError.ValidationError.ContentTooLong
            
            else -> null
        }
    }
}
