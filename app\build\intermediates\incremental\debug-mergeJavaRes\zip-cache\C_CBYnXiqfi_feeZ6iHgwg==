[{"key": "androidx/compose/material/ripple/AndroidRippleIndicationInstance$onInvalidateRipple$1.class", "name": "androidx/compose/material/ripple/AndroidRippleIndicationInstance$onInvalidateRipple$1.class", "size": 1677, "crc": 383764628}, {"key": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "size": 12459, "crc": -253389465}, {"key": "androidx/compose/material/ripple/AndroidRippleNode$addRipple$1$1$1.class", "name": "androidx/compose/material/ripple/AndroidRippleNode$addRipple$1$1$1.class", "size": 1638, "crc": 1162044238}, {"key": "androidx/compose/material/ripple/AndroidRippleNode.class", "name": "androidx/compose/material/ripple/AndroidRippleNode.class", "size": 8817, "crc": -1055168457}, {"key": "androidx/compose/material/ripple/CommonRipple.class", "name": "androidx/compose/material/ripple/CommonRipple.class", "size": 5677, "crc": 1290126307}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "size": 4462, "crc": -152175446}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "size": 9305, "crc": 358943482}, {"key": "androidx/compose/material/ripple/CommonRippleNode$addRipple$2.class", "name": "androidx/compose/material/ripple/CommonRippleNode$addRipple$2.class", "size": 4483, "crc": -1250259037}, {"key": "androidx/compose/material/ripple/CommonRippleNode.class", "name": "androidx/compose/material/ripple/CommonRippleNode.class", "size": 9631, "crc": 1327443013}, {"key": "androidx/compose/material/ripple/DebugRippleTheme.class", "name": "androidx/compose/material/ripple/DebugRippleTheme.class", "size": 3086, "crc": 156743916}, {"key": "androidx/compose/material/ripple/PlatformRipple.class", "name": "androidx/compose/material/ripple/PlatformRipple.class", "size": 6732, "crc": -2011195788}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1$1.class", "size": 2980, "crc": 1917629621}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "size": 4201, "crc": -764096171}, {"key": "androidx/compose/material/ripple/Ripple.class", "name": "androidx/compose/material/ripple/Ripple.class", "size": 9206, "crc": -1614188145}, {"key": "androidx/compose/material/ripple/RippleAlpha.class", "name": "androidx/compose/material/ripple/RippleAlpha.class", "size": 2585, "crc": 1134105483}, {"key": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "size": 1774, "crc": -1460894024}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "size": 4149, "crc": -445106301}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "size": 4165, "crc": -1157592549}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "size": 4158, "crc": -1681328834}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "size": 3809, "crc": -501683746}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "size": 4155, "crc": 1691607812}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "size": 3601, "crc": -883764751}, {"key": "androidx/compose/material/ripple/RippleAnimation.class", "name": "androidx/compose/material/ripple/RippleAnimation.class", "size": 12348, "crc": -1206473550}, {"key": "androidx/compose/material/ripple/RippleAnimationKt.class", "name": "androidx/compose/material/ripple/RippleAnimationKt.class", "size": 2779, "crc": 799869655}, {"key": "androidx/compose/material/ripple/RippleContainer.class", "name": "androidx/compose/material/ripple/RippleContainer.class", "size": 5285, "crc": -1397088536}, {"key": "androidx/compose/material/ripple/RippleHostKey.class", "name": "androidx/compose/material/ripple/RippleHostKey.class", "size": 489, "crc": -2128754303}, {"key": "androidx/compose/material/ripple/RippleHostMap.class", "name": "androidx/compose/material/ripple/RippleHostMap.class", "size": 3238, "crc": -611290487}, {"key": "androidx/compose/material/ripple/RippleHostView$Companion.class", "name": "androidx/compose/material/ripple/RippleHostView$Companion.class", "size": 1065, "crc": -1203003571}, {"key": "androidx/compose/material/ripple/RippleHostView.class", "name": "androidx/compose/material/ripple/RippleHostView.class", "size": 8196, "crc": 1558333018}, {"key": "androidx/compose/material/ripple/RippleIndicationInstance$stateLayer$1.class", "name": "androidx/compose/material/ripple/RippleIndicationInstance$stateLayer$1.class", "size": 1589, "crc": 1546933980}, {"key": "androidx/compose/material/ripple/RippleIndicationInstance.class", "name": "androidx/compose/material/ripple/RippleIndicationInstance.class", "size": 4810, "crc": -1080622096}, {"key": "androidx/compose/material/ripple/RippleKt.class", "name": "androidx/compose/material/ripple/RippleKt.class", "size": 8102, "crc": 1235430278}, {"key": "androidx/compose/material/ripple/RippleNode$onAttach$1$1.class", "name": "androidx/compose/material/ripple/RippleNode$onAttach$1$1.class", "size": 3628, "crc": -1832175426}, {"key": "androidx/compose/material/ripple/RippleNode$onAttach$1.class", "name": "androidx/compose/material/ripple/RippleNode$onAttach$1.class", "size": 3844, "crc": 471175588}, {"key": "androidx/compose/material/ripple/RippleNode.class", "name": "androidx/compose/material/ripple/RippleNode.class", "size": 12138, "crc": 1426069444}, {"key": "androidx/compose/material/ripple/RippleTheme$Companion.class", "name": "androidx/compose/material/ripple/RippleTheme$Companion.class", "size": 3216, "crc": 174326626}, {"key": "androidx/compose/material/ripple/RippleTheme.class", "name": "androidx/compose/material/ripple/RippleTheme.class", "size": 1762, "crc": 1128417110}, {"key": "androidx/compose/material/ripple/RippleThemeKt$LocalRippleTheme$1.class", "name": "androidx/compose/material/ripple/RippleThemeKt$LocalRippleTheme$1.class", "size": 1372, "crc": -803616162}, {"key": "androidx/compose/material/ripple/RippleThemeKt.class", "name": "androidx/compose/material/ripple/RippleThemeKt.class", "size": 2947, "crc": -246907677}, {"key": "androidx/compose/material/ripple/Ripple_androidKt.class", "name": "androidx/compose/material/ripple/Ripple_androidKt.class", "size": 4723, "crc": 1949496417}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "size": 4187, "crc": 135636437}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "size": 4125, "crc": 736989324}, {"key": "androidx/compose/material/ripple/StateLayer.class", "name": "androidx/compose/material/ripple/StateLayer.class", "size": 9839, "crc": -1906158590}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "size": 1015, "crc": 315430872}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "size": 1324, "crc": 2028973594}, {"key": "androidx/compose/material/ripple/UnprojectedRipple.class", "name": "androidx/compose/material/ripple/UnprojectedRipple.class", "size": 4507, "crc": 2071905506}, {"key": "META-INF/androidx.compose.material_material-ripple.version", "name": "META-INF/androidx.compose.material_material-ripple.version", "size": 6, "crc": 333960004}, {"key": "META-INF/material-ripple_release.kotlin_module", "name": "META-INF/material-ripple_release.kotlin_module", "size": 122, "crc": 1199081355}]