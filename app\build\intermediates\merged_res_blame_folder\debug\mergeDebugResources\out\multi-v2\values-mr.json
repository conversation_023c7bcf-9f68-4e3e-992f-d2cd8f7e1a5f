{"logs": [{"outputFile": "io.github.simplenote.app-mergeDebugResources-66:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8734d346b2e96ba5091fe03752072eab\\transformed\\core-1.16.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2868,2968,3072,3173,3276,3378,3483,11368", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "2963,3067,3168,3271,3373,3478,3595,11464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250d8cc481173cc0c0524c32c74c00eb\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,355,452,550,637,723,808,897,980,1060,1145,1216,1300,1376,1452,1528,1604,1670", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "268,350,447,545,632,718,803,892,975,1055,1140,1211,1295,1371,1447,1523,1599,1665,1783"}, "to": {"startLines": "37,38,39,40,41,42,43,102,103,104,105,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3600,3694,3776,3873,3971,4058,4144,10568,10657,10740,10820,10985,11056,11140,11216,11292,11469,11545,11611", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "3689,3771,3868,3966,4053,4139,4224,10652,10735,10815,10900,11051,11135,11211,11287,11363,11540,11606,11724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96ccc887d394ddaa1dd6d0e3b9de1bba\\transformed\\foundation-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "29,116,117", "startColumns": "4,4,4", "startOffsets": "2795,11729,11814", "endColumns": "72,84,84", "endOffsets": "2863,11809,11894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d61245d3231dfae84075323e154f2e0\\transformed\\material3-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4687,4772,4862,4962,5042,5125,5224,5323,5420,5519,5606,5710,5810,5914,6032,6112,6212", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4682,4767,4857,4957,5037,5120,5219,5318,5415,5514,5601,5705,5805,5909,6027,6107,6207,6301"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4229,4354,4477,4588,4713,4816,4916,5031,5167,5290,5436,5521,5627,5718,5816,5930,6060,6171,6306,6440,6568,6746,6871,6987,7106,7231,7323,7418,7538,7667,7767,7870,7979,8116,8258,8373,8471,8547,8650,8754,8861,8946,9036,9136,9216,9299,9398,9497,9594,9693,9780,9884,9984,10088,10206,10286,10386", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "4349,4472,4583,4708,4811,4911,5026,5162,5285,5431,5516,5622,5713,5811,5925,6055,6166,6301,6435,6563,6741,6866,6982,7101,7226,7318,7413,7533,7662,7762,7865,7974,8111,8253,8368,8466,8542,8645,8749,8856,8941,9031,9131,9211,9294,9393,9492,9589,9688,9775,9879,9979,10083,10201,10281,10381,10475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a52cd15d91b3b91eba876f6457455010\\transformed\\appcompat-1.7.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,10905", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,10980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8f6b7cfc7dddc58c7ce7adfd537c1781\\transformed\\material-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "10480", "endColumns": "87", "endOffsets": "10563"}}]}]}