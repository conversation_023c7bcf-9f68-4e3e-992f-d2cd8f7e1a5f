package io.github.simplenote.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import io.github.simplenote.data.local.entity.NoteEntity
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for Note operations.
 * Provides reactive queries using Flow for real-time UI updates.
 */
@Dao
interface NoteDao {
    
    /**
     * Get all notes ordered by updated date (most recent first)
     */
    @Query("SELECT * FROM notes ORDER BY updated_at DESC")
    fun getAllNotes(): Flow<List<NoteEntity>>
    
    /**
     * Get all notes ordered by created date (most recent first)
     */
    @Query("SELECT * FROM notes ORDER BY created_at DESC")
    fun getAllNotesByCreatedDate(): Flow<List<NoteEntity>>
    
    /**
     * Get all notes ordered by title alphabetically
     */
    @Query("SELECT * FROM notes ORDER BY title ASC")
    fun getAllNotesByTitle(): Flow<List<NoteEntity>>
    
    /**
     * Get a specific note by ID
     */
    @Query("SELECT * FROM notes WHERE id = :id")
    suspend fun getNoteById(id: Long): NoteEntity?
    
    /**
     * Search notes by title or content
     */
    @Query("""
        SELECT * FROM notes 
        WHERE title LIKE '%' || :query || '%' 
        OR content LIKE '%' || :query || '%'
        ORDER BY updated_at DESC
    """)
    fun searchNotes(query: String): Flow<List<NoteEntity>>
    
    /**
     * Get notes by color
     */
    @Query("SELECT * FROM notes WHERE color_id = :colorId ORDER BY updated_at DESC")
    fun getNotesByColor(colorId: Int): Flow<List<NoteEntity>>
    
    /**
     * Insert a new note
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNote(note: NoteEntity): Long
    
    /**
     * Insert multiple notes
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotes(notes: List<NoteEntity>): List<Long>
    
    /**
     * Update an existing note
     */
    @Update
    suspend fun updateNote(note: NoteEntity): Int
    
    /**
     * Delete a note
     */
    @Delete
    suspend fun deleteNote(note: NoteEntity): Int
    
    /**
     * Delete a note by ID
     */
    @Query("DELETE FROM notes WHERE id = :id")
    suspend fun deleteNoteById(id: Long): Int
    
    /**
     * Delete all notes
     */
    @Query("DELETE FROM notes")
    suspend fun deleteAllNotes(): Int
    
    /**
     * Get the count of all notes
     */
    @Query("SELECT COUNT(*) FROM notes")
    suspend fun getNotesCount(): Int
    
    /**
     * Update note color
     */
    @Query("UPDATE notes SET color_id = :colorId, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateNoteColor(id: Long, colorId: Int, updatedAt: Long): Int
}
