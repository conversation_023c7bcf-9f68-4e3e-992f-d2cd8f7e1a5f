package io.github.simplenote.data.util

import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.random.Random
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours

/**
 * Utility class for generating sample data for development and testing purposes.
 */
object SampleDataGenerator {
    
    private val sampleTitles = listOf(
        "Meeting Notes",
        "Shopping List",
        "Project Ideas",
        "Daily Journal",
        "Book Recommendations",
        "Travel Plans",
        "Recipe Collection",
        "Workout Routine",
        "Budget Planning",
        "Learning Goals",
        "Gift Ideas",
        "Home Improvement",
        "Garden Planning",
        "Movie Watchlist",
        "Important Contacts",
        "Password Reminders",
        "Health Tracker",
        "Car Maintenance",
        "Weekend Plans",
        "Work Tasks",
        "Inspiration Quotes",
        "Photography Ideas",
        "Music Playlist",
        "Coding Notes",
        "Language Learning",
        "Meditation Log",
        "Dream Journal",
        "Gratitude List",
        "Business Ideas",
        "Random Thoughts"
    )
    
    private val sampleContents = listOf(
        // Short notes
        "Remember to call mom",
        "Buy milk, eggs, bread",
        "Meeting at 3 PM",
        "Check email",
        "Water the plants",
        
        // Medium notes
        """Today was a productive day. Completed the project proposal and sent it to the client. 
        |Need to follow up next week. Also scheduled a team meeting for Thursday to discuss the new requirements.""".trimMargin(),
        
        """Shopping list for this week:
        |- Groceries: milk, eggs, bread, fruits
        |- Household: detergent, paper towels
        |- Personal: shampoo, toothpaste""".trimMargin(),
        
        """Project Ideas:
        |1. Mobile app for habit tracking
        |2. Web platform for local businesses
        |3. AI-powered note-taking assistant
        |4. Sustainable living blog""".trimMargin(),
        
        """Travel Plans for Summer:
        |- Visit Japan in July
        |- Book flights by end of month
        |- Research hotels in Tokyo and Kyoto
        |- Learn basic Japanese phrases""".trimMargin(),
        
        // Longer content
        """Daily Reflection - March 15th
        |
        |Today I learned something important about time management. Instead of trying to multitask, 
        |I focused on one task at a time and found myself much more productive. The morning was spent 
        |working on the presentation, and I was able to complete it without any distractions.
        |
        |In the afternoon, I had a great conversation with Sarah about the new project. She brought up 
        |some excellent points about user experience that I hadn't considered. We decided to schedule 
        |a brainstorming session next week to explore these ideas further.
        |
        |Evening was relaxing - read a few chapters of the new book and practiced guitar for 30 minutes. 
        |Small consistent efforts really do add up over time.""".trimMargin(),
        
        """Recipe: Homemade Pizza
        |
        |Ingredients:
        |- 2 cups flour
        |- 1 tsp yeast
        |- 1 cup warm water
        |- 2 tbsp olive oil
        |- 1 tsp salt
        |- Pizza sauce
        |- Mozzarella cheese
        |- Your favorite toppings
        |
        |Instructions:
        |1. Mix flour, yeast, and salt in a bowl
        |2. Add warm water and olive oil, knead for 10 minutes
        |3. Let rise for 1 hour
        |4. Roll out, add toppings, bake at 450°F for 12-15 minutes""".trimMargin(),
        
        """Workout Plan - Week 1
        |
        |Monday: Upper Body
        |- Push-ups: 3 sets of 15
        |- Pull-ups: 3 sets of 8
        |- Dumbbell rows: 3 sets of 12
        |- Shoulder press: 3 sets of 10
        |
        |Wednesday: Lower Body
        |- Squats: 3 sets of 20
        |- Lunges: 3 sets of 15 each leg
        |- Deadlifts: 3 sets of 12
        |- Calf raises: 3 sets of 20
        |
        |Friday: Full Body
        |- Burpees: 3 sets of 10
        |- Mountain climbers: 3 sets of 30 seconds
        |- Plank: 3 sets of 1 minute""".trimMargin(),
        
        """Book Notes: "Atomic Habits" by James Clear
        |
        |Key Takeaways:
        |1. Focus on systems, not goals
        |2. Make good habits obvious, attractive, easy, and satisfying
        |3. The compound effect of small improvements
        |4. Identity-based habits are more powerful than outcome-based habits
        |
        |Favorite Quote: "You do not rise to the level of your goals. You fall to the level of your systems."
        |
        |Action Items:
        |- Start with 2-minute habits
        |- Use habit stacking
        |- Design environment for success""".trimMargin()
    )
    
    /**
     * Generates a list of sample notes with varied content, colors, and timestamps.
     */
    fun generateSampleNotes(count: Int = 30): List<Note> {
        val notes = mutableListOf<Note>()
        val now = Clock.System.now()
        val colors = NoteColor.entries.toList()
        
        repeat(count) { index ->
            val title = if (Random.nextBoolean()) {
                sampleTitles[index % sampleTitles.size]
            } else {
                "" // Some notes without titles
            }
            
            val content = sampleContents[index % sampleContents.size]
            val color = colors[Random.nextInt(colors.size)]
            
            // Generate timestamps spread across the last 30 days
            val daysAgo = Random.nextInt(0, 30)
            val hoursAgo = Random.nextInt(0, 24)
            val timestamp = now - daysAgo.days - hoursAgo.hours
            
            val note = Note.create(
                title = title,
                content = content,
                color = color,
                timestamp = timestamp
            )
            
            notes.add(note)
        }
        
        return notes.sortedByDescending { it.updatedAt }
    }
}
