package io.github.simplenote.di

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import io.github.simplenote.data.preferences.ThemePreferences
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module

/**
 * Koin module for preferences and DataStore dependencies.
 */
val preferencesModule = module {
    
    // DataStore
    single<DataStore<Preferences>> {
        androidContext().preferencesDataStore
    }
    
    // Theme Preferences
    single {
        ThemePreferences(get())
    }
}

// DataStore extension
private val android.content.Context.preferencesDataStore: DataStore<Preferences> by preferencesDataStore(
    name = "app_preferences"
)
