[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/annotation.kotlin_module", "name": "META-INF/annotation.kotlin_module", "size": 24, "crc": -1111743755}, {"key": "androidx/annotation/AnimRes.class", "name": "androidx/annotation/AnimRes.class", "size": 1002, "crc": 1215425483}, {"key": "androidx/annotation/AnimatorRes.class", "name": "androidx/annotation/AnimatorRes.class", "size": 1014, "crc": 847503277}, {"key": "androidx/annotation/AnyRes.class", "name": "androidx/annotation/AnyRes.class", "size": 999, "crc": 288344926}, {"key": "androidx/annotation/AnyThread.class", "name": "androidx/annotation/AnyThread.class", "size": 1047, "crc": 1757512208}, {"key": "androidx/annotation/ArrayRes.class", "name": "androidx/annotation/ArrayRes.class", "size": 1005, "crc": 1552816904}, {"key": "androidx/annotation/AttrRes.class", "name": "androidx/annotation/AttrRes.class", "size": 1002, "crc": -1011220444}, {"key": "androidx/annotation/BinderThread.class", "name": "androidx/annotation/BinderThread.class", "size": 1060, "crc": 49632340}, {"key": "androidx/annotation/BoolRes.class", "name": "androidx/annotation/BoolRes.class", "size": 1002, "crc": -1117128471}, {"key": "androidx/annotation/CallSuper.class", "name": "androidx/annotation/CallSuper.class", "size": 919, "crc": 103751090}, {"key": "androidx/annotation/CheckResult.class", "name": "androidx/annotation/CheckResult.class", "size": 1039, "crc": -1231230383}, {"key": "androidx/annotation/ChecksSdkIntAtLeast.class", "name": "androidx/annotation/ChecksSdkIntAtLeast.class", "size": 1341, "crc": 1099624993}, {"key": "androidx/annotation/ColorInt.class", "name": "androidx/annotation/ColorInt.class", "size": 1001, "crc": 1037506261}, {"key": "androidx/annotation/ColorLong.class", "name": "androidx/annotation/ColorLong.class", "size": 996, "crc": 1741857305}, {"key": "androidx/annotation/ColorRes.class", "name": "androidx/annotation/ColorRes.class", "size": 1005, "crc": 2017614750}, {"key": "androidx/annotation/ContentView.class", "name": "androidx/annotation/ContentView.class", "size": 794, "crc": 1678667135}, {"key": "androidx/annotation/DeprecatedSinceApi.class", "name": "androidx/annotation/DeprecatedSinceApi.class", "size": 1214, "crc": -1226400833}, {"key": "androidx/annotation/DimenRes.class", "name": "androidx/annotation/DimenRes.class", "size": 1005, "crc": -900109000}, {"key": "androidx/annotation/Dimension$Companion.class", "name": "androidx/annotation/Dimension$Companion.class", "size": 854, "crc": 794829108}, {"key": "androidx/annotation/Dimension.class", "name": "androidx/annotation/Dimension.class", "size": 1555, "crc": -1737954606}, {"key": "androidx/annotation/Discouraged.class", "name": "androidx/annotation/Discouraged.class", "size": 1071, "crc": 2116577400}, {"key": "androidx/annotation/DisplayContext.class", "name": "androidx/annotation/DisplayContext.class", "size": 977, "crc": 2007343067}, {"key": "androidx/annotation/DoNotInline.class", "name": "androidx/annotation/DoNotInline.class", "size": 846, "crc": -1895229479}, {"key": "androidx/annotation/DrawableRes.class", "name": "androidx/annotation/DrawableRes.class", "size": 1014, "crc": -678965102}, {"key": "androidx/annotation/EmptySuper.class", "name": "androidx/annotation/EmptySuper.class", "size": 876, "crc": 157401704}, {"key": "androidx/annotation/FloatRange.class", "name": "androidx/annotation/FloatRange.class", "size": 1365, "crc": -394151024}, {"key": "androidx/annotation/FontRes.class", "name": "androidx/annotation/FontRes.class", "size": 1002, "crc": 607091741}, {"key": "androidx/annotation/FractionRes.class", "name": "androidx/annotation/FractionRes.class", "size": 1014, "crc": -849263183}, {"key": "androidx/annotation/GravityInt.class", "name": "androidx/annotation/GravityInt.class", "size": 1011, "crc": -147453349}, {"key": "androidx/annotation/GuardedBy.class", "name": "androidx/annotation/GuardedBy.class", "size": 927, "crc": 869690227}, {"key": "androidx/annotation/HalfFloat.class", "name": "androidx/annotation/HalfFloat.class", "size": 1000, "crc": 2120465263}, {"key": "androidx/annotation/IdRes.class", "name": "androidx/annotation/IdRes.class", "size": 996, "crc": -1514734860}, {"key": "androidx/annotation/InspectableProperty$EnumEntry.class", "name": "androidx/annotation/InspectableProperty$EnumEntry.class", "size": 1102, "crc": -1916979014}, {"key": "androidx/annotation/InspectableProperty$FlagEntry.class", "name": "androidx/annotation/InspectableProperty$FlagEntry.class", "size": 1186, "crc": 1373896158}, {"key": "androidx/annotation/InspectableProperty$ValueType.class", "name": "androidx/annotation/InspectableProperty$ValueType.class", "size": 2300, "crc": 1853052077}, {"key": "androidx/annotation/InspectableProperty.class", "name": "androidx/annotation/InspectableProperty.class", "size": 2114, "crc": -1495724819}, {"key": "androidx/annotation/IntDef.class", "name": "androidx/annotation/IntDef.class", "size": 1018, "crc": -622019917}, {"key": "androidx/annotation/IntRange.class", "name": "androidx/annotation/IntRange.class", "size": 1213, "crc": -726745230}, {"key": "androidx/annotation/IntegerRes.class", "name": "androidx/annotation/IntegerRes.class", "size": 1011, "crc": 1803323834}, {"key": "androidx/annotation/InterpolatorRes.class", "name": "androidx/annotation/InterpolatorRes.class", "size": 1026, "crc": 2132993870}, {"key": "androidx/annotation/Keep.class", "name": "androidx/annotation/Keep.class", "size": 963, "crc": -1854165574}, {"key": "androidx/annotation/LayoutRes.class", "name": "androidx/annotation/LayoutRes.class", "size": 1008, "crc": 1179091065}, {"key": "androidx/annotation/LongDef.class", "name": "androidx/annotation/LongDef.class", "size": 1021, "crc": 38906921}, {"key": "androidx/annotation/MainThread.class", "name": "androidx/annotation/MainThread.class", "size": 1050, "crc": -1170789585}, {"key": "androidx/annotation/MenuRes.class", "name": "androidx/annotation/MenuRes.class", "size": 1002, "crc": 653459782}, {"key": "androidx/annotation/NavigationRes.class", "name": "androidx/annotation/NavigationRes.class", "size": 1020, "crc": 1795010382}, {"key": "androidx/annotation/NonNull.class", "name": "androidx/annotation/NonNull.class", "size": 1076, "crc": -1324465062}, {"key": "androidx/annotation/NonUiContext.class", "name": "androidx/annotation/NonUiContext.class", "size": 971, "crc": 1473898514}, {"key": "androidx/annotation/Nullable.class", "name": "androidx/annotation/Nullable.class", "size": 1079, "crc": 519820827}, {"key": "androidx/annotation/OpenForTesting.class", "name": "androidx/annotation/OpenForTesting.class", "size": 951, "crc": -514060772}, {"key": "androidx/annotation/PluralsRes.class", "name": "androidx/annotation/PluralsRes.class", "size": 1011, "crc": -2096656617}, {"key": "androidx/annotation/Px.class", "name": "androidx/annotation/Px.class", "size": 1075, "crc": -1261072717}, {"key": "androidx/annotation/RawRes.class", "name": "androidx/annotation/RawRes.class", "size": 999, "crc": 2025648318}, {"key": "androidx/annotation/ReplaceWith.class", "name": "androidx/annotation/ReplaceWith.class", "size": 971, "crc": -679985814}, {"key": "androidx/annotation/RequiresApi.class", "name": "androidx/annotation/RequiresApi.class", "size": 1180, "crc": 1378701574}, {"key": "androidx/annotation/RequiresExtension$Container.class", "name": "androidx/annotation/RequiresExtension$Container.class", "size": 1059, "crc": 236610253}, {"key": "androidx/annotation/RequiresExtension.class", "name": "androidx/annotation/RequiresExtension.class", "size": 1406, "crc": 310206004}, {"key": "androidx/annotation/RequiresFeature.class", "name": "androidx/annotation/RequiresFeature.class", "size": 1091, "crc": 1135534722}, {"key": "androidx/annotation/RequiresPermission$Read.class", "name": "androidx/annotation/RequiresPermission$Read.class", "size": 1096, "crc": -1096512624}, {"key": "androidx/annotation/RequiresPermission$Write.class", "name": "androidx/annotation/RequiresPermission$Write.class", "size": 1099, "crc": -866008142}, {"key": "androidx/annotation/RequiresPermission.class", "name": "androidx/annotation/RequiresPermission.class", "size": 1601, "crc": 1236747203}, {"key": "androidx/annotation/RestrictTo$Scope.class", "name": "androidx/annotation/RestrictTo$Scope.class", "size": 2247, "crc": -1023918886}, {"key": "androidx/annotation/RestrictTo.class", "name": "androidx/annotation/RestrictTo.class", "size": 1308, "crc": 942783740}, {"key": "androidx/annotation/ReturnThis.class", "name": "androidx/annotation/ReturnThis.class", "size": 893, "crc": -1909240851}, {"key": "androidx/annotation/Size.class", "name": "androidx/annotation/Size.class", "size": 1326, "crc": -1534725216}, {"key": "androidx/annotation/StringDef.class", "name": "androidx/annotation/StringDef.class", "size": 1005, "crc": -1857959037}, {"key": "androidx/annotation/StringRes.class", "name": "androidx/annotation/StringRes.class", "size": 1008, "crc": 1716338032}, {"key": "androidx/annotation/StyleRes.class", "name": "androidx/annotation/StyleRes.class", "size": 1005, "crc": 1907240834}, {"key": "androidx/annotation/StyleableRes.class", "name": "androidx/annotation/StyleableRes.class", "size": 1017, "crc": -403115595}, {"key": "androidx/annotation/TransitionRes.class", "name": "androidx/annotation/TransitionRes.class", "size": 985, "crc": 1865674518}, {"key": "androidx/annotation/UiContext.class", "name": "androidx/annotation/UiContext.class", "size": 962, "crc": 1772311488}, {"key": "androidx/annotation/UiThread.class", "name": "androidx/annotation/UiThread.class", "size": 1044, "crc": 497372290}, {"key": "androidx/annotation/VisibleForTesting$Companion.class", "name": "androidx/annotation/VisibleForTesting$Companion.class", "size": 967, "crc": 1134852008}, {"key": "androidx/annotation/VisibleForTesting.class", "name": "androidx/annotation/VisibleForTesting.class", "size": 1279, "crc": -**********}, {"key": "androidx/annotation/WorkerThread.class", "name": "androidx/annotation/WorkerThread.class", "size": 1060, "crc": 348613925}, {"key": "androidx/annotation/XmlRes.class", "name": "androidx/annotation/XmlRes.class", "size": 999, "crc": **********}, {"key": "META-INF/proguard/androidx-annotations.pro", "name": "META-INF/proguard/androidx-annotations.pro", "size": 433, "crc": **********}, {"key": "META-INF/androidx/annotation/annotation/LICENSE.txt", "name": "META-INF/androidx/annotation/annotation/LICENSE.txt", "size": 10175, "crc": -106424664}]