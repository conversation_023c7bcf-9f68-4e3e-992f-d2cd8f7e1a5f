package io.github.simplenote.data.repository

import arrow.core.Either
import io.github.simplenote.data.local.dao.NoteDao
import io.github.simplenote.data.local.entity.toDomain
import io.github.simplenote.data.local.entity.toEntity
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.model.NoteError
import io.github.simplenote.domain.repository.NoteRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock

/**
 * Implementation of NoteRepository using Room database.
 * Handles all data operations with proper error handling using Arrow.
 */
class NoteRepositoryImpl(
    private val noteDao: NoteDao
) : NoteRepository {

    override fun getAllNotes(): Flow<Either<NoteError, List<Note>>> {
        return noteDao.getAllNotes()
            .map { entities ->
                Either.Right(entities.map { it.toDomain() }) as Either<NoteError, List<Note>>
            }
            .catch { throwable ->
                emit(
                    Either.Left(
                        NoteError.DatabaseError.QueryFailed(
                            throwable.message ?: "Unknown error"
                        )
                    ) as Either<NoteError, List<Note>>
                )
            }
    }

    override fun getAllNotesByCreatedDate(): Flow<Either<NoteError, List<Note>>> {
        return noteDao.getAllNotesByCreatedDate()
            .map { entities ->
                Either.Right(entities.map { it.toDomain() }) as Either<NoteError, List<Note>>
            }
            .catch { throwable ->
                emit(
                    Either.Left(
                        NoteError.DatabaseError.QueryFailed(
                            throwable.message ?: "Unknown error"
                        )
                    ) as Either<NoteError, List<Note>>
                )
            }
    }

    override fun getAllNotesByTitle(): Flow<Either<NoteError, List<Note>>> {
        return noteDao.getAllNotesByTitle()
            .map { entities ->
                Either.Right(entities.map { it.toDomain() }) as Either<NoteError, List<Note>>
            }
            .catch { throwable ->
                emit(
                    Either.Left(
                        NoteError.DatabaseError.QueryFailed(
                            throwable.message ?: "Unknown error"
                        )
                    ) as Either<NoteError, List<Note>>
                )
            }
    }

    override suspend fun getNoteById(id: Long): Either<NoteError, Note> {
        return try {
            val entity = noteDao.getNoteById(id)
            if (entity != null) {
                Either.Right(entity.toDomain())
            } else {
                Either.Left(NoteError.DatabaseError.NotFound)
            }
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.QueryFailed(e.message ?: "Unknown error"))
        }
    }

    override fun searchNotes(query: String): Flow<Either<NoteError, List<Note>>> {
        return noteDao.searchNotes(query)
            .map { entities ->
                Either.Right(entities.map { it.toDomain() }) as Either<NoteError, List<Note>>
            }
            .catch { throwable ->
                emit(
                    Either.Left(
                        NoteError.DatabaseError.QueryFailed(
                            throwable.message ?: "Unknown error"
                        )
                    ) as Either<NoteError, List<Note>>
                )
            }
    }

    override fun getNotesByColor(color: NoteColor): Flow<Either<NoteError, List<Note>>> {
        return noteDao.getNotesByColor(color.id)
            .map { entities ->
                Either.Right(entities.map { it.toDomain() }) as Either<NoteError, List<Note>>
            }
            .catch { throwable ->
                emit(
                    Either.Left(
                        NoteError.DatabaseError.QueryFailed(
                            throwable.message ?: "Unknown error"
                        )
                    ) as Either<NoteError, List<Note>>
                )
            }
    }

    override suspend fun createNote(note: Note): Either<NoteError, Note> {
        return try {
            val id = noteDao.insertNote(note.toEntity())
            Either.Right(note.copy(id = id))
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.InsertFailed(e.message ?: "Unknown error"))
        }
    }

    override suspend fun updateNote(note: Note): Either<NoteError, Note> {
        return try {
            val rowsAffected = noteDao.updateNote(note.toEntity())
            if (rowsAffected > 0) {
                Either.Right(note)
            } else {
                Either.Left(NoteError.DatabaseError.NotFound)
            }
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.UpdateFailed(e.message ?: "Unknown error"))
        }
    }

    override suspend fun updateNoteColor(id: Long, color: NoteColor): Either<NoteError, Unit> {
        return try {
            val currentTime = Clock.System.now().toEpochMilliseconds()
            val rowsAffected = noteDao.updateNoteColor(id, color.id, currentTime)
            if (rowsAffected > 0) {
                Either.Right(Unit)
            } else {
                Either.Left(NoteError.DatabaseError.NotFound)
            }
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.UpdateFailed(e.message ?: "Unknown error"))
        }
    }

    override suspend fun deleteNote(note: Note): Either<NoteError, Unit> {
        return try {
            val rowsAffected = noteDao.deleteNote(note.toEntity())
            if (rowsAffected > 0) {
                Either.Right(Unit)
            } else {
                Either.Left(NoteError.DatabaseError.NotFound)
            }
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.DeleteFailed(e.message ?: "Unknown error"))
        }
    }

    override suspend fun deleteNoteById(id: Long): Either<NoteError, Unit> {
        return try {
            val rowsAffected = noteDao.deleteNoteById(id)
            if (rowsAffected > 0) {
                Either.Right(Unit)
            } else {
                Either.Left(NoteError.DatabaseError.NotFound)
            }
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.DeleteFailed(e.message ?: "Unknown error"))
        }
    }

    override suspend fun deleteAllNotes(): Either<NoteError, Unit> {
        return try {
            noteDao.deleteAllNotes()
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.DeleteFailed(e.message ?: "Unknown error"))
        }
    }

    override suspend fun getNotesCount(): Either<NoteError, Int> {
        return try {
            Either.Right(noteDao.getNotesCount())
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.QueryFailed(e.message ?: "Unknown error"))
        }
    }
}
