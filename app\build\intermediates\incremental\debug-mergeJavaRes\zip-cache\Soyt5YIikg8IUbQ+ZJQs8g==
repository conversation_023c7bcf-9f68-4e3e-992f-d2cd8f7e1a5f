[{"key": "androidx/compose/foundation/AbstractClickableNode$TraverseKey.class", "name": "androidx/compose/foundation/AbstractClickableNode$TraverseKey.class", "size": 862, "crc": 1497625053}, {"key": "androidx/compose/foundation/AbstractClickableNode$applySemantics$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$applySemantics$1.class", "size": 1422, "crc": -1573904610}, {"key": "androidx/compose/foundation/AbstractClickableNode$emitHoverEnter$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$emitHoverEnter$1$1.class", "size": 3908, "crc": -1701009107}, {"key": "androidx/compose/foundation/AbstractClickableNode$emitHoverExit$1$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$emitHoverExit$1$1$1.class", "size": 3906, "crc": 708804421}, {"key": "androidx/compose/foundation/AbstractClickableNode$focusableNode$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$focusableNode$1.class", "size": 1636, "crc": 1981202702}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1$delayJob$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1$delayJob$1.class", "size": 4860, "crc": 1366904999}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1.class", "size": 7337, "crc": -1507589501}, {"key": "androidx/compose/foundation/AbstractClickableNode$onFocusChange$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onFocusChange$1$1.class", "size": 4203, "crc": 271916700}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "size": 4040, "crc": 2055494226}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2.class", "size": 4219, "crc": 835055976}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$1.class", "size": 3327, "crc": 1962920278}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$2.class", "size": 3326, "crc": -64081457}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$3.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$3.class", "size": 1780, "crc": -678225952}, {"key": "androidx/compose/foundation/AbstractClickableNode.class", "name": "androidx/compose/foundation/AbstractClickableNode.class", "size": 26674, "crc": 1807964511}, {"key": "androidx/compose/foundation/ActualJvm_jvmKt.class", "name": "androidx/compose/foundation/ActualJvm_jvmKt.class", "size": 671, "crc": 1881166195}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "size": 1981, "crc": 119363890}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1$1.class", "size": 8402, "crc": -1624803953}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1.class", "size": 2300, "crc": 1622941133}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "size": 35665, "crc": -2055482299}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollFactory.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollFactory.class", "size": 3823, "crc": -1748560812}, {"key": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "size": 5373, "crc": -370531799}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "size": 1276, "crc": 52173348}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "size": 2694, "crc": 1589812477}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "size": 1491, "crc": -1902991539}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "size": 2956, "crc": -1444607761}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$1.class", "size": 1661, "crc": 1495278697}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$2.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$2.class", "size": 1568, "crc": -1223136596}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$3$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$3$1.class", "size": 5497, "crc": 459041152}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$4.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$4.class", "size": 2395, "crc": 1837714859}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$1$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$1$1.class", "size": 2537, "crc": -791353049}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$2.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$2.class", "size": 1544, "crc": -461932916}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$3$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$3$1.class", "size": 4238, "crc": 1016648627}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$4.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$4.class", "size": 2413, "crc": -397199112}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "size": 17004, "crc": 2105962718}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "size": 8912, "crc": -277361547}, {"key": "androidx/compose/foundation/Api31Impl.class", "name": "androidx/compose/foundation/Api31Impl.class", "size": 2115, "crc": 1324962331}, {"key": "androidx/compose/foundation/BackgroundElement.class", "name": "androidx/compose/foundation/BackgroundElement.class", "size": 5340, "crc": -1803720775}, {"key": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "size": 3117, "crc": 2140708096}, {"key": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "size": 3082, "crc": 1803521661}, {"key": "androidx/compose/foundation/BackgroundKt.class", "name": "androidx/compose/foundation/BackgroundKt.class", "size": 4204, "crc": -713276089}, {"key": "androidx/compose/foundation/BackgroundNode$getOutline$1.class", "name": "androidx/compose/foundation/BackgroundNode$getOutline$1.class", "size": 2084, "crc": -1215301066}, {"key": "androidx/compose/foundation/BackgroundNode.class", "name": "androidx/compose/foundation/BackgroundNode.class", "size": 8114, "crc": 1303928199}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "size": 2805, "crc": 2082466122}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "size": 4897, "crc": -1798886869}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "size": 6169, "crc": -931442795}, {"key": "androidx/compose/foundation/BasicMarqueeKt.class", "name": "androidx/compose/foundation/BasicMarqueeKt.class", "size": 6705, "crc": -1976989803}, {"key": "androidx/compose/foundation/BasicTooltipDefaults.class", "name": "androidx/compose/foundation/BasicTooltipDefaults.class", "size": 1332, "crc": -923761258}, {"key": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "size": 2141, "crc": -388484566}, {"key": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$2$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$2$1.class", "size": 2849, "crc": 883453900}, {"key": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$3.class", "name": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$3.class", "size": 2928, "crc": -1455933088}, {"key": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$1$1$1.class", "size": 3160, "crc": -1431135547}, {"key": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$1$1.class", "size": 2114, "crc": 904216953}, {"key": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$2$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$2$1$1.class", "size": 2053, "crc": -388672218}, {"key": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$2.class", "name": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$2.class", "size": 10302, "crc": 1713611927}, {"key": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$3.class", "name": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$3.class", "size": 2626, "crc": 2138545347}, {"key": "androidx/compose/foundation/BasicTooltipKt$WrappedAnchor$2.class", "name": "androidx/compose/foundation/BasicTooltipKt$WrappedAnchor$2.class", "size": 2411, "crc": -1390086798}, {"key": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1$1$1.class", "size": 3512, "crc": -1100001406}, {"key": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1$1.class", "size": 1915, "crc": -1067017615}, {"key": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1.class", "size": 2328, "crc": -1506532693}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1$1.class", "size": 3607, "crc": -1630321842}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1.class", "size": 6278, "crc": -1944745933}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1.class", "size": 4007, "crc": -1941313264}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1.class", "size": 2117, "crc": -320050461}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1$1.class", "size": 3607, "crc": -172841572}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1.class", "size": 5664, "crc": -1747332838}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1.class", "size": 3960, "crc": 1688737219}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2.class", "size": 2117, "crc": 1301745129}, {"key": "androidx/compose/foundation/BasicTooltipKt.class", "name": "androidx/compose/foundation/BasicTooltipKt.class", "size": 25661, "crc": -570058130}, {"key": "androidx/compose/foundation/BasicTooltipState.class", "name": "androidx/compose/foundation/BasicTooltipState.class", "size": 1806, "crc": 742847730}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "size": 3326, "crc": 1230424931}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "size": 3855, "crc": -506195006}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "size": 5061, "crc": -1496493949}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl.class", "size": 5596, "crc": 1964806254}, {"key": "androidx/compose/foundation/BasicTooltipStrings.class", "name": "androidx/compose/foundation/BasicTooltipStrings.class", "size": 2479, "crc": 457211278}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBoxAndroid$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBoxAndroid$1.class", "size": 2974, "crc": -1030504247}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt.class", "size": 4460, "crc": -1779353712}, {"key": "androidx/compose/foundation/BorderCache.class", "name": "androidx/compose/foundation/BorderCache.class", "size": 13671, "crc": 1214307068}, {"key": "androidx/compose/foundation/BorderKt$drawContentWithoutBorder$1.class", "name": "androidx/compose/foundation/BorderKt$drawContentWithoutBorder$1.class", "size": 1589, "crc": 53066126}, {"key": "androidx/compose/foundation/BorderKt$drawRectBorder$1.class", "name": "androidx/compose/foundation/BorderKt$drawRectBorder$1.class", "size": 2227, "crc": 1714423156}, {"key": "androidx/compose/foundation/BorderKt.class", "name": "androidx/compose/foundation/BorderKt.class", "size": 10596, "crc": 828386133}, {"key": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$1.class", "size": 2398, "crc": 119058936}, {"key": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$3.class", "name": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$3.class", "size": 4674, "crc": -1750170424}, {"key": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$1.class", "size": 7232, "crc": 1384645931}, {"key": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$2.class", "name": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$2.class", "size": 2337, "crc": 1296038319}, {"key": "androidx/compose/foundation/BorderModifierNode$drawWithCacheModifierNode$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawWithCacheModifierNode$1.class", "size": 6788, "crc": -1584966906}, {"key": "androidx/compose/foundation/BorderModifierNode.class", "name": "androidx/compose/foundation/BorderModifierNode.class", "size": 24410, "crc": 889565697}, {"key": "androidx/compose/foundation/BorderModifierNodeElement.class", "name": "androidx/compose/foundation/BorderModifierNodeElement.class", "size": 6400, "crc": 1393931084}, {"key": "androidx/compose/foundation/BorderStroke.class", "name": "androidx/compose/foundation/BorderStroke.class", "size": 3269, "crc": -1658103383}, {"key": "androidx/compose/foundation/BorderStrokeKt.class", "name": "androidx/compose/foundation/BorderStrokeKt.class", "size": 1197, "crc": 1912376172}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$1.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$1.class", "size": 1973, "crc": 480695502}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$2$1.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$2$1.class", "size": 1734, "crc": -128744199}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$3.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$3.class", "size": 2108, "crc": -1595795029}, {"key": "androidx/compose/foundation/CanvasKt.class", "name": "androidx/compose/foundation/CanvasKt.class", "size": 6313, "crc": -1488818257}, {"key": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "name": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "size": 4075, "crc": 1190765573}, {"key": "androidx/compose/foundation/ClickableElement.class", "name": "androidx/compose/foundation/ClickableElement.class", "size": 5650, "crc": -474821025}, {"key": "androidx/compose/foundation/ClickableKt$clickable$2.class", "name": "androidx/compose/foundation/ClickableKt$clickable$2.class", "size": 6251, "crc": -918463901}, {"key": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6657, "crc": 1729272679}, {"key": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3288, "crc": -1474895125}, {"key": "androidx/compose/foundation/ClickableKt$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$clickableWithIndicationIfNeeded$1.class", "size": 5818, "crc": 109783891}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "size": 6956, "crc": 1843666026}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable$4.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable$4.class", "size": 6891, "crc": 1210777191}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 7123, "crc": -680026376}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-auXiCPI$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-auXiCPI$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 7185, "crc": -1963148671}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "size": 3757, "crc": 1236046571}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-f5TDLPQ$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-f5TDLPQ$$inlined$debugInspectorInfo$1.class", "size": 3865, "crc": -406311452}, {"key": "androidx/compose/foundation/ClickableKt$hasScrollableContainer$1.class", "name": "androidx/compose/foundation/ClickableKt$hasScrollableContainer$1.class", "size": 2074, "crc": 230394900}, {"key": "androidx/compose/foundation/ClickableKt.class", "name": "androidx/compose/foundation/ClickableKt.class", "size": 21393, "crc": -1699902553}, {"key": "androidx/compose/foundation/ClickableNode$clickPointerInput$2.class", "name": "androidx/compose/foundation/ClickableNode$clickPointerInput$2.class", "size": 3588, "crc": 846580302}, {"key": "androidx/compose/foundation/ClickableNode$clickPointerInput$3.class", "name": "androidx/compose/foundation/ClickableNode$clickPointerInput$3.class", "size": 1837, "crc": -350921013}, {"key": "androidx/compose/foundation/ClickableNode.class", "name": "androidx/compose/foundation/ClickableNode.class", "size": 5244, "crc": 1252308363}, {"key": "androidx/compose/foundation/Clickable_androidKt.class", "name": "androidx/compose/foundation/Clickable_androidKt.class", "size": 1692, "crc": 1027512900}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt.class", "size": 3013, "crc": 1838186301}, {"key": "androidx/compose/foundation/CombinedClickableElement.class", "name": "androidx/compose/foundation/CombinedClickableElement.class", "size": 7630, "crc": -1637182104}, {"key": "androidx/compose/foundation/CombinedClickableNode$DoubleKeyClickState.class", "name": "androidx/compose/foundation/CombinedClickableNode$DoubleKeyClickState.class", "size": 1580, "crc": -1685548418}, {"key": "androidx/compose/foundation/CombinedClickableNode$applyAdditionalSemantics$1.class", "name": "androidx/compose/foundation/CombinedClickableNode$applyAdditionalSemantics$1.class", "size": 1553, "crc": -1495240799}, {"key": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$2.class", "name": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$2.class", "size": 1861, "crc": -1100068984}, {"key": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$3.class", "name": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$3.class", "size": 2807, "crc": 1468560069}, {"key": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$4.class", "name": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$4.class", "size": 3597, "crc": -578324820}, {"key": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$5.class", "name": "androidx/compose/foundation/CombinedClickableNode$clickPointerInput$5.class", "size": 1830, "crc": -2039605169}, {"key": "androidx/compose/foundation/CombinedClickableNode$onClickKeyDownEvent$1.class", "name": "androidx/compose/foundation/CombinedClickableNode$onClickKeyDownEvent$1.class", "size": 4168, "crc": -10602294}, {"key": "androidx/compose/foundation/CombinedClickableNode$onClickKeyUpEvent$2.class", "name": "androidx/compose/foundation/CombinedClickableNode$onClickKeyUpEvent$2.class", "size": 4968, "crc": 1177533337}, {"key": "androidx/compose/foundation/CombinedClickableNode.class", "name": "androidx/compose/foundation/CombinedClickableNode.class", "size": 16374, "crc": 922680838}, {"key": "androidx/compose/foundation/ComposeFoundationFlags.class", "name": "androidx/compose/foundation/ComposeFoundationFlags.class", "size": 1567, "crc": -1780349404}, {"key": "androidx/compose/foundation/DarkThemeKt.class", "name": "androidx/compose/foundation/DarkThemeKt.class", "size": 1453, "crc": 1316159548}, {"key": "androidx/compose/foundation/DarkTheme_androidKt.class", "name": "androidx/compose/foundation/DarkTheme_androidKt.class", "size": 2813, "crc": -728192710}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1$1.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1$1.class", "size": 4218, "crc": -1221421234}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1.class", "size": 4612, "crc": 415942536}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "size": 4199, "crc": -1129948284}, {"key": "androidx/compose/foundation/DefaultDebugIndication.class", "name": "androidx/compose/foundation/DefaultDebugIndication.class", "size": 1898, "crc": 184218637}, {"key": "androidx/compose/foundation/EdgeEffectCompat.class", "name": "androidx/compose/foundation/EdgeEffectCompat.class", "size": 3643, "crc": 758159027}, {"key": "androidx/compose/foundation/EdgeEffectCompat_androidKt.class", "name": "androidx/compose/foundation/EdgeEffectCompat_androidKt.class", "size": 1660, "crc": -96338520}, {"key": "androidx/compose/foundation/EdgeEffectWrapper.class", "name": "androidx/compose/foundation/EdgeEffectWrapper.class", "size": 12471, "crc": -2110834298}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "size": 3916, "crc": -2139056696}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "size": 1784, "crc": 1270507353}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "size": 3674, "crc": 341736286}, {"key": "androidx/compose/foundation/ExperimentalFoundationApi.class", "name": "androidx/compose/foundation/ExperimentalFoundationApi.class", "size": 833, "crc": 1000818722}, {"key": "androidx/compose/foundation/FixedMotionDurationScale.class", "name": "androidx/compose/foundation/FixedMotionDurationScale.class", "size": 3079, "crc": -1215062803}, {"key": "androidx/compose/foundation/FocusGroupElement.class", "name": "androidx/compose/foundation/FocusGroupElement.class", "size": 2542, "crc": -548654404}, {"key": "androidx/compose/foundation/FocusGroupNode.class", "name": "androidx/compose/foundation/FocusGroupNode.class", "size": 1246, "crc": 1957778615}, {"key": "androidx/compose/foundation/FocusableElement.class", "name": "androidx/compose/foundation/FocusableElement.class", "size": 3440, "crc": 583936878}, {"key": "androidx/compose/foundation/FocusableKt.class", "name": "androidx/compose/foundation/FocusableKt.class", "size": 2132, "crc": 221113557}, {"key": "androidx/compose/foundation/FocusableNode$TraverseKey.class", "name": "androidx/compose/foundation/FocusableNode$TraverseKey.class", "size": 838, "crc": 1518593723}, {"key": "androidx/compose/foundation/FocusableNode$applySemantics$1.class", "name": "androidx/compose/foundation/FocusableNode$applySemantics$1.class", "size": 1619, "crc": 830062079}, {"key": "androidx/compose/foundation/FocusableNode$emitWithFallback$1.class", "name": "androidx/compose/foundation/FocusableNode$emitWithFallback$1.class", "size": 4022, "crc": -2585787}, {"key": "androidx/compose/foundation/FocusableNode$emitWithFallback$handler$1.class", "name": "androidx/compose/foundation/FocusableNode$emitWithFallback$handler$1.class", "size": 1778, "crc": -1767985647}, {"key": "androidx/compose/foundation/FocusableNode$focusTargetNode$1.class", "name": "androidx/compose/foundation/FocusableNode$focusTargetNode$1.class", "size": 1870, "crc": -317384319}, {"key": "androidx/compose/foundation/FocusableNode$onFocusStateChange$1.class", "name": "androidx/compose/foundation/FocusableNode$onFocusStateChange$1.class", "size": 3531, "crc": -53993342}, {"key": "androidx/compose/foundation/FocusableNode$retrievePinnableContainer$1.class", "name": "androidx/compose/foundation/FocusableNode$retrievePinnableContainer$1.class", "size": 2263, "crc": -965704319}, {"key": "androidx/compose/foundation/FocusableNode.class", "name": "androidx/compose/foundation/FocusableNode.class", "size": 14237, "crc": 1480734272}, {"key": "androidx/compose/foundation/FocusedBoundsKt.class", "name": "androidx/compose/foundation/FocusedBoundsKt.class", "size": 1384, "crc": 1757782724}, {"key": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "name": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "size": 3634, "crc": -1642837670}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode$TraverseKey.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode$TraverseKey.class", "size": 878, "crc": 1960845782}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "size": 3053, "crc": 2145695580}, {"key": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "name": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "size": 3182, "crc": 1589054858}, {"key": "androidx/compose/foundation/GlowOverscrollNode.class", "name": "androidx/compose/foundation/GlowOverscrollNode.class", "size": 10597, "crc": -674896520}, {"key": "androidx/compose/foundation/HorizontalScrollableClipShape.class", "name": "androidx/compose/foundation/HorizontalScrollableClipShape.class", "size": 3874, "crc": 1455065132}, {"key": "androidx/compose/foundation/HoverableElement.class", "name": "androidx/compose/foundation/HoverableElement.class", "size": 3211, "crc": 1225259836}, {"key": "androidx/compose/foundation/HoverableKt.class", "name": "androidx/compose/foundation/HoverableKt.class", "size": 1774, "crc": 347746363}, {"key": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "name": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "size": 1854, "crc": 258518113}, {"key": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "name": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "size": 1812, "crc": 226903161}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "size": 3373, "crc": -52094706}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "size": 3372, "crc": -1819901027}, {"key": "androidx/compose/foundation/HoverableNode.class", "name": "androidx/compose/foundation/HoverableNode.class", "size": 6755, "crc": -493786347}, {"key": "androidx/compose/foundation/ImageKt$Image$1$1.class", "name": "androidx/compose/foundation/ImageKt$Image$1$1.class", "size": 1586, "crc": -510523694}, {"key": "androidx/compose/foundation/ImageKt$Image$1.class", "name": "androidx/compose/foundation/ImageKt$Image$1.class", "size": 2182, "crc": -1345080441}, {"key": "androidx/compose/foundation/ImageKt$Image$2.class", "name": "androidx/compose/foundation/ImageKt$Image$2.class", "size": 2489, "crc": -267417587}, {"key": "androidx/compose/foundation/ImageKt$Image$semantics$1$1.class", "name": "androidx/compose/foundation/ImageKt$Image$semantics$1$1.class", "size": 2191, "crc": 916063676}, {"key": "androidx/compose/foundation/ImageKt.class", "name": "androidx/compose/foundation/ImageKt.class", "size": 16489, "crc": -1370071375}, {"key": "androidx/compose/foundation/Indication.class", "name": "androidx/compose/foundation/Indication.class", "size": 2453, "crc": -326009605}, {"key": "androidx/compose/foundation/IndicationInstance.class", "name": "androidx/compose/foundation/IndicationInstance.class", "size": 1099, "crc": 341937149}, {"key": "androidx/compose/foundation/IndicationKt$LocalIndication$1.class", "name": "androidx/compose/foundation/IndicationKt$LocalIndication$1.class", "size": 1329, "crc": -120107410}, {"key": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "size": 3077, "crc": 183243142}, {"key": "androidx/compose/foundation/IndicationKt$indication$2.class", "name": "androidx/compose/foundation/IndicationKt$indication$2.class", "size": 4802, "crc": 984270703}, {"key": "androidx/compose/foundation/IndicationKt.class", "name": "androidx/compose/foundation/IndicationKt.class", "size": 4932, "crc": -1379507256}, {"key": "androidx/compose/foundation/IndicationModifier.class", "name": "androidx/compose/foundation/IndicationModifier.class", "size": 2018, "crc": 287234510}, {"key": "androidx/compose/foundation/IndicationModifierElement.class", "name": "androidx/compose/foundation/IndicationModifierElement.class", "size": 3670, "crc": -229847614}, {"key": "androidx/compose/foundation/IndicationModifierNode.class", "name": "androidx/compose/foundation/IndicationModifierNode.class", "size": 1244, "crc": 1031764258}, {"key": "androidx/compose/foundation/IndicationNodeFactory.class", "name": "androidx/compose/foundation/IndicationNodeFactory.class", "size": 1176, "crc": 528918224}, {"key": "androidx/compose/foundation/InternalFoundationApi.class", "name": "androidx/compose/foundation/InternalFoundationApi.class", "size": 1046, "crc": -394311367}, {"key": "androidx/compose/foundation/MagnifierElement.class", "name": "androidx/compose/foundation/MagnifierElement.class", "size": 7872, "crc": -1794449873}, {"key": "androidx/compose/foundation/MagnifierNode$anchorPositionInRoot$1.class", "name": "androidx/compose/foundation/MagnifierNode$anchorPositionInRoot$1.class", "size": 1894, "crc": -620997926}, {"key": "androidx/compose/foundation/MagnifierNode$applySemantics$1.class", "name": "androidx/compose/foundation/MagnifierNode$applySemantics$1.class", "size": 1492, "crc": -1368950151}, {"key": "androidx/compose/foundation/MagnifierNode$onAttach$1$1.class", "name": "androidx/compose/foundation/MagnifierNode$onAttach$1$1.class", "size": 1306, "crc": 1287359596}, {"key": "androidx/compose/foundation/MagnifierNode$onAttach$1.class", "name": "androidx/compose/foundation/MagnifierNode$onAttach$1.class", "size": 4025, "crc": 391951461}, {"key": "androidx/compose/foundation/MagnifierNode$onObservedReadsChanged$1.class", "name": "androidx/compose/foundation/MagnifierNode$onObservedReadsChanged$1.class", "size": 1261, "crc": -2018331382}, {"key": "androidx/compose/foundation/MagnifierNode.class", "name": "androidx/compose/foundation/MagnifierNode.class", "size": 20778, "crc": -854689754}, {"key": "androidx/compose/foundation/Magnifier_androidKt.class", "name": "androidx/compose/foundation/Magnifier_androidKt.class", "size": 7220, "crc": 43680151}, {"key": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "name": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "size": 1301, "crc": 1448307324}, {"key": "androidx/compose/foundation/MarqueeAnimationMode.class", "name": "androidx/compose/foundation/MarqueeAnimationMode.class", "size": 2905, "crc": -1404568054}, {"key": "androidx/compose/foundation/MarqueeDefaults.class", "name": "androidx/compose/foundation/MarqueeDefaults.class", "size": 2889, "crc": -1100564857}, {"key": "androidx/compose/foundation/MarqueeModifierElement.class", "name": "androidx/compose/foundation/MarqueeModifierElement.class", "size": 6730, "crc": -1640227918}, {"key": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "name": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "size": 819, "crc": -1782017515}, {"key": "androidx/compose/foundation/MarqueeModifierNode$draw$1$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$draw$1$1.class", "size": 1619, "crc": 1366482228}, {"key": "androidx/compose/foundation/MarqueeModifierNode$measure$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$measure$1.class", "size": 2485, "crc": -1076735104}, {"key": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "size": 3629, "crc": 1644124546}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$1.class", "size": 2042, "crc": -1046926743}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "size": 5257, "crc": 602897865}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "size": 3838, "crc": 1412385284}, {"key": "androidx/compose/foundation/MarqueeModifierNode$spacingPx$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$spacingPx$2.class", "size": 2532, "crc": -558115322}, {"key": "androidx/compose/foundation/MarqueeModifierNode.class", "name": "androidx/compose/foundation/MarqueeModifierNode.class", "size": 23322, "crc": -1655503358}, {"key": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "name": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "size": 1764, "crc": 377095770}, {"key": "androidx/compose/foundation/MarqueeSpacing.class", "name": "androidx/compose/foundation/MarqueeSpacing.class", "size": 1051, "crc": -342090363}, {"key": "androidx/compose/foundation/MutatePriority.class", "name": "androidx/compose/foundation/MutatePriority.class", "size": 1922, "crc": 1879632454}, {"key": "androidx/compose/foundation/MutationInterruptedException.class", "name": "androidx/compose/foundation/MutationInterruptedException.class", "size": 1240, "crc": 841278813}, {"key": "androidx/compose/foundation/MutatorMutex$Mutator.class", "name": "androidx/compose/foundation/MutatorMutex$Mutator.class", "size": 2022, "crc": 597092511}, {"key": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "size": 6969, "crc": -482437629}, {"key": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "size": 7143, "crc": 984199180}, {"key": "androidx/compose/foundation/MutatorMutex.class", "name": "androidx/compose/foundation/MutatorMutex.class", "size": 7010, "crc": -410164508}, {"key": "androidx/compose/foundation/NoIndicationInstance.class", "name": "androidx/compose/foundation/NoIndicationInstance.class", "size": 1227, "crc": -384154060}, {"key": "androidx/compose/foundation/OverscrollConfiguration.class", "name": "androidx/compose/foundation/OverscrollConfiguration.class", "size": 4281, "crc": -764624707}, {"key": "androidx/compose/foundation/OverscrollConfiguration_androidKt$LocalOverscrollConfiguration$1.class", "name": "androidx/compose/foundation/OverscrollConfiguration_androidKt$LocalOverscrollConfiguration$1.class", "size": 1491, "crc": -2130584988}, {"key": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "name": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "size": 2626, "crc": 405589181}, {"key": "androidx/compose/foundation/OverscrollEffect$node$1.class", "name": "androidx/compose/foundation/OverscrollEffect$node$1.class", "size": 814, "crc": -624143019}, {"key": "androidx/compose/foundation/OverscrollEffect.class", "name": "androidx/compose/foundation/OverscrollEffect.class", "size": 3216, "crc": -1955337059}, {"key": "androidx/compose/foundation/OverscrollFactory.class", "name": "androidx/compose/foundation/OverscrollFactory.class", "size": 871, "crc": 210415439}, {"key": "androidx/compose/foundation/OverscrollKt$LocalOverscrollFactory$1.class", "name": "androidx/compose/foundation/OverscrollKt$LocalOverscrollFactory$1.class", "size": 1653, "crc": 1328382422}, {"key": "androidx/compose/foundation/OverscrollKt.class", "name": "androidx/compose/foundation/OverscrollKt.class", "size": 6500, "crc": 348967176}, {"key": "androidx/compose/foundation/OverscrollModifierElement.class", "name": "androidx/compose/foundation/OverscrollModifierElement.class", "size": 3474, "crc": -2056073970}, {"key": "androidx/compose/foundation/OverscrollModifierNode.class", "name": "androidx/compose/foundation/OverscrollModifierNode.class", "size": 2679, "crc": -1011138752}, {"key": "androidx/compose/foundation/PlatformMagnifier.class", "name": "androidx/compose/foundation/PlatformMagnifier.class", "size": 856, "crc": 181691033}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "size": 1878, "crc": 1044459789}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory.class", "size": 1634, "crc": 1982200504}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "size": 4421, "crc": 1115306849}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "size": 2716, "crc": 215925517}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "size": 4604, "crc": 1504826259}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "size": 5908, "crc": -26907453}, {"key": "androidx/compose/foundation/PreferKeepClearElement.class", "name": "androidx/compose/foundation/PreferKeepClearElement.class", "size": 3808, "crc": -169577376}, {"key": "androidx/compose/foundation/PreferKeepClearNode.class", "name": "androidx/compose/foundation/PreferKeepClearNode.class", "size": 3547, "crc": 1317152406}, {"key": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "name": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "size": 2971, "crc": -1326888501}, {"key": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$1.class", "name": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$1.class", "size": 2486, "crc": 1625087922}, {"key": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$2.class", "name": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$2.class", "size": 2077, "crc": 1227312858}, {"key": "androidx/compose/foundation/ProgressSemanticsKt.class", "name": "androidx/compose/foundation/ProgressSemanticsKt.class", "size": 2335, "crc": 686793326}, {"key": "androidx/compose/foundation/RectListNode.class", "name": "androidx/compose/foundation/RectListNode.class", "size": 9372, "crc": 1510425397}, {"key": "androidx/compose/foundation/ScrollKt$rememberScrollState$1$1.class", "name": "androidx/compose/foundation/ScrollKt$rememberScrollState$1$1.class", "size": 1284, "crc": -274337898}, {"key": "androidx/compose/foundation/ScrollKt.class", "name": "androidx/compose/foundation/ScrollKt.class", "size": 8601, "crc": -444207284}, {"key": "androidx/compose/foundation/ScrollNode$applySemantics$accessibilityScrollState$1.class", "name": "androidx/compose/foundation/ScrollNode$applySemantics$accessibilityScrollState$1.class", "size": 1468, "crc": 779637629}, {"key": "androidx/compose/foundation/ScrollNode$applySemantics$accessibilityScrollState$2.class", "name": "androidx/compose/foundation/ScrollNode$applySemantics$accessibilityScrollState$2.class", "size": 1471, "crc": -524421783}, {"key": "androidx/compose/foundation/ScrollNode$measure$1$1.class", "name": "androidx/compose/foundation/ScrollNode$measure$1$1.class", "size": 1865, "crc": -1200066305}, {"key": "androidx/compose/foundation/ScrollNode$measure$1.class", "name": "androidx/compose/foundation/ScrollNode$measure$1.class", "size": 3555, "crc": 796631101}, {"key": "androidx/compose/foundation/ScrollNode.class", "name": "androidx/compose/foundation/ScrollNode.class", "size": 7372, "crc": 42433819}, {"key": "androidx/compose/foundation/ScrollState$Companion$Saver$1.class", "name": "androidx/compose/foundation/ScrollState$Companion$Saver$1.class", "size": 1662, "crc": 1190387188}, {"key": "androidx/compose/foundation/ScrollState$Companion$Saver$2.class", "name": "androidx/compose/foundation/ScrollState$Companion$Saver$2.class", "size": 1342, "crc": 892228430}, {"key": "androidx/compose/foundation/ScrollState$Companion.class", "name": "androidx/compose/foundation/ScrollState$Companion.class", "size": 1315, "crc": -362446592}, {"key": "androidx/compose/foundation/ScrollState$canScrollBackward$2.class", "name": "androidx/compose/foundation/ScrollState$canScrollBackward$2.class", "size": 1271, "crc": -1582326752}, {"key": "androidx/compose/foundation/ScrollState$canScrollForward$2.class", "name": "androidx/compose/foundation/ScrollState$canScrollForward$2.class", "size": 1300, "crc": -1520768549}, {"key": "androidx/compose/foundation/ScrollState$scrollableState$1.class", "name": "androidx/compose/foundation/ScrollState$scrollableState$1.class", "size": 2883, "crc": -409981956}, {"key": "androidx/compose/foundation/ScrollState.class", "name": "androidx/compose/foundation/ScrollState.class", "size": 12356, "crc": 1270846762}, {"key": "androidx/compose/foundation/ScrollingContainerElement.class", "name": "androidx/compose/foundation/ScrollingContainerElement.class", "size": 6135, "crc": -1891645393}, {"key": "androidx/compose/foundation/ScrollingContainerKt.class", "name": "androidx/compose/foundation/ScrollingContainerKt.class", "size": 3297, "crc": -62870992}, {"key": "androidx/compose/foundation/ScrollingContainerNode$attachOverscrollNodeIfNeeded$1.class", "name": "androidx/compose/foundation/ScrollingContainerNode$attachOverscrollNodeIfNeeded$1.class", "size": 2554, "crc": -1305062891}, {"key": "androidx/compose/foundation/ScrollingContainerNode.class", "name": "androidx/compose/foundation/ScrollingContainerNode.class", "size": 10406, "crc": 987802483}, {"key": "androidx/compose/foundation/ScrollingLayoutElement.class", "name": "androidx/compose/foundation/ScrollingLayoutElement.class", "size": 4068, "crc": 583730607}, {"key": "androidx/compose/foundation/StretchOverscrollNode.class", "name": "androidx/compose/foundation/StretchOverscrollNode.class", "size": 14887, "crc": -2077565488}, {"key": "androidx/compose/foundation/SurfaceCoroutineScope.class", "name": "androidx/compose/foundation/SurfaceCoroutineScope.class", "size": 615, "crc": -598445233}, {"key": "androidx/compose/foundation/SurfaceScope.class", "name": "androidx/compose/foundation/SurfaceScope.class", "size": 1306, "crc": 950359649}, {"key": "androidx/compose/foundation/SystemGestureExclusionKt.class", "name": "androidx/compose/foundation/SystemGestureExclusionKt.class", "size": 3114, "crc": -408927847}, {"key": "androidx/compose/foundation/VerticalScrollableClipShape.class", "name": "androidx/compose/foundation/VerticalScrollableClipShape.class", "size": 3860, "crc": -78316925}, {"key": "androidx/compose/foundation/WrappedOverscrollEffect$node$1.class", "name": "androidx/compose/foundation/WrappedOverscrollEffect$node$1.class", "size": 832, "crc": 1576816493}, {"key": "androidx/compose/foundation/WrappedOverscrollEffect.class", "name": "androidx/compose/foundation/WrappedOverscrollEffect.class", "size": 4617, "crc": -1899994951}, {"key": "androidx/compose/foundation/content/MediaType$Companion.class", "name": "androidx/compose/foundation/content/MediaType$Companion.class", "size": 1793, "crc": -1187816055}, {"key": "androidx/compose/foundation/content/MediaType.class", "name": "androidx/compose/foundation/content/MediaType.class", "size": 2878, "crc": 135671911}, {"key": "androidx/compose/foundation/content/PlatformTransferableContent.class", "name": "androidx/compose/foundation/content/PlatformTransferableContent.class", "size": 2537, "crc": 182079491}, {"key": "androidx/compose/foundation/content/ReceiveContentElement.class", "name": "androidx/compose/foundation/content/ReceiveContentElement.class", "size": 4293, "crc": 1534306527}, {"key": "androidx/compose/foundation/content/ReceiveContentKt.class", "name": "androidx/compose/foundation/content/ReceiveContentKt.class", "size": 1259, "crc": 1847996385}, {"key": "androidx/compose/foundation/content/ReceiveContentListener.class", "name": "androidx/compose/foundation/content/ReceiveContentListener.class", "size": 1352, "crc": 383841438}, {"key": "androidx/compose/foundation/content/ReceiveContentNode$1.class", "name": "androidx/compose/foundation/content/ReceiveContentNode$1.class", "size": 1862, "crc": -1087515005}, {"key": "androidx/compose/foundation/content/ReceiveContentNode.class", "name": "androidx/compose/foundation/content/ReceiveContentNode.class", "size": 3686, "crc": -211603769}, {"key": "androidx/compose/foundation/content/TransferableContent$Source$Companion.class", "name": "androidx/compose/foundation/content/TransferableContent$Source$Companion.class", "size": 1597, "crc": 122993562}, {"key": "androidx/compose/foundation/content/TransferableContent$Source.class", "name": "androidx/compose/foundation/content/TransferableContent$Source.class", "size": 3265, "crc": -64575784}, {"key": "androidx/compose/foundation/content/TransferableContent.class", "name": "androidx/compose/foundation/content/TransferableContent.class", "size": 3107, "crc": -1091244903}, {"key": "androidx/compose/foundation/content/TransferableContent_androidKt.class", "name": "androidx/compose/foundation/content/TransferableContent_androidKt.class", "size": 6722, "crc": -2010774288}, {"key": "androidx/compose/foundation/content/internal/DragAndDropRequestPermission_androidKt.class", "name": "androidx/compose/foundation/content/internal/DragAndDropRequestPermission_androidKt.class", "size": 3327, "crc": 1011834454}, {"key": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration$receiveContentListener$1.class", "name": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration$receiveContentListener$1.class", "size": 3037, "crc": -1886718119}, {"key": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration.class", "name": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration.class", "size": 2841, "crc": -2141879164}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration$Companion.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration$Companion.class", "size": 1670, "crc": -61415158}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration.class", "size": 2179, "crc": 1498003070}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationImpl.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationImpl.class", "size": 2905, "crc": -2079700402}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt$ModifierLocalReceiveContent$1.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt$ModifierLocalReceiveContent$1.class", "size": 1406, "crc": -864922525}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt.class", "size": 2699, "crc": 1612169214}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$1.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$1.class", "size": 1853, "crc": 215439990}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$2.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$2.class", "size": 3672, "crc": 1986337527}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt.class", "size": 4190, "crc": -60559256}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$1.class", "size": 2715, "crc": 1641229062}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenu$2.class", "size": 2715, "crc": -188758916}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$1.class", "size": 1383, "crc": -1652278932}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$3.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$3.class", "size": 3320, "crc": -1306028108}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$finalModifier$1$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt$ContextMenuArea$finalModifier$1$1.class", "size": 2651, "crc": -1306708662}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt.class", "size": 16656, "crc": 2098697599}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuColors.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuColors.class", "size": 3503, "crc": -27129957}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$awaitFirstRightClickDown$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$awaitFirstRightClickDown$1.class", "size": 1828, "crc": -2027373486}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$1.class", "size": 2133, "crc": 440103932}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$2.class", "size": 2124, "crc": -1006803019}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$onRightClickDown$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$onRightClickDown$2.class", "size": 4618, "crc": -809448237}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt.class", "size": 7859, "crc": 671312473}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuKey.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuKey.class", "size": 741, "crc": 681087257}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider.class", "size": 4489, "crc": 860381267}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider_androidKt.class", "size": 2245, "crc": -27279953}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope$Content$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope$Content$2.class", "size": 1927, "crc": -**********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope$item$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope$item$1.class", "size": 5942, "crc": -864436095}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope.class", "size": 7327, "crc": -946799139}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuSpec.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuSpec.class", "size": 7090, "crc": **********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Closed.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Closed.class", "size": 1300, "crc": -1419048276}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Open.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Open.class", "size": 4160, "crc": -1439887665}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status.class", "size": 1420, "crc": -455021153}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState.class", "size": 4446, "crc": 2101796846}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState_androidKt.class", "size": 1482, "crc": 1497615158}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuColumn$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuColumn$1.class", "size": 2549, "crc": -1571206517}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$1$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$1$1.class", "size": 1680, "crc": 1180133965}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$3.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuItem$3.class", "size": 2954, "crc": -1810259929}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$1.class", "size": 2706, "crc": 920608640}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2$1.class", "size": 5625, "crc": 1794344829}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2.class", "size": 3946, "crc": 1983763906}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$3.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$3.class", "size": 2993, "crc": -2001752330}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt.class", "size": 34805, "crc": 603475064}, {"key": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "name": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "size": 1571, "crc": -663185705}, {"key": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt__LegacyDragAndDropSourceWithDefaultPainter_androidKt.class", "name": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt__LegacyDragAndDropSourceWithDefaultPainter_androidKt.class", "size": 2201, "crc": -1448530939}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$1$1.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$1$1.class", "size": 1680, "crc": -1945046557}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$2.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$2.class", "size": 2294, "crc": -526096240}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "size": 4434, "crc": -1865210798}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults$DefaultStartDetector$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults$DefaultStartDetector$1$1.class", "size": 1785, "crc": 964954892}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults$DefaultStartDetector$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults$DefaultStartDetector$1.class", "size": 3986, "crc": -1735623835}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults.class", "size": 1947, "crc": 475213371}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "size": 8172, "crc": 1971095704}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "size": 2725, "crc": 935230235}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__DragAndDropSourceKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__DragAndDropSourceKt.class", "size": 3142, "crc": -873495379}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__LegacyDragAndDropSource_androidKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__LegacyDragAndDropSource_androidKt.class", "size": 2477, "crc": 1787926360}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$dragAndDropModifierNode$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$dragAndDropModifierNode$1.class", "size": 2848, "crc": -1092278063}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1$1.class", "size": 6666, "crc": -917173325}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1.class", "size": 2106, "crc": 1365233185}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "size": 8306, "crc": 1918385713}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "size": 1132, "crc": 222811972}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "size": 7792, "crc": 1458197252}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropStartDetectorScope.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropStartDetectorScope.class", "size": 1496, "crc": 329005159}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "size": 1773, "crc": -631867802}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode$createAndAttachDragAndDropModifierNode$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode$createAndAttachDragAndDropModifierNode$1.class", "size": 1843, "crc": 494271666}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "size": 4233, "crc": 1664921812}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "size": 1779, "crc": -577430392}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$dragAndDropModifierNode$1.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$dragAndDropModifierNode$1.class", "size": 2051, "crc": -63886756}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "size": 5878, "crc": 790987699}, {"key": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "name": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "size": 4376, "crc": -1203788711}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceElement.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceElement.class", "size": 7061, "crc": 1636397034}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1$1.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1$1.class", "size": 6960, "crc": 19770122}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1.class", "size": 2441, "crc": 959445074}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode.class", "size": 5274, "crc": 1841470340}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceWithDefaultShadowElement.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceWithDefaultShadowElement.class", "size": 4533, "crc": 1441859762}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$1.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$1.class", "size": 1823, "crc": 1668408811}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$2.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$2.class", "size": 3731, "crc": -1890622661}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "size": 1797, "crc": 421245993}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter.class", "size": 3790, "crc": 1973860029}, {"key": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "name": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "size": 1261, "crc": 918384503}, {"key": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "name": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "size": 954, "crc": -427218132}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableDefaults$PositionalThreshold$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableDefaults$PositionalThreshold$1.class", "size": 1372, "crc": -1501430023}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableDefaults.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableDefaults.class", "size": 8060, "crc": **********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableElement.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableElement.class", "size": 7121, "crc": 428410566}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AlwaysDrag$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AlwaysDrag$1.class", "size": 1516, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableLayoutInfoProvider$1.class", "size": 2904, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableState$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableState$1.class", "size": 1710, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableState$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableState$3.class", "size": 1765, "crc": -457189484}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$GetOrNan$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$GetOrNan$1.class", "size": 1363, "crc": **********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$NoOpDecayAnimationSpec$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$NoOpDecayAnimationSpec$1.class", "size": 1677, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$anchoredDraggableFlingBehavior$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$anchoredDraggableFlingBehavior$1.class", "size": 2884, "crc": -393750871}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2$2.class", "size": 2155, "crc": 888148167}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$4.class", "size": 4732, "crc": 667028675}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$1.class", "size": 1922, "crc": -1554544869}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2$3.class", "size": 3011, "crc": 763982086}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2.class", "size": 8618, "crc": 760581241}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "size": 1721, "crc": -285094169}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4077, "crc": 1919667117}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 2019, "crc": -169456717}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "size": 4458, "crc": -1355951465}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "size": 4485, "crc": -1797273902}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "size": 3845, "crc": -1716708488}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "size": 35578, "crc": 2013616443}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1$1.class", "size": 3109, "crc": 685744474}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2$1.class", "size": 3873, "crc": 1501724046}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2.class", "size": 4665, "crc": 1270460974}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$1.class", "size": 2069, "crc": -1325638064}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2$scrollScope$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2$scrollScope$1.class", "size": 2250, "crc": -65030500}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2.class", "size": 5014, "crc": -1994656300}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1$1.class", "size": 4778, "crc": -541803882}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1.class", "size": 4694, "crc": 364903115}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode.class", "size": 16951, "crc": 1467461485}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$1.class", "size": 1500, "crc": -214118229}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$1.class", "size": 2048, "crc": 2005476597}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$2.class", "size": 1722, "crc": 2138210960}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$3.class", "size": 1674, "crc": -971132540}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$4.class", "size": 2080, "crc": 1580395532}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$5.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$5.class", "size": 1979, "crc": 1439704111}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$6.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$6.class", "size": 1837, "crc": 1960780984}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$7.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$7.class", "size": 2243, "crc": -928212323}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$8.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$8.class", "size": 3557, "crc": 517806562}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "size": 6579, "crc": 1423551830}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$1.class", "size": 1793, "crc": 1379312713}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4610, "crc": 741533282}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "size": 5377, "crc": 164997737}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "size": 2038, "crc": 932894990}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$1.class", "size": 2011, "crc": 1943585412}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4823, "crc": 328933918}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "size": 5583, "crc": 332716260}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "size": 4471, "crc": 1434996146}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$confirmValueChange$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$confirmValueChange$1.class", "size": 1404, "crc": -1658369685}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$progress$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$progress$2.class", "size": 2097, "crc": 548526658}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$targetValue$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$targetValue$2.class", "size": 2032, "crc": 1345744363}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "size": 28123, "crc": -2086530000}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggable_jvmKt.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggable_jvmKt.class", "size": 1134, "crc": -503299836}, {"key": "androidx/compose/foundation/gestures/AndroidConfig.class", "name": "androidx/compose/foundation/gestures/AndroidConfig.class", "size": 6452, "crc": 2076424366}, {"key": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "name": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "size": 1486, "crc": -552572826}, {"key": "androidx/compose/foundation/gestures/AnimationData.class", "name": "androidx/compose/foundation/gestures/AnimationData.class", "size": 3409, "crc": 490337798}, {"key": "androidx/compose/foundation/gestures/AnimationDataConverter$convertFromVector$1.class", "name": "androidx/compose/foundation/gestures/AnimationDataConverter$convertFromVector$1.class", "size": 3264, "crc": -1880985198}, {"key": "androidx/compose/foundation/gestures/AnimationDataConverter$convertToVector$1.class", "name": "androidx/compose/foundation/gestures/AnimationDataConverter$convertToVector$1.class", "size": 3548, "crc": 2023475750}, {"key": "androidx/compose/foundation/gestures/AnimationDataConverter.class", "name": "androidx/compose/foundation/gestures/AnimationDataConverter.class", "size": 2292, "crc": 1793051123}, {"key": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue$enqueue$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue$enqueue$1.class", "size": 2187, "crc": 1650744924}, {"key": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "name": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "size": 10141, "crc": -1371558914}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "size": 896, "crc": 206616}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "size": 2574, "crc": -1737551894}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "size": 1825, "crc": -1778308282}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$LocalBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$LocalBringIntoViewSpec$1.class", "size": 2689, "crc": 4376257}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$PivotBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$PivotBringIntoViewSpec$1.class", "size": 1922, "crc": -1937545670}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt.class", "size": 2292, "crc": 1287327057}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "size": 4342, "crc": -1804387406}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "size": 874, "crc": 56417197}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$1.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$1.class", "size": 3945, "crc": 1284833348}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$2.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$2.class", "size": 5869, "crc": -1931559415}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "size": 5219, "crc": -1713520520}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "size": 5906, "crc": -758744247}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode.class", "size": 22612, "crc": 270601192}, {"key": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "name": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "size": 675, "crc": -1904784230}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "size": 4516, "crc": -649108710}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "size": 1560, "crc": 58223414}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "size": 4486, "crc": -584667412}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableAnchors.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableAnchors.class", "size": 7906, "crc": -1252776777}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "size": 4486, "crc": -1487511360}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "size": 1410, "crc": -287924311}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "size": 4252, "crc": 29128391}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2$1.class", "size": 2960, "crc": -1153865214}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "size": 5534, "crc": 588329229}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "size": 4139, "crc": 1504839569}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "size": 4329, "crc": 1434463582}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "size": 4762, "crc": 527207079}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "size": 2024, "crc": -487648323}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "size": 5987, "crc": -103915452}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "size": 4401, "crc": -1845438387}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "size": 4837, "crc": 433040782}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "size": 1808, "crc": 331375264}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "size": 5444, "crc": -605703211}, {"key": "androidx/compose/foundation/gestures/DelegatingAnimationSpec$vectorize$1.class", "name": "androidx/compose/foundation/gestures/DelegatingAnimationSpec$vectorize$1.class", "size": 10116, "crc": 1298331724}, {"key": "androidx/compose/foundation/gestures/DelegatingAnimationSpec.class", "name": "androidx/compose/foundation/gestures/DelegatingAnimationSpec.class", "size": 3897, "crc": 1200942887}, {"key": "androidx/compose/foundation/gestures/Drag2DScope.class", "name": "androidx/compose/foundation/gestures/Drag2DScope.class", "size": 576, "crc": 1231944747}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "size": 1025, "crc": -996824721}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "size": 1369, "crc": 1989751309}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "size": 1385, "crc": 344993911}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "size": 1379, "crc": -1119913170}, {"key": "androidx/compose/foundation/gestures/DragEvent.class", "name": "androidx/compose/foundation/gestures/DragEvent.class", "size": 1582, "crc": 1815534563}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitAllPointersUpWithSlopDetection$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitAllPointersUpWithSlopDetection$1.class", "size": 2078, "crc": 632675234}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "size": 1771, "crc": 1011319487}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "size": 1811, "crc": -13826406}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2127, "crc": 2146240523}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "size": 2115, "crc": -885819896}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "size": 1803, "crc": -1571845356}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "size": 11318, "crc": -19198388}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitPointerSlopOrCancellation$1.class", "size": 2114, "crc": 912642856}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "size": 2075, "crc": 1385162240}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "size": 1803, "crc": 2738408}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "size": 2117, "crc": -947169028}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "size": 2107, "crc": -1199296020}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$2.class", "size": 1728, "crc": 1386960912}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$3.class", "size": 1450, "crc": -1456838411}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$4.class", "size": 1450, "crc": -1735203904}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5.class", "size": 2635, "crc": 100546536}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$6.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$6.class", "size": 1981, "crc": 10344663}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$7.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$7.class", "size": 1485, "crc": -411157331}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$9.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$9.class", "size": 29845, "crc": 959228010}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$2.class", "size": 1770, "crc": -1066869607}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$3.class", "size": 1492, "crc": -1102637486}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$4.class", "size": 1492, "crc": 389127236}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5$1.class", "size": 2324, "crc": 1892452633}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "size": 8400, "crc": 595045029}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$2.class", "size": 1758, "crc": 1565979026}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$3.class", "size": 1480, "crc": -702796318}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$4.class", "size": 1480, "crc": -1300507057}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$1.class", "size": 3798, "crc": 1236699783}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$drag$1.class", "size": 1966, "crc": 1950393489}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "size": 6855, "crc": -493166879}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$2.class", "size": 1752, "crc": -2029626638}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$3.class", "size": 1474, "crc": 603392088}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$4.class", "size": 1474, "crc": -905976857}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$1.class", "size": 3792, "crc": 1934909551}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$drag$1.class", "size": 1960, "crc": 1063656497}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "size": 6835, "crc": -1132813480}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "size": 1712, "crc": 499124934}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "size": 1968, "crc": -901506544}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "size": 1896, "crc": 178767235}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "size": 1888, "crc": -1289116716}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "size": 93445, "crc": -415269300}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$_canDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$_canDrag$1.class", "size": 1767, "crc": 69765951}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$1.class", "size": 7072, "crc": 1613660570}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDrag$1.class", "size": 3071, "crc": -1723611710}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragCancel$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragCancel$1.class", "size": 2127, "crc": -1833442016}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragEnd$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragEnd$1.class", "size": 3638, "crc": 582102270}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragStart$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$onDragStart$1.class", "size": 4205, "crc": 1257856211}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$shouldAwaitTouchSlop$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$shouldAwaitTouchSlop$1.class", "size": 1623, "crc": -1069655695}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1.class", "size": 4148, "crc": 1426893536}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragCancel$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragCancel$1.class", "size": 1956, "crc": -1175868728}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragStart$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragStart$1.class", "size": 2304, "crc": -1191424887}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragStop$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragStop$1.class", "size": 2236, "crc": 2072578991}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1$1.class", "size": 5033, "crc": -1117296692}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1.class", "size": 6559, "crc": -651526174}, {"key": "androidx/compose/foundation/gestures/DragGestureNode.class", "name": "androidx/compose/foundation/gestures/DragGestureNode.class", "size": 17022, "crc": -1528809311}, {"key": "androidx/compose/foundation/gestures/DragScope.class", "name": "androidx/compose/foundation/gestures/DragScope.class", "size": 486, "crc": 590929696}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement$Companion$CanDrag$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement$Companion$CanDrag$1.class", "size": 1521, "crc": -489121700}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement$Companion.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement$Companion.class", "size": 1386, "crc": 663491451}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement.class", "size": 6706, "crc": 1659078483}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStart$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStart$1.class", "size": 1401, "crc": -1476186643}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStop$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpOnDragStop$1.class", "size": 1393, "crc": -1046934313}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$rememberDraggable2DState$1$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$rememberDraggable2DState$1$1.class", "size": 2062, "crc": -1360068809}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt.class", "size": 7607, "crc": 312882850}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2$1.class", "size": 3393, "crc": -474203515}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "size": 4292, "crc": -72466513}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode.class", "size": 7650, "crc": 1525722885}, {"key": "androidx/compose/foundation/gestures/Draggable2DState.class", "name": "androidx/compose/foundation/gestures/Draggable2DState.class", "size": 2162, "crc": -728003732}, {"key": "androidx/compose/foundation/gestures/DraggableAnchors.class", "name": "androidx/compose/foundation/gestures/DraggableAnchors.class", "size": 1362, "crc": 497562394}, {"key": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "name": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "size": 2847, "crc": -1439169759}, {"key": "androidx/compose/foundation/gestures/DraggableElement$Companion$CanDrag$1.class", "name": "androidx/compose/foundation/gestures/DraggableElement$Companion$CanDrag$1.class", "size": 1513, "crc": -1166306803}, {"key": "androidx/compose/foundation/gestures/DraggableElement$Companion.class", "name": "androidx/compose/foundation/gestures/DraggableElement$Companion.class", "size": 1378, "crc": 1580624745}, {"key": "androidx/compose/foundation/gestures/DraggableElement.class", "name": "androidx/compose/foundation/gestures/DraggableElement.class", "size": 7686, "crc": -1329166800}, {"key": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStarted$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStarted$1.class", "size": 2828, "crc": -1291606148}, {"key": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStopped$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStopped$1.class", "size": 2731, "crc": 1096364063}, {"key": "androidx/compose/foundation/gestures/DraggableKt$rememberDraggableState$1$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$rememberDraggableState$1$1.class", "size": 1904, "crc": -1348248932}, {"key": "androidx/compose/foundation/gestures/DraggableKt.class", "name": "androidx/compose/foundation/gestures/DraggableKt.class", "size": 10856, "crc": 1144245085}, {"key": "androidx/compose/foundation/gestures/DraggableNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$drag$2$1.class", "size": 2382, "crc": -1048950475}, {"key": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "name": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "size": 4258, "crc": 779972942}, {"key": "androidx/compose/foundation/gestures/DraggableNode$onDragStarted$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$onDragStarted$1.class", "size": 3706, "crc": -112510243}, {"key": "androidx/compose/foundation/gestures/DraggableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$onDragStopped$1.class", "size": 4089, "crc": -472145450}, {"key": "androidx/compose/foundation/gestures/DraggableNode.class", "name": "androidx/compose/foundation/gestures/DraggableNode.class", "size": 9402, "crc": 1646975796}, {"key": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "size": 685, "crc": -1463985869}, {"key": "androidx/compose/foundation/gestures/DraggableState.class", "name": "androidx/compose/foundation/gestures/DraggableState.class", "size": 2173, "crc": -601882604}, {"key": "androidx/compose/foundation/gestures/ExperimentalTapGestureDetectorBehaviorApi.class", "name": "androidx/compose/foundation/gestures/ExperimentalTapGestureDetectorBehaviorApi.class", "size": 1408, "crc": -1820990721}, {"key": "androidx/compose/foundation/gestures/FlingBehavior.class", "name": "androidx/compose/foundation/gestures/FlingBehavior.class", "size": 1051, "crc": 1749191400}, {"key": "androidx/compose/foundation/gestures/FlingCancellationException.class", "name": "androidx/compose/foundation/gestures/FlingCancellationException.class", "size": 1373, "crc": 937942080}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "size": 3573, "crc": 1724520394}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "size": 1749, "crc": 1562741074}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "size": 4927, "crc": 134277558}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "size": 1815, "crc": 612095504}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "size": 10110, "crc": 417534021}, {"key": "androidx/compose/foundation/gestures/GestureCancellationException.class", "name": "androidx/compose/foundation/gestures/GestureCancellationException.class", "size": 1297, "crc": 251231136}, {"key": "androidx/compose/foundation/gestures/LongPressResult$Canceled.class", "name": "androidx/compose/foundation/gestures/LongPressResult$Canceled.class", "size": 1043, "crc": 109502313}, {"key": "androidx/compose/foundation/gestures/LongPressResult$Released.class", "name": "androidx/compose/foundation/gestures/LongPressResult$Released.class", "size": 1407, "crc": 1875760090}, {"key": "androidx/compose/foundation/gestures/LongPressResult$Success.class", "name": "androidx/compose/foundation/gestures/LongPressResult$Success.class", "size": 1040, "crc": -889671045}, {"key": "androidx/compose/foundation/gestures/LongPressResult.class", "name": "androidx/compose/foundation/gestures/LongPressResult.class", "size": 1448, "crc": 824514491}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollableKt.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollableKt.class", "size": 2356, "crc": -2071192999}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$MouseWheelScrollDelta.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$MouseWheelScrollDelta.class", "size": 4244, "crc": -778067505}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$animateMouseWheelScroll$2.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$animateMouseWheelScroll$2.class", "size": 3700, "crc": 1379889044}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2$job$1$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2$job$1$1.class", "size": 1485, "crc": -2039834282}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2$job$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2$job$1.class", "size": 3819, "crc": -1037001812}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2.class", "size": 4964, "crc": 783665087}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$1.class", "size": 2624, "crc": 957634946}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$3$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$3$1.class", "size": 4359, "crc": -46608259}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$3.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$3.class", "size": 8556, "crc": 1640011581}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$1.class", "size": 2346, "crc": 617557463}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$2.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$2.class", "size": 4549, "crc": 152244316}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$startReceivingMouseWheelEvents$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$startReceivingMouseWheelEvents$1.class", "size": 6633, "crc": -1919368226}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$sumOrNull$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$sumOrNull$1.class", "size": 2149, "crc": -506190341}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$untilNull$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$untilNull$1.class", "size": 4490, "crc": -1631721678}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$1.class", "size": 2185, "crc": -207221411}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$2.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$2.class", "size": 4172, "crc": -1299909103}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic.class", "size": 29127, "crc": 1750284924}, {"key": "androidx/compose/foundation/gestures/MouseWheelVelocityTracker.class", "name": "androidx/compose/foundation/gestures/MouseWheelVelocityTracker.class", "size": 3345, "crc": -859148787}, {"key": "androidx/compose/foundation/gestures/NestedScrollScope.class", "name": "androidx/compose/foundation/gestures/NestedScrollScope.class", "size": 786, "crc": -1934391116}, {"key": "androidx/compose/foundation/gestures/Orientation.class", "name": "androidx/compose/foundation/gestures/Orientation.class", "size": 1895, "crc": -1058864836}, {"key": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "size": 3551, "crc": 947562992}, {"key": "androidx/compose/foundation/gestures/PressGestureScope.class", "name": "androidx/compose/foundation/gestures/PressGestureScope.class", "size": 3573, "crc": 586079994}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "size": 1829, "crc": 320488060}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "size": 1824, "crc": 2025490872}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "size": 1864, "crc": 213407831}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "size": 7628, "crc": -883932973}, {"key": "androidx/compose/foundation/gestures/ScrollConfig.class", "name": "androidx/compose/foundation/gestures/ScrollConfig.class", "size": 1324, "crc": 1026224499}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "size": 1701, "crc": -881403899}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2$1.class", "size": 1939, "crc": -1391159414}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "size": 4364, "crc": 1467573712}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "size": 1620, "crc": -938383262}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "size": 3502, "crc": 67105755}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "size": 3145, "crc": 770567318}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "size": 5940, "crc": 17757044}, {"key": "androidx/compose/foundation/gestures/ScrollScope.class", "name": "androidx/compose/foundation/gestures/ScrollScope.class", "size": 489, "crc": 327138994}, {"key": "androidx/compose/foundation/gestures/ScrollableContainerNode$TraverseKey.class", "name": "androidx/compose/foundation/gestures/ScrollableContainerNode$TraverseKey.class", "size": 896, "crc": -598022807}, {"key": "androidx/compose/foundation/gestures/ScrollableContainerNode.class", "name": "androidx/compose/foundation/gestures/ScrollableContainerNode.class", "size": 1885, "crc": 578095514}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaultFlingBehavior.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaultFlingBehavior.class", "size": 921, "crc": -1544147779}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect$node$1.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect$node$1.class", "size": 1002, "crc": -1488752870}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect.class", "size": 3633, "crc": -1900673778}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "size": 6096, "crc": 1105338827}, {"key": "androidx/compose/foundation/gestures/ScrollableElement.class", "name": "androidx/compose/foundation/gestures/ScrollableElement.class", "size": 7034, "crc": -1451731668}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$CanDragCalculation$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$CanDragCalculation$1.class", "size": 1853, "crc": -1314039040}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "size": 2889, "crc": 744784369}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "size": 931, "crc": -1238274369}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "size": 1005, "crc": -1079854474}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$1.class", "size": 1735, "crc": -253493035}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2$1.class", "size": 2675, "crc": -252200419}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2.class", "size": 4431, "crc": -1346402504}, {"key": "androidx/compose/foundation/gestures/ScrollableKt.class", "name": "androidx/compose/foundation/gestures/ScrollableKt.class", "size": 9338, "crc": -1157388979}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "size": 1973, "crc": 121790987}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "size": 4459, "crc": 1670831648}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$1.class", "size": 2070, "crc": -279257168}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1$1.class", "size": 2370, "crc": -1059856380}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1.class", "size": 4333, "crc": -930984541}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$ensureMouseWheelScrollNodeInitialized$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$ensureMouseWheelScrollNodeInitialized$1.class", "size": 2053, "crc": -1249235038}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onDragStopped$1.class", "size": 3586, "crc": -361082739}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "size": 3465, "crc": -1305394845}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1.class", "size": 3913, "crc": 943345152}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onWheelScrollStopped$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onWheelScrollStopped$1.class", "size": 3621, "crc": -1124667804}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$scrollingLogic$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$scrollingLogic$1.class", "size": 1633, "crc": 1137272743}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1$1.class", "size": 5213, "crc": 1531744433}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1.class", "size": 2126, "crc": -1945194500}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$2.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$2.class", "size": 3803, "crc": 1001395742}, {"key": "androidx/compose/foundation/gestures/ScrollableNode.class", "name": "androidx/compose/foundation/gestures/ScrollableNode.class", "size": 23931, "crc": 857234517}, {"key": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "size": 1597, "crc": 1942269319}, {"key": "androidx/compose/foundation/gestures/ScrollableState.class", "name": "androidx/compose/foundation/gestures/ScrollableState.class", "size": 3317, "crc": 1586049200}, {"key": "androidx/compose/foundation/gestures/ScrollableStateKt$rememberScrollableState$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableStateKt$rememberScrollableState$1$1.class", "size": 1877, "crc": -906076570}, {"key": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "name": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "size": 4445, "crc": -135072140}, {"key": "androidx/compose/foundation/gestures/Scrollable_jvmKt.class", "name": "androidx/compose/foundation/gestures/Scrollable_jvmKt.class", "size": 808, "crc": 641938169}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "size": 1837, "crc": -1944024158}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$reverseScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$reverseScope$1.class", "size": 2465, "crc": 664572683}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "size": 5498, "crc": 2077602293}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$nestedScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$nestedScrollScope$1.class", "size": 3941, "crc": 403089044}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$onScrollStopped$performFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$onScrollStopped$performFling$1.class", "size": 4612, "crc": 1880967589}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$performScrollForOverscroll$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$performScrollForOverscroll$1.class", "size": 3012, "crc": 124518057}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$scroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$scroll$2.class", "size": 4317, "crc": 1681019887}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic.class", "size": 20123, "crc": 2109754318}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "size": 2975, "crc": 1225475440}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "size": 1802, "crc": -1151890066}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitPrimaryFirstDown$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitPrimaryFirstDown$1.class", "size": 1837, "crc": -94696941}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "size": 4464, "crc": 1544592857}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "size": 1699, "crc": 1826502256}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "size": 4455, "crc": -701602655}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "size": 3410, "crc": 266303628}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "size": 3407, "crc": 1527294641}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$resetJob$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$resetJob$1.class", "size": 3564, "crc": -336246418}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "size": 8076, "crc": 246919315}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "size": 5124, "crc": 2119164334}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "size": 4455, "crc": 1925192620}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "size": 3407, "crc": 877337386}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "size": 3410, "crc": 1592631129}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "size": 3407, "crc": -73190393}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "size": 3763, "crc": -1814747077}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "size": 4461, "crc": -1341681435}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "size": 3407, "crc": -1581515348}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "size": 3406, "crc": 1867782873}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$resetJob$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$resetJob$1.class", "size": 3564, "crc": -1793954261}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$secondUp$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$secondUp$1.class", "size": 3443, "crc": -239696583}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "size": 11928, "crc": -1692014308}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "size": 5640, "crc": 1425106438}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$launchAwaitingReset$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$launchAwaitingReset$1.class", "size": 4169, "crc": -1639672450}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$1.class", "size": 1708, "crc": -1468530366}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$2.class", "size": 9098, "crc": -457855703}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "size": 1824, "crc": -953309150}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "size": 26240, "crc": -1110239174}, {"key": "androidx/compose/foundation/gestures/TapGestureDetector_androidKt.class", "name": "androidx/compose/foundation/gestures/TapGestureDetector_androidKt.class", "size": 972, "crc": -318863852}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehavior.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehavior.class", "size": 2560, "crc": -397598199}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt$NoOnReport$1.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt$NoOnReport$1.class", "size": 1329, "crc": 456345615}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt.class", "size": 1098, "crc": -1903706799}, {"key": "androidx/compose/foundation/gestures/TouchSlopDetector.class", "name": "androidx/compose/foundation/gestures/TouchSlopDetector.class", "size": 6898, "crc": -554871761}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "size": 1798, "crc": 141419920}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "size": 1058, "crc": 1560911895}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "size": 1058, "crc": 321138965}, {"key": "androidx/compose/foundation/gestures/TransformEvent.class", "name": "androidx/compose/foundation/gestures/TransformEvent.class", "size": 1328, "crc": -1992702891}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "size": 11869, "crc": -691230761}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "size": 10564, "crc": -1214885357}, {"key": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "size": 588, "crc": 1903495389}, {"key": "androidx/compose/foundation/gestures/TransformScope.class", "name": "androidx/compose/foundation/gestures/TransformScope.class", "size": 1477, "crc": -1527523797}, {"key": "androidx/compose/foundation/gestures/TransformableElement.class", "name": "androidx/compose/foundation/gestures/TransformableElement.class", "size": 4246, "crc": -1529513583}, {"key": "androidx/compose/foundation/gestures/TransformableKt$awaitCtrlMouseScrollOrNull$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$awaitCtrlMouseScrollOrNull$1.class", "size": 1831, "crc": -1579179070}, {"key": "androidx/compose/foundation/gestures/TransformableKt$awaitFirstCtrlMouseScroll$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$awaitFirstCtrlMouseScroll$1.class", "size": 1825, "crc": 376509892}, {"key": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "size": 2306, "crc": -1126951795}, {"key": "androidx/compose/foundation/gestures/TransformableKt$detectZoomByCtrlMouseScroll$2.class", "name": "androidx/compose/foundation/gestures/TransformableKt$detectZoomByCtrlMouseScroll$2.class", "size": 7613, "crc": 879228909}, {"key": "androidx/compose/foundation/gestures/TransformableKt$transformable$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$transformable$1.class", "size": 1600, "crc": -1059792564}, {"key": "androidx/compose/foundation/gestures/TransformableKt.class", "name": "androidx/compose/foundation/gestures/TransformableKt.class", "size": 20752, "crc": 1256670186}, {"key": "androidx/compose/foundation/gestures/TransformableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/gestures/TransformableNode$onPointerEvent$2.class", "size": 2375, "crc": -1711806122}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "size": 5248, "crc": 1827874117}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "size": 5332, "crc": -834569066}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "size": 5210, "crc": -2020876551}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "size": 4739, "crc": -1701175030}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "size": 2313, "crc": -1767532650}, {"key": "androidx/compose/foundation/gestures/TransformableNode$updatedCanPan$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$updatedCanPan$1.class", "size": 1813, "crc": 1234289674}, {"key": "androidx/compose/foundation/gestures/TransformableNode.class", "name": "androidx/compose/foundation/gestures/TransformableNode.class", "size": 8995, "crc": 1473429961}, {"key": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "size": 711, "crc": 2133184523}, {"key": "androidx/compose/foundation/gestures/TransformableState.class", "name": "androidx/compose/foundation/gestures/TransformableState.class", "size": 2228, "crc": -1806006483}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateBy$3$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateBy$3$1.class", "size": 3021, "crc": -663515180}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateBy$3.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateBy$3.class", "size": 5500, "crc": 312900262}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2$1.class", "size": 2570, "crc": 394489145}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "size": 5249, "crc": -1646744770}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2$1.class", "size": 2448, "crc": 710768155}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "size": 4795, "crc": 1891083964}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3$1.class", "size": 2503, "crc": 1404199515}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "size": 4786, "crc": 1498925295}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "size": 3288, "crc": -278580173}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$rememberTransformableState$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$rememberTransformableState$1$1.class", "size": 2470, "crc": 807118257}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "size": 3511, "crc": 1790398520}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "size": 3215, "crc": 1112394299}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "size": 3504, "crc": -95223857}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt.class", "size": 15747, "crc": 686379339}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "size": 1540, "crc": 767277023}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "size": 2085, "crc": 79669348}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$4.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$4.class", "size": 4348, "crc": -2143211126}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$5.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$5.class", "size": 2104, "crc": -917111600}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "size": 9482, "crc": -679790211}, {"key": "androidx/compose/foundation/gestures/ViewConfigurationApi26Impl.class", "name": "androidx/compose/foundation/gestures/ViewConfigurationApi26Impl.class", "size": 1367, "crc": 1065265458}, {"key": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "name": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "size": 2027, "crc": 2117795698}, {"key": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "size": 1685, "crc": 1293985504}, {"key": "androidx/compose/foundation/gestures/snapping/DecayApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/DecayApproachAnimation.class", "size": 4204, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "size": 1543, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "size": 2920, "crc": -910342863}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 6664, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "size": 8136, "crc": 960248744}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 6406, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "size": 7905, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 10045, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt.class", "size": 8192, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "size": 2204, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$4.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$4.class", "size": 1987, "crc": -20137791}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$animationState$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$animationState$1.class", "size": 2017, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "size": 9514, "crc": 504149083}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$1.class", "size": 1954, "crc": -633632581}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "size": 2182, "crc": 443325929}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "size": 13839, "crc": -1032666291}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "size": 1932, "crc": -849630784}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$2.class", "size": 3410, "crc": 1724300500}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "size": 1997, "crc": 303202668}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$2.class", "size": 3665, "crc": -628432}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "size": 19270, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "name": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "size": 764, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$Center.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$Center.class", "size": 1531, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$End.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$End.class", "size": 1518, "crc": 390129586}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$Start.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$Start.class", "size": 1476, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition.class", "size": 1021, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPositionKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPositionKt.class", "size": 1446, "crc": 89471175}, {"key": "androidx/compose/foundation/gestures/snapping/TargetApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/TargetApproachAnimation.class", "size": 4325, "crc": -275647282}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "size": 1475, "crc": 69428585}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "size": 911, "crc": 240826345}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "size": 1469, "crc": 1503604815}, {"key": "androidx/compose/foundation/interaction/DragInteraction.class", "name": "androidx/compose/foundation/interaction/DragInteraction.class", "size": 831, "crc": -768486122}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "size": 3232, "crc": 395384538}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "size": 4519, "crc": 1016563731}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt.class", "size": 4883, "crc": 2117086114}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "size": 916, "crc": 2124615409}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "size": 1487, "crc": -61583581}, {"key": "androidx/compose/foundation/interaction/FocusInteraction.class", "name": "androidx/compose/foundation/interaction/FocusInteraction.class", "size": 753, "crc": 1068317673}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "size": 3127, "crc": 976245829}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "size": 4529, "crc": 861394402}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "size": 4897, "crc": 1743686534}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "size": 916, "crc": -512486823}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "size": 1478, "crc": 202330039}, {"key": "androidx/compose/foundation/interaction/HoverInteraction.class", "name": "androidx/compose/foundation/interaction/HoverInteraction.class", "size": 747, "crc": -62848354}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "size": 3121, "crc": 1789758342}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "size": 4529, "crc": 1609687414}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "size": 4897, "crc": -1578810157}, {"key": "androidx/compose/foundation/interaction/Interaction.class", "name": "androidx/compose/foundation/interaction/Interaction.class", "size": 421, "crc": 1849544176}, {"key": "androidx/compose/foundation/interaction/InteractionSource.class", "name": "androidx/compose/foundation/interaction/InteractionSource.class", "size": 897, "crc": -824177501}, {"key": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "name": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "size": 852, "crc": 426372683}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "size": 1318, "crc": 1579698372}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "size": 3025, "crc": 126084959}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "size": 1484, "crc": -86149171}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "size": 1391, "crc": 6940183}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "size": 1487, "crc": -235465076}, {"key": "androidx/compose/foundation/interaction/PressInteraction.class", "name": "androidx/compose/foundation/interaction/PressInteraction.class", "size": 843, "crc": -596048242}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "size": 3250, "crc": 962482891}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "size": 4529, "crc": -519635879}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt.class", "size": 4901, "crc": 1587535890}, {"key": "androidx/compose/foundation/internal/ClipboardUtils.class", "name": "androidx/compose/foundation/internal/ClipboardUtils.class", "size": 3137, "crc": 648113857}, {"key": "androidx/compose/foundation/internal/ClipboardUtils_androidKt.class", "name": "androidx/compose/foundation/internal/ClipboardUtils_androidKt.class", "size": 8346, "crc": 2071022551}, {"key": "androidx/compose/foundation/internal/DecodeHelper.class", "name": "androidx/compose/foundation/internal/DecodeHelper.class", "size": 10392, "crc": -1637293999}, {"key": "androidx/compose/foundation/internal/EncodeHelper.class", "name": "androidx/compose/foundation/internal/EncodeHelper.class", "size": 10304, "crc": -369384716}, {"key": "androidx/compose/foundation/internal/InlineClassHelperKt.class", "name": "androidx/compose/foundation/internal/InlineClassHelperKt.class", "size": 3694, "crc": -719009293}, {"key": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 566, "crc": 1436889868}, {"key": "androidx/compose/foundation/internal/MutableSpanStyle.class", "name": "androidx/compose/foundation/internal/MutableSpanStyle.class", "size": 10226, "crc": 1230455304}, {"key": "androidx/compose/foundation/lazy/DefaultLazyListPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/DefaultLazyListPrefetchStrategy.class", "size": 5303, "crc": 1891517589}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$1.class", "size": 3948, "crc": 2119101724}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$2.class", "size": 3703, "crc": 1176978211}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$3.class", "size": 3641, "crc": -1292774426}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$1.class", "size": 3939, "crc": -567379180}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$2.class", "size": 3694, "crc": 815051573}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$3.class", "size": 3632, "crc": 1259899848}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "size": 1324, "crc": 935199244}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "size": 1778, "crc": -811839477}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "size": 1788, "crc": 1228991176}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "size": 3529, "crc": -1342038272}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "size": 1327, "crc": -340450626}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "size": 1712, "crc": -1955181797}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "size": 1722, "crc": 608741275}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "size": 3440, "crc": 1887451650}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "size": 2372, "crc": 1336944805}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "size": 2344, "crc": -558552201}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "size": 1495, "crc": 737663221}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "size": 2005, "crc": -2072744426}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "size": 2015, "crc": 278998695}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "size": 3756, "crc": -1074102927}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "size": 1498, "crc": -428446114}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "size": 1939, "crc": -1739087804}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "size": 1949, "crc": 2015187200}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "size": 3666, "crc": 510704921}, {"key": "androidx/compose/foundation/lazy/LazyDslKt.class", "name": "androidx/compose/foundation/lazy/LazyDslKt.class", "size": 40258, "crc": -81694729}, {"key": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "size": 2451, "crc": -803735532}, {"key": "androidx/compose/foundation/lazy/LazyItemScope.class", "name": "androidx/compose/foundation/lazy/LazyItemScope.class", "size": 4807, "crc": 295070323}, {"key": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "size": 4201, "crc": -482703387}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 5156, "crc": 1764999257}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "size": 1229, "crc": 1985860872}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "size": 3928, "crc": 1037132589}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "size": 3381, "crc": 2032218915}, {"key": "androidx/compose/foundation/lazy/LazyListInterval.class", "name": "androidx/compose/foundation/lazy/LazyListInterval.class", "size": 3327, "crc": -554254101}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$1.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$1.class", "size": 1352, "crc": 618654276}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$2.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$2.class", "size": 1355, "crc": 253515306}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "size": 3357, "crc": 312337019}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$stickyHeader$1.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$stickyHeader$1.class", "size": 3395, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "size": 7851, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "name": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "size": 990, "crc": -122339484}, {"key": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "size": 1226, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "size": 4843, "crc": 842320330}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "size": 5610, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "size": 1239, "crc": 409527011}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$intervalContentState$1.class", "size": 2083, "crc": -876457693}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$itemProviderState$1.class", "size": 3297, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "size": 5536, "crc": 999517710}, {"key": "androidx/compose/foundation/lazy/LazyListKt$LazyList$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$LazyList$1.class", "size": 4771, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measureResult$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measureResult$1.class", "size": 3121, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 4290, "crc": 508059564}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "size": 16172, "crc": -954010779}, {"key": "androidx/compose/foundation/lazy/LazyListKt.class", "name": "androidx/compose/foundation/lazy/LazyListKt.class", "size": 21463, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "size": 1887, "crc": -144813024}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "size": 3406, "crc": -980455156}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfoKt.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfoKt.class", "size": 2670, "crc": 424236519}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$3.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$3.class", "size": 2395, "crc": 1841336624}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$8$1.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$8$1.class", "size": 3706, "crc": 307029026}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$8.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$8.class", "size": 3452, "crc": 183879020}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$stickingItems$1.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$stickingItems$1.class", "size": 2684, "crc": 275949807}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "size": 31511, "crc": -2311508}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "size": 11273, "crc": -39912249}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "size": 18301, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "size": 422, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "size": 5550, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchScope.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchScope.class", "size": 1039, "crc": -416959634}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchStrategy.class", "size": 1806, "crc": -730549546}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchStrategyKt.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchStrategyKt.class", "size": 1199, "crc": -154905603}, {"key": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "size": 4792, "crc": 106458916}, {"key": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "name": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "size": 1309, "crc": -353357429}, {"key": "androidx/compose/foundation/lazy/LazyListScope$items$2.class", "name": "androidx/compose/foundation/lazy/LazyListScope$items$2.class", "size": 1327, "crc": 623691139}, {"key": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$1.class", "name": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$1.class", "size": 3317, "crc": 1375653645}, {"key": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$2.class", "name": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$2.class", "size": 3259, "crc": -498345956}, {"key": "androidx/compose/foundation/lazy/LazyListScope.class", "name": "androidx/compose/foundation/lazy/LazyListScope.class", "size": 8108, "crc": 119754265}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "size": 7376, "crc": -1354175640}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "size": 523, "crc": -254161024}, {"key": "androidx/compose/foundation/lazy/LazyListScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/lazy/LazyListScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 5495, "crc": -1721612380}, {"key": "androidx/compose/foundation/lazy/LazyListScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/LazyListScrollScopeKt.class", "size": 1349, "crc": 1668197762}, {"key": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "name": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "size": 3786, "crc": 1940640493}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$1.class", "size": 2059, "crc": 1291887128}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$2.class", "size": 1662, "crc": 2100586407}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$3.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$3.class", "size": 2278, "crc": 2053979159}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$4.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$saver$4.class", "size": 2035, "crc": 1933518983}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "size": 2528, "crc": -1844988434}, {"key": "androidx/compose/foundation/lazy/LazyListState$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$animateScrollToItem$2.class", "size": 4101, "crc": -1077550870}, {"key": "androidx/compose/foundation/lazy/LazyListState$prefetchScope$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$prefetchScope$1.class", "size": 4265, "crc": 1259669800}, {"key": "androidx/compose/foundation/lazy/LazyListState$prefetchState$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$prefetchState$1.class", "size": 4281, "crc": -1613277236}, {"key": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "size": 1427, "crc": -1922410925}, {"key": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1$1.class", "size": 3000, "crc": -497952980}, {"key": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1.class", "size": 3646, "crc": -427892438}, {"key": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "size": 1884, "crc": 1135657427}, {"key": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "size": 3400, "crc": 2071368965}, {"key": "androidx/compose/foundation/lazy/LazyListState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$scrollableState$1.class", "size": 1498, "crc": -1932207844}, {"key": "androidx/compose/foundation/lazy/LazyListState.class", "name": "androidx/compose/foundation/lazy/LazyListState.class", "size": 26845, "crc": -863015285}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "size": 1791, "crc": -2008183486}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$1$1.class", "size": 1479, "crc": 757363004}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$3$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$3$1.class", "size": 1736, "crc": 518936279}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt.class", "size": 8930, "crc": 1411477226}, {"key": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "name": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "size": 611, "crc": 968258692}, {"key": "androidx/compose/foundation/lazy/ParentSizeElement.class", "name": "androidx/compose/foundation/lazy/ParentSizeElement.class", "size": 4801, "crc": 719190238}, {"key": "androidx/compose/foundation/lazy/ParentSizeNode$measure$1.class", "name": "androidx/compose/foundation/lazy/ParentSizeNode$measure$1.class", "size": 1848, "crc": -1527009356}, {"key": "androidx/compose/foundation/lazy/ParentSizeNode.class", "name": "androidx/compose/foundation/lazy/ParentSizeNode.class", "size": 6029, "crc": 1053658998}, {"key": "androidx/compose/foundation/lazy/grid/DefaultLazyGridPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/grid/DefaultLazyGridPrefetchStrategy.class", "size": 9339, "crc": -808170718}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "size": 3995, "crc": 1827886425}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "size": 3166, "crc": 1055107160}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "size": 4464, "crc": -1223078301}, {"key": "androidx/compose/foundation/lazy/grid/GridCells.class", "name": "androidx/compose/foundation/lazy/grid/GridCells.class", "size": 1199, "crc": 1475381714}, {"key": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "name": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "size": 2339, "crc": 476227885}, {"key": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "name": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "size": 3555, "crc": -1723224606}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "size": 3829, "crc": 324099810}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "size": 3168, "crc": -1158089831}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$1.class", "size": 4387, "crc": 90329556}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$2.class", "size": 4085, "crc": 1219410419}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$1.class", "size": 4384, "crc": 1473128242}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$2.class", "size": 4082, "crc": 824946566}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "size": 1392, "crc": -1455760333}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "size": 3577, "crc": 1059578119}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "size": 1846, "crc": -1494724579}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "size": 2650, "crc": 818431642}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "size": 1856, "crc": 1000396084}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "size": 3665, "crc": -466518216}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "size": 1395, "crc": -356684914}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "size": 1780, "crc": 1348705635}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "size": 2560, "crc": -2100466586}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "size": 1790, "crc": -735277269}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "size": 1563, "crc": 764661464}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "size": 3803, "crc": 1519758945}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "size": 2073, "crc": -455101074}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "size": 2895, "crc": -1704864898}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "size": 2083, "crc": 1080425488}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "size": 3891, "crc": -1131740137}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "size": 1566, "crc": -1910765666}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "size": 2007, "crc": 444342990}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "size": 2805, "crc": -971546799}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "size": 2017, "crc": 953222500}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberColumnWidthSums$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberColumnWidthSums$1$1.class", "size": 4923, "crc": -1237262592}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberRowHeightSums$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberRowHeightSums$1$1.class", "size": 4832, "crc": 694574412}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "size": 37637, "crc": 151019704}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "size": 4337, "crc": -1101630114}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion$DefaultSpan$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion$DefaultSpan$1.class", "size": 1981, "crc": 2082609324}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "size": 1608, "crc": 111147094}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$1$1.class", "size": 1396, "crc": 390887612}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$2$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$2$1.class", "size": 2448, "crc": 1836942128}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$3.class", "size": 1402, "crc": -99195303}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "size": 3463, "crc": 1143093655}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$stickyHeader$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$stickyHeader$1.class", "size": 1943, "crc": 2075741392}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$stickyHeader$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$stickyHeader$2.class", "size": 3469, "crc": -1349941799}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "size": 11060, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "size": 916, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "size": 1671, "crc": 334346995}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "size": 1282, "crc": -664272590}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "size": 4963, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "size": 5671, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "size": 1259, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$intervalContentState$1.class", "size": 2133, "crc": -68920012}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$itemProviderState$1.class", "size": 3126, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "size": 5420, "crc": 193079334}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "size": 3038, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "size": 2497, "crc": -800153391}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "size": 782, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$LazyGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$LazyGrid$1.class", "size": 4449, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measureResult$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measureResult$1.class", "size": 3141, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 3754, "crc": 738337162}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "size": 3374, "crc": 793492272}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$prefetchInfoRetriever$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$prefetchInfoRetriever$1.class", "size": 5136, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "size": 17774, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "size": 21326, "crc": 167189238}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "size": 1893, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfoKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfoKt.class", "size": 3592, "crc": -1834857478}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$3.class", "size": 2525, "crc": 1854975776}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$6$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$6$1.class", "size": 3766, "crc": 217313638}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$6.class", "size": 3602, "crc": 542602815}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$stickingItems$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$stickingItems$1.class", "size": 3220, "crc": 1202678549}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "size": 35378, "crc": 835374445}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "size": 12342, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "size": 17697, "crc": -594747122}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "size": 427, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "size": 6110, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "size": 5862, "crc": 681014191}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "size": 6463, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchScope.class", "size": 1130, "crc": -187495812}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategy.class", "size": 1846, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategyKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategyKt.class", "size": 1229, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "size": 1365, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "size": 4769, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "size": 633, "crc": -952114053}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "size": 7767, "crc": 450811067}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "size": 528, "crc": 130101199}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 6309, "crc": -144483200}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt.class", "size": 1374, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "size": 1225, "crc": -827259742}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "size": 952, "crc": 50241184}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "size": 1961, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "size": 1258, "crc": 853799703}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "size": 1610, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "size": 1802, "crc": 962559776}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$getLineIndexOfItem$lowerBoundBucket$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$getLineIndexOfItem$lowerBoundBucket$1.class", "size": 1726, "crc": 200670325}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "size": 11822, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$1.class", "size": 2094, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$2.class", "size": 1697, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$3.class", "size": 2323, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$saver$4.class", "size": 2095, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "size": 2654, "crc": 1980303036}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$animateScrollToItem$2.class", "size": 4258, "crc": -1396815586}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchScope$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchScope$1.class", "size": 6060, "crc": -1040883102}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchState$1.class", "size": 4351, "crc": 508098609}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "size": 1462, "crc": -290330518}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$requestScrollToItem$1.class", "size": 3576, "crc": -1937206593}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "size": 1924, "crc": -1859628305}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "size": 3440, "crc": 2034565419}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollableState$1.class", "size": 1528, "crc": -1574765637}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "size": 27443, "crc": -1119152589}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "size": 1800, "crc": 1623113092}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$2.class", "size": 1650, "crc": -899411668}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$1$1.class", "size": 1519, "crc": -482279481}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$3$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$3$1.class", "size": 1791, "crc": -1133198665}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "size": 9067, "crc": 1324410725}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "size": 5080, "crc": -997014909}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "size": 3757, "crc": 1085187326}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$Companion.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$Companion.class", "size": 1892, "crc": -351479056}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$PrefetchRequestScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$PrefetchRequestScopeImpl.class", "size": 1389, "crc": 1133570255}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler.class", "size": 6978, "crc": 878742872}, {"key": "androidx/compose/foundation/lazy/layout/Averages.class", "name": "androidx/compose/foundation/lazy/layout/Averages.class", "size": 1959, "crc": 739658693}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "size": 1971, "crc": 341462649}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "size": 5265, "crc": 87838598}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "size": 1790, "crc": -1610007057}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "size": 1105, "crc": -937053211}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "size": 3239, "crc": 127372122}, {"key": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "name": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "size": 1261, "crc": 2004698704}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "size": 2941, "crc": 2097126356}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList.class", "size": 1988, "crc": -313023170}, {"key": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "name": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "size": 2643, "crc": 2054701453}, {"key": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "name": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "size": 1895, "crc": 1366667215}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateItemElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateItemElement.class", "size": 6755, "crc": 342684798}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "size": 3758, "crc": 134073451}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "size": 4345, "crc": 936056766}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "size": 5478, "crc": -1010047833}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierElement.class", "size": 4518, "crc": -646715953}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "size": 2338, "crc": 339691171}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion$emptyBeyondBoundsScope$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion$emptyBeyondBoundsScope$1.class", "size": 1211, "crc": -1817736734}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion.class", "size": 1244, "crc": -1815436785}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$WhenMappings.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$WhenMappings.class", "size": 896, "crc": -474522573}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$layout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$layout$2.class", "size": 2553, "crc": 2068955890}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$measure$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$measure$1.class", "size": 1947, "crc": 998956967}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode.class", "size": 11300, "crc": 530450133}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "size": 839, "crc": -42759079}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "size": 4704, "crc": 367819381}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "size": 1463, "crc": -738689622}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "size": 1525, "crc": -1476362044}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "size": 4722, "crc": 1771788868}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$1.class", "size": 1313, "crc": 680468711}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$Companion.class", "size": 1176, "crc": 1732290785}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$1.class", "size": 3734, "crc": -2120230945}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2$1.class", "size": 2530, "crc": -1643774033}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2.class", "size": 5216, "crc": -747743365}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1$1.class", "size": 2539, "crc": 1352273380}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1.class", "size": 5064, "crc": -2108571421}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1$1.class", "size": 2553, "crc": -1711987643}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1.class", "size": 5987, "crc": -1490848682}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$cancelPlacementAnimation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$cancelPlacementAnimation$1.class", "size": 4226, "crc": -85810913}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$1.class", "size": 3577, "crc": -668393164}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$2.class", "size": 3573, "crc": -2073026620}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$3.class", "size": 3573, "crc": -2043379152}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation.class", "size": 17491, "crc": 612629798}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimationKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimationKt.class", "size": 1514, "crc": 2100519839}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsElement.class", "size": 4994, "crc": -89244482}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsNode.class", "size": 7999, "crc": 950779570}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo$updateAnimation$1$animation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo$updateAnimation$1$animation$1.class", "size": 2228, "crc": 2060703800}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo.class", "size": 8305, "crc": -879299660}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$1.class", "size": 3060, "crc": -2051821327}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$2.class", "size": 3051, "crc": 1456727765}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "size": 3130, "crc": -1053200363}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "size": 3120, "crc": -368171688}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator.class", "size": 34652, "crc": 660644694}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimatorKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimatorKt.class", "size": 1524, "crc": 434285587}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$1$1$invoke$$inlined$onDispose$1.class", "size": 2733, "crc": 1752689450}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$1$1.class", "size": 3461, "crc": 1532586373}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "size": 6932, "crc": 1265427020}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "size": 4267, "crc": 384003886}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "size": 4633, "crc": 1461961580}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "size": 2794, "crc": 999935488}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$2.class", "size": 2097, "crc": -23984382}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "size": 3815, "crc": -761617261}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "size": 1581, "crc": 811406987}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "size": 1350, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "size": 3929, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "size": 1456, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "size": 1083, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1$1$invoke$$inlined$onDispose$1.class", "size": 2343, "crc": 463530480}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1$1.class", "size": 3894, "crc": 56882113}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$2$1.class", "size": 3133, "crc": -873310096}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$itemContentFactory$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$itemContentFactory$1$1.class", "size": 1907, "crc": -627246363}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1.class", "size": 10237, "crc": 43735819}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "size": 2940, "crc": -1766740285}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "size": 5471, "crc": 1222935256}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "size": 7078, "crc": 1311112261}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "size": 10831, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItem.class", "size": 1735, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemKt.class", "size": 4629, "crc": 481226610}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemProvider.class", "size": 1080, "crc": 988672379}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "size": 1723, "crc": -442003375}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "size": 4083, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "size": 7873, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1$invoke$$inlined$onDispose$1.class", "size": 2390, "crc": -556592009}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1.class", "size": 3113, "crc": 1529888223}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$2.class", "size": 2430, "crc": 789265866}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "size": 7470, "crc": 85372622}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "size": 911, "crc": -1844472542}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "size": 8582, "crc": 324752765}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$NestedPrefetchScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$NestedPrefetchScopeImpl.class", "size": 3046, "crc": 1370939451}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "size": 853, "crc": 69093522}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "size": 5813, "crc": 807599072}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchStateKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchStateKt.class", "size": 2649, "crc": -1768676937}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses$updateScrollDeltaForApproach$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses$updateScrollDeltaForApproach$2$1.class", "size": 4438, "crc": 1744949694}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses$updateScrollDeltaForApproach$2$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses$updateScrollDeltaForApproach$2$2.class", "size": 4438, "crc": -1286214026}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses.class", "size": 7031, "crc": -1450669309}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPassesKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPassesKt.class", "size": 1767, "crc": 809505520}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScope.class", "size": 1844, "crc": 1887475197}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$1.class", "size": 2175, "crc": 1188220521}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$4.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$4.class", "size": 7065, "crc": -763298922}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$6.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$6.class", "size": 4080, "crc": 901675163}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt.class", "size": 12537, "crc": -1071342843}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "size": 1329, "crc": -301162395}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "size": 3441, "crc": 1345587582}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifier.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifier.class", "size": 5092, "crc": 1939717813}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$applySemantics$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$applySemantics$2.class", "size": 1776, "crc": -1030652639}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$indexForKeyMapping$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$indexForKeyMapping$1.class", "size": 2424, "crc": -688000467}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$1.class", "size": 1716, "crc": 1577763628}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$2.class", "size": 1719, "crc": -1475459133}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3$2.class", "size": 3944, "crc": -490922285}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3.class", "size": 4431, "crc": 429330351}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode.class", "size": 7824, "crc": 1489625697}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutStickyItemsKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutStickyItemsKt.class", "size": 7752, "crc": 1276416077}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$1.class", "size": 1707, "crc": 706013941}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$1.class", "size": 3094, "crc": 2022516783}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$2.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$2.class", "size": 2411, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "size": 2733, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$1$1$invoke$$inlined$onDispose$1.class", "size": 2675, "crc": 498384865}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$1$1.class", "size": 3369, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "size": 9946, "crc": -224993208}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "size": 3209, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$2.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$2.class", "size": 2101, "crc": -406786339}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$holder$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$holder$1$1.class", "size": 2031, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "size": 7883, "crc": -314216040}, {"key": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "size": 714, "crc": -88574304}, {"key": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "name": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "size": 7659, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap$2$1.class", "name": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap$2$1.class", "size": 3803, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "size": 5895, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/NestedPrefetchScope.class", "name": "androidx/compose/foundation/lazy/layout/NestedPrefetchScope.class", "size": 906, "crc": 694257403}, {"key": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "name": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "size": 4450, "crc": 684269482}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$NestedPrefetchController.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$NestedPrefetchController.class", "size": 6539, "crc": 40275664}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$resolveNestedPrefetchStates$1.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$resolveNestedPrefetchStates$1.class", "size": 4484, "crc": 253333381}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl.class", "size": 14624, "crc": 869142734}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider.class", "size": 4130, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchMetrics.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchMetrics.class", "size": 4258, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchRequest.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchRequest.class", "size": 910, "crc": -754291801}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchRequestScope.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchRequestScope.class", "size": 603, "crc": 365743966}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler.class", "size": 841, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt$RobolectricImpl$1.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt$RobolectricImpl$1.class", "size": 1155, "crc": -765537853}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt.class", "size": 5499, "crc": 341691532}, {"key": "androidx/compose/foundation/lazy/layout/StableValue.class", "name": "androidx/compose/foundation/lazy/layout/StableValue.class", "size": 2678, "crc": -169538817}, {"key": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion$StickToTopPlacement$1.class", "name": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion$StickToTopPlacement$1.class", "size": 5782, "crc": 1416333482}, {"key": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion.class", "name": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion.class", "size": 1324, "crc": 1691099468}, {"key": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement.class", "name": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement.class", "size": 1684, "crc": 570708128}, {"key": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateModifierElement.class", "name": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateModifierElement.class", "size": 4434, "crc": 790630823}, {"key": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateNode.class", "name": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateNode.class", "size": 1965, "crc": -648740539}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "size": 1041, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "size": 4152, "crc": -916829485}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "size": 3286, "crc": -126816195}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "size": 1093, "crc": -568339589}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$1.class", "size": 4349, "crc": 1130668634}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$2.class", "size": 4047, "crc": 71031709}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$1.class", "size": 4358, "crc": -360252198}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$2.class", "size": 4056, "crc": -1049919018}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "size": 1473, "crc": -2119820685}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "size": 3792, "crc": -1024193301}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "size": 1931, "crc": -827971284}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "size": 1937, "crc": -746230069}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "size": 2312, "crc": 937013336}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "size": 3880, "crc": -1546348850}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "size": 1476, "crc": 1447462190}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "size": 1865, "crc": -2094423678}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "size": 1871, "crc": 1654740577}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "size": 2222, "crc": 1962283812}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "size": 1644, "crc": 1823718043}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "size": 4018, "crc": 1877952449}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "size": 2158, "crc": 1561033820}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "size": 2164, "crc": -144162188}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "size": 2539, "crc": 564611911}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "size": 4106, "crc": 1225508731}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "size": 1647, "crc": -451570799}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "size": 2092, "crc": -1221083053}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "size": 2098, "crc": 2051007726}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "size": 2449, "crc": 1606346100}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberColumnSlots$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberColumnSlots$1$1.class", "size": 5916, "crc": -1360370052}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberRowSlots$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberRowSlots$1$1.class", "size": 5724, "crc": 1269000294}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "size": 40454, "crc": -404801045}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "size": 4208, "crc": -240973514}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$1$1.class", "size": 1497, "crc": -278958989}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$2.class", "size": 1503, "crc": -1626213898}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$3$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$3$1.class", "size": 1726, "crc": -186760730}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "size": 3704, "crc": 2025451833}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "size": 7933, "crc": 110652505}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "size": 1245, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "size": 1176, "crc": 923093587}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "size": 5341, "crc": -292171487}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "size": 5706, "crc": 289016851}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "size": 1355, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$intervalContentState$1.class", "size": 2337, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$itemProviderState$1.class", "size": 3474, "crc": -409889306}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "size": 5869, "crc": -189760005}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "size": 3137, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "size": 2574, "crc": 793831476}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt$LazyStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt$LazyStaggeredGrid$1.class", "size": 4259, "crc": -988337239}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "size": 16976, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "size": 1051, "crc": -1011742212}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "size": 1382, "crc": -1917906340}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "size": 3076, "crc": -240758965}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "size": 3077, "crc": -449469460}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "size": 8714, "crc": -284591070}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "size": 1845, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "size": 4235, "crc": -747208916}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "size": 12854, "crc": -648398824}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$1.class", "size": 1764, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$29$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$29$1.class", "size": 1922, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$30$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$30$1.class", "size": 1922, "crc": -1573568171}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$33$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$33$1.class", "size": 4366, "crc": -1550085339}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$33.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$33.class", "size": 3478, "crc": -127459473}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "size": 73825, "crc": -229456894}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "size": 937, "crc": 263817597}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "size": 12071, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "size": 9857, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "size": 7427, "crc": -34850089}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "size": 12609, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "size": 1913, "crc": -656454344}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$findVisibleItem$index$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$findVisibleItem$index$1.class", "size": 1873, "crc": 384499526}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "size": 8164, "crc": 864698006}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "size": 20106, "crc": 119855287}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "size": 1446, "crc": 1914515534}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "size": 4028, "crc": -1337731428}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "size": 670, "crc": -1697877773}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "size": 12240, "crc": -1632367474}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "size": 555, "crc": -253443156}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 6622, "crc": 1017491446}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt.class", "size": 1473, "crc": -449198364}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "size": 5514, "crc": -1327607094}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "size": 4158, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "size": 3852, "crc": 272286191}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "size": 1270, "crc": 858197865}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "size": 3378, "crc": -154896036}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$1.class", "size": 2356, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$2.class", "size": 1797, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "size": 1500, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$animateScrollToItem$2.class", "size": 4762, "crc": 1267677547}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "size": 1576, "crc": 1023091080}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$requestScrollToItem$1.class", "size": 3729, "crc": 1378319926}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "size": 2077, "crc": -1665265024}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "size": 1608, "crc": -190336276}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "size": 3588, "crc": -794093191}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollableState$1.class", "size": 1683, "crc": -2111282244}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "size": 35010, "crc": -2126547267}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt$rememberLazyStaggeredGridState$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt$rememberLazyStaggeredGridState$1$1.class", "size": 1699, "crc": 1522205424}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "size": 4716, "crc": 1169910564}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "size": 4267, "crc": 1141742997}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "size": 4079, "crc": 1761449675}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "size": 3266, "crc": 386151386}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "size": 2542, "crc": 1772460803}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "size": 1185, "crc": 1232393733}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "size": 1477, "crc": -1841356951}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "size": 1672, "crc": -1256156835}, {"key": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "name": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "size": 7747, "crc": 1379306067}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$1.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$1.class", "size": 2293, "crc": -679301268}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2$1.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2$1.class", "size": 1567, "crc": -409789501}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion$Saver$2.class", "size": 2146, "crc": -331213638}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion.class", "size": 1379, "crc": -966667944}, {"key": "androidx/compose/foundation/pager/DefaultPagerState.class", "name": "androidx/compose/foundation/pager/DefaultPagerState.class", "size": 3697, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$2.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$2.class", "size": 5599, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$measurePolicy$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$measurePolicy$1$1.class", "size": 2170, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$pagerItemProvider$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$pagerItemProvider$1$1.class", "size": 2178, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "size": 7295, "crc": 987301455}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "size": 3993, "crc": -506473753}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "size": 2179, "crc": -889742907}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "size": 1275, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$intervalContentState$1.class", "size": 2967, "crc": -442121937}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$itemProviderState$1.class", "size": 3106, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "size": 27860, "crc": 227170174}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 5164, "crc": 856172820}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "size": 1225, "crc": -**********}, {"key": "androidx/compose/foundation/pager/MeasuredPage.class", "name": "androidx/compose/foundation/pager/MeasuredPage.class", "size": 12012, "crc": -831325304}, {"key": "androidx/compose/foundation/pager/MeasuredPageKt.class", "name": "androidx/compose/foundation/pager/MeasuredPageKt.class", "size": 407, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PageInfo.class", "name": "androidx/compose/foundation/pager/PageInfo.class", "size": 755, "crc": 228392556}, {"key": "androidx/compose/foundation/pager/PageSize$Fill.class", "name": "androidx/compose/foundation/pager/PageSize$Fill.class", "size": 1323, "crc": 24300144}, {"key": "androidx/compose/foundation/pager/PageSize$Fixed.class", "name": "androidx/compose/foundation/pager/PageSize$Fixed.class", "size": 2290, "crc": -407840688}, {"key": "androidx/compose/foundation/pager/PageSize.class", "name": "androidx/compose/foundation/pager/PageSize.class", "size": 946, "crc": 1479737860}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "size": 3865, "crc": -1556614058}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "size": 3126, "crc": 393111092}, {"key": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "name": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "size": 2733, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerDebugConfig.class", "name": "androidx/compose/foundation/pager/PagerDebugConfig.class", "size": 1195, "crc": 918832572}, {"key": "androidx/compose/foundation/pager/PagerDefaults$flingBehavior$2$snapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/pager/PagerDefaults$flingBehavior$2$snapLayoutInfoProvider$1.class", "size": 2399, "crc": -693078741}, {"key": "androidx/compose/foundation/pager/PagerDefaults.class", "name": "androidx/compose/foundation/pager/PagerDefaults.class", "size": 10745, "crc": -969966911}, {"key": "androidx/compose/foundation/pager/PagerIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerIntervalContent.class", "size": 2952, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$1.class", "name": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$1.class", "size": 5034, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$2.class", "name": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$2.class", "size": 4789, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$VerticalPager$1.class", "name": "androidx/compose/foundation/pager/PagerKt$VerticalPager$1.class", "size": 5042, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerKt$VerticalPager$2.class", "name": "androidx/compose/foundation/pager/PagerKt$VerticalPager$2.class", "size": 4797, "crc": 1723845847}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$1.class", "size": 1612, "crc": 712451196}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$2.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$2.class", "size": 1611, "crc": -26851826}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$3.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$3.class", "size": 1612, "crc": -124458923}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$4.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$4.class", "size": 1611, "crc": 836072739}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1.class", "size": 2728, "crc": 712196321}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "size": 3508, "crc": 1101897480}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "size": 3499, "crc": -1908182104}, {"key": "androidx/compose/foundation/pager/PagerKt.class", "name": "androidx/compose/foundation/pager/PagerKt.class", "size": 29800, "crc": -1604743365}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "size": 2174, "crc": 468200310}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "size": 2375, "crc": -803367013}, {"key": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "size": 4177, "crc": 500772643}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "size": 4940, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "size": 5793, "crc": -12799676}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$14$1.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$14$1.class", "size": 3078, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$14.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$14.class", "size": 3014, "crc": 376373765}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$4.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$4.class", "size": 2203, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesAfter$1.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesAfter$1.class", "size": 3708, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesBefore$1.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesBefore$1.class", "size": 3710, "crc": -1259473513}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt.class", "size": 30603, "crc": 411307629}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1$measureResult$1.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1$measureResult$1.class", "size": 3153, "crc": -1085033347}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "size": 14225, "crc": 1545274395}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "size": 8327, "crc": -366306462}, {"key": "androidx/compose/foundation/pager/PagerMeasureResult.class", "name": "androidx/compose/foundation/pager/PagerMeasureResult.class", "size": 12780, "crc": -534881478}, {"key": "androidx/compose/foundation/pager/PagerScope.class", "name": "androidx/compose/foundation/pager/PagerScope.class", "size": 471, "crc": 1588605353}, {"key": "androidx/compose/foundation/pager/PagerScopeImpl.class", "name": "androidx/compose/foundation/pager/PagerScopeImpl.class", "size": 917, "crc": -1047110124}, {"key": "androidx/compose/foundation/pager/PagerScrollPosition.class", "name": "androidx/compose/foundation/pager/PagerScrollPosition.class", "size": 7617, "crc": 2081839266}, {"key": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "name": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "size": 1596, "crc": -1985598935}, {"key": "androidx/compose/foundation/pager/PagerScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/pager/PagerScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 3865, "crc": 668295493}, {"key": "androidx/compose/foundation/pager/PagerScrollScopeKt.class", "name": "androidx/compose/foundation/pager/PagerScrollScopeKt.class", "size": 1336, "crc": 1585696198}, {"key": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "name": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "size": 3724, "crc": -72369324}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "size": 2676, "crc": -1430246469}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance.class", "size": 1038, "crc": 519033178}, {"key": "androidx/compose/foundation/pager/PagerSnapDistanceKt.class", "name": "androidx/compose/foundation/pager/PagerSnapDistanceKt.class", "size": 732, "crc": 372425747}, {"key": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "name": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "size": 2830, "crc": 384211018}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "size": 1937, "crc": 94067090}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3$1.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3$1.class", "size": 1838, "crc": 1183932891}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3.class", "size": 4592, "crc": 226578032}, {"key": "androidx/compose/foundation/pager/PagerState$prefetchState$1.class", "name": "androidx/compose/foundation/pager/PagerState$prefetchState$1.class", "size": 3689, "crc": -268292060}, {"key": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "size": 1410, "crc": 35648384}, {"key": "androidx/compose/foundation/pager/PagerState$requestScrollToPage$1.class", "name": "androidx/compose/foundation/pager/PagerState$requestScrollToPage$1.class", "size": 3517, "crc": -1188765485}, {"key": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "name": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "size": 1940, "crc": -1318405921}, {"key": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "size": 5459, "crc": 1069852923}, {"key": "androidx/compose/foundation/pager/PagerState$scrollableState$1.class", "name": "androidx/compose/foundation/pager/PagerState$scrollableState$1.class", "size": 1522, "crc": 639726472}, {"key": "androidx/compose/foundation/pager/PagerState$settledPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$settledPage$2.class", "size": 1518, "crc": 1156395888}, {"key": "androidx/compose/foundation/pager/PagerState$targetPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$targetPage$2.class", "size": 2009, "crc": -1542585371}, {"key": "androidx/compose/foundation/pager/PagerState.class", "name": "androidx/compose/foundation/pager/PagerState.class", "size": 44705, "crc": 767120269}, {"key": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "size": 1764, "crc": 164650892}, {"key": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "size": 1064, "crc": 918058605}, {"key": "androidx/compose/foundation/pager/PagerStateKt$animateScrollToPage$4.class", "name": "androidx/compose/foundation/pager/PagerStateKt$animateScrollToPage$4.class", "size": 3024, "crc": 1693706469}, {"key": "androidx/compose/foundation/pager/PagerStateKt$rememberPagerState$1$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$rememberPagerState$1$1.class", "size": 1738, "crc": -697837219}, {"key": "androidx/compose/foundation/pager/PagerStateKt.class", "name": "androidx/compose/foundation/pager/PagerStateKt.class", "size": 16347, "crc": 1772823273}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "size": 1910, "crc": -492338711}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$resultVelocity$1$1.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$resultVelocity$1$1.class", "size": 3062, "crc": -333900032}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "size": 4596, "crc": 1560397910}, {"key": "androidx/compose/foundation/platform/Synchronization_androidKt.class", "name": "androidx/compose/foundation/platform/Synchronization_androidKt.class", "size": 2063, "crc": -1176801507}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "size": 1567, "crc": -1419947924}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "size": 3274, "crc": -916076360}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "size": 2058, "crc": -556448600}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$2$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$2$1.class", "size": 1336, "crc": -679025798}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "size": 5553, "crc": -2020612435}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "size": 1633, "crc": 838020376}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewRequesterKt.class", "size": 1643, "crc": 41167110}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewResponderKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewResponderKt.class", "size": 2454, "crc": -145697629}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "size": 3491, "crc": -354848941}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "size": 1301, "crc": -833016166}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "size": 3274, "crc": -1969694123}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1$1.class", "size": 2675, "crc": -1232134624}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1.class", "size": 4484, "crc": 1827449906}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$2.class", "size": 3881, "crc": -414859021}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2.class", "size": 4976, "crc": -1807052264}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$parentRect$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$parentRect$1.class", "size": 3386, "crc": -1241720485}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "size": 6438, "crc": 1720060686}, {"key": "androidx/compose/foundation/selection/SelectableElement.class", "name": "androidx/compose/foundation/selection/SelectableElement.class", "size": 5614, "crc": -1304979364}, {"key": "androidx/compose/foundation/selection/SelectableGroupKt$selectableGroup$1.class", "name": "androidx/compose/foundation/selection/SelectableGroupKt$selectableGroup$1.class", "size": 1639, "crc": 1746266004}, {"key": "androidx/compose/foundation/selection/SelectableGroupKt.class", "name": "androidx/compose/foundation/selection/SelectableGroupKt.class", "size": 1235, "crc": 32293088}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "size": 6249, "crc": 49296150}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6699, "crc": 716054352}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3290, "crc": 857957207}, {"key": "androidx/compose/foundation/selection/SelectableKt.class", "name": "androidx/compose/foundation/selection/SelectableKt.class", "size": 7047, "crc": 1973675819}, {"key": "androidx/compose/foundation/selection/SelectableNode.class", "name": "androidx/compose/foundation/selection/SelectableNode.class", "size": 3691, "crc": -854686042}, {"key": "androidx/compose/foundation/selection/ToggleableElement.class", "name": "androidx/compose/foundation/selection/ToggleableElement.class", "size": 5709, "crc": 1140496143}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "size": 6291, "crc": 2035686353}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6702, "crc": 1345398096}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3288, "crc": 1444794660}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "size": 6549, "crc": 1697198766}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6908, "crc": -818380868}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3461, "crc": 191601201}, {"key": "androidx/compose/foundation/selection/ToggleableKt.class", "name": "androidx/compose/foundation/selection/ToggleableKt.class", "size": 11522, "crc": -561015129}, {"key": "androidx/compose/foundation/selection/ToggleableNode$1.class", "name": "androidx/compose/foundation/selection/ToggleableNode$1.class", "size": 1769, "crc": -1094036951}, {"key": "androidx/compose/foundation/selection/ToggleableNode$_onClick$1.class", "name": "androidx/compose/foundation/selection/ToggleableNode$_onClick$1.class", "size": 1854, "crc": 245669267}, {"key": "androidx/compose/foundation/selection/ToggleableNode.class", "name": "androidx/compose/foundation/selection/ToggleableNode.class", "size": 5163, "crc": -1596859945}, {"key": "androidx/compose/foundation/selection/TriStateToggleableElement.class", "name": "androidx/compose/foundation/selection/TriStateToggleableElement.class", "size": 5917, "crc": 589891646}, {"key": "androidx/compose/foundation/selection/TriStateToggleableNode.class", "name": "androidx/compose/foundation/selection/TriStateToggleableNode.class", "size": 3980, "crc": -729324569}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "size": 8087, "crc": 2072370283}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "size": 4801, "crc": 2024327700}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "size": 7312, "crc": -1842498214}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "size": 4905, "crc": -1041239064}, {"key": "androidx/compose/foundation/shape/CornerBasedShape.class", "name": "androidx/compose/foundation/shape/CornerBasedShape.class", "size": 6318, "crc": -669796649}, {"key": "androidx/compose/foundation/shape/CornerSize.class", "name": "androidx/compose/foundation/shape/CornerSize.class", "size": 839, "crc": -1020805876}, {"key": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "name": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "size": 1589, "crc": -213898037}, {"key": "androidx/compose/foundation/shape/CornerSizeKt.class", "name": "androidx/compose/foundation/shape/CornerSizeKt.class", "size": 2027, "crc": 1736944252}, {"key": "androidx/compose/foundation/shape/CutCornerShape.class", "name": "androidx/compose/foundation/shape/CutCornerShape.class", "size": 8081, "crc": 1583859584}, {"key": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "size": 4589, "crc": -1512967658}, {"key": "androidx/compose/foundation/shape/DpCornerSize.class", "name": "androidx/compose/foundation/shape/DpCornerSize.class", "size": 3873, "crc": 676541803}, {"key": "androidx/compose/foundation/shape/GenericShape.class", "name": "androidx/compose/foundation/shape/GenericShape.class", "size": 3535, "crc": -1411243660}, {"key": "androidx/compose/foundation/shape/PercentCornerSize.class", "name": "androidx/compose/foundation/shape/PercentCornerSize.class", "size": 3320, "crc": 2063537042}, {"key": "androidx/compose/foundation/shape/PxCornerSize.class", "name": "androidx/compose/foundation/shape/PxCornerSize.class", "size": 2806, "crc": -1458560014}, {"key": "androidx/compose/foundation/shape/RoundedCornerShape.class", "name": "androidx/compose/foundation/shape/RoundedCornerShape.class", "size": 7185, "crc": -281366971}, {"key": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "size": 4966, "crc": -1188979530}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1.class", "size": 9717, "crc": 1589334771}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$2.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$2.class", "size": 2054, "crc": 867305831}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$finalModifier$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$finalModifier$1$1.class", "size": 2717, "crc": 486943255}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$DefaultCursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$DefaultCursorHandle$1.class", "size": 1799, "crc": 1777689019}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1$1.class", "size": 5264, "crc": -568159591}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1.class", "size": 4291, "crc": -683512265}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "size": 5756, "crc": 663634360}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "size": 9842, "crc": -1880140169}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "size": 3585, "crc": -1321040844}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2.class", "size": 4330, "crc": -1046766116}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$2.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$2.class", "size": 2425, "crc": 767199391}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "size": 13204, "crc": 601868939}, {"key": "androidx/compose/foundation/text/AutoSizeStepBased.class", "name": "androidx/compose/foundation/text/AutoSizeStepBased.class", "size": 7781, "crc": 1757991755}, {"key": "androidx/compose/foundation/text/AutofillHighlightKt$LocalAutofillHighlightColor$1.class", "name": "androidx/compose/foundation/text/AutofillHighlightKt$LocalAutofillHighlightColor$1.class", "size": 1411, "crc": 1975907784}, {"key": "androidx/compose/foundation/text/AutofillHighlightKt.class", "name": "androidx/compose/foundation/text/AutofillHighlightKt.class", "size": 1577, "crc": -482371125}, {"key": "androidx/compose/foundation/text/AutofillHighlight_androidKt.class", "name": "androidx/compose/foundation/text/AutofillHighlight_androidKt.class", "size": 593, "crc": 1283782336}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "size": 4000, "crc": -720325410}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$2$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$2$1.class", "size": 4128, "crc": -548525765}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "size": 7664, "crc": -1538320310}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$4.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$4.class", "size": 4736, "crc": -1494457227}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$5.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$5.class", "size": 4683, "crc": -859865830}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1.class", "size": 2570, "crc": 1206014210}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$2.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$2.class", "size": 2695, "crc": 1692919435}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$1.class", "size": 1935, "crc": 486254201}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "size": 3055, "crc": -1314078097}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt.class", "size": 25335, "crc": 2009256344}, {"key": "androidx/compose/foundation/text/BasicTextFieldDefaults.class", "name": "androidx/compose/foundation/text/BasicTextFieldDefaults.class", "size": 1358, "crc": 1798208650}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$1.class", "size": 5373, "crc": 1145191057}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$10.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$10.class", "size": 1836, "crc": 1359083052}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$11$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$11$1.class", "size": 2512, "crc": -1450159228}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$12.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$12.class", "size": 5072, "crc": -708199642}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$13.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$13.class", "size": 1806, "crc": -1448875071}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$15.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$15.class", "size": 4845, "crc": 580118677}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$16.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$16.class", "size": 1835, "crc": -208303769}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$18.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$18.class", "size": 5019, "crc": 1233950345}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$2$1.class", "size": 4004, "crc": 459755982}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1$invoke$$inlined$onDispose$1.class", "size": 2300, "crc": 79409369}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1.class", "size": 3533, "crc": 32076130}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1$1.class", "size": 15144, "crc": 1390495034}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "size": 7236, "crc": 1445961716}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$5.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$5.class", "size": 5788, "crc": 968524994}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$6.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$6.class", "size": 1805, "crc": -1243848229}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$7$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$7$1.class", "size": 2811, "crc": -2050210322}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$8$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$8$1.class", "size": 3364, "crc": -664564514}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$9.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$9.class", "size": 4896, "crc": -1885638648}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$decorationModifiers$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$decorationModifiers$1$1.class", "size": 2381, "crc": -974751224}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$1.class", "size": 2158, "crc": -719274668}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$1$1.class", "size": 3965, "crc": 122963403}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$2$1.class", "size": 3852, "crc": -499109822}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$3$1.class", "size": 3850, "crc": -321490221}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$1.class", "size": 4327, "crc": -1789122982}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$2.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$2.class", "size": 4327, "crc": 1226974330}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$3.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$3.class", "size": 4327, "crc": 2010012242}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$4.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$4.class", "size": 3572, "crc": -828484677}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$5.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$5.class", "size": 3562, "crc": 204832190}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1.class", "size": 9747, "crc": 420558201}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1.class", "size": 2295, "crc": -1522335852}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$1$1.class", "size": 1613, "crc": 302287802}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$2$1.class", "size": 2738, "crc": 1557789514}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$3.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$3.class", "size": 1833, "crc": -370892206}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$cursorHandleState$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$cursorHandleState$2$1.class", "size": 1881, "crc": 926453897}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$1$1.class", "size": 1638, "crc": -292575379}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$2$1.class", "size": 2875, "crc": 872573766}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$3$1.class", "size": 1638, "crc": -2137235330}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$4$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$4$1.class", "size": 2875, "crc": 129881829}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$5.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$5.class", "size": 1845, "crc": 925357758}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$endHandleState$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$endHandleState$2$1.class", "size": 1908, "crc": -1877447482}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$startHandleState$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$startHandleState$2$1.class", "size": 1912, "crc": -626245814}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt.class", "size": 85249, "crc": -629521167}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$1.class", "size": 3060, "crc": -1548390166}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$2$1.class", "size": 2708, "crc": 792861877}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$3.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$3.class", "size": 3501, "crc": 2009314119}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$4.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$4.class", "size": 2819, "crc": 1264300097}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$5.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$5.class", "size": 3203, "crc": 1362740439}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$6.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$6.class", "size": 2540, "crc": 1394596801}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$7.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$7.class", "size": 2924, "crc": -1374803224}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$8.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$8.class", "size": 2593, "crc": -1572564207}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$9.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$9.class", "size": 2977, "crc": -1459263553}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$1$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$1$1.class", "size": 1697, "crc": 1154735746}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$2$1.class", "size": 1737, "crc": -529649842}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$2$1.class", "size": 2637, "crc": 1001476173}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$3$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$3$1.class", "size": 2733, "crc": 1584876484}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$4$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$4$1.class", "size": 2733, "crc": 865866925}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$5$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$5$1.class", "size": 2294, "crc": 1977052709}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$6.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$6.class", "size": 4996, "crc": -835740198}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$onPlaceholderLayout$1$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$onPlaceholderLayout$1$1.class", "size": 2535, "crc": 1298183031}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$1$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$1$1.class", "size": 2202, "crc": -1571342742}, {"key": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$2$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$LayoutWithLinksAndInlineContent$styledText$2$1.class", "size": 1842, "crc": 607875369}, {"key": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$1.class", "size": 2016, "crc": 1053244150}, {"key": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$2.class", "name": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$2.class", "size": 1426, "crc": 2031698302}, {"key": "androidx/compose/foundation/text/BasicTextKt.class", "name": "androidx/compose/foundation/text/BasicTextKt.class", "size": 63067, "crc": 1877900129}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$1.class", "size": 1540, "crc": 1779475542}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$2$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$2$1.class", "size": 2327, "crc": -563309594}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$3.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$3.class", "size": 2921, "crc": -406641654}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1$1.class", "size": 2599, "crc": 696276367}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "size": 2813, "crc": 391821088}, {"key": "androidx/compose/foundation/text/ClickableTextKt.class", "name": "androidx/compose/foundation/text/ClickableTextKt.class", "size": 9976, "crc": 1083543817}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-1$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-1$1.class", "size": 3086, "crc": 1086637745}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-2$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-2$1.class", "size": 3086, "crc": 482242737}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-3$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-3$1.class", "size": 3086, "crc": 1072352031}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-4$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-4$1.class", "size": 3086, "crc": 1029012708}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "size": 2849, "crc": -1932371137}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda-1$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda-1$1.class", "size": 3079, "crc": -215936003}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "size": 1749, "crc": 522761331}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$1$1.class", "size": 1502, "crc": -515344183}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2$1$1.class", "size": 4042, "crc": -802150635}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2$1.class", "size": 2688, "crc": 1495033082}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$3.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$3.class", "size": 2236, "crc": 390478620}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$4$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$4$1.class", "size": 1516, "crc": -390719285}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1$1.class", "size": 4101, "crc": 1543909346}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1.class", "size": 2761, "crc": 1478143305}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$6.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$6.class", "size": 2351, "crc": -986094259}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$7$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$7$1.class", "size": 1493, "crc": 929349368}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$8.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$8.class", "size": 2200, "crc": -1363938265}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1$WhenMappings.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1$WhenMappings.class", "size": 1198, "crc": -366837411}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1.class", "size": 4544, "crc": -905873102}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1.class", "size": 2822, "crc": 1836770464}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$1.class", "size": 2500, "crc": -600687999}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$2.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$2.class", "size": 1925, "crc": -1609588974}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$1.class", "size": 1784, "crc": 158200390}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$2.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$2.class", "size": 1841, "crc": 416480405}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "size": 24071, "crc": 2129430257}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$1.class", "size": 1880, "crc": -1693920447}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$1.class", "size": 1580, "crc": -883479852}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$2.class", "size": 3068, "crc": -1972653426}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "size": 5706, "crc": -123534894}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1$invoke$$inlined$onDispose$1.class", "size": 2273, "crc": -881249280}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$1.class", "size": 3272, "crc": -2140205260}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1$invoke$$inlined$onDispose$1.class", "size": 1950, "crc": -426426132}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$1.class", "size": 4708, "crc": 90865918}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2$measure$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2$measure$2.class", "size": 1931, "crc": 1163042716}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "size": 10130, "crc": -1353713551}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "size": 10349, "crc": -2008727913}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$coreTextFieldModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$coreTextFieldModifier$1$1.class", "size": 1600, "crc": 1309949753}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "size": 9886, "crc": 1029931165}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "size": 7416, "crc": 1535390254}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$6.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$6.class", "size": 5333, "crc": -154065182}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "size": 2412, "crc": -985039832}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawDecorationModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawDecorationModifier$1$1.class", "size": 2616, "crc": 481231385}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawModifier$1$1.class", "size": 5052, "crc": 920413282}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1$1.class", "size": 4907, "crc": 558291604}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1.class", "size": 5321, "crc": -1972482775}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$onPositionedModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$onPositionedModifier$1$1.class", "size": 5794, "crc": -1078857839}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$1$1.class", "size": 2092, "crc": 1079806276}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$2$1.class", "size": 5000, "crc": 128800904}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$scrollerPosition$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$scrollerPosition$1$1.class", "size": 2122, "crc": -1775296258}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$stylusHandwritingModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$stylusHandwritingModifier$1$1.class", "size": 2164, "crc": 423198676}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextFieldRootBox$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextFieldRootBox$2.class", "size": 2435, "crc": 1241562798}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$SelectionToolbarAndHandles$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$SelectionToolbarAndHandles$2.class", "size": 1890, "crc": 1873975544}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "size": 1078, "crc": -1369064252}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$1.class", "size": 3838, "crc": 1154752650}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2$1.class", "size": 1936, "crc": 1592517102}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2.class", "size": 4207, "crc": -1925125456}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1.class", "size": 4754, "crc": 1761036556}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "size": 2451, "crc": -2140567760}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$3$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$3$1.class", "size": 2407, "crc": -1148358272}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$4.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$4.class", "size": 1783, "crc": -1554391590}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "size": 2775, "crc": 1836847309}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt.class", "size": 80421, "crc": -2093060370}, {"key": "androidx/compose/foundation/text/DeadKeyCombiner.class", "name": "androidx/compose/foundation/text/DeadKeyCombiner.class", "size": 2458, "crc": 333813534}, {"key": "androidx/compose/foundation/text/EmptyMeasurePolicy$placementBlock$1.class", "name": "androidx/compose/foundation/text/EmptyMeasurePolicy$placementBlock$1.class", "size": 1506, "crc": 757780098}, {"key": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "name": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "size": 2736, "crc": -942913060}, {"key": "androidx/compose/foundation/text/Handle.class", "name": "androidx/compose/foundation/text/Handle.class", "size": 1902, "crc": -1050428375}, {"key": "androidx/compose/foundation/text/HandleState.class", "name": "androidx/compose/foundation/text/HandleState.class", "size": 1924, "crc": 1356620027}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "size": 3159, "crc": -82286979}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "size": 12425, "crc": -1617208917}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "size": 4366, "crc": -1039643967}, {"key": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier$measure$1.class", "name": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier$measure$1.class", "size": 5141, "crc": 1256000101}, {"key": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "size": 7292, "crc": -448257418}, {"key": "androidx/compose/foundation/text/InlineTextContent.class", "name": "androidx/compose/foundation/text/InlineTextContent.class", "size": 1996, "crc": 22792240}, {"key": "androidx/compose/foundation/text/InlineTextContentKt.class", "name": "androidx/compose/foundation/text/InlineTextContentKt.class", "size": 2877, "crc": -778593101}, {"key": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "name": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "size": 1163, "crc": -191534232}, {"key": "androidx/compose/foundation/text/KeyCommand.class", "name": "androidx/compose/foundation/text/KeyCommand.class", "size": 5229, "crc": -1398571551}, {"key": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "name": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "size": 1392, "crc": 1396738814}, {"key": "androidx/compose/foundation/text/KeyMapping.class", "name": "androidx/compose/foundation/text/KeyMapping.class", "size": 848, "crc": -1315925662}, {"key": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "size": 4641, "crc": 952293218}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "size": 1276, "crc": -956486515}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "size": 3114, "crc": 2085217161}, {"key": "androidx/compose/foundation/text/KeyMappingKt.class", "name": "androidx/compose/foundation/text/KeyMappingKt.class", "size": 2031, "crc": 1438691576}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "size": 2441, "crc": -1118552162}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "size": 968, "crc": -643396264}, {"key": "androidx/compose/foundation/text/KeyboardActionRunner.class", "name": "androidx/compose/foundation/text/KeyboardActionRunner.class", "size": 4743, "crc": -1785024149}, {"key": "androidx/compose/foundation/text/KeyboardActionScope.class", "name": "androidx/compose/foundation/text/KeyboardActionScope.class", "size": 626, "crc": 791906571}, {"key": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "size": 1338, "crc": -1998168281}, {"key": "androidx/compose/foundation/text/KeyboardActions.class", "name": "androidx/compose/foundation/text/KeyboardActions.class", "size": 5335, "crc": -462378897}, {"key": "androidx/compose/foundation/text/KeyboardActionsKt.class", "name": "androidx/compose/foundation/text/KeyboardActionsKt.class", "size": 1452, "crc": 1206498083}, {"key": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "size": 1649, "crc": 980707463}, {"key": "androidx/compose/foundation/text/KeyboardOptions.class", "name": "androidx/compose/foundation/text/KeyboardOptions.class", "size": 20065, "crc": -1510605116}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState$onImeActionPerformed$1.class", "size": 1948, "crc": -439927981}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChange$1.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChange$1.class", "size": 3105, "crc": 1922034305}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChangeOriginal$1.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState$onValueChangeOriginal$1.class", "size": 1597, "crc": 1557534217}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState.class", "size": 20825, "crc": -1565486440}, {"key": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver$collectInteractionsForLinks$2.class", "name": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver$collectInteractionsForLinks$2.class", "size": 5939, "crc": 920081466}, {"key": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver.class", "name": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver.class", "size": 4936, "crc": 2058352344}, {"key": "androidx/compose/foundation/text/LinksTextMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/text/LinksTextMeasurePolicy$measure$1.class", "size": 4430, "crc": -1555931958}, {"key": "androidx/compose/foundation/text/LinksTextMeasurePolicy.class", "name": "androidx/compose/foundation/text/LinksTextMeasurePolicy.class", "size": 2698, "crc": -266775701}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "size": 3922, "crc": -1955810291}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "size": 3919, "crc": -1009852150}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "size": 4479, "crc": -1851501141}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$2.class", "size": 1881, "crc": 886277947}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$3.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$3.class", "size": 1599, "crc": 116358344}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$4.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$4.class", "size": 1601, "crc": -35230101}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$5.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$5.class", "size": 2198, "crc": 745145706}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$2.class", "size": 1839, "crc": 94153041}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$3.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$3.class", "size": 1557, "crc": 307083779}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$4.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$4.class", "size": 1559, "crc": 452921617}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$5.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$5.class", "size": 2156, "crc": -1350987489}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "size": 7107, "crc": 1694820072}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "size": 5063, "crc": -701151718}, {"key": "androidx/compose/foundation/text/MappedKeys.class", "name": "androidx/compose/foundation/text/MappedKeys.class", "size": 4879, "crc": -63688101}, {"key": "androidx/compose/foundation/text/MenuItemsAvailability$Companion.class", "name": "androidx/compose/foundation/text/MenuItemsAvailability$Companion.class", "size": 1331, "crc": -2138996695}, {"key": "androidx/compose/foundation/text/MenuItemsAvailability.class", "name": "androidx/compose/foundation/text/MenuItemsAvailability.class", "size": 3935, "crc": 777970038}, {"key": "androidx/compose/foundation/text/PasswordInputTransformation.class", "name": "androidx/compose/foundation/text/PasswordInputTransformation.class", "size": 4585, "crc": -2138799820}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "size": 6709, "crc": 2033831517}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "size": 5418, "crc": -1039340490}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "size": 2665, "crc": 1974186016}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$focusChangeModifier$1.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$focusChangeModifier$1.class", "size": 1838, "crc": 1094074506}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$observeHideEvents$2.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$observeHideEvents$2.class", "size": 3540, "crc": 398976208}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$passwordInputTransformation$1.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$passwordInputTransformation$1.class", "size": 1400, "crc": 16767240}, {"key": "androidx/compose/foundation/text/SecureTextFieldController.class", "name": "androidx/compose/foundation/text/SecureTextFieldController.class", "size": 6100, "crc": -293130313}, {"key": "androidx/compose/foundation/text/StringHelpersKt.class", "name": "androidx/compose/foundation/text/StringHelpersKt.class", "size": 1466, "crc": -553094929}, {"key": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "name": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "size": 3020, "crc": 1578552480}, {"key": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "name": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "size": 913, "crc": -118994966}, {"key": "androidx/compose/foundation/text/TextAnnotatorScope$replaceStyle$1.class", "name": "androidx/compose/foundation/text/TextAnnotatorScope$replaceStyle$1.class", "size": 3826, "crc": -624198854}, {"key": "androidx/compose/foundation/text/TextAnnotatorScope.class", "name": "androidx/compose/foundation/text/TextAnnotatorScope.class", "size": 2543, "crc": -282479981}, {"key": "androidx/compose/foundation/text/TextAutoSize$Companion.class", "name": "androidx/compose/foundation/text/TextAutoSize$Companion.class", "size": 1986, "crc": 1467517132}, {"key": "androidx/compose/foundation/text/TextAutoSize.class", "name": "androidx/compose/foundation/text/TextAutoSize.class", "size": 1488, "crc": 1690884269}, {"key": "androidx/compose/foundation/text/TextAutoSizeDefaults.class", "name": "androidx/compose/foundation/text/TextAutoSizeDefaults.class", "size": 1319, "crc": -1951215545}, {"key": "androidx/compose/foundation/text/TextContextMenuItems.class", "name": "androidx/compose/foundation/text/TextContextMenuItems.class", "size": 3770, "crc": -637320361}, {"key": "androidx/compose/foundation/text/TextDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextDelegate$Companion.class", "size": 1417, "crc": 848562028}, {"key": "androidx/compose/foundation/text/TextDelegate.class", "name": "androidx/compose/foundation/text/TextDelegate.class", "size": 14616, "crc": 1827309212}, {"key": "androidx/compose/foundation/text/TextDelegateKt.class", "name": "androidx/compose/foundation/text/TextDelegateKt.class", "size": 5325, "crc": -1188199970}, {"key": "androidx/compose/foundation/text/TextDragObserver.class", "name": "androidx/compose/foundation/text/TextDragObserver.class", "size": 859, "crc": -840039185}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "size": 3511, "crc": -146147252}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$2$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$2$1.class", "size": 6601, "crc": 1002000275}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "size": 9319, "crc": -679752275}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt.class", "size": 2002, "crc": -1744713505}, {"key": "androidx/compose/foundation/text/TextFieldCursor_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldCursor_androidKt.class", "size": 1583, "crc": -205640831}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion$restartInput$1.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion$restartInput$1.class", "size": 3127, "crc": 390463619}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "size": 2294, "crc": 1203034530}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "size": 23162, "crc": -386030662}, {"key": "androidx/compose/foundation/text/TextFieldDelegate.class", "name": "androidx/compose/foundation/text/TextFieldDelegate.class", "size": 7199, "crc": -2007140709}, {"key": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "name": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "size": 4454, "crc": -2012722806}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "size": 3611, "crc": 1350558945}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "size": 2255, "crc": 603643894}, {"key": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "name": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "size": 2235, "crc": -1224397981}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$1.class", "size": 1915, "crc": 509919805}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$1.class", "size": 1641, "crc": 961103663}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$2.class", "size": 1643, "crc": -1779654344}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$3.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$3.class", "size": 2019, "crc": 196527088}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$4.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$4.class", "size": 2092, "crc": 1598643802}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$5.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$5.class", "size": 2228, "crc": 1145315881}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$6.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$6.class", "size": 2224, "crc": 2080048616}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$7.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$7.class", "size": 2227, "crc": -304910240}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$8.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$8.class", "size": 2225, "crc": -2058962469}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$WhenMappings.class", "size": 3290, "crc": 169898339}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2.class", "size": 8365, "crc": -362780550}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput.class", "size": 14016, "crc": -20441276}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$1.class", "size": 1858, "crc": 1008839468}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1$1.class", "size": 1789, "crc": -1082210248}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "size": 8336, "crc": -2133545932}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "size": 3870, "crc": 1985075947}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "size": 948, "crc": -958315386}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1$invoke$$inlined$onDispose$1.class", "size": 3450, "crc": -1270729485}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1.class", "size": 3800, "crc": 364786670}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$1.class", "size": 5506, "crc": 1280495703}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$2.class", "size": 5443, "crc": 354433509}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "size": 5715, "crc": -1911914179}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$2.class", "size": 2219, "crc": -1143235719}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "size": 3777, "crc": 1127510463}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "size": 9677, "crc": -1039636804}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "size": 2403, "crc": 49007625}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "size": 864, "crc": -1312763747}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "size": 3443, "crc": 1145119692}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$scrollableState$1$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$scrollableState$1$1.class", "size": 1925, "crc": 987962086}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollBackward$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollBackward$2.class", "size": 1783, "crc": 1854935705}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollForward$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollForward$2.class", "size": 1810, "crc": 348570113}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "size": 5125, "crc": 243315692}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "size": 8015, "crc": 2017282843}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt.class", "size": 8569, "crc": 1795659073}, {"key": "androidx/compose/foundation/text/TextFieldScroll_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldScroll_androidKt.class", "size": 1931, "crc": 1897038868}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$1.class", "size": 2478, "crc": 1843971582}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$2.class", "size": 2317, "crc": 1147032149}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "size": 1438, "crc": -211243789}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "size": 9842, "crc": -2094196889}, {"key": "androidx/compose/foundation/text/TextFieldSize.class", "name": "androidx/compose/foundation/text/TextFieldSize.class", "size": 4426, "crc": 2138142923}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1$1.class", "size": 1995, "crc": -17044955}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1.class", "size": 4521, "crc": 670961134}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "size": 10204, "crc": 980601246}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt.class", "size": 1335, "crc": 185165764}, {"key": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "name": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "size": 6524, "crc": 149280100}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "size": 9694, "crc": 1972406986}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "size": 4182, "crc": -342785102}, {"key": "androidx/compose/foundation/text/TextLinkScope$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$1.class", "size": 3806, "crc": -801532884}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$1.class", "size": 1840, "crc": -210236698}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$2$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$2$1.class", "size": 2207, "crc": -1809296617}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$3$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$3$1.class", "size": 3474, "crc": -1753734615}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$4$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$4$1.class", "size": 3850, "crc": 1613822699}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$2.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$2.class", "size": 1608, "crc": -1065942954}, {"key": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1$invoke$$inlined$onDispose$1.class", "size": 2481, "crc": 938496196}, {"key": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$1$1.class", "size": 3382, "crc": -320002822}, {"key": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$2.class", "name": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$2.class", "size": 2456, "crc": -420676242}, {"key": "androidx/compose/foundation/text/TextLinkScope$clipLink$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$clipLink$1.class", "size": 2629, "crc": 514693049}, {"key": "androidx/compose/foundation/text/TextLinkScope$shapeForRange$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$shapeForRange$1$1.class", "size": 1801, "crc": -1890881343}, {"key": "androidx/compose/foundation/text/TextLinkScope$shouldMeasureLinks$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$shouldMeasureLinks$1.class", "size": 1924, "crc": -412551645}, {"key": "androidx/compose/foundation/text/TextLinkScope$textRange$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$textRange$1$1.class", "size": 1600, "crc": -1191377360}, {"key": "androidx/compose/foundation/text/TextLinkScope$textRange$1$layoutResult$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$textRange$1$layoutResult$1.class", "size": 1686, "crc": 5247249}, {"key": "androidx/compose/foundation/text/TextLinkScope$textRange$1$updatedRange$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$textRange$1$updatedRange$1.class", "size": 1686, "crc": 910827059}, {"key": "androidx/compose/foundation/text/TextLinkScope.class", "name": "androidx/compose/foundation/text/TextLinkScope.class", "size": 30116, "crc": -1737476844}, {"key": "androidx/compose/foundation/text/TextLinkScopeKt.class", "name": "androidx/compose/foundation/text/TextLinkScopeKt.class", "size": 1119, "crc": -952962625}, {"key": "androidx/compose/foundation/text/TextMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/text/TextMeasurePolicy$measure$1.class", "size": 4610, "crc": 388692495}, {"key": "androidx/compose/foundation/text/TextMeasurePolicy.class", "name": "androidx/compose/foundation/text/TextMeasurePolicy.class", "size": 8847, "crc": -57237649}, {"key": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "name": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "size": 1091, "crc": -746305070}, {"key": "androidx/compose/foundation/text/TextRangeLayoutMeasureResult.class", "name": "androidx/compose/foundation/text/TextRangeLayoutMeasureResult.class", "size": 1809, "crc": 875240840}, {"key": "androidx/compose/foundation/text/TextRangeLayoutMeasureScope.class", "name": "androidx/compose/foundation/text/TextRangeLayoutMeasureScope.class", "size": 1615, "crc": -1382840212}, {"key": "androidx/compose/foundation/text/TextRangeLayoutModifier.class", "name": "androidx/compose/foundation/text/TextRangeLayoutModifier.class", "size": 1933, "crc": -1570986852}, {"key": "androidx/compose/foundation/text/TextRangeScopeMeasurePolicy.class", "name": "androidx/compose/foundation/text/TextRangeScopeMeasurePolicy.class", "size": 890, "crc": -485755446}, {"key": "androidx/compose/foundation/text/TouchMode_androidKt.class", "name": "androidx/compose/foundation/text/TouchMode_androidKt.class", "size": 789, "crc": 1890193592}, {"key": "androidx/compose/foundation/text/UndoManager$Entry.class", "name": "androidx/compose/foundation/text/UndoManager$Entry.class", "size": 2020, "crc": 413975752}, {"key": "androidx/compose/foundation/text/UndoManager.class", "name": "androidx/compose/foundation/text/UndoManager.class", "size": 4698, "crc": 784281197}, {"key": "androidx/compose/foundation/text/UndoManagerKt.class", "name": "androidx/compose/foundation/text/UndoManagerKt.class", "size": 584, "crc": -1639782108}, {"key": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "name": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "size": 496, "crc": 648442783}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "size": 1962, "crc": -799532529}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "size": 6109, "crc": -1388798410}, {"key": "androidx/compose/foundation/text/VerticalScrollLayoutModifier$measure$1.class", "name": "androidx/compose/foundation/text/VerticalScrollLayoutModifier$measure$1.class", "size": 4848, "crc": -161359669}, {"key": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "size": 7147, "crc": 57920765}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorElement.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorElement.class", "size": 3297, "crc": -285065959}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$composeImm$2.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$composeImm$2.class", "size": 1938, "crc": -1278714959}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode$pointerInputNode$1.class", "size": 1778, "crc": -1165259235}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode.class", "size": 4477, "crc": 797251899}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetector_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetector_androidKt.class", "size": 2076, "crc": -566729112}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerElement.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerElement.class", "size": 2575, "crc": -105242123}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$composeImm$2.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$composeImm$2.class", "size": 1900, "crc": 519109424}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$onFocusEvent$1.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$onFocusEvent$1.class", "size": 3634, "crc": 496203032}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode.class", "size": 3332, "crc": -1463567680}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandler_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandler_androidKt.class", "size": 1100, "crc": -43779177}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingElement.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingElement.class", "size": 4738, "crc": -182347098}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingKt.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingKt.class", "size": 3942, "crc": 1371681001}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1$1.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1$1.class", "size": 12983, "crc": -1441474465}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1.class", "size": 2303, "crc": 704536047}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode.class", "size": 5057, "crc": 307965755}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwriting_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwriting_androidKt.class", "size": 748, "crc": -221821798}, {"key": "androidx/compose/foundation/text/input/AllCapsTransformation.class", "name": "androidx/compose/foundation/text/input/AllCapsTransformation.class", "size": 5825, "crc": -295156793}, {"key": "androidx/compose/foundation/text/input/FilterChain.class", "name": "androidx/compose/foundation/text/input/FilterChain.class", "size": 4369, "crc": 165856885}, {"key": "androidx/compose/foundation/text/input/InputTransformation$Companion.class", "name": "androidx/compose/foundation/text/input/InputTransformation$Companion.class", "size": 1206, "crc": -271072962}, {"key": "androidx/compose/foundation/text/input/InputTransformation.class", "name": "androidx/compose/foundation/text/input/InputTransformation.class", "size": 1722, "crc": 925714946}, {"key": "androidx/compose/foundation/text/input/InputTransformationByValue.class", "name": "androidx/compose/foundation/text/input/InputTransformationByValue.class", "size": 4772, "crc": -1957007683}, {"key": "androidx/compose/foundation/text/input/InputTransformationKt.class", "name": "androidx/compose/foundation/text/input/InputTransformationKt.class", "size": 3048, "crc": 1379899447}, {"key": "androidx/compose/foundation/text/input/KeyboardActionHandler.class", "name": "androidx/compose/foundation/text/input/KeyboardActionHandler.class", "size": 853, "crc": 2020449360}, {"key": "androidx/compose/foundation/text/input/MaxLengthFilter.class", "name": "androidx/compose/foundation/text/input/MaxLengthFilter.class", "size": 4206, "crc": 164995188}, {"key": "androidx/compose/foundation/text/input/OutputTransformation.class", "name": "androidx/compose/foundation/text/input/OutputTransformation.class", "size": 799, "crc": -131846518}, {"key": "androidx/compose/foundation/text/input/TextFieldBuffer$ChangeList.class", "name": "androidx/compose/foundation/text/input/TextFieldBuffer$ChangeList.class", "size": 906, "crc": -791215026}, {"key": "androidx/compose/foundation/text/input/TextFieldBuffer$composingAnnotations$1.class", "name": "androidx/compose/foundation/text/input/TextFieldBuffer$composingAnnotations$1.class", "size": 2404, "crc": -1374494019}, {"key": "androidx/compose/foundation/text/input/TextFieldBuffer.class", "name": "androidx/compose/foundation/text/input/TextFieldBuffer.class", "size": 21800, "crc": 1282748352}, {"key": "androidx/compose/foundation/text/input/TextFieldBufferKt.class", "name": "androidx/compose/foundation/text/input/TextFieldBufferKt.class", "size": 6230, "crc": 1302469840}, {"key": "androidx/compose/foundation/text/input/TextFieldCharSequence.class", "name": "androidx/compose/foundation/text/input/TextFieldCharSequence.class", "size": 7643, "crc": 500254501}, {"key": "androidx/compose/foundation/text/input/TextFieldCharSequenceKt.class", "name": "androidx/compose/foundation/text/input/TextFieldCharSequenceKt.class", "size": 1879, "crc": -1723142569}, {"key": "androidx/compose/foundation/text/input/TextFieldDecorator.class", "name": "androidx/compose/foundation/text/input/TextFieldDecorator.class", "size": 1034, "crc": 737543429}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$Companion.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$Companion.class", "size": 1340, "crc": 1367082443}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$MultiLine.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$MultiLine.class", "size": 3860, "crc": 1241696655}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$SingleLine.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$SingleLine.class", "size": 1207, "crc": -1941834291}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits.class", "size": 1236, "crc": 2022475994}, {"key": "androidx/compose/foundation/text/input/TextFieldState$NotifyImeListener.class", "name": "androidx/compose/foundation/text/input/TextFieldState$NotifyImeListener.class", "size": 1003, "crc": 701944858}, {"key": "androidx/compose/foundation/text/input/TextFieldState$Saver.class", "name": "androidx/compose/foundation/text/input/TextFieldState$Saver.class", "size": 4979, "crc": -1709041166}, {"key": "androidx/compose/foundation/text/input/TextFieldState$WhenMappings.class", "name": "androidx/compose/foundation/text/input/TextFieldState$WhenMappings.class", "size": 1018, "crc": -1425412583}, {"key": "androidx/compose/foundation/text/input/TextFieldState.class", "name": "androidx/compose/foundation/text/input/TextFieldState.class", "size": 23260, "crc": 1495599916}, {"key": "androidx/compose/foundation/text/input/TextFieldStateKt$rememberTextFieldState$1$1.class", "name": "androidx/compose/foundation/text/input/TextFieldStateKt$rememberTextFieldState$1$1.class", "size": 1638, "crc": 1292200546}, {"key": "androidx/compose/foundation/text/input/TextFieldStateKt.class", "name": "androidx/compose/foundation/text/input/TextFieldStateKt.class", "size": 11040, "crc": -722736667}, {"key": "androidx/compose/foundation/text/input/TextHighlightType$Companion.class", "name": "androidx/compose/foundation/text/input/TextHighlightType$Companion.class", "size": 1417, "crc": -1463465950}, {"key": "androidx/compose/foundation/text/input/TextHighlightType.class", "name": "androidx/compose/foundation/text/input/TextHighlightType.class", "size": 2819, "crc": -606552563}, {"key": "androidx/compose/foundation/text/input/TextObfuscationMode$Companion.class", "name": "androidx/compose/foundation/text/input/TextObfuscationMode$Companion.class", "size": 1503, "crc": -1546097400}, {"key": "androidx/compose/foundation/text/input/TextObfuscationMode.class", "name": "androidx/compose/foundation/text/input/TextObfuscationMode.class", "size": 2954, "crc": 455194366}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "size": 7118, "crc": 448349511}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver.class", "size": 6743, "crc": -1983624651}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion.class", "size": 975, "crc": 1295515321}, {"key": "androidx/compose/foundation/text/input/TextUndoManager.class", "name": "androidx/compose/foundation/text/input/TextUndoManager.class", "size": 9165, "crc": 1438131661}, {"key": "androidx/compose/foundation/text/input/TextUndoManagerKt.class", "name": "androidx/compose/foundation/text/input/TextUndoManagerKt.class", "size": 5500, "crc": -598695137}, {"key": "androidx/compose/foundation/text/input/UndoState.class", "name": "androidx/compose/foundation/text/input/UndoState.class", "size": 2002, "crc": -1706193112}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$1.class", "size": 3887, "crc": -1820677094}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$1.class", "size": 1745, "crc": 1383839681}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$2.class", "size": 2204, "crc": -1701622253}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1.class", "size": 5267, "crc": -666584489}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$request$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$request$1.class", "size": 2669, "crc": 412992108}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1.class", "size": 7559, "crc": -673443963}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2.class", "size": 5477, "crc": 1242849994}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter.class", "size": 10418, "crc": 347353583}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextFieldKeyEventHandler.class", "size": 5439, "crc": 170649970}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "size": 2246, "crc": 786591213}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "size": 2322, "crc": -410585171}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$1.class", "size": 6393, "crc": -1873165665}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$1.class", "size": 1654, "crc": -396931752}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$2.class", "size": 2122, "crc": 610922355}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1.class", "size": 4849, "crc": 369409449}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$1.class", "size": 1909, "crc": -1369182177}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$textInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$textInputSession$1.class", "size": 8223, "crc": -223722913}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3.class", "size": 14383, "crc": -901119981}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt.class", "size": 9915, "crc": -285846568}, {"key": "androidx/compose/foundation/text/input/internal/Api25CommitContentImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api25CommitContentImpl.class", "size": 1584, "crc": -552619660}, {"key": "androidx/compose/foundation/text/input/internal/Api34LegacyPerformHandwritingGestureImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api34LegacyPerformHandwritingGestureImpl.class", "size": 4933, "crc": 225648637}, {"key": "androidx/compose/foundation/text/input/internal/Api34PerformHandwritingGestureImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api34PerformHandwritingGestureImpl.class", "size": 3184, "crc": 1961955970}, {"key": "androidx/compose/foundation/text/input/internal/Api34StartStylusHandwriting.class", "name": "androidx/compose/foundation/text/input/internal/Api34StartStylusHandwriting.class", "size": 1417, "crc": 563018119}, {"key": "androidx/compose/foundation/text/input/internal/ChangeTracker$Change.class", "name": "androidx/compose/foundation/text/input/internal/ChangeTracker$Change.class", "size": 3715, "crc": -791211935}, {"key": "androidx/compose/foundation/text/input/internal/ChangeTracker.class", "name": "androidx/compose/foundation/text/input/internal/ChangeTracker.class", "size": 8940, "crc": 1987206563}, {"key": "androidx/compose/foundation/text/input/internal/ClipboardKeyCommandsHandler.class", "name": "androidx/compose/foundation/text/input/internal/ClipboardKeyCommandsHandler.class", "size": 3958, "crc": -595165244}, {"key": "androidx/compose/foundation/text/input/internal/CodepointHelpers_jvmKt.class", "name": "androidx/compose/foundation/text/input/internal/CodepointHelpers_jvmKt.class", "size": 1048, "crc": 1869505160}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformation$Companion.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformation$Companion.class", "size": 833, "crc": -637081425}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformation.class", "size": 1030, "crc": 1520700605}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformationKt.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformationKt.class", "size": 3667, "crc": -1359817592}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager.class", "size": 1545, "crc": -1864013684}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImpl.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImpl.class", "size": 4692, "crc": -1004514915}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi21.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi21.class", "size": 2355, "crc": 811743859}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi24.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi24.class", "size": 1535, "crc": -1594565916}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi34.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi34.class", "size": 1651, "crc": 1055942400}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt$ComposeInputMethodManagerFactory$1.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt$ComposeInputMethodManagerFactory$1.class", "size": 2212, "crc": 1309644854}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt.class", "size": 2417, "crc": 95013371}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifier.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifier.class", "size": 9956, "crc": 1209563576}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$1.class", "size": 1906, "crc": -1646228347}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$1.class", "size": 2380, "crc": 109918150}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$10.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$10.class", "size": 1774, "crc": 1868479453}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$11.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$11.class", "size": 1776, "crc": -165217608}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$2.class", "size": 2450, "crc": -194536672}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$3.class", "size": 2219, "crc": -1859097783}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$4.class", "size": 4678, "crc": -605831138}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$5.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$5.class", "size": 3886, "crc": 78611939}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$6.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$6.class", "size": 2129, "crc": -1642065453}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$7.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$7.class", "size": 2088, "crc": -2099450831}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$8.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$8.class", "size": 1864, "crc": 1286874269}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$9.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$applySemantics$9.class", "size": 1874, "crc": -935599364}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$updateNodeSemantics$1.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode$updateNodeSemantics$1.class", "size": 1968, "crc": 1775148605}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode.class", "size": 15113, "crc": 310847556}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi33Helper.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi33Helper.class", "size": 2249, "crc": -741450827}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi34Helper.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi34Helper.class", "size": 2364, "crc": 560934449}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoBuilder_androidKt.class", "size": 8371, "crc": 1860640291}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$1.class", "size": 1783, "crc": 696806190}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$2.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$2.class", "size": 2425, "crc": -462920657}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1.class", "size": 4409, "crc": 1853178572}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController.class", "size": 10262, "crc": -618183109}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2$1.class", "size": 4494, "crc": -80064946}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2.class", "size": 4494, "crc": 407455596}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState.class", "size": 5222, "crc": 819304321}, {"key": "androidx/compose/foundation/text/input/internal/DefaultImeEditCommandScope.class", "name": "androidx/compose/foundation/text/input/internal/DefaultImeEditCommandScope.class", "size": 8031, "crc": -1203579312}, {"key": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi21.class", "name": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi21.class", "size": 1374, "crc": -972257360}, {"key": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi24.class", "name": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi24.class", "size": 1461, "crc": -1478345670}, {"key": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi28.class", "name": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi28.class", "size": 1650, "crc": -1896043292}, {"key": "androidx/compose/foundation/text/input/internal/EditorInfoApi34.class", "name": "androidx/compose/foundation/text/input/internal/EditorInfoApi34.class", "size": 1998, "crc": 1742794840}, {"key": "androidx/compose/foundation/text/input/internal/EditorInfo_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/EditorInfo_androidKt.class", "size": 6728, "crc": -520919289}, {"key": "androidx/compose/foundation/text/input/internal/GapBuffer.class", "name": "androidx/compose/foundation/text/input/internal/GapBuffer.class", "size": 4577, "crc": 979052440}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$1.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$1.class", "size": 2166, "crc": 937017410}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$2.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34$performRemoveSpaceGesture$newText$2.class", "size": 2155, "crc": -1748578520}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34.class", "size": 40217, "crc": -386159989}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt$compoundEditCommand$1.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt$compoundEditCommand$1.class", "size": 1508, "crc": 1347523907}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt.class", "size": 18912, "crc": -819219624}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommandScope.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommandScope.class", "size": 1289, "crc": 1961837855}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$commitText$1.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$commitText$1.class", "size": 2647, "crc": -1988944316}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$deleteSurroundingText$1.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$deleteSurroundingText$1.class", "size": 4683, "crc": 2079358174}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$deleteSurroundingTextInCodePoints$1.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$deleteSurroundingTextInCodePoints$1.class", "size": 4498, "crc": -1598882375}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$finishComposingText$1.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$finishComposingText$1.class", "size": 1692, "crc": 1899550249}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$setComposingRegion$1.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$setComposingRegion$1.class", "size": 2175, "crc": -100861765}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$setComposingText$1.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$setComposingText$1.class", "size": 3502, "crc": 354133659}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$setSelection$1.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt$setSelection$1.class", "size": 3904, "crc": -529056965}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt.class", "size": 7604, "crc": -1234989571}, {"key": "androidx/compose/foundation/text/input/internal/IndexTransformationType.class", "name": "androidx/compose/foundation/text/input/internal/IndexTransformationType.class", "size": 2201, "crc": -236600054}, {"key": "androidx/compose/foundation/text/input/internal/InputEventCallback2.class", "name": "androidx/compose/foundation/text/input/internal/InputEventCallback2.class", "size": 1620, "crc": 879653554}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManager.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManager.class", "size": 1335, "crc": 1680091752}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl$imm$2.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl$imm$2.class", "size": 1983, "crc": 1399393211}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl.class", "size": 4521, "crc": 2039919152}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifier.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifier.class", "size": 6630, "crc": 783662750}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode$launchTextInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode$launchTextInputSession$1.class", "size": 4459, "crc": -1505816817}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode.class", "size": 9448, "crc": -316516531}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNodeKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNodeKt.class", "size": 1921, "crc": 741182274}, {"key": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoBuilder_androidKt.class", "size": 9316, "crc": -1649440}, {"key": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoController.class", "name": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoController.class", "size": 6825, "crc": -644373262}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter$LegacyPlatformTextInputNode.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter$LegacyPlatformTextInputNode.class", "size": 2647, "crc": -424435847}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter.class", "size": 4599, "crc": 890482278}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt$inputMethodManagerFactory$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt$inputMethodManagerFactory$1.class", "size": 1725, "crc": -1078791485}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt.class", "size": 3340, "crc": -1114417621}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$baseInputConnection$2.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$baseInputConnection$2.class", "size": 1728, "crc": -1815518317}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$createInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$createInputConnection$1.class", "size": 4383, "crc": 106710385}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onEditCommand$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onEditCommand$1.class", "size": 1716, "crc": -512486111}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$onImeActionPerformed$1.class", "size": 1661, "crc": -833152170}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest.class", "size": 15475, "crc": -1832424897}, {"key": "androidx/compose/foundation/text/input/internal/LocaleListHelper.class", "name": "androidx/compose/foundation/text/input/internal/LocaleListHelper.class", "size": 4322, "crc": -1962683557}, {"key": "androidx/compose/foundation/text/input/internal/MaskCodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/MaskCodepointTransformation.class", "size": 2530, "crc": 1338923038}, {"key": "androidx/compose/foundation/text/input/internal/MathUtilsKt.class", "name": "androidx/compose/foundation/text/input/internal/MathUtilsKt.class", "size": 3964, "crc": 766394037}, {"key": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculator.class", "name": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculator.class", "size": 5986, "crc": -491417781}, {"key": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculatorKt.class", "name": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculatorKt.class", "size": 456, "crc": -1566930933}, {"key": "androidx/compose/foundation/text/input/internal/OpArray.class", "name": "androidx/compose/foundation/text/input/internal/OpArray.class", "size": 4818, "crc": -871550676}, {"key": "androidx/compose/foundation/text/input/internal/PartialGapBuffer$Companion.class", "name": "androidx/compose/foundation/text/input/internal/PartialGapBuffer$Companion.class", "size": 1018, "crc": -1335071944}, {"key": "androidx/compose/foundation/text/input/internal/PartialGapBuffer.class", "name": "androidx/compose/foundation/text/input/internal/PartialGapBuffer.class", "size": 6835, "crc": 3895944}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection$performHandwritingGesture$1.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection$performHandwritingGesture$1.class", "size": 1968, "crc": -1811424942}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection.class", "size": 23682, "crc": -1475136801}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection_androidKt.class", "size": 2071, "crc": 605308452}, {"key": "androidx/compose/foundation/text/input/internal/SelectionWedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/SelectionWedgeAffinity.class", "size": 3577, "crc": 710441313}, {"key": "androidx/compose/foundation/text/input/internal/SingleLineCodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/SingleLineCodepointTransformation.class", "size": 1696, "crc": -1477377815}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitContentDelegateInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitContentDelegateInputConnection$1.class", "size": 3735, "crc": 1912659933}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$terminalInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$terminalInputConnection$1.class", "size": 1865, "crc": 33482930}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection.class", "size": 17400, "crc": -1226932095}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection_androidKt.class", "size": 12424, "crc": -1999438241}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifier.class", "size": 8609, "crc": 1591567467}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierKt.class", "size": 3821, "crc": 2065361645}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureHorizontalScroll$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureHorizontalScroll$1.class", "size": 3563, "crc": -257370358}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureVerticalScroll$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$measureVerticalScroll$1.class", "size": 3557, "crc": -1651569524}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$1.class", "size": 3629, "crc": -91063511}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$2.class", "size": 3998, "crc": -825882249}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1.class", "size": 4315, "crc": 357656823}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "size": 4877, "crc": -1875128776}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode.class", "size": 25332, "crc": -1233553680}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifier.class", "size": 11777, "crc": -1989460822}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierKt.class", "size": 1602, "crc": 1136882737}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$1.class", "size": 2036, "crc": 1336790489}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1$1.class", "size": 3796, "crc": -1396749129}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1.class", "size": 2918, "crc": -1839650399}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10$1.class", "size": 3861, "crc": -1173338365}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10.class", "size": 2127, "crc": -1685840561}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$11$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$11$1.class", "size": 3863, "crc": 757773703}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$11.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$11.class", "size": 2127, "crc": -1034288792}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$2.class", "size": 2993, "crc": 717973098}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$3.class", "size": 2150, "crc": 1436684499}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$4.class", "size": 2349, "crc": 448021099}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$5.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$5.class", "size": 3630, "crc": -1356309715}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$6.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$6.class", "size": 1620, "crc": 919180243}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$7.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$7.class", "size": 2204, "crc": -1199557568}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$8.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$8.class", "size": 2398, "crc": -155726839}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9$1.class", "size": 3971, "crc": -218941404}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9.class", "size": 2124, "crc": -580331149}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1$WhenMappings.class", "size": 1144, "crc": 1071006013}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1.class", "size": 4681, "crc": -1895392740}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1.class", "size": 2991, "crc": 114651379}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$1.class", "size": 2802, "crc": -1314941340}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$2.class", "size": 6094, "crc": 885863356}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$3.class", "size": 2838, "crc": 566851416}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$4.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$4.class", "size": 4401, "crc": -499111929}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$5.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$5.class", "size": 3542, "crc": 1506853251}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$6.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$6.class", "size": 3174, "crc": 679146398}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$7.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$dragAndDropNode$7.class", "size": 2233, "crc": -1185988378}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "size": 3713, "crc": -1888470038}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$observeUntransformedTextChanges$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$observeUntransformedTextChanges$2.class", "size": 1849, "crc": -450290454}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$observeUntransformedTextChanges$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$observeUntransformedTextChanges$3.class", "size": 2043, "crc": -1971857156}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onFocusChange$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onFocusChange$1.class", "size": 3770, "crc": -54519893}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onImeActionPerformed$1.class", "size": 1867, "crc": -1157261064}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onKeyEvent$1.class", "size": 1773, "crc": 1972991700}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onObservedReadsChanged$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onObservedReadsChanged$1.class", "size": 2176, "crc": 783635546}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$1.class", "size": 4076, "crc": 1344919207}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2$1.class", "size": 2399, "crc": 2066943912}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2.class", "size": 5216, "crc": -254453765}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$3.class", "size": 4349, "crc": -439476156}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$requestFocus$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$requestFocus$1.class", "size": 2190, "crc": -1425455690}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1.class", "size": 6192, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "size": 2798, "crc": -581757908}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$receiveContentConfigurationProvider$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$receiveContentConfigurationProvider$1.class", "size": 2409, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "size": 1915, "crc": -171914940}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$2.class", "size": 2206, "crc": -643262891}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "size": 6445, "crc": 84346448}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "size": 4275, "crc": 1322721855}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$updateNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$updateNode$1.class", "size": 4148, "crc": 134900016}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$updateNode$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$updateNode$2.class", "size": 2071, "crc": -1358452818}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode.class", "size": 40730, "crc": 321688129}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$1.class", "size": 4975, "crc": 1797890313}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$2.class", "size": 7484, "crc": 96801304}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt.class", "size": 4945, "crc": -1493557820}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "size": 3313, "crc": 515512772}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$processKeyDownEvent$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$processKeyDownEvent$1$1.class", "size": 2072, "crc": 1748024199}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$processKeyDownEvent$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$processKeyDownEvent$1$2.class", "size": 2074, "crc": 353601247}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler.class", "size": 18696, "crc": -285792387}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler_androidKt.class", "size": 1997, "crc": -504877230}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "size": 8330, "crc": -1474901756}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "size": 2555, "crc": -636632227}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "size": 1725, "crc": 1794810231}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "size": 4768, "crc": 1345097281}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "size": 2355, "crc": 1305771093}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "size": 1743, "crc": 1957408422}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "size": 3803, "crc": -964691460}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache.class", "size": 23180, "crc": 1207753445}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache_androidKt.class", "size": 1952, "crc": -134493123}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifier.class", "size": 8917, "crc": 1041408301}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode$measure$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode$measure$1.class", "size": 1958, "crc": -534976853}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode.class", "size": 11104, "crc": -966291732}, {"key": "androidx/compose/foundation/text/input/internal/TextInputSession.class", "name": "androidx/compose/foundation/text/input/internal/TextInputSession.class", "size": 2099, "crc": 837789612}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutState$layoutWithNewMeasureInputs$1$textLayoutProvider$1.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutState$layoutWithNewMeasureInputs$1$textLayoutProvider$1.class", "size": 2036, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutState.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutState.class", "size": 13990, "crc": 368356846}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutStateKt.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutStateKt.class", "size": 6852, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/ToCharArray_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ToCharArray_androidKt.class", "size": 1158, "crc": 807469328}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion$WhenMappings.class", "size": 1068, "crc": -**********}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion.class", "size": 11474, "crc": -1678315948}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$TransformedText.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$TransformedText.class", "size": 3818, "crc": -743259018}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$codepointTransformedText$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$codepointTransformedText$1$1.class", "size": 3727, "crc": 1242906474}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "size": 2315, "crc": 820492197}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "size": 2413, "crc": 218082931}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$outputTransformedText$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$outputTransformedText$1$1.class", "size": 3275, "crc": 1353472470}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState.class", "size": 32036, "crc": 993848051}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldStateKt.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldStateKt.class", "size": 2515, "crc": 1605824100}, {"key": "androidx/compose/foundation/text/input/internal/WedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/WedgeAffinity.class", "size": 1990, "crc": 1051332562}, {"key": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "size": 2097, "crc": -1302847374}, {"key": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "size": 2451, "crc": -2013116378}, {"key": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity$WhenMappings.class", "size": 959, "crc": -115598226}, {"key": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity.class", "size": 5151, "crc": -1133965399}, {"key": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "size": 7482, "crc": 1001353312}, {"key": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt.class", "size": 2696, "crc": 322319782}, {"key": "androidx/compose/foundation/text/input/internal/selection/TapOnPosition.class", "name": "androidx/compose/foundation/text/input/internal/selection/TapOnPosition.class", "size": 618, "crc": 880978782}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState$Companion.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState$Companion.class", "size": 1356, "crc": 972808288}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState.class", "size": 5770, "crc": -889028056}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "size": 950, "crc": -381268658}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt.class", "size": 7747, "crc": 1852717282}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNode.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNode.class", "size": 2754, "crc": 1986817224}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$1.class", "size": 2331, "crc": -491106772}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$2.class", "size": 4348, "crc": 1787128851}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$1.class", "size": 3772, "crc": -1862856996}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "size": 4723, "crc": 1631493069}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "size": 5633, "crc": 937063869}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "size": 4539, "crc": 527678118}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "size": 12138, "crc": 1493492901}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection$Companion.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection$Companion.class", "size": 1024, "crc": -932682040}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelection.class", "size": 33003, "crc": -2012233016}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelectionState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelectionState.class", "size": 1224, "crc": 1678835297}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$InputType.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$InputType.class", "size": 2388, "crc": 1170344492}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDrag$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDrag$1.class", "size": 1803, "crc": 279386476}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDragDone$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onDragDone$1.class", "size": 1467, "crc": -1128149610}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtend$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtend$1.class", "size": 1474, "crc": 2117250687}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtendDrag$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onExtendDrag$1.class", "size": 1490, "crc": 196089212}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onStart$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver$onStart$1.class", "size": 1534, "crc": -1137827862}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver.class", "size": 8759, "crc": -1145739397}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDrag$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDrag$1.class", "size": 1718, "crc": 802739778}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDragStop$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onDragStop$1.class", "size": 1443, "crc": 1007020039}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onStart$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver$onStart$1.class", "size": 1733, "crc": 862363058}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver.class", "size": 11408, "crc": 723000972}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$WhenMappings.class", "size": 1106, "crc": 941355628}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$canPaste$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$canPaste$1.class", "size": 2033, "crc": 164382527}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$copy$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$copy$1.class", "size": 2056, "crc": 69827564}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "size": 3961, "crc": 586639906}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "size": 4066, "crc": 1871681446}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3$1.class", "size": 2626, "crc": -1560223132}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "size": 4367, "crc": -1069326510}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "size": 4872, "crc": 1526743981}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cut$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cut$1.class", "size": 2005, "crc": 1588905}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "size": 2485, "crc": 1890510447}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$2.class", "size": 3010, "crc": -446204046}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$3.class", "size": 1923, "crc": 1425603583}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$4.class", "size": 1923, "crc": -214548474}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$5.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$5.class", "size": 3577, "crc": -1097054967}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "size": 2539, "crc": -1769785171}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$2.class", "size": 3050, "crc": 1744961560}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$3.class", "size": 1942, "crc": -180830994}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$4.class", "size": 1942, "crc": -1876642490}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$5.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$5.class", "size": 5076, "crc": -600889724}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$6.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$6.class", "size": 2068, "crc": -427802492}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1$1.class", "size": 5748, "crc": 1805401640}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1.class", "size": 6746, "crc": 1937934500}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "size": 5107, "crc": 1403583297}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3$1.class", "size": 1324, "crc": -1623877297}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3.class", "size": 4835, "crc": -202713951}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "size": 4555, "crc": 884789788}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$2.class", "size": 1900, "crc": 1335920756}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "size": 1969, "crc": 2115827912}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "size": 2503, "crc": -1056358033}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$2.class", "size": 5433, "crc": 460867024}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$3.class", "size": 3011, "crc": -2103614026}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$paste$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$paste$1.class", "size": 2077, "crc": 1483500968}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$pasteAsPlainText$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$pasteAsPlainText$1.class", "size": 2226, "crc": -288100921}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "size": 3979, "crc": -1733863218}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "size": 2361, "crc": 85067086}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$2.class", "size": 1750, "crc": -785819000}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "size": 4737, "crc": -2040912078}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "size": 4153, "crc": 969351609}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "size": 5149, "crc": -1615421927}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$1.class", "size": 2153, "crc": 306750505}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$1.class", "size": 3869, "crc": 2010130031}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$2.class", "size": 3879, "crc": -1308735180}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2.class", "size": 4273, "crc": -1441031716}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState.class", "size": 64660, "crc": 1611798519}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt$menuItem$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt$menuItem$1.class", "size": 2464, "crc": 1234018987}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt.class", "size": 3084, "crc": 711057839}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "size": 3506, "crc": 2026198988}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "size": 3507, "crc": 624030496}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "size": 3508, "crc": -1009436679}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "size": 3512, "crc": 558952615}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$5.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$5.class", "size": 3511, "crc": 1903616733}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$1.class", "size": 7566, "crc": 1575876994}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt.class", "size": 2641, "crc": 433613179}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt$WhenMappings.class", "size": 1104, "crc": 228772796}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt.class", "size": 4212, "crc": -32129774}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextToolbarHandler.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextToolbarHandler.class", "size": 1345, "crc": 1322299572}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextToolbarState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextToolbarState.class", "size": 2144, "crc": 286781745}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextDeleteType.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextDeleteType.class", "size": 2146, "crc": -315665}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextEditType.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextEditType.class", "size": 2075, "crc": 2047225493}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextFieldEditUndoBehavior.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextFieldEditUndoBehavior.class", "size": 2184, "crc": 720306240}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "size": 3652, "crc": 406690444}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion.class", "size": 1500, "crc": 47967214}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation.class", "size": 5544, "crc": 131761079}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperationKt.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperationKt.class", "size": 4493, "crc": 843734761}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion$createSaver$1.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion$createSaver$1.class", "size": 6851, "crc": 842895371}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion.class", "size": 1854, "crc": -1060591741}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager.class", "size": 6790, "crc": 1300024371}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "size": 1158, "crc": 1122991762}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "size": 4981, "crc": -2102259228}, {"key": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "size": 2680, "crc": -601654019}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "size": 4551, "crc": 1305064688}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "size": 6475, "crc": 571245539}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "size": 1311, "crc": -287061320}, {"key": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "size": 1367, "crc": 701680718}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache$TextAutoSizeLayoutScopeImpl.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache$TextAutoSizeLayoutScopeImpl.class", "size": 6567, "crc": -167173684}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "size": 20740, "crc": -294523206}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCacheKt.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCacheKt.class", "size": 3287, "crc": -1532763721}, {"key": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "size": 18794, "crc": -17290196}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "size": 15596, "crc": -1879237764}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "size": 13768, "crc": -367946962}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$modifier$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$modifier$1.class", "size": 1770, "crc": 1941960782}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$1.class", "size": 1658, "crc": 1632501747}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$2.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$2.class", "size": 1648, "crc": -1630462722}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController.class", "size": 12727, "crc": -1267417457}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "size": 5279, "crc": 1848480422}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "size": 5717, "crc": -1199179180}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "size": 2646, "crc": -290010326}, {"key": "androidx/compose/foundation/text/modifiers/SimpleTextAutoSizeLayoutScope.class", "name": "androidx/compose/foundation/text/modifiers/SimpleTextAutoSizeLayoutScope.class", "size": 924, "crc": -2112468699}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "size": 1302, "crc": -1352526080}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "size": 4204, "crc": -534786393}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "size": 10799, "crc": 2086852910}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "size": 5652, "crc": -1104859377}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$1.class", "size": 6006, "crc": -2102295858}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$2.class", "size": 1929, "crc": 1890878596}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$3.class", "size": 2277, "crc": -918377419}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$4.class", "size": 1553, "crc": 162384179}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$measure$1.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$measure$1.class", "size": 1915, "crc": 1856481617}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "size": 33799, "crc": -1015613413}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNodeKt.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNodeKt.class", "size": 825, "crc": 519930054}, {"key": "androidx/compose/foundation/text/modifiers/TextAutoSizeLayoutScope.class", "name": "androidx/compose/foundation/text/modifiers/TextAutoSizeLayoutScope.class", "size": 1328, "crc": 1381565965}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "size": 6175, "crc": 1289059772}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "size": 5242, "crc": -522959075}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$1.class", "size": 4588, "crc": 127560538}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$2.class", "size": 1930, "crc": -1105878704}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$3.class", "size": 2025, "crc": -2093148888}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$4.class", "size": 1517, "crc": -1630826860}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$measure$1.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$measure$1.class", "size": 1903, "crc": -397251454}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "size": 25498, "crc": 368607524}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$HandlePopup$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$HandlePopup$1.class", "size": 2456, "crc": -638781230}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$1$1$1.class", "size": 2868, "crc": 1462505405}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$2$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$2$1.class", "size": 2856, "crc": -423274690}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "size": 12792, "crc": 723985243}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "size": 4145, "crc": -1509805561}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$2.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$2.class", "size": 2502, "crc": 622278125}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$semanticsModifier$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$semanticsModifier$1$1.class", "size": 4517, "crc": -1891003355}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandleIcon$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandleIcon$1.class", "size": 2128, "crc": -71801762}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1$1.class", "size": 5941, "crc": -1733768438}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1.class", "size": 4797, "crc": 105523964}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "size": 6185, "crc": -11575559}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "size": 21094, "crc": -787293482}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "size": 976, "crc": 1883173097}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "size": 26365, "crc": 1548293009}, {"key": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "name": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "size": 851, "crc": -1167167450}, {"key": "androidx/compose/foundation/text/selection/ClicksCounter.class", "name": "androidx/compose/foundation/text/selection/ClicksCounter.class", "size": 3187, "crc": 716503027}, {"key": "androidx/compose/foundation/text/selection/CrossStatus.class", "name": "androidx/compose/foundation/text/selection/CrossStatus.class", "size": 2004, "crc": -1483611044}, {"key": "androidx/compose/foundation/text/selection/Direction.class", "name": "androidx/compose/foundation/text/selection/Direction.class", "size": 1976, "crc": -188192516}, {"key": "androidx/compose/foundation/text/selection/DownResolution.class", "name": "androidx/compose/foundation/text/selection/DownResolution.class", "size": 2073, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/HandleImageCache.class", "name": "androidx/compose/foundation/text/selection/HandleImageCache.class", "size": 2166, "crc": 254838685}, {"key": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "name": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "size": 4296, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "name": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "size": 1212, "crc": 337646130}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "size": 957, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout$createSubSelections$2$1.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout$createSubSelections$2$1.class", "size": 2715, "crc": -635838320}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "size": 14929, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "size": 12549, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "size": 11369, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/OffsetProvider.class", "name": "androidx/compose/foundation/text/selection/OffsetProvider.class", "size": 567, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/Selectable.class", "name": "androidx/compose/foundation/text/selection/Selectable.class", "size": 2446, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectableInfo.class", "name": "androidx/compose/foundation/text/selection/SelectableInfo.class", "size": 5861, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "name": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "size": 3613, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/Selection.class", "name": "androidx/compose/foundation/text/selection/Selection.class", "size": 4883, "crc": 883899336}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "size": 1587, "crc": -92684704}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "size": 1550, "crc": 697807871}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "size": 6295, "crc": 1915444121}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "size": 1213, "crc": 33677356}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$anchorSnappedToWordBoundary$2.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$anchorSnappedToWordBoundary$2.class", "size": 3276, "crc": 1012825877}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$currentRawLine$2.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$currentRawLine$2.class", "size": 2086, "crc": -1183971217}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "size": 11908, "crc": 300110652}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$DisableSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$DisableSelection$1.class", "size": 1934, "crc": 1761082528}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$1$1.class", "size": 2064, "crc": -1929694700}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$2.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$2.class", "size": 2165, "crc": -991379850}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "size": 3858, "crc": 962588946}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1.class", "size": 2508, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$1$1.class", "size": 2222, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$positionProvider$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$positionProvider$1$1.class", "size": 2138, "crc": 297738507}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$positionProvider$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$positionProvider$1$2.class", "size": 2136, "crc": 762788099}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1.class", "size": 10534, "crc": 931313414}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1.class", "size": 4175, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4.class", "size": 4058, "crc": 163324989}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5$1$invoke$$inlined$onDispose$1.class", "size": 2388, "crc": 1272720277}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5$1.class", "size": 3068, "crc": -1754879508}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$6.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$6.class", "size": 2808, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$registrarImpl$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$registrarImpl$1.class", "size": 1650, "crc": 265599753}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "size": 1881, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "size": 17985, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "size": 1693, "crc": 961547789}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "size": 1997, "crc": -38672542}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$1.class", "size": 2160, "crc": 80472248}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$2.class", "size": 2659, "crc": 2001866075}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$1.class", "size": 2021, "crc": -876510155}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$1.class", "size": 2172, "crc": 145080196}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$shouldConsumeUp$2.class", "size": 2671, "crc": -324725847}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "size": 7484, "crc": 2025777656}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "size": 3097, "crc": -1685973393}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGesturePointerInputBtf2$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGesturePointerInputBtf2$2.class", "size": 8103, "crc": -859367545}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "size": 1919, "crc": 1399820562}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$2.class", "size": 2075, "crc": 1076786062}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$1.class", "size": 1979, "crc": 648573174}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$dragCompletedWithUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$dragCompletedWithUp$1.class", "size": 2145, "crc": -728225508}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$1.class", "size": 2082, "crc": -2043530362}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1$firstDragPastSlop$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1$firstDragPastSlop$1.class", "size": 2148, "crc": 1090945911}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1.class", "size": 6653, "crc": 15911451}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$dragCompletedWithUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$dragCompletedWithUp$1.class", "size": 2160, "crc": 1057618047}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "size": 4581, "crc": 796736577}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "size": 2322, "crc": -724379174}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "size": 27004, "crc": -769055226}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "size": 2063, "crc": -1937996840}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "size": 4842, "crc": -324334744}, {"key": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "size": 5155, "crc": 1586743213}, {"key": "androidx/compose/foundation/text/selection/SelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SelectionLayout.class", "size": 2643, "crc": 193656048}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "size": 941, "crc": -2022772460}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "size": 7817, "crc": 724690288}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "size": 931, "crc": -697822086}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt$isCollapsed$1.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt$isCollapsed$1.class", "size": 1966, "crc": 110567110}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "size": 6475, "crc": 67394930}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$1.class", "size": 3889, "crc": -812834324}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$2.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$2.class", "size": 3173, "crc": -139906345}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1$1$1.class", "size": 1781, "crc": 1910193706}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "size": 6446, "crc": 1431101093}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$1.class", "size": 1853, "crc": -135641794}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2$1.class", "size": 4495, "crc": 1523851657}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2.class", "size": 5378, "crc": -2017524552}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "size": 4857, "crc": -1774775339}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "size": 11127, "crc": 1858991508}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$1.class", "size": 2930, "crc": 1734416808}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$2.class", "size": 5548, "crc": 12515961}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$3.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$3.class", "size": 3268, "crc": -739751213}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$4.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$4.class", "size": 3209, "crc": -1362053783}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$5.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$5.class", "size": 1698, "crc": 2145049383}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$6.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$6.class", "size": 2964, "crc": 1167563351}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$7.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$7.class", "size": 3609, "crc": 1982861395}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "size": 2710, "crc": 50459564}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "size": 8971, "crc": -1655333059}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$1.class", "size": 1333, "crc": -1018195896}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$2.class", "size": 1639, "crc": -1319864326}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$3.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$3.class", "size": 1735, "crc": 309441833}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$4.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$4.class", "size": 1514, "crc": 164328859}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "size": 1932, "crc": -1678516821}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "size": 5327, "crc": 928208728}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "size": 2616, "crc": 1387663354}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$1.class", "size": 1698, "crc": 253755238}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$2.class", "size": 2138, "crc": 488612175}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$1.class", "size": 1374, "crc": -2079459633}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$2.class", "size": 1332, "crc": -1850885836}, {"key": "androidx/compose/foundation/text/selection/SelectionManager.class", "name": "androidx/compose/foundation/text/selection/SelectionManager.class", "size": 49534, "crc": -504297161}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "size": 914, "crc": 926337600}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "size": 14683, "crc": -429810168}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "size": 3065, "crc": -779370896}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "size": 3070, "crc": 1357648057}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$contextMenuBuilder$1.class", "size": 4869, "crc": 238521261}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$1$1.class", "size": 2242, "crc": 1161231835}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$1.class", "size": 2115, "crc": 1164840800}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$2.class", "size": 3847, "crc": 2003811416}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1.class", "size": 3355, "crc": 792985463}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "size": 9026, "crc": 2011134581}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "size": 3530, "crc": 1908385750}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "size": 3547, "crc": -1176718449}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "size": 3535, "crc": 521680076}, {"key": "androidx/compose/foundation/text/selection/SelectionMode.class", "name": "androidx/compose/foundation/text/selection/SelectionMode.class", "size": 5267, "crc": -2039867798}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "size": 897, "crc": -347770588}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "size": 2914, "crc": 1212969059}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$1.class", "size": 2012, "crc": -809269335}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$2.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$2.class", "size": 1571, "crc": -217988618}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "size": 1485, "crc": 1362190635}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$sort$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$sort$1.class", "size": 5105, "crc": 1610166835}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "size": 19150, "crc": -1611143512}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarKt$LocalSelectionRegistrar$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarKt$LocalSelectionRegistrar$1.class", "size": 1323, "crc": 735066136}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "size": 2301, "crc": 1581599561}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "size": 3277, "crc": 730664626}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1.class", "size": 4288, "crc": 48875719}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$2.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$2.class", "size": 2123, "crc": -1685020557}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "size": 7675, "crc": -2115161094}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "size": 1010, "crc": 1068030950}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "size": 6782, "crc": -83409043}, {"key": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "size": 11267, "crc": 1726879481}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$copy$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$copy$1.class", "size": 5360, "crc": 1247621174}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "size": 6316, "crc": 502026679}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cut$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cut$1.class", "size": 5968, "crc": 617535669}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "size": 5329, "crc": 611055691}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "size": 5603, "crc": -954941204}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$onValueChange$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$onValueChange$1.class", "size": 1539, "crc": -1397461807}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$paste$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$paste$1.class", "size": 5858, "crc": -1867137214}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$autofill$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$autofill$1.class", "size": 1564, "crc": 1846213884}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$copy$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$copy$1$1.class", "size": 3722, "crc": 1319175529}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$copy$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$copy$1.class", "size": 2391, "crc": 2104237904}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$cut$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$cut$1$1.class", "size": 3615, "crc": 333900898}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$cut$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$cut$1.class", "size": 2388, "crc": -806652867}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$paste$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$paste$1$1.class", "size": 3627, "crc": -27540730}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$paste$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$paste$1.class", "size": 2394, "crc": 1418683544}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$selectAll$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1$selectAll$1.class", "size": 1567, "crc": 2073343379}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$1.class", "size": 6839, "crc": -1166924994}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "size": 9979, "crc": -1504060365}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "size": 40140, "crc": 659006205}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1$1.class", "size": 1539, "crc": 261572101}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2$1.class", "size": 2159, "crc": 778727043}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$3.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$3.class", "size": 2146, "crc": 1897379694}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "size": 941, "crc": 1164379986}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "size": 13817, "crc": 1135049380}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$1.class", "size": 3193, "crc": 33892988}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$2.class", "size": 3196, "crc": 337176367}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$3.class", "size": 3195, "crc": 2137083638}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$4.class", "size": 3171, "crc": 805169184}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$5.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1$invoke$$inlined$TextItem$5.class", "size": 3170, "crc": -117798733}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$contextMenuBuilder$1.class", "size": 7134, "crc": 461370430}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$1$1.class", "size": 2323, "crc": -616280761}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$1.class", "size": 2160, "crc": 1279394210}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$2.class", "size": 4001, "crc": 500148788}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1.class", "size": 3409, "crc": 1966561758}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "size": 9283, "crc": 399204126}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "size": 3480, "crc": 922632148}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "size": 3041, "crc": 599604080}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "size": 1333, "crc": -556953134}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "size": 2560, "crc": 876597589}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColorsKt$LocalTextSelectionColors$1.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColorsKt$LocalTextSelectionColors$1.class", "size": 1383, "crc": 583859698}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "size": 2580, "crc": -1772656834}, {"key": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "size": 4139, "crc": 1214691152}, {"key": "META-INF/androidx.compose.foundation_foundation.version", "name": "META-INF/androidx.compose.foundation_foundation.version", "size": 6, "crc": 333960004}, {"key": "META-INF/foundation_release.kotlin_module", "name": "META-INF/foundation_release.kotlin_module", "size": 7684, "crc": 1585162491}]