package io.github.simplenote.presentation.components

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandHorizontally
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkHorizontally
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import kotlinx.coroutines.delay
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import io.github.simplenote.ui.theme.SimpleNOTETheme

/**
 * Google Keep-style search bar that stays fixed at the top with smooth visibility animations.
 * Provides a consistent search experience with proper state management.
 */
@Composable
fun GoogleKeepSearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    onSettingsClick: () -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "Search notes...",
    showAppTitle: Boolean = true,
    isEnabled: Boolean = true,
    isSearching: Boolean = false,
    isFocused: Boolean = false,
    onFocusChange: (Boolean) -> Unit = {},
    onBackPressed: () -> Unit = {},
    isVisible: Boolean = true
) {
    var isSearchActive by remember(isFocused, query) { 
        mutableStateOf(isFocused || query.isNotEmpty()) 
    }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    val hapticFeedback = LocalHapticFeedback.current
    val interactionSource = remember { MutableInteractionSource() }
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // Local query state for immediate UI updates
    var localQuery by remember(query) { mutableStateOf(query) }
    
    // Debounce the search query to avoid excessive API calls
    LaunchedEffect(localQuery) {
        if (localQuery != query) {
            delay(300) // 300ms debounce
            onQueryChange(localQuery)
        }
    }
    
    // Update local query when external query changes (e.g., from back navigation)
    LaunchedEffect(query) {
        if (localQuery != query) {
            localQuery = query
        }
    }
    
    // Handle predictive back gesture when search is active
    BackHandler(enabled = isSearchActive && localQuery.isNotEmpty()) {
        if (isFocused) {
            // First back: Hide keyboard and remove focus, preserve query
            keyboardController?.hide()
            focusRequester.freeFocus()
            onFocusChange(false)
        } else {
            // Second back: Clear search and navigate back
            localQuery = ""
            onQueryChange("")
            onBackPressed()
        }
    }
    
    // Handle empty search with focus
    BackHandler(enabled = isSearchActive && localQuery.isEmpty() && isFocused) {
        keyboardController?.hide()
        focusRequester.freeFocus()
        onFocusChange(false)
        isSearchActive = false
    }
    
    // Animated values for smooth transitions
    val animatedBackgroundColor by animateColorAsState(
        targetValue = if (isFocused || isSearchActive) {
            MaterialTheme.colorScheme.background // Use background color in edit mode
        } else {
            MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.95f)
        },
        animationSpec = tween(300),
        label = "backgroundColor"
    )
    
    val animatedElevation by animateFloatAsState(
        targetValue = if (isFocused || isSearchActive) 12f else 4f,
        animationSpec = tween(300),
        label = "elevation"
    )

    // Handle focus changes from external source
    LaunchedEffect(isFocused) {
        if (isFocused && isEnabled) {
            try {
                focusRequester.requestFocus()
            } catch (e: Exception) {
                // Handle focus request failure gracefully
            }
        }
    }
    
    // Handle app lifecycle changes and configuration changes
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    // Hide keyboard when app goes to background
                    if (isFocused) {
                        keyboardController?.hide()
                        focusRequester.freeFocus()
                    }
                }
                Lifecycle.Event.ON_STOP -> {
                    // Clear focus when app goes to background for longer period
                    if (isFocused) {
                        keyboardController?.hide()
                        focusRequester.freeFocus()
                        onFocusChange(false)
                    }
                }
                Lifecycle.Event.ON_RESUME -> {
                    // Restore focus state if needed after configuration change
                    if (isFocused && query.isNotEmpty()) {
                        try {
                            focusRequester.requestFocus()
                        } catch (e: Exception) {
                            // Handle gracefully
                        }
                    }
                }
                else -> {}
            }
        }
        
        lifecycleOwner.lifecycle.addObserver(observer)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // Search bar with smooth visibility animation
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { -it },
            animationSpec = tween(300)
        ) + fadeIn(animationSpec = tween(300)),
        exit = slideOutVertically(
            targetOffsetY = { -it },
            animationSpec = tween(300)
        ) + fadeOut(animationSpec = tween(300)),
        modifier = modifier
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .windowInsetsPadding(WindowInsets.statusBars)
                .padding(horizontal = 16.dp, vertical = 8.dp)
                .shadow(
                    elevation = animatedElevation.dp,
                    shape = RoundedCornerShape(32.dp)
                )
                .zIndex(1f), // Ensure it stays above content
            shape = RoundedCornerShape(32.dp),
            color = animatedBackgroundColor
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        interactionSource = interactionSource,
                        indication = null
                    ) {
                        if (!isSearchActive) {
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            isSearchActive = true
                            if (isEnabled) {
                                onFocusChange(true)
                            }
                        }
                    }
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // Leading icon: Back button in edit mode, Search icon otherwise
                AnimatedContent(
                    targetState = isSearchActive,
                    transitionSpec = {
                        slideInHorizontally(initialOffsetX = { -it/2 }) + fadeIn() togetherWith
                        slideOutHorizontally(targetOffsetX = { -it/2 }) + fadeOut()
                    },
                    label = "leadingIcon"
                ) { searchActive ->
                    if (searchActive) {
                        // Back button in edit mode
                        IconButton(
                            onClick = {
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                keyboardController?.hide()
                                focusRequester.freeFocus() // Clear focus from text field
                                onFocusChange(false)
                                // Always exit search active state when back is pressed
                                isSearchActive = localQuery.isNotEmpty()
                            },
                            modifier = Modifier.size(20.dp)
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    } else {
                        // Search icon or loading indicator
                        if (isSearching && localQuery.isNotEmpty()) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.primary
                            )
                        } else {
                            Icon(
                                imageVector = Icons.Default.Search,
                                contentDescription = "Search",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                }
                
                // App title or search field
                Box(
                    modifier = Modifier.weight(1f)
                ) {
                    // Always render the BasicTextField for immediate focus capability
                    BasicTextField(
                        value = localQuery,
                        onValueChange = { newQuery ->
                            localQuery = newQuery
                            isSearchActive = true // Activate search mode on any input
                            if (newQuery.isNotEmpty()) {
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester)
                            .onFocusChanged { focusState ->
                                onFocusChange(focusState.isFocused)
                                if (focusState.isFocused) {
                                    isSearchActive = true
                                }
                            },
                        enabled = isEnabled,
                        singleLine = true,
                        textStyle = MaterialTheme.typography.bodyLarge.copy(
                            color = if (isSearchActive || isFocused || localQuery.isNotEmpty()) {
                                MaterialTheme.colorScheme.onSurface
                            } else {
                                androidx.compose.ui.graphics.Color.Transparent // Hide text when showing title
                            }
                        ),
                        cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Search
                        ),
                        keyboardActions = KeyboardActions(
                            onSearch = {
                                onSearch(localQuery)
                                keyboardController?.hide()
                                onFocusChange(false)
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            }
                        )
                    )

                    // Show app title overlay when not active and no query
                    if (!isSearchActive && !isFocused && localQuery.isEmpty() && showAppTitle) {
                        Text(
                            text = "SimpleNote",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.align(Alignment.CenterStart)
                        )
                    }

                    // Show placeholder when search is active but no query
                    if ((isSearchActive || isFocused) && localQuery.isEmpty()) {
                        Text(
                            text = placeholder,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                            modifier = Modifier.align(Alignment.CenterStart)
                        )
                    }
                }
                
                // Clear button (only when there's a query)
                AnimatedVisibility(
                    visible = localQuery.isNotEmpty(),
                    enter = expandHorizontally() + fadeIn(),
                    exit = shrinkHorizontally() + fadeOut()
                ) {
                    IconButton(
                        onClick = {
                            localQuery = ""
                            onQueryChange("")
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "Clear search",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
                
                // Settings icon (only visible when search is not active)
                AnimatedVisibility(
                    visible = !isSearchActive,
                    enter = expandHorizontally() + fadeIn(),
                    exit = shrinkHorizontally() + fadeOut()
                ) {
                    IconButton(
                        onClick = {
                            onSettingsClick()
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = "Settings",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true, name = "Default State")
@Composable
fun GoogleKeepSearchBarPreview() {
    SimpleNOTETheme {
        GoogleKeepSearchBar(
            query = "",
            onQueryChange = {},
            onSearch = {},
            onSettingsClick = {},
            isVisible = true
        )
    }
}

@Preview(showBackground = true, name = "With Query")
@Composable
fun GoogleKeepSearchBarWithQueryPreview() {
    SimpleNOTETheme {
        GoogleKeepSearchBar(
            query = "Sample search query",
            onQueryChange = {},
            onSearch = {},
            onSettingsClick = {},
            isFocused = true,
            isVisible = true
        )
    }
}

@Preview(showBackground = true, name = "Searching State")
@Composable
fun GoogleKeepSearchBarSearchingPreview() {
    SimpleNOTETheme {
        GoogleKeepSearchBar(
            query = "Search query",
            onQueryChange = {},
            onSearch = {},
            onSettingsClick = {},
            isFocused = true,
            isSearching = true,
            isVisible = true
        )
    }
}
