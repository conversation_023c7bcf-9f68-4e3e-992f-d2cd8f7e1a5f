package io.github.simplenote.data.local.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import io.github.simplenote.data.local.dao.NoteDao
import io.github.simplenote.data.local.entity.NoteEntity
import android.content.Context

/**
 * Room database for the SimpleNote application.
 * Contains the notes table and provides access to the NoteDao.
 */
@Database(
    entities = [NoteEntity::class],
    version = 1,
    exportSchema = true
)
abstract class NoteDatabase : RoomDatabase() {
    
    abstract fun noteDao(): NoteDao
    
    companion object {
        const val DATABASE_NAME = "simple_note_database"
        
        /**
         * Create the database instance with proper configuration
         */
        fun create(context: Context): NoteDatabase {
            return Room.databaseBuilder(
                context = context,
                klass = NoteDatabase::class.java,
                name = DATABASE_NAME
            )
                .addMigrations(*getAllMigrations())
                .fallbackToDestructiveMigration() // Only for development
                .build()
        }
        
        /**
         * Get all database migrations
         */
        private fun getAllMigrations(): Array<Migration> {
            return arrayOf(
                // Future migrations will be added here
            )
        }
        
        /**
         * Migration from version 1 to 2 (example for future use)
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Example migration - add new column
                // database.execSQL("ALTER TABLE notes ADD COLUMN is_favorite INTEGER NOT NULL DEFAULT 0")
            }
        }
    }
}
