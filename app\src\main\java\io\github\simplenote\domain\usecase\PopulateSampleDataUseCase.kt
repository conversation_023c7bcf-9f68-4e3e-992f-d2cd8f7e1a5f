package io.github.simplenote.domain.usecase

import arrow.core.Either
import io.github.simplenote.data.util.SampleDataGenerator
import io.github.simplenote.domain.model.NoteError
import io.github.simplenote.domain.repository.NoteRepository
import kotlinx.coroutines.flow.first

/**
 * Use case for populating the database with sample data.
 * Only adds sample data if the database is empty.
 */
class PopulateSampleDataUseCase(
    private val repository: NoteRepository
) {
    
    /**
     * Populates the database with sample data if it's empty.
     * @return Either.Right(Unit) if successful, Either.Left(NoteError) if failed
     */
    suspend operator fun invoke(): Either<NoteError, Unit> {
        return try {
            // Check if database is empty
            val existingNotes = repository.getAllNotes().first()
            
            when (existingNotes) {
                is Either.Left -> {
                    // Error getting notes, return the error
                    existingNotes
                }
                is Either.Right -> {
                    if (existingNotes.value.isEmpty()) {
                        // Database is empty, populate with sample data
                        val sampleNotes = SampleDataGenerator.generateSampleNotes(30)
                        
                        // Insert all sample notes
                        sampleNotes.forEach { note ->
                            when (val result = repository.createNote(note)) {
                                is Either.Left -> return result
                                is Either.Right -> { /* Continue */ }
                            }
                        }
                        
                        Either.Right(Unit)
                    } else {
                        // Database already has data, do nothing
                        Either.Right(Unit)
                    }
                }
            }
        } catch (e: Exception) {
            Either.Left(NoteError.DatabaseError.InsertFailed(e.message ?: "Failed to populate sample data"))
        }
    }
}
