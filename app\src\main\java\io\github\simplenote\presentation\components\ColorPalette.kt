package io.github.simplenote.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.ui.theme.SimpleNOTETheme

/**
 * Composable for displaying a grid of color options.
 */
@Composable
fun ColorPalette(
    selectedColor: NoteColor,
    onColorSelected: (NoteColor) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(6),
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(NoteColor.getAllColors()) { color ->
            ColorItem(
                color = color,
                isSelected = color == selectedColor,
                onColorClick = { onColorSelected(color) }
            )
        }
    }
}

/**
 * Individual color item in the palette.
 */
@Composable
private fun ColorItem(
    color: NoteColor,
    isSelected: Boolean,
    onColorClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
//            .size(48.dp)
            .aspectRatio(1f)
            .clip(CircleShape)
            .background(color.color)
            .border(
                width = if (isSelected) 3.dp else 1.dp,
                color = if (isSelected) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                },
                shape = CircleShape
            )
            .clickable { onColorClick() },
        contentAlignment = Alignment.Center
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = getContrastColor(color.color),
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * Get contrasting color for the check icon.
 */
private fun getContrastColor(backgroundColor: Color): Color {
    // Simple contrast calculation - use white for dark colors, black for light colors
    val luminance = (0.299 * backgroundColor.red + 0.587 * backgroundColor.green + 0.114 * backgroundColor.blue)
    return if (luminance > 0.5) Color.Black else Color.White
}

@Preview(showBackground = true)
@Composable
fun ColorPalettePreview() {
    SimpleNOTETheme {
        ColorPalette(
            selectedColor = NoteColor.BLUE,
            onColorSelected = {}
        )
    }
}
