---
type: "always_apply"
---

# Kotlin Coding Standards

## Naming Conventions
- **Classes**: PascalCase (`UserRepository`)
- **Functions**: camelCase (`getUserById`)
- **Constants**: SCREAMING_SNAKE_CASE (`MAX_RETRY_COUNT`)
- **Packages**: lowercase (`com.app.feature.chat`)

## Code Organization
```

// File structure order:

1. Package declaration
2. Imports (Android, then 3rd party, then internal)
3. Class declaration
4. Properties (private first)
5. Init blocks
6. Public methods
7. Private methods
8. Companion object
```

## Best Practices
- Use **sealed classes** for state representation
- Prefer **data classes** with validation
- Use **extension functions** for readability
- Implement **proper null safety**
- Use **@JvmInline value classes** for type safety

## Compose Guidelines
- Use **remember** for expensive calculations
- Apply **keys** in LazyColumn/LazyRow
- Prefer **stateless composables**
- Use **derivedStateOf** for derived state
- Implement **proper preview functions**
