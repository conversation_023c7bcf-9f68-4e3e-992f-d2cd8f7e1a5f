[{"key": "androidx/lifecycle/LiveDataKt$observe$wrappedObserver$1.class", "name": "androidx/lifecycle/LiveDataKt$observe$wrappedObserver$1.class", "size": 1310, "crc": 340673706}, {"key": "androidx/lifecycle/LiveDataKt.class", "name": "androidx/lifecycle/LiveDataKt.class", "size": 2009, "crc": 585856359}, {"key": "androidx/lifecycle/Observer.class", "name": "androidx/lifecycle/Observer.class", "size": 581, "crc": -1062994136}, {"key": "androidx/lifecycle/LiveData$1.class", "name": "androidx/lifecycle/LiveData$1.class", "size": 997, "crc": -378205089}, {"key": "androidx/lifecycle/LiveData$AlwaysActiveObserver.class", "name": "androidx/lifecycle/LiveData$AlwaysActiveObserver.class", "size": 1058, "crc": 490911418}, {"key": "androidx/lifecycle/LiveData$LifecycleBoundObserver.class", "name": "androidx/lifecycle/LiveData$LifecycleBoundObserver.class", "size": 2893, "crc": -387464342}, {"key": "androidx/lifecycle/LiveData$ObserverWrapper.class", "name": "androidx/lifecycle/LiveData$ObserverWrapper.class", "size": 1622, "crc": 788645168}, {"key": "androidx/lifecycle/LiveData.class", "name": "androidx/lifecycle/LiveData.class", "size": 8939, "crc": -232260782}, {"key": "androidx/lifecycle/MutableLiveData.class", "name": "androidx/lifecycle/MutableLiveData.class", "size": 946, "crc": -710747396}, {"key": "META-INF/androidx.lifecycle_lifecycle-livedata-core.version", "name": "META-INF/androidx.lifecycle_lifecycle-livedata-core.version", "size": 6, "crc": 886372302}, {"key": "META-INF/lifecycle-livedata-core_release.kotlin_module", "name": "META-INF/lifecycle-livedata-core_release.kotlin_module", "size": 58, "crc": -2017646999}]