package io.github.simplenote.presentation.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import io.github.simplenote.domain.model.NoteError
import io.github.simplenote.ui.theme.SimpleNOTETheme

/**
 * Error state component for displaying errors with retry option.
 */
@Composable
fun ErrorState(
    modifier: Modifier = Modifier,
    error: NoteError,
    onRetry: (() -> Unit)? = null
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = getErrorTitle(error),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error.message,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
        
        if (onRetry != null) {
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = onRetry
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.padding(4.dp))
                Text("Try Again")
            }
        }
    }
}

/**
 * Get appropriate title for different error types.
 */
private fun getErrorTitle(error: NoteError): String {
    return when (error) {
        is NoteError.DatabaseError -> "Database Error"
        is NoteError.ValidationError -> "Validation Error"
        is NoteError.AppError.NetworkUnavailable -> "Network Error"
        is NoteError.AppError.StorageUnavailable -> "Storage Error"
        is NoteError.AppError.Unknown -> "Something went wrong"
    }
}

@Preview(showBackground = true)
@Composable
fun ErrorStatePreview() {
    SimpleNOTETheme {
        ErrorState(
            error = NoteError.DatabaseError.QueryFailed("Failed to load notes"),
            onRetry = {}
        )
    }
}
