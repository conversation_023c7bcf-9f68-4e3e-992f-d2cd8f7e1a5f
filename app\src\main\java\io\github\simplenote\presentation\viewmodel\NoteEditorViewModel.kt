package io.github.simplenote.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import arrow.core.Either
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.usecase.CreateNoteUseCase
import io.github.simplenote.domain.usecase.DeleteNoteUseCase
import io.github.simplenote.domain.usecase.GetNotesUseCase
import io.github.simplenote.domain.usecase.UpdateNoteUseCase
import io.github.simplenote.presentation.model.NavigationEvent
import io.github.simplenote.presentation.model.NoteEditorUiEvent
import io.github.simplenote.presentation.model.NoteEditorUiState
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for the note editor screen.
 * Manages the state of note editing, saving, and deletion.
 */
class NoteEditorViewModel(
    private val getNotesUseCase: GetNotesUseCase,
    private val createNoteUseCase: CreateNoteUseCase,
    private val updateNoteUseCase: UpdateNoteUseCase,
    private val deleteNoteUseCase: DeleteNoteUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(NoteEditorUiState())
    val uiState: StateFlow<NoteEditorUiState> = _uiState.asStateFlow()

    private val _navigationEvents = MutableSharedFlow<NavigationEvent>()
    val navigationEvents: SharedFlow<NavigationEvent> = _navigationEvents.asSharedFlow()

    fun onEvent(event: NoteEditorUiEvent) {
        when (event) {
            is NoteEditorUiEvent.LoadNote -> loadNote(event.noteId)
            is NoteEditorUiEvent.UpdateTitle -> updateTitle(event.title)
            is NoteEditorUiEvent.UpdateContent -> updateContent(event.content)
            is NoteEditorUiEvent.ChangeColor -> changeColor(event.color)
            is NoteEditorUiEvent.SaveNote -> saveNote()
            is NoteEditorUiEvent.DeleteNote -> deleteNote()
            is NoteEditorUiEvent.NavigateBack -> navigateBack()
            is NoteEditorUiEvent.ClearError -> clearError()
        }
    }

    private fun loadNote(noteId: Long) {
        if (noteId <= 0) {
            // New note
            _uiState.value = _uiState.value.copy(
                isNewNote = true,
                isLoading = false
            )
            return
        }

        _uiState.value = _uiState.value.copy(isLoading = true, error = null)

        viewModelScope.launch {
            val result = getNotesUseCase.getNoteById(noteId)
            when (result) {
                is Either.Left -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.value
                    )
                }

                is Either.Right -> {
                    val note = result.value
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        note = note,
                        title = note.title,
                        content = note.content,
                        selectedColor = note.color,
                        isNewNote = false,
                        hasUnsavedChanges = false,
                        error = null
                    )
                }
            }
        }
    }

    private fun updateTitle(title: String) {
        val currentState = _uiState.value
        val hasChanges = title != (currentState.note?.title ?: "") ||
                currentState.content != (currentState.note?.content ?: "") ||
                currentState.selectedColor != (currentState.note?.color ?: NoteColor.DEFAULT)

        _uiState.value = currentState.copy(
            title = title,
            hasUnsavedChanges = hasChanges
        )
    }

    private fun updateContent(content: String) {
        val currentState = _uiState.value
        val hasChanges = currentState.title != (currentState.note?.title ?: "") ||
                content != (currentState.note?.content ?: "") ||
                currentState.selectedColor != (currentState.note?.color ?: NoteColor.DEFAULT)

        _uiState.value = currentState.copy(
            content = content,
            hasUnsavedChanges = hasChanges
        )
    }

    private fun changeColor(color: NoteColor) {
        val currentState = _uiState.value
        val hasChanges = currentState.title != (currentState.note?.title ?: "") ||
                currentState.content != (currentState.note?.content ?: "") ||
                color != (currentState.note?.color ?: NoteColor.DEFAULT)

        _uiState.value = currentState.copy(
            selectedColor = color,
            hasUnsavedChanges = hasChanges
        )
    }

    private fun saveNote() {
        val currentState = _uiState.value
        if (!currentState.canSave) return

        _uiState.value = currentState.copy(isSaving = true, error = null)

        viewModelScope.launch {
            val result = if (currentState.isNewNote) {
                createNoteUseCase(
                    title = currentState.title,
                    content = currentState.content,
                    color = currentState.selectedColor
                )
            } else {
                currentState.note?.let { note ->
                    updateNoteUseCase(
                        note = note,
                        newTitle = currentState.title,
                        newContent = currentState.content,
                        newColor = currentState.selectedColor
                    )
                }
                    ?: Either.Left(io.github.simplenote.domain.model.NoteError.AppError.Unknown("Note not found"))
            }

            when (result) {
                is Either.Left -> {
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        error = result.value
                    )
                }

                is Either.Right -> {
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        note = result.value,
                        hasUnsavedChanges = false,
                        isNewNote = false,
                        error = null
                    )
                    _navigationEvents.emit(NavigationEvent.NavigateBack)
                }
            }
        }
    }

    private fun deleteNote() {
        val note = _uiState.value.note ?: return

        viewModelScope.launch {
            _navigationEvents.emit(NavigationEvent.ShowDeleteConfirmation(note))
        }
    }

    fun confirmDelete() {
        val note = _uiState.value.note ?: return

        viewModelScope.launch {
            val result = deleteNoteUseCase(note)
            when (result) {
                is Either.Left -> {
                    _uiState.value = _uiState.value.copy(error = result.value)
                }

                is Either.Right -> {
                    _navigationEvents.emit(NavigationEvent.NavigateBack)
                }
            }
        }
    }

    private fun navigateBack() {
        viewModelScope.launch {
            _navigationEvents.emit(NavigationEvent.NavigateBack)
        }
    }

    private fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}
