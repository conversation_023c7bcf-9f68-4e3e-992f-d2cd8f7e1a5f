io.github.simplenote.app-lifecycle-livedata-core-2.9.2-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0226efcf44c5e5d49af1b0dc8369cf79\transformed\lifecycle-livedata-core-2.9.2\res
io.github.simplenote.app-savedstate-compose-release-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\0af7a71bc77c40ff487916a8f0efe29c\transformed\savedstate-compose-release\res
io.github.simplenote.app-emoji2-views-helper-1.4.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0b649523b489aab6eb4df3f50cf82e90\transformed\emoji2-views-helper-1.4.0\res
io.github.simplenote.app-navigation-compose-release-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5916c99aabdefec4ef03690d7a2a3c\transformed\navigation-compose-release\res
io.github.simplenote.app-emoji2-1.4.0-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\res
io.github.simplenote.app-customview-poolingcontainer-1.0.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\186e30df85fe320f7190373ad5d1ca2c\transformed\customview-poolingcontainer-1.0.0\res
io.github.simplenote.app-ui-release-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\250d8cc481173cc0c0524c32c74c00eb\transformed\ui-release\res
io.github.simplenote.app-sqlite-release-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac58664721739588eccfba72ea0587d\transformed\sqlite-release\res
io.github.simplenote.app-startup-runtime-1.1.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\res
io.github.simplenote.app-activity-1.10.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\2cf858d13e6a5e1f5b119836a8b04239\transformed\activity-1.10.1\res
io.github.simplenote.app-runtime-release-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\3032d8c69540cf626cf55a11c7eb6ae7\transformed\runtime-release\res
io.github.simplenote.app-material-icons-core-release-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\312f557dc9df8ea6cbb049518740c4f6\transformed\material-icons-core-release\res
io.github.simplenote.app-lifecycle-runtime-release-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\36de0711b8724c92786f0bf89a4d43c7\transformed\lifecycle-runtime-release\res
io.github.simplenote.app-ui-geometry-release-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\381547b80a35280790ec30a8cc1a672e\transformed\ui-geometry-release\res
io.github.simplenote.app-core-ktx-1.16.0-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\3b3727a0af9938b2b4f53392a3dce9bb\transformed\core-ktx-1.16.0\res
io.github.simplenote.app-profileinstaller-1.4.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\res
io.github.simplenote.app-sqlite-framework-release-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\409cd7c9c3057a3e7553a6631d5e1b7b\transformed\sqlite-framework-release\res
io.github.simplenote.app-animation-core-release-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\4126eedf04e080472ca9b82cdac66986\transformed\animation-core-release\res
io.github.simplenote.app-annotation-experimental-1.4.1-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\43b10ea5a82b477a24598e9b953b9eba\transformed\annotation-experimental-1.4.1\res
io.github.simplenote.app-lifecycle-viewmodel-ktx-2.9.2-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\43dd482a7c96b77d6cc6ccc24a17e047\transformed\lifecycle-viewmodel-ktx-2.9.2\res
io.github.simplenote.app-runtime-saveable-release-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\44072d45c1c5c9f9fbbd156a68477164\transformed\runtime-saveable-release\res
io.github.simplenote.app-animation-release-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\4f5dcfdd8f75e3f8cbb984085971c6e9\transformed\animation-release\res
io.github.simplenote.app-ui-tooling-release-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\res
io.github.simplenote.app-ui-text-release-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\54bf382fb7c1129431b3418c21685dda\transformed\ui-text-release\res
io.github.simplenote.app-appcompat-resources-1.7.1-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\55ccf585c648384d6d3306f9dd19b331\transformed\appcompat-resources-1.7.1\res
io.github.simplenote.app-navigation-common-release-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\5d7b96133fd54175221e748800b0b16e\transformed\navigation-common-release\res
io.github.simplenote.app-lifecycle-viewmodel-savedstate-release-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\61f7e35418771ab73cd674216e8ea897\transformed\lifecycle-viewmodel-savedstate-release\res
io.github.simplenote.app-activity-ktx-1.10.1-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\61f8129004ccff9ecbc01885518a2d0c\transformed\activity-ktx-1.10.1\res
io.github.simplenote.app-lifecycle-viewmodel-release-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\71a734cd1832c1a454590ad921e7204f\transformed\lifecycle-viewmodel-release\res
io.github.simplenote.app-navigation-runtime-release-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\759f44b938018b86b4c9fb3e5df6883b\transformed\navigation-runtime-release\res
io.github.simplenote.app-core-viewtree-1.0.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae9817b57a187e60b0aad2683452b55\transformed\core-viewtree-1.0.0\res
io.github.simplenote.app-material3-release-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\7d61245d3231dfae84075323e154f2e0\transformed\material3-release\res
io.github.simplenote.app-tracing-1.2.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\7fd5e6e4172b7c73b7467af17cfed33f\transformed\tracing-1.2.0\res
io.github.simplenote.app-foundation-layout-release-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\84aef995668f3afb4a1cd22a31387706\transformed\foundation-layout-release\res
io.github.simplenote.app-material-icons-extended-release-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\863ed73f3c29931470523f99024b69e7\transformed\material-icons-extended-release\res
io.github.simplenote.app-core-1.16.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\res
io.github.simplenote.app-core-splashscreen-1.0.1-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\8835490be1a29b5f40d11535f6927e40\transformed\core-splashscreen-1.0.1\res
io.github.simplenote.app-lifecycle-runtime-compose-release-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\8e6dfe0612115486eaa52457b6b985db\transformed\lifecycle-runtime-compose-release\res
io.github.simplenote.app-material-release-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\8f6b7cfc7dddc58c7ce7adfd537c1781\transformed\material-release\res
io.github.simplenote.app-ui-graphics-release-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\9191134d19d2f1df3d192d102235a2e1\transformed\ui-graphics-release\res
io.github.simplenote.app-foundation-release-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\96ccc887d394ddaa1dd6d0e3b9de1bba\transformed\foundation-release\res
io.github.simplenote.app-room-runtime-release-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\res
io.github.simplenote.app-ui-unit-release-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\a381dee28a0246f2ac113bec67c9bdd7\transformed\ui-unit-release\res
io.github.simplenote.app-appcompat-1.7.1-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\a52cd15d91b3b91eba876f6457455010\transformed\appcompat-1.7.1\res
io.github.simplenote.app-ui-tooling-data-release-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\a595bc9036bece929a4901376504e526\transformed\ui-tooling-data-release\res
io.github.simplenote.app-datastore-release-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\ae9198a09dc580746a955a921bc37a89\transformed\datastore-release\res
io.github.simplenote.app-lifecycle-livedata-core-ktx-2.9.2-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\af31a73ad5c0b7922c384dc7240c9e38\transformed\lifecycle-livedata-core-ktx-2.9.2\res
io.github.simplenote.app-room-ktx-2.7.2-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\b073acf1deccb16be6bbe2264189300d\transformed\room-ktx-2.7.2\res
io.github.simplenote.app-graphics-path-1.0.1-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fbdca4cff0337663acf0a2aafd5ff6\transformed\graphics-path-1.0.1\res
io.github.simplenote.app-material-ripple-release-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\b666e5e24eb30aa3f982dbadc803c1c4\transformed\material-ripple-release\res
io.github.simplenote.app-lifecycle-process-2.9.2-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\res
io.github.simplenote.app-datastore-preferences-release-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\be56c65c9154680c10169f24ca1efe81\transformed\datastore-preferences-release\res
io.github.simplenote.app-core-runtime-2.2.0-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\c30a28a2b4c3895a9a01eab52b571f33\transformed\core-runtime-2.2.0\res
io.github.simplenote.app-datastore-preferences-core-release-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\c522a1397267ebfa7112d0505a40ec28\transformed\datastore-preferences-core-release\res
io.github.simplenote.app-lifecycle-viewmodel-compose-release-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\cb8b0930d663c4970257b91c063e82d5\transformed\lifecycle-viewmodel-compose-release\res
io.github.simplenote.app-ui-util-release-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\cc60f3403f29b909ebc5a5bdad6b8e61\transformed\ui-util-release\res
io.github.simplenote.app-savedstate-release-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\d0ffb6d4d4d2356baf020d19e3081802\transformed\savedstate-release\res
io.github.simplenote.app-fragment-1.8.8-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\d2f04f83541c4522bc76f261d3ab1327\transformed\fragment-1.8.8\res
io.github.simplenote.app-datastore-core-release-58 C:\Users\<USER>\.gradle\caches\8.13\transforms\dd49277c138bc5ea9b5faa44fe8b0bb1\transformed\datastore-core-release\res
io.github.simplenote.app-ui-tooling-preview-release-59 C:\Users\<USER>\.gradle\caches\8.13\transforms\ed21dc111fd125e1031bf6e008f544fd\transformed\ui-tooling-preview-release\res
io.github.simplenote.app-lifecycle-livedata-2.9.2-60 C:\Users\<USER>\.gradle\caches\8.13\transforms\edb49963624f80a748f5dc5659581840\transformed\lifecycle-livedata-2.9.2\res
io.github.simplenote.app-savedstate-ktx-1.3.1-61 C:\Users\<USER>\.gradle\caches\8.13\transforms\ee3e6af81af78cd6a62020dc6325c477\transformed\savedstate-ktx-1.3.1\res
io.github.simplenote.app-fragment-ktx-1.8.8-62 C:\Users\<USER>\.gradle\caches\8.13\transforms\efd0c7f813c432e2c0785136f0b2016e\transformed\fragment-ktx-1.8.8\res
io.github.simplenote.app-activity-compose-1.10.1-63 C:\Users\<USER>\.gradle\caches\8.13\transforms\f92d728c24f0eb7aa621a3c78cd669f0\transformed\activity-compose-1.10.1\res
io.github.simplenote.app-pngs-64 C:\Users\<USER>\STUDIO\SimpleNOTE\app\build\generated\res\pngs\debug
io.github.simplenote.app-resValues-65 C:\Users\<USER>\STUDIO\SimpleNOTE\app\build\generated\res\resValues\debug
io.github.simplenote.app-packageDebugResources-66 C:\Users\<USER>\STUDIO\SimpleNOTE\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
io.github.simplenote.app-packageDebugResources-67 C:\Users\<USER>\STUDIO\SimpleNOTE\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
io.github.simplenote.app-debug-68 C:\Users\<USER>\STUDIO\SimpleNOTE\app\build\intermediates\merged_res\debug\mergeDebugResources
io.github.simplenote.app-debug-69 C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\debug\res
io.github.simplenote.app-main-70 C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\res
