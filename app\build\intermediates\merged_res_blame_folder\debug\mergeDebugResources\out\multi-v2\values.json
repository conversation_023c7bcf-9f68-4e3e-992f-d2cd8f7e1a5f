{"logs": [{"outputFile": "io.github.simplenote.app-mergeDebugResources-66:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d2f04f83541c4522bc76f261d3ab1327\\transformed\\fragment-1.8.8\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "258,271,297,2799,2804", "startColumns": "4,4,4,4,4", "startOffsets": "17051,17696,19062,161678,161848", "endLines": "258,271,297,2803,2807", "endColumns": "56,64,63,24,24", "endOffsets": "17103,17756,19121,161843,161992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d0ffb6d4d4d2356baf020d19e3081802\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "295", "startColumns": "4", "startOffsets": "18958", "endColumns": "53", "endOffsets": "19007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d61245d3231dfae84075323e154f2e0\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "304,350,351,352,353,354,355,356,357,358,359,362,363,364,365,366,367,368,369,370,371,372,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,1526,1536", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19490,22643,22731,22817,22898,22982,23051,23116,23199,23305,23391,23511,23565,23634,23695,23764,23853,23948,24022,24119,24212,24310,24459,24550,24638,24734,24832,24896,24964,25051,25145,25212,25284,25356,25457,25566,25642,25711,25759,25825,25889,25963,26020,26077,26149,26199,26253,26324,26395,26465,26534,26592,26668,26739,26813,26899,26949,27019,98046,98761", "endLines": "304,350,351,352,353,354,355,356,357,358,361,362,363,364,365,366,367,368,369,370,371,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,1535,1538", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19558,22726,22812,22893,22977,23046,23111,23194,23300,23386,23506,23560,23629,23690,23759,23848,23943,24017,24114,24207,24305,24454,24545,24633,24729,24827,24891,24959,25046,25140,25207,25279,25351,25452,25561,25637,25706,25754,25820,25884,25958,26015,26072,26144,26194,26248,26319,26390,26460,26529,26587,26663,26734,26808,26894,26944,27014,27079,98756,98909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2c209c48bc5c39759a1bfd4c2f5ff390\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "333", "startColumns": "4", "startOffsets": "21499", "endColumns": "82", "endOffsets": "21577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250d8cc481173cc0c0524c32c74c00eb\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "255,256,257,260,262,298,343,344,345,346,347,348,349,412,413,414,415,417,418,419,420,421,423,424,425,1523,1539,1542", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16864,16938,16996,17174,17259,19126,22187,22252,22306,22372,22473,22531,22583,27142,27204,27258,27308,27415,27461,27515,27561,27603,27714,27761,27797,97934,98914,99025", "endLines": "255,256,257,260,262,298,343,344,345,346,347,348,349,412,413,414,415,417,418,419,420,421,423,424,425,1525,1541,1546", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "16933,16991,17046,17220,17309,19174,22247,22301,22367,22468,22526,22578,22638,27199,27253,27303,27357,27456,27510,27556,27598,27638,27756,27792,27882,98041,99020,99277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55ccf585c648384d6d3306f9dd19b331\\transformed\\appcompat-resources-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2122,2138,2144,3150,3166", "startColumns": "4,4,4,4,4", "startOffsets": "138299,138724,138902,172987,173398", "endLines": "2137,2143,2153,3165,3169", "endColumns": "24,24,24,24,24", "endOffsets": "138719,138897,139181,173393,173520"}}, {"source": "C:\\Users\\<USER>\\STUDIO\\SimpleNOTE\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "87", "endOffsets": "139"}, "to": {"startLines": "1865", "startColumns": "4", "startOffsets": "123562", "endColumns": "86", "endOffsets": "123644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8f6b7cfc7dddc58c7ce7adfd537c1781\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "411", "startColumns": "4", "startOffsets": "27084", "endColumns": "57", "endOffsets": "27137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8835490be1a29b5f40d11535f6927e40\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "6,7,9,10,11,12,201,202,203,204,205,206,207,303,687,688,689,1519,1521,1866,1875,1888", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "359,419,519,588,660,723,13569,13643,13719,13795,13872,13943,14012,19422,45094,45175,45267,97704,97813,123649,124109,124884", "endLines": "6,7,9,10,11,12,201,202,203,204,205,206,207,303,687,688,689,1520,1522,1874,1887,1891", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "414,473,583,655,718,790,13638,13714,13790,13867,13938,14007,14078,19485,45170,45262,45355,97808,97929,124104,124879,125152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96ccc887d394ddaa1dd6d0e3b9de1bba\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "335,426,427", "startColumns": "4,4,4", "startOffsets": "21630,27887,27943", "endColumns": "45,55,54", "endOffsets": "21671,27938,27993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\36de0711b8724c92786f0bf89a4d43c7\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "18855", "endColumns": "42", "endOffsets": "18893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\186e30df85fe320f7190373ad5d1ca2c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "263,267", "startColumns": "4,4", "startOffsets": "17314,17491", "endColumns": "53,66", "endOffsets": "17363,17553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2cf858d13e6a5e1f5b119836a8b04239\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "270,294", "startColumns": "4,4", "startOffsets": "17654,18898", "endColumns": "41,59", "endOffsets": "17691,18953"}}, {"source": "C:\\Users\\<USER>\\STUDIO\\SimpleNOTE\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "28,71,72,73,84,85,88", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1892,4779,4826,4873,5617,5662,5828", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1929,4821,4868,4915,5657,5702,5865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8734d346b2e96ba5091fe03752072eab\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "5,22,23,37,38,61,62,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,264,265,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,305,336,337,338,339,340,341,342,422,1818,1819,1823,1824,1828,1999,2000,2653,2687,2743,2778,2808,2841", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1413,1485,2575,2640,4059,4128,11319,11389,11457,11529,11599,11660,11734,12591,12652,12713,12775,12839,12901,12962,13030,13130,13190,13256,13329,13398,13455,13507,14536,14608,14684,14749,14808,14867,14927,14987,15047,15107,15167,15227,15287,15347,15407,15467,15526,15586,15646,15706,15766,15826,15886,15946,16006,16066,16126,16185,16245,16305,16364,16423,16482,16541,16600,17368,17403,17807,17862,17925,17980,18038,18094,18152,18213,18276,18333,18384,18442,18492,18553,18610,18676,18710,18745,19563,21676,21743,21815,21884,21953,22027,22099,27643,120168,120285,120486,120596,120797,133816,133888,155388,156961,159191,160997,161997,162679", "endLines": "5,22,23,37,38,61,62,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,264,265,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,305,336,337,338,339,340,341,342,422,1818,1822,1823,1827,1828,1999,2000,2658,2696,2777,2798,2840,2846", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1480,1568,2635,2701,4123,4186,11384,11452,11524,11594,11655,11729,11802,12647,12708,12770,12834,12896,12957,13025,13125,13185,13251,13324,13393,13450,13502,13564,14603,14679,14744,14803,14862,14922,14982,15042,15102,15162,15222,15282,15342,15402,15462,15521,15581,15641,15701,15761,15821,15881,15941,16001,16061,16121,16180,16240,16300,16359,16418,16477,16536,16595,16654,17398,17433,17857,17920,17975,18033,18089,18147,18208,18271,18328,18379,18437,18487,18548,18605,18671,18705,18740,18775,19628,21738,21810,21879,21948,22022,22094,22182,27709,120280,120481,120591,120792,120921,133883,133950,155586,157257,160992,161673,162674,162841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9191134d19d2f1df3d192d102235a2e1\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "259", "startColumns": "4", "startOffsets": "17108", "endColumns": "65", "endOffsets": "17169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\71a734cd1832c1a454590ad921e7204f\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "19012", "endColumns": "49", "endOffsets": "19057"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5d7b96133fd54175221e748800b0b16e\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3048,3061,3067,3073,3082", "startColumns": "4,4,4,4,4", "startOffsets": "169322,169961,170205,170452,170815", "endLines": "3060,3066,3072,3075,3086", "endColumns": "24,24,24,24,24", "endOffsets": "169956,170200,170447,170580,170992"}}, {"source": "C:\\Users\\<USER>\\STUDIO\\SimpleNOTE\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "47", "endOffsets": "59"}, "to": {"startLines": "334", "startColumns": "4", "startOffsets": "21582", "endColumns": "47", "endOffsets": "21625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7ae9817b57a187e60b0aad2683452b55\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "292", "startColumns": "4", "startOffsets": "18812", "endColumns": "42", "endOffsets": "18850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a52cd15d91b3b91eba876f6457455010\\transformed\\appcompat-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,8,13,14,15,16,17,18,19,20,21,24,25,26,27,29,30,31,32,33,34,35,36,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,74,75,76,77,78,79,80,81,82,83,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,208,209,210,211,212,213,214,215,251,252,253,254,261,268,269,272,291,299,300,301,302,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,416,428,429,430,431,432,433,441,442,446,450,454,459,465,472,476,480,485,489,493,497,501,505,509,515,519,525,529,535,539,544,548,551,555,561,565,571,575,581,584,588,592,596,600,604,605,606,607,610,613,616,619,623,624,625,626,627,630,632,634,636,641,642,646,652,656,657,659,671,672,676,682,686,690,691,695,722,726,727,731,759,931,957,1128,1154,1185,1193,1199,1215,1237,1242,1247,1257,1266,1275,1279,1286,1305,1312,1313,1322,1325,1328,1332,1336,1340,1343,1344,1349,1354,1364,1369,1376,1382,1383,1386,1390,1395,1397,1399,1402,1405,1407,1411,1414,1421,1424,1427,1431,1433,1437,1439,1441,1443,1447,1455,1463,1475,1481,1490,1493,1504,1507,1508,1513,1514,1547,1616,1686,1687,1697,1706,1707,1709,1713,1716,1719,1722,1725,1728,1731,1734,1738,1741,1744,1747,1751,1754,1758,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1784,1786,1787,1788,1789,1790,1791,1792,1793,1795,1796,1798,1799,1801,1803,1804,1806,1807,1808,1809,1810,1811,1813,1814,1815,1816,1817,1829,1831,1833,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1849,1850,1851,1852,1853,1854,1855,1857,1861,1892,1893,1894,1895,1896,1897,1901,1902,1903,1904,1906,1908,1910,1912,1914,1915,1916,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1937,1938,1939,1940,1942,1944,1945,1947,1948,1950,1952,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1967,1968,1969,1970,1972,1973,1974,1975,1976,1978,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,2001,2076,2079,2082,2085,2099,2112,2154,2157,2186,2213,2222,2286,2649,2659,2697,2725,2847,2871,2877,2883,2904,3028,3087,3093,3097,3103,3138,3170,3236,3256,3311,3323,3349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,478,795,850,912,976,1046,1107,1182,1258,1335,1573,1658,1740,1816,1934,2011,2089,2195,2301,2380,2460,2517,2706,2780,2855,2920,2986,3046,3107,3179,3252,3319,3387,3446,3505,3564,3623,3682,3736,3790,3843,3897,3951,4005,4191,4265,4344,4417,4491,4562,4634,4706,4920,4977,5035,5108,5182,5256,5331,5403,5476,5546,5707,5767,5870,5939,6008,6078,6152,6228,6292,6369,6445,6522,6587,6656,6733,6808,6877,6945,7022,7088,7149,7246,7311,7380,7479,7550,7609,7667,7724,7783,7847,7918,7990,8062,8134,8206,8273,8341,8409,8468,8531,8595,8685,8776,8836,8902,8969,9035,9105,9169,9222,9289,9350,9417,9530,9588,9651,9716,9781,9856,9929,10001,10045,10092,10138,10187,10248,10309,10370,10432,10496,10560,10624,10689,10752,10812,10873,10939,10998,11058,11120,11191,11251,11807,11893,11980,12070,12157,12245,12327,12410,12500,14083,14135,14193,14238,14304,14368,14425,14482,16659,16716,16764,16813,17225,17558,17605,17761,18780,19179,19243,19305,19365,19633,19707,19777,19855,19909,19979,20064,20112,20158,20219,20282,20348,20412,20483,20546,20611,20675,20736,20797,20849,20922,20996,21065,21140,21214,21288,21429,27362,27998,28076,28166,28254,28350,28440,29022,29111,29358,29639,29891,30176,30569,31046,31268,31490,31766,31993,32223,32453,32683,32913,33140,33559,33785,34210,34440,34868,35087,35370,35578,35709,35936,36362,36587,37014,37235,37660,37780,38056,38357,38681,38972,39286,39423,39554,39659,39901,40068,40272,40480,40751,40863,40975,41080,41197,41411,41557,41697,41783,42131,42219,42465,42883,43132,43214,43312,43969,44069,44321,44745,45000,45360,45449,45686,47710,47952,48054,48307,50463,61144,62660,73355,74883,76640,77266,77686,78947,80212,80468,80704,81251,81745,82350,82548,83128,84496,84871,84989,85527,85684,85880,86153,86409,86579,86720,86784,87149,87516,88192,88456,88794,89147,89241,89427,89733,89995,90120,90247,90486,90697,90816,91009,91186,91641,91822,91944,92203,92316,92503,92605,92712,92841,93116,93624,94120,94997,95291,95861,96010,96742,96914,96998,97334,97426,99282,104513,109884,109946,110524,111108,111199,111312,111541,111701,111853,112024,112190,112359,112526,112689,112932,113102,113275,113446,113720,113919,114124,114454,114538,114634,114730,114828,114928,115030,115132,115234,115336,115438,115538,115634,115746,115875,115998,116129,116260,116358,116472,116566,116706,116840,116936,117048,117148,117264,117360,117472,117572,117712,117848,118012,118142,118300,118450,118591,118735,118870,118982,119132,119260,119388,119524,119656,119786,119916,120028,120926,121072,121216,121354,121420,121510,121586,121690,121780,121882,121990,122098,122198,122278,122370,122468,122578,122630,122708,122814,122906,123010,123120,123242,123405,125157,125237,125337,125427,125537,125627,125868,125962,126068,126160,126260,126372,126486,126602,126718,126812,126926,127038,127140,127260,127382,127464,127568,127688,127814,127912,128006,128094,128206,128322,128444,128556,128731,128847,128933,129025,129137,129261,129328,129454,129522,129650,129794,129922,129991,130086,130201,130314,130413,130522,130633,130744,130845,130950,131050,131180,131271,131394,131488,131600,131686,131790,131886,131974,132092,132196,132300,132426,132514,132622,132722,132812,132922,133006,133108,133192,133246,133310,133416,133502,133612,133696,133955,136571,136689,136804,136884,137245,137782,139186,139264,140608,141969,142357,145200,155253,155591,157262,158619,162846,163597,163859,164059,164438,168716,170997,171226,171377,171592,172675,173525,176551,177295,179426,179766,181077", "endLines": "2,3,4,8,13,14,15,16,17,18,19,20,21,24,25,26,27,29,30,31,32,33,34,35,36,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,74,75,76,77,78,79,80,81,82,83,86,87,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,208,209,210,211,212,213,214,215,251,252,253,254,261,268,269,272,291,299,300,301,302,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,416,428,429,430,431,432,440,441,445,449,453,458,464,471,475,479,484,488,492,496,500,504,508,514,518,524,528,534,538,543,547,550,554,560,564,570,574,580,583,587,591,595,599,603,604,605,606,609,612,615,618,622,623,624,625,626,629,631,633,635,640,641,645,651,655,656,658,670,671,675,681,685,686,690,694,721,725,726,730,758,930,956,1127,1153,1184,1192,1198,1214,1236,1241,1246,1256,1265,1274,1278,1285,1304,1311,1312,1321,1324,1327,1331,1335,1339,1342,1343,1348,1353,1363,1368,1375,1381,1382,1385,1389,1394,1396,1398,1401,1404,1406,1410,1413,1420,1423,1426,1430,1432,1436,1438,1440,1442,1446,1454,1462,1474,1480,1489,1492,1503,1506,1507,1512,1513,1518,1615,1685,1686,1696,1705,1706,1708,1712,1715,1718,1721,1724,1727,1730,1733,1737,1740,1743,1746,1750,1753,1757,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1783,1785,1786,1787,1788,1789,1790,1791,1792,1794,1795,1797,1798,1800,1802,1803,1805,1806,1807,1808,1809,1810,1812,1813,1814,1815,1816,1817,1830,1832,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1848,1849,1850,1851,1852,1853,1854,1856,1860,1864,1892,1893,1894,1895,1896,1900,1901,1902,1903,1905,1907,1909,1911,1913,1914,1915,1916,1918,1920,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1936,1937,1938,1939,1941,1943,1944,1946,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1966,1967,1968,1969,1971,1972,1973,1974,1975,1977,1979,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,2075,2078,2081,2084,2098,2104,2121,2156,2185,2212,2221,2285,2648,2652,2686,2724,2742,2870,2876,2882,2903,3027,3047,3092,3096,3102,3137,3149,3235,3255,3310,3322,3348,3355", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,514,845,907,971,1041,1102,1177,1253,1330,1408,1653,1735,1811,1887,2006,2084,2190,2296,2375,2455,2512,2570,2775,2850,2915,2981,3041,3102,3174,3247,3314,3382,3441,3500,3559,3618,3677,3731,3785,3838,3892,3946,4000,4054,4260,4339,4412,4486,4557,4629,4701,4774,4972,5030,5103,5177,5251,5326,5398,5471,5541,5612,5762,5823,5934,6003,6073,6147,6223,6287,6364,6440,6517,6582,6651,6728,6803,6872,6940,7017,7083,7144,7241,7306,7375,7474,7545,7604,7662,7719,7778,7842,7913,7985,8057,8129,8201,8268,8336,8404,8463,8526,8590,8680,8771,8831,8897,8964,9030,9100,9164,9217,9284,9345,9412,9525,9583,9646,9711,9776,9851,9924,9996,10040,10087,10133,10182,10243,10304,10365,10427,10491,10555,10619,10684,10747,10807,10868,10934,10993,11053,11115,11186,11246,11314,11888,11975,12065,12152,12240,12322,12405,12495,12586,14130,14188,14233,14299,14363,14420,14477,14531,16711,16759,16808,16859,17254,17600,17649,17802,18807,19238,19300,19360,19417,19702,19772,19850,19904,19974,20059,20107,20153,20214,20277,20343,20407,20478,20541,20606,20670,20731,20792,20844,20917,20991,21060,21135,21209,21283,21424,21494,27410,28071,28161,28249,28345,28435,29017,29106,29353,29634,29886,30171,30564,31041,31263,31485,31761,31988,32218,32448,32678,32908,33135,33554,33780,34205,34435,34863,35082,35365,35573,35704,35931,36357,36582,37009,37230,37655,37775,38051,38352,38676,38967,39281,39418,39549,39654,39896,40063,40267,40475,40746,40858,40970,41075,41192,41406,41552,41692,41778,42126,42214,42460,42878,43127,43209,43307,43964,44064,44316,44740,44995,45089,45444,45681,47705,47947,48049,48302,50458,61139,62655,73350,74878,76635,77261,77681,78942,80207,80463,80699,81246,81740,82345,82543,83123,84491,84866,84984,85522,85679,85875,86148,86404,86574,86715,86779,87144,87511,88187,88451,88789,89142,89236,89422,89728,89990,90115,90242,90481,90692,90811,91004,91181,91636,91817,91939,92198,92311,92498,92600,92707,92836,93111,93619,94115,94992,95286,95856,96005,96737,96909,96993,97329,97421,97699,104508,109879,109941,110519,111103,111194,111307,111536,111696,111848,112019,112185,112354,112521,112684,112927,113097,113270,113441,113715,113914,114119,114449,114533,114629,114725,114823,114923,115025,115127,115229,115331,115433,115533,115629,115741,115870,115993,116124,116255,116353,116467,116561,116701,116835,116931,117043,117143,117259,117355,117467,117567,117707,117843,118007,118137,118295,118445,118586,118730,118865,118977,119127,119255,119383,119519,119651,119781,119911,120023,120163,121067,121211,121349,121415,121505,121581,121685,121775,121877,121985,122093,122193,122273,122365,122463,122573,122625,122703,122809,122901,123005,123115,123237,123400,123557,125232,125332,125422,125532,125622,125863,125957,126063,126155,126255,126367,126481,126597,126713,126807,126921,127033,127135,127255,127377,127459,127563,127683,127809,127907,128001,128089,128201,128317,128439,128551,128726,128842,128928,129020,129132,129256,129323,129449,129517,129645,129789,129917,129986,130081,130196,130309,130408,130517,130628,130739,130840,130945,131045,131175,131266,131389,131483,131595,131681,131785,131881,131969,132087,132191,132295,132421,132509,132617,132717,132807,132917,133001,133103,133187,133241,133305,133411,133497,133607,133691,133811,136566,136684,136799,136879,137240,137473,138294,139259,140603,141964,142352,145195,155248,155383,156956,158614,159186,163592,163854,164054,164433,168711,169317,171221,171372,171587,172670,172982,176546,177290,179421,179761,181072,181275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\759f44b938018b86b4c9fb3e5df6883b\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "266,2105,3076,3079", "startColumns": "4,4,4,4", "startOffsets": "17438,137478,170585,170700", "endLines": "266,2111,3078,3081", "endColumns": "52,24,24,24", "endOffsets": "17486,137777,170695,170810"}}]}]}