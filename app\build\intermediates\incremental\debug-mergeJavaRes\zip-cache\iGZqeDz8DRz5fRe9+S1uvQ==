[{"key": "arrow/core/ArrowCoreInternalException.class", "name": "arrow/core/ArrowCoreInternalException.class", "size": 1192, "crc": 233068376}, {"key": "arrow/core/AtomicMemoizationCache.class", "name": "arrow/core/AtomicMemoizationCache.class", "size": 7222, "crc": 1027858261}, {"key": "arrow/core/ComparisonKt.class", "name": "arrow/core/ComparisonKt.class", "size": 8640, "crc": 1365198037}, {"key": "arrow/core/ConcurrentMapMemoizationCache.class", "name": "arrow/core/ConcurrentMapMemoizationCache.class", "size": 5004, "crc": 968106863}, {"key": "arrow/core/Either$Companion.class", "name": "arrow/core/Either$Companion.class", "size": 95327, "crc": -655906264}, {"key": "arrow/core/Either$Left$Companion.class", "name": "arrow/core/Either$Left$Companion.class", "size": 815, "crc": 1905556802}, {"key": "arrow/core/Either$Left.class", "name": "arrow/core/Either$Left.class", "size": 2660, "crc": -487947156}, {"key": "arrow/core/Either$Right$Companion.class", "name": "arrow/core/Either$Right$Companion.class", "size": 1239, "crc": -1662432163}, {"key": "arrow/core/Either$Right.class", "name": "arrow/core/Either$Right.class", "size": 2875, "crc": 1763131136}, {"key": "arrow/core/Either.class", "name": "arrow/core/Either.class", "size": 9937, "crc": -1574676753}, {"key": "arrow/core/EitherKt.class", "name": "arrow/core/EitherKt.class", "size": 5073, "crc": 932561967}, {"key": "arrow/core/EitherKt__EitherKt.class", "name": "arrow/core/EitherKt__EitherKt.class", "size": 14205, "crc": 97651595}, {"key": "arrow/core/EmptyValue.class", "name": "arrow/core/EmptyValue.class", "size": 3644, "crc": -1456635100}, {"key": "arrow/core/Entry.class", "name": "arrow/core/Entry.class", "size": 2586, "crc": 1644137051}, {"key": "arrow/core/Ior$Both.class", "name": "arrow/core/Ior$Both.class", "size": 2978, "crc": -179517101}, {"key": "arrow/core/Ior$Companion.class", "name": "arrow/core/Ior$Companion.class", "size": 2869, "crc": 1750878640}, {"key": "arrow/core/Ior$Left$Companion.class", "name": "arrow/core/Ior$Left$Companion.class", "size": 800, "crc": -203387611}, {"key": "arrow/core/Ior$Left.class", "name": "arrow/core/Ior$Left.class", "size": 2621, "crc": -332261765}, {"key": "arrow/core/Ior$Right$Companion.class", "name": "arrow/core/Ior$Right$Companion.class", "size": 804, "crc": 128619526}, {"key": "arrow/core/Ior$Right.class", "name": "arrow/core/Ior$Right.class", "size": 2631, "crc": -229037089}, {"key": "arrow/core/Ior.class", "name": "arrow/core/Ior.class", "size": 11324, "crc": -712976873}, {"key": "arrow/core/IorKt.class", "name": "arrow/core/IorKt.class", "size": 11630, "crc": 807060483}, {"key": "arrow/core/IterableKt.class", "name": "arrow/core/IterableKt.class", "size": 92822, "crc": -1136162503}, {"key": "arrow/core/MapKt.class", "name": "arrow/core/MapKt.class", "size": 71540, "crc": -1104345292}, {"key": "arrow/core/MemoizationCache.class", "name": "arrow/core/MemoizationCache.class", "size": 829, "crc": -1536844929}, {"key": "arrow/core/MemoizedDeepRecursiveFunctionKt$MemoizedDeepRecursiveFunction$1.class", "name": "arrow/core/MemoizedDeepRecursiveFunctionKt$MemoizedDeepRecursiveFunction$1.class", "size": 3932, "crc": 1160669812}, {"key": "arrow/core/MemoizedDeepRecursiveFunctionKt.class", "name": "arrow/core/MemoizedDeepRecursiveFunctionKt.class", "size": 2977, "crc": -52707000}, {"key": "arrow/core/NonEmptyCollection$DefaultImpls.class", "name": "arrow/core/NonEmptyCollection$DefaultImpls.class", "size": 8700, "crc": 1835860984}, {"key": "arrow/core/NonEmptyCollection.class", "name": "arrow/core/NonEmptyCollection.class", "size": 3562, "crc": -1366901505}, {"key": "arrow/core/NonEmptyList$Companion.class", "name": "arrow/core/NonEmptyList$Companion.class", "size": 1281, "crc": 602827740}, {"key": "arrow/core/NonEmptyList.class", "name": "arrow/core/NonEmptyList.class", "size": 48778, "crc": 1262062129}, {"key": "arrow/core/NonEmptyListKt$toNonEmptyListOrNull$$inlined$Iterable$1.class", "name": "arrow/core/NonEmptyListKt$toNonEmptyListOrNull$$inlined$Iterable$1.class", "size": 1937, "crc": -1961656010}, {"key": "arrow/core/NonEmptyListKt$toNonEmptyListOrThrow$$inlined$Iterable$1.class", "name": "arrow/core/NonEmptyListKt$toNonEmptyListOrThrow$$inlined$Iterable$1.class", "size": 1941, "crc": -964364841}, {"key": "arrow/core/NonEmptyListKt.class", "name": "arrow/core/NonEmptyListKt.class", "size": 30632, "crc": 180679921}, {"key": "arrow/core/NonEmptySet.class", "name": "arrow/core/NonEmptySet.class", "size": 16628, "crc": -1145466624}, {"key": "arrow/core/NonEmptySetKt$toNonEmptySetOrNull$$inlined$Iterable$1.class", "name": "arrow/core/NonEmptySetKt$toNonEmptySetOrNull$$inlined$Iterable$1.class", "size": 1922, "crc": -756896582}, {"key": "arrow/core/NonEmptySetKt$toNonEmptySetOrThrow$$inlined$Iterable$1.class", "name": "arrow/core/NonEmptySetKt$toNonEmptySetOrThrow$$inlined$Iterable$1.class", "size": 1926, "crc": 1486266950}, {"key": "arrow/core/NonEmptySetKt.class", "name": "arrow/core/NonEmptySetKt.class", "size": 23141, "crc": -2119581124}, {"key": "arrow/core/NonFatalKt.class", "name": "arrow/core/NonFatalKt.class", "size": 551, "crc": -769899669}, {"key": "arrow/core/NonFatalKt__NonFatalKt.class", "name": "arrow/core/NonFatalKt__NonFatalKt.class", "size": 1044, "crc": 433787377}, {"key": "arrow/core/NonFatalOrThrowKt.class", "name": "arrow/core/NonFatalOrThrowKt.class", "size": 944, "crc": 1350419193}, {"key": "arrow/core/None.class", "name": "arrow/core/None.class", "size": 885, "crc": -1613794110}, {"key": "arrow/core/Option$Companion.class", "name": "arrow/core/Option$Companion.class", "size": 6457, "crc": 769305746}, {"key": "arrow/core/Option.class", "name": "arrow/core/Option.class", "size": 10228, "crc": 2074630220}, {"key": "arrow/core/OptionKt$toOption$2.class", "name": "arrow/core/OptionKt$toOption$2.class", "size": 1492, "crc": -1418913066}, {"key": "arrow/core/OptionKt.class", "name": "arrow/core/OptionKt.class", "size": 14260, "crc": 951541660}, {"key": "arrow/core/PotentiallyUnsafeNonEmptyOperation.class", "name": "arrow/core/PotentiallyUnsafeNonEmptyOperation.class", "size": 906, "crc": 2074529785}, {"key": "arrow/core/PredefKt.class", "name": "arrow/core/PredefKt.class", "size": 594, "crc": -1139149111}, {"key": "arrow/core/ResultKt.class", "name": "arrow/core/ResultKt.class", "size": 2746, "crc": 625993426}, {"key": "arrow/core/SequenceKt$alignRec$1.class", "name": "arrow/core/SequenceKt$alignRec$1.class", "size": 4682, "crc": -1820145319}, {"key": "arrow/core/SequenceKt$filterOption$1.class", "name": "arrow/core/SequenceKt$filterOption$1.class", "size": 5340, "crc": 1561767669}, {"key": "arrow/core/SequenceKt$flatten$1.class", "name": "arrow/core/SequenceKt$flatten$1.class", "size": 2148, "crc": -1029082015}, {"key": "arrow/core/SequenceKt$interleave$1.class", "name": "arrow/core/SequenceKt$interleave$1.class", "size": 4464, "crc": 1629764228}, {"key": "arrow/core/SequenceKt$leftPadZip$2.class", "name": "arrow/core/SequenceKt$leftPadZip$2.class", "size": 1837, "crc": 1824428861}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$1.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$1.class", "size": 2204, "crc": 2100529281}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$2.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$2.class", "size": 2309, "crc": 1245746447}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$3.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$3.class", "size": 2404, "crc": -914945311}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$4.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$4.class", "size": 2501, "crc": -225237394}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$5.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$5.class", "size": 2598, "crc": 1392924620}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$6.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$6.class", "size": 2693, "crc": 701022423}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$7.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$7.class", "size": 2788, "crc": -1357581325}, {"key": "arrow/core/SequenceKt$zip$$inlined$Sequence$8.class", "name": "arrow/core/SequenceKt$zip$$inlined$Sequence$8.class", "size": 2886, "crc": -894955802}, {"key": "arrow/core/SequenceKt$zip$1$1.class", "name": "arrow/core/SequenceKt$zip$1$1.class", "size": 2896, "crc": -2072439475}, {"key": "arrow/core/SequenceKt$zip$2$1.class", "name": "arrow/core/SequenceKt$zip$2$1.class", "size": 3272, "crc": 152610401}, {"key": "arrow/core/SequenceKt$zip$3$1.class", "name": "arrow/core/SequenceKt$zip$3$1.class", "size": 3650, "crc": -706789877}, {"key": "arrow/core/SequenceKt$zip$4$1.class", "name": "arrow/core/SequenceKt$zip$4$1.class", "size": 4026, "crc": -951136349}, {"key": "arrow/core/SequenceKt$zip$5$1.class", "name": "arrow/core/SequenceKt$zip$5$1.class", "size": 4438, "crc": -2144053110}, {"key": "arrow/core/SequenceKt$zip$6$1.class", "name": "arrow/core/SequenceKt$zip$6$1.class", "size": 4818, "crc": -714228820}, {"key": "arrow/core/SequenceKt$zip$7$1.class", "name": "arrow/core/SequenceKt$zip$7$1.class", "size": 5198, "crc": -1037710221}, {"key": "arrow/core/SequenceKt$zip$8$1.class", "name": "arrow/core/SequenceKt$zip$8$1.class", "size": 5586, "crc": 953670285}, {"key": "arrow/core/SequenceKt.class", "name": "arrow/core/SequenceKt.class", "size": 53259, "crc": -920544687}, {"key": "arrow/core/Some$Companion.class", "name": "arrow/core/Some$Companion.class", "size": 756, "crc": -917951777}, {"key": "arrow/core/Some.class", "name": "arrow/core/Some.class", "size": 2587, "crc": 1956121378}, {"key": "arrow/core/SortedMapKKt.class", "name": "arrow/core/SortedMapKKt.class", "size": 2864, "crc": 1579008122}, {"key": "arrow/core/StringKt.class", "name": "arrow/core/StringKt.class", "size": 1070, "crc": -1181698088}, {"key": "arrow/core/Tuple4$Companion.class", "name": "arrow/core/Tuple4$Companion.class", "size": 762, "crc": 1672136459}, {"key": "arrow/core/Tuple4.class", "name": "arrow/core/Tuple4.class", "size": 4023, "crc": -1019408701}, {"key": "arrow/core/Tuple5$Companion.class", "name": "arrow/core/Tuple5$Companion.class", "size": 762, "crc": 1554144340}, {"key": "arrow/core/Tuple5.class", "name": "arrow/core/Tuple5.class", "size": 4509, "crc": 565294286}, {"key": "arrow/core/Tuple6$Companion.class", "name": "arrow/core/Tuple6$Companion.class", "size": 762, "crc": 637007816}, {"key": "arrow/core/Tuple6.class", "name": "arrow/core/Tuple6.class", "size": 4995, "crc": 203947561}, {"key": "arrow/core/Tuple7$Companion.class", "name": "arrow/core/Tuple7$Companion.class", "size": 762, "crc": -345903991}, {"key": "arrow/core/Tuple7.class", "name": "arrow/core/Tuple7.class", "size": 5485, "crc": -1157252793}, {"key": "arrow/core/Tuple8$Companion.class", "name": "arrow/core/Tuple8$Companion.class", "size": 762, "crc": 1569550138}, {"key": "arrow/core/Tuple8.class", "name": "arrow/core/Tuple8.class", "size": 5976, "crc": 1798743536}, {"key": "arrow/core/Tuple9$Companion.class", "name": "arrow/core/Tuple9$Companion.class", "size": 762, "crc": 1523184152}, {"key": "arrow/core/Tuple9.class", "name": "arrow/core/Tuple9.class", "size": 6459, "crc": -1316392259}, {"key": "arrow/core/TupleNKt.class", "name": "arrow/core/TupleNKt.class", "size": 7076, "crc": 134201419}, {"key": "arrow/core/TupleNKt__PairKt.class", "name": "arrow/core/TupleNKt__PairKt.class", "size": 1274, "crc": -1473078518}, {"key": "arrow/core/TupleNKt__TripleKt.class", "name": "arrow/core/TupleNKt__TripleKt.class", "size": 1446, "crc": 716552424}, {"key": "arrow/core/TupleNKt__Tuple4Kt.class", "name": "arrow/core/TupleNKt__Tuple4Kt.class", "size": 1630, "crc": 808210301}, {"key": "arrow/core/TupleNKt__Tuple5Kt.class", "name": "arrow/core/TupleNKt__Tuple5Kt.class", "size": 1798, "crc": -707683848}, {"key": "arrow/core/TupleNKt__Tuple6Kt.class", "name": "arrow/core/TupleNKt__Tuple6Kt.class", "size": 1955, "crc": 625797718}, {"key": "arrow/core/TupleNKt__Tuple7Kt.class", "name": "arrow/core/TupleNKt__Tuple7Kt.class", "size": 2114, "crc": 198585643}, {"key": "arrow/core/TupleNKt__Tuple8Kt.class", "name": "arrow/core/TupleNKt__Tuple8Kt.class", "size": 2274, "crc": -989615918}, {"key": "arrow/core/TupleNKt__Tuple9Kt.class", "name": "arrow/core/TupleNKt__Tuple9Kt.class", "size": 2428, "crc": -2127856772}, {"key": "arrow/core/TupleNKt__TupleNKt.class", "name": "arrow/core/TupleNKt__TupleNKt.class", "size": 6484, "crc": 1015070518}, {"key": "arrow/core/UtilsKt.class", "name": "arrow/core/UtilsKt.class", "size": 3303, "crc": 2051473100}, {"key": "arrow/core/raise/DefaultRaise.class", "name": "arrow/core/raise/DefaultRaise.class", "size": 5280, "crc": -1933920499}, {"key": "arrow/core/raise/DelicateRaiseApi.class", "name": "arrow/core/raise/DelicateRaiseApi.class", "size": 1228, "crc": -1153248938}, {"key": "arrow/core/raise/ExperimentalRaiseAccumulateApi.class", "name": "arrow/core/raise/ExperimentalRaiseAccumulateApi.class", "size": 1132, "crc": 1584260616}, {"key": "arrow/core/raise/ExperimentalTraceApi.class", "name": "arrow/core/raise/ExperimentalTraceApi.class", "size": 831, "crc": -1657498274}, {"key": "arrow/core/raise/IorRaise.class", "name": "arrow/core/raise/IorRaise.class", "size": 17810, "crc": -1184127306}, {"key": "arrow/core/raise/NoTrace.class", "name": "arrow/core/raise/NoTrace.class", "size": 1520, "crc": 846401157}, {"key": "arrow/core/raise/Raise$DefaultImpls.class", "name": "arrow/core/raise/Raise$DefaultImpls.class", "size": 8443, "crc": 2018878211}, {"key": "arrow/core/raise/Raise.class", "name": "arrow/core/raise/Raise.class", "size": 3374, "crc": -1148763520}, {"key": "arrow/core/raise/RaiseAccumulate$Error.class", "name": "arrow/core/raise/RaiseAccumulate$Error.class", "size": 1349, "crc": 132958285}, {"key": "arrow/core/raise/RaiseAccumulate$Ok.class", "name": "arrow/core/raise/RaiseAccumulate$Ok.class", "size": 1177, "crc": -992883641}, {"key": "arrow/core/raise/RaiseAccumulate$Value.class", "name": "arrow/core/raise/RaiseAccumulate$Value.class", "size": 742, "crc": -1158935654}, {"key": "arrow/core/raise/RaiseAccumulate.class", "name": "arrow/core/raise/RaiseAccumulate.class", "size": 64117, "crc": -734601864}, {"key": "arrow/core/raise/RaiseCancellationException.class", "name": "arrow/core/raise/RaiseCancellationException.class", "size": 2303, "crc": 1350527403}, {"key": "arrow/core/raise/RaiseDSL.class", "name": "arrow/core/raise/RaiseDSL.class", "size": 555, "crc": 894615467}, {"key": "arrow/core/raise/RaiseKt.class", "name": "arrow/core/raise/RaiseKt.class", "size": 55900, "crc": -1470116350}, {"key": "arrow/core/raise/RaiseKt__BuildersKt$iorNel$1.class", "name": "arrow/core/raise/RaiseKt__BuildersKt$iorNel$1.class", "size": 1713, "crc": -1988793557}, {"key": "arrow/core/raise/RaiseKt__BuildersKt.class", "name": "arrow/core/raise/RaiseKt__BuildersKt.class", "size": 18199, "crc": 534068702}, {"key": "arrow/core/raise/RaiseKt__EffectKt$merge$1.class", "name": "arrow/core/raise/RaiseKt__EffectKt$merge$1.class", "size": 1582, "crc": -823952477}, {"key": "arrow/core/raise/RaiseKt__EffectKt.class", "name": "arrow/core/raise/RaiseKt__EffectKt.class", "size": 8213, "crc": 504357408}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$1.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$1.class", "size": 5569, "crc": -2099451346}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$2.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$2.class", "size": 6654, "crc": 1205928324}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$3.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$3.class", "size": 5148, "crc": 1289938160}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$5.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt$catch$5.class", "size": 3651, "crc": 119668796}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt$getOrElse$1.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt$getOrElse$1.class", "size": 1697, "crc": 1761664427}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt$mapError$1.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt$mapError$1.class", "size": 6970, "crc": -932556875}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt$recover$1.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt$recover$1.class", "size": 6927, "crc": -1353600933}, {"key": "arrow/core/raise/RaiseKt__ErrorHandlersKt.class", "name": "arrow/core/raise/RaiseKt__ErrorHandlersKt.class", "size": 19704, "crc": 1626689287}, {"key": "arrow/core/raise/RaiseKt__FoldKt$fold$1.class", "name": "arrow/core/raise/RaiseKt__FoldKt$fold$1.class", "size": 1843, "crc": 576913064}, {"key": "arrow/core/raise/RaiseKt__FoldKt$fold$7.class", "name": "arrow/core/raise/RaiseKt__FoldKt$fold$7.class", "size": 2881, "crc": -1712977524}, {"key": "arrow/core/raise/RaiseKt__FoldKt.class", "name": "arrow/core/raise/RaiseKt__FoldKt.class", "size": 15826, "crc": -1253662000}, {"key": "arrow/core/raise/RaiseKt__MappersKt$getOrNull$1.class", "name": "arrow/core/raise/RaiseKt__MappersKt$getOrNull$1.class", "size": 1628, "crc": 209097064}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toEither$1.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toEither$1.class", "size": 1624, "crc": 816318782}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toIor$2.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toIor$2.class", "size": 2933, "crc": 980926362}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toIor$3.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toIor$3.class", "size": 2927, "crc": -988486370}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toOption$2.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toOption$2.class", "size": 2903, "crc": 1886571666}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toResult$1.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toResult$1.class", "size": 1861, "crc": 1057227227}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toResult$2.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toResult$2.class", "size": 3238, "crc": 1829696867}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toResult$3.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toResult$3.class", "size": 3327, "crc": -481621833}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toResult$4.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toResult$4.class", "size": 3061, "crc": 253358598}, {"key": "arrow/core/raise/RaiseKt__MappersKt$toResult$8.class", "name": "arrow/core/raise/RaiseKt__MappersKt$toResult$8.class", "size": 1841, "crc": 1698693588}, {"key": "arrow/core/raise/RaiseKt__MappersKt.class", "name": "arrow/core/raise/RaiseKt__MappersKt.class", "size": 20062, "crc": -623533603}, {"key": "arrow/core/raise/RaiseKt__RaiseAccumulateKt$accumulate$1.class", "name": "arrow/core/raise/RaiseKt__RaiseAccumulateKt$accumulate$1.class", "size": 2990, "crc": -1425367622}, {"key": "arrow/core/raise/RaiseKt__RaiseAccumulateKt.class", "name": "arrow/core/raise/RaiseKt__RaiseAccumulateKt.class", "size": 309263, "crc": 125509024}, {"key": "arrow/core/raise/RaiseKt__RaiseKt.class", "name": "arrow/core/raise/RaiseKt__RaiseKt.class", "size": 11251, "crc": -1656224425}, {"key": "arrow/core/raise/RaiseLeakedException.class", "name": "arrow/core/raise/RaiseLeakedException.class", "size": 898, "crc": 438411025}, {"key": "arrow/core/raise/ResultRaise.class", "name": "arrow/core/raise/ResultRaise.class", "size": 13874, "crc": 1093899018}, {"key": "arrow/core/raise/SingletonRaise.class", "name": "arrow/core/raise/SingletonRaise.class", "size": 16926, "crc": 65184805}, {"key": "arrow/core/raise/Trace.class", "name": "arrow/core/raise/Trace.class", "size": 4146, "crc": -1582174856}, {"key": "arrow/core/raise/Traced.class", "name": "arrow/core/raise/Traced.class", "size": 1878, "crc": 1058543484}, {"key": "META-INF/arrow-core_release.kotlin_module", "name": "META-INF/arrow-core_release.kotlin_module", "size": 692, "crc": 706124049}]