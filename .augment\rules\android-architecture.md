---
type: "always_apply"
---

# Android Architecture Rules

# Senior Android Developer Context

You are assisting a Senior Android Developer working on large-scale Android applications. Always follow these core principles:

## Architecture & Design
- Use **Clean Architecture** with **MVVM** pattern
- Follow **Now in Android** modular project structure  
- Apply **SOLID**, **DRY**, **KISS** principles
- Implement **error handling with Arrow-kt** (Either, Validated)
- Use **Repository pattern** with clear data flow

## Technology Stack
- **UI**: Jetpack Compose + Material3
- **Language**: 100% Kotlin with modern features
- **DI**: Koin for dependency injection
- **Database**: Room with proper migrations
- **Async**: Coroutines + Flow for reactive programming
- **Navigation**: Type-safe Compose Navigation
- **Testing**: JUnit5, Turbine, Kotest, MockK

## Code Quality Standards
- Write **comprehensive unit tests** (90%+ domain coverage)
- Use **immutable data classes** and sealed classes
- Follow **Kotlin coding conventions**
- Implement **proper logging** and error tracking
- Use **functional programming** patterns where appropriate
- Use **modifier parameter** Modifier parameter should be the first optional parameter in @Composable functions

## Performance & Security
- Optimize for **large codebases** and **scalability**
- Implement **secure data storage** and network practices
- Use **lazy loading**, **pagination**, and **caching**
- Apply **ProGuard/R8** obfuscation rules

When generating code, always consider maintainability, testability, and team collaboration.

***

## 🏗️ Technical Architecture

### Tech Stack

- **UI**: Jetpack Compose + Material3 UI Components
- **Language**: 100% Kotlin
- **Database**: Room (SQLite)
- **Dependency Injection**: Koin
- **Async/Reactive**: Kotlin Coroutines, Flow
- **Navigation**: Compose Navigation (type-safe)
- **Architecture**: Clean Architecture, MVVM, Multi-module, SOLID, DRY, KISS
- **Error Handling**: Arrow-kt for functional error handling
- **Other**: kotlinx-immutable, kotlinx-datetime, kotlinx-serialization
- **Widgets**: Jetpack Datastore Preferences, Jetpack Glance App widget, glance-material3
- **Code Quality**: Detekt, Ktlint, Spotless
- **Testing**: JUnit5, Turbine, Kotest, MockK

***

### Modular Architecture (Now in Android Style)

```
┌─────────────────────────────────────┐
│           App Module                │
│  ┌─────────────────────────────────┐ │
│  │     MainActivity                │ │
│  │     Application Class           │ │
│  │     App-level DI                │ │
│  └─────────────────────────────────┘ │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│         Feature Modules             │
│  ┌─────────────────────────────────┐ │
│  │   feature-chat                  │ │
│  │   feature-settings              │ │
│  │   feature-profile               │ │
│  │   feature-search                │ │
│  └─────────────────────────────────┘ │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│           Core Modules              │
│  ┌─────────────────────────────────┐ │
│  │   core-ui, core-domain          │ │
│  │   core-data, core-database      │ │
│  │   core-network, core-model      │ │
│  │   core-common, core-datastore   │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```


***

## 📁 Project Structure

```
├── app/
│   ├── src/
│   │   ├── androidTest/
│   │   ├── main/
│   │   │   ├── java/com/yourapp/augmentchat/
│   │   │   │   ├── MainActivity.kt
│   │   │   │   ├── AugmentChatApplication.kt
│   │   │   │   ├── di/
│   │   │   │   └── navigation/
│   │   │   ├── res/
│   │   │   └── AndroidManifest.xml
│   │   └── test/
│   ├── build.gradle.kts
│   ├── proguard-rules.pro
│   └── srcDebugAndroidTest/
├── core/
│   ├── core-analytics/          # Analytics and tracking
│   ├── core-common/             # Common utilities, extensions
│   ├── core-data/               # Repository implementations
│   ├── core-datastore/          # DataStore preferences
│   ├── core-database/           # Room database
│   ├── core-model/              # Domain models
│   ├── core-network/            # Network layer (Retrofit, OkHttp)
│   ├── core-notifications/      # Push notifications
│   ├── core-domain/             # Use cases, business logic
│   ├── core-ui/                 # Common UI components
│   └── core-testing/            # Test utilities
├── feature/
│   ├── feature-chat/            # Chat functionality
│   ├── feature-settings/        # App settings
│   ├── feature-profile/         # User profile
│   ├── feature-search/          # Search functionality
│   ├── feature-onboarding/      # User onboarding
│   └── feature-auth/            # Authentication
├── build.gradle.kts
├── settings.gradle.kts
├── gradle.properties
├── gradlew
├── gradlew.bat
└── README.md
```


***

## 🔧 Implementation Guidelines

### 1. Modular Architecture \& Clean Code

- **Strict module separation**: Each feature module is independent
- **Core modules**: Shared across features, well-defined APIs
- **Feature modules**: Self-contained, can be developed independently
- **Dependency direction**: App → Feature → Core
- **No circular dependencies**: Use dependency inversion principle


### 2. Dependency Injection (Koin)

- **Module-specific DI**: Each module defines its own Koin module
- **Feature modules**: Provide ViewModels and use cases
- **Core modules**: Provide repositories, data sources, network clients
- Use `by viewModel()` and `by inject()` for lifecycle-safe DI


### 3. Error Handling with Arrow-kt

```kotlin
// Use Either for error handling
suspend fun getChatMessages(): Either<ChatError, List<ChatMessage>> {
    return either {
        val response = chatRepository.getMessages().bind()
        response.map { it.toDomain() }
    }
}

// Use Validated for input validation
fun validateMessage(content: String): ValidatedNel<ValidationError, String> {
    return when {
        content.isBlank() -> "Message cannot be empty".invalidNel()
        content.length > 1000 -> "Message too long".invalidNel()
        else -> content.validNel()
    }
}
```


### 4. Asynchronous Programming

- Use **Coroutines** for all background work
- Use **Flow** for reactive streams (UI state, DB, network)
- Prefer suspend functions for data access
- Use **StateFlow** and **SharedFlow** appropriately


### 5. Navigation (Type-safe)

```kotlin
@Serializable
sealed class AppDestination {
    @Serializable
    data object Chat : AppDestination()
    
    @Serializable
    data class Profile(val userId: String) : AppDestination()
}
```


### 6. Database (Room) Best Practices

- **Migration strategy**: Always provide migration paths
- **Type converters**: For complex data types
- **Indices**: Add appropriate indices for queries
- **Transactions**: Use for multiple related operations
- Keep all DB operations off main thread


### 7. Network Layer

```kotlin
// Use sealed classes for API responses
sealed class ApiResult<T> {
    data class Success<T>(val data: T) : ApiResult<T>()
    data class Error<T>(val exception: Throwable) : ApiResult<T>()
    data class Loading<T> : ApiResult<T>()
}
```


### 8. Testing Strategy

- **Unit Tests**: Business logic in domain layer (95%+ coverage)
- **Integration Tests**: Repository and use case integration
- **UI Tests**: Key user journeys and interactions
- **Screenshot Tests**: Visual regression testing
- **Performance Tests**: Database queries and UI rendering

***


## Module Structure (Now in Android Style)
```

app/ → feature/ → core/
├── app/ (MainActivity, Application, Navigation)
├── feature/ (feature-chat, feature-settings, etc.)
└── core/ (core-ui, core-data, core-domain, etc.)

```

## Dependency Rules
- **App module**: Only depends on feature modules
- **Feature modules**: Only depend on core modules  
- **Core modules**: Can depend on other core modules
- **No circular dependencies** allowed

## Layer Responsibilities
- **Presentation**: Compose UI, ViewModels, Navigation
- **Domain**: Use Cases, Business Logic, Models
- **Data**: Repositories, Data Sources, Network/DB

## Error Handling with Arrow
```

// Use Either for operations that can fail
suspend fun getUser(): Either<UserError, User>

// Use Validated for input validation
fun validateEmail(email: String): ValidatedNel<ValidationError, Email>

```

## State Management
- Use **StateFlow** for UI state
- Use **SharedFlow** for events
- Implement **UiState** sealed classes
- Handle **loading**, **success**, **error** states