package io.github.simplenote.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import io.github.simplenote.presentation.screen.NoteEditorScreen
import io.github.simplenote.presentation.screen.NotesScreen
import io.github.simplenote.presentation.screen.SettingsScreen

/**
 * Main navigation component for the app.
 */
@Composable
fun AppNavigation(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = AppDestination.Notes,
        modifier = modifier
    ) {
        // Notes list screen
        composable<AppDestination.Notes> {
            NotesScreen(
                onNavigateToEditor = { noteId ->
                    navController.navigate(AppDestination.NoteEditor(noteId))
                },
                onNavigateToSettings = {
                    navController.navigate(AppDestination.Settings)
                }
            )
        }
        
        // Note editor screen
        composable<AppDestination.NoteEditor> { backStackEntry ->
            val destination = backStackEntry.arguments?.let {
                AppDestination.NoteEditor(
                    noteId = it.getLong("noteId").takeIf { id -> id > 0 }
                )
            } ?: AppDestination.NoteEditor()
            
            NoteEditorScreen(
                noteId = destination.noteId,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Settings screen
        composable<AppDestination.Settings> {
            SettingsScreen(
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }
    }
}
