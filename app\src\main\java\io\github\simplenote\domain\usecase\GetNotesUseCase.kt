package io.github.simplenote.domain.usecase

import arrow.core.Either
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.domain.model.NoteError
import io.github.simplenote.domain.repository.NoteRepository
import kotlinx.coroutines.flow.Flow

/**
 * Use case for retrieving notes with different sorting and filtering options.
 */
class GetNotesUseCase(
    private val repository: NoteRepository
) {
    
    /**
     * Get all notes ordered by last updated (default)
     */
    operator fun invoke(): Flow<Either<NoteError, List<Note>>> {
        return repository.getAllNotes()
    }
    
    /**
     * Get all notes with specific sorting
     */
    operator fun invoke(sortBy: SortBy): Flow<Either<NoteError, List<Note>>> {
        return when (sortBy) {
            SortBy.UPDATED_DATE -> repository.getAllNotes()
            SortBy.CREATED_DATE -> repository.getAllNotesByCreatedDate()
            SortBy.TITLE -> repository.getAllNotesByTitle()
        }
    }
    
    /**
     * Get notes filtered by color
     */
    fun getNotesByColor(color: NoteColor): Flow<Either<NoteError, List<Note>>> {
        return repository.getNotesByColor(color)
    }
    
    /**
     * Search notes by query
     */
    fun searchNotes(query: String): Flow<Either<NoteError, List<Note>>> {
        return if (query.isBlank()) {
            repository.getAllNotes()
        } else {
            repository.searchNotes(query.trim())
        }
    }
    
    /**
     * Get a specific note by ID
     */
    suspend fun getNoteById(id: Long): Either<NoteError, Note> {
        return if (id <= 0) {
            Either.Left(NoteError.ValidationError.InvalidColor)
        } else {
            repository.getNoteById(id)
        }
    }
    
    /**
     * Sorting options for notes
     */
    enum class SortBy {
        UPDATED_DATE,
        CREATED_DATE,
        TITLE
    }
}
