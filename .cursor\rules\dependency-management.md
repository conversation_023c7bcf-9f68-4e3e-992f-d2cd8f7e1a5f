---
type: "always_apply"
---

# Dependency Management Rules

## Version Catalog (libs.versions.toml)
Always use version catalog for dependency management:

## 📦 Dependencies (libs.versions.toml)

```toml
[versions]
agp = "8.12.0"
kotlin = "2.2.0"
coreKtx = "1.16.0"
lifecycleRuntimeKtx = "2.9.2"
activityCompose = "1.10.1"
composeBom = "2025.07.00"

# KotlinX
kotlinxCoroutines = "1.10.2"
kotlinxSerializationJson = "1.9.0"
kotlinxCollectionsImmutable = "0.4.0"
kotlinxDatetime = "0.6.2"

# AndroidX
datastore = "1.1.7"
glance = "1.1.1"
workmanager = "2.10.3"
navigation = "2.9.3"
hiltNavigationCompose = "1.2.0"
coreSplashscreen = "1.0.1"

# Compose
composeNavigation = "2.8.0"
material = "1.8.3"

# Arrow
arrow = "2.1.2"

# Koin
koinBom = "4.1.0"

# Networking
okhttp = "5.1.0"
retrofit = "3.0.0"

# Database
room = "2.7.2"

# Image Loading
coil = "2.7.0"

# Code Quality
detekt = "1.24.0"
ktlint = "12.1.2"
spotless = "6.25.0"

# Testing
junit5 = "5.11.3"
turbine = "1.2.0"
kotest = "5.9.1"
mockk = "1.13.13"

# Other
ksp = "2.2.0-2.0.2"
aboutLibrary = "12.2.4"
reorderable = "2.5.1"
places = "4.4.1"
playServicesLocation = "21.3.0"

[libraries]
# AndroidX Core
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "coreSplashscreen" }

# Compose BOM
compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
compose-ui = { module = "androidx.compose.ui:ui" }
compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
compose-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
compose-material3 = { module = "androidx.compose.material3:material3" }
compose-icons = { module = "androidx.compose.material:material-icons-extended" }
compose-material = { group = "androidx.compose.material", name = "material", version.ref = "material"}

# Navigation
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hiltNavigationCompose" }

# KotlinX
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "kotlinxCoroutines" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
kotlinx-collections-immutable = { module = "org.jetbrains.kotlinx:kotlinx-collections-immutable", version.ref = "kotlinxCollectionsImmutable" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }

# DataStore
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastore" }
androidx-datastore-core = { module = "androidx.datastore:datastore-preferences-core", version.ref = "datastore" }

# Glance
androidx-glance = { module = "androidx.glance:glance-appwidget", version.ref = "glance" }
androidx-glance-material3 = { module = "androidx.glance:glance-material3", version.ref = "glance" }
androidx-glance-preview = { group = "androidx.glance", name = "glance-preview", version.ref = "glance" }
androidx-glance-appwidget-preview = { group = "androidx.glance", name = "glance-appwidget-preview", version.ref = "glance" }

# WorkManager
androidx-workmanager = { module = "androidx.work:work-runtime-ktx", version.ref = "workmanager" }

# Arrow (Functional Error Handling)
arrow-core = { group = "io.arrow-kt", name = "arrow-core", version.ref = "arrow" }
arrow-fx-coroutines = { group = "io.arrow-kt", name = "arrow-fx-coroutines", version.ref = "arrow" }

# Koin
koin-bom = { module = "io.insert-koin:koin-bom", version.ref = "koinBom" }
koin-core = { module = "io.insert-koin:koin-core" }
koin-android = { module = "io.insert-koin:koin-android" }
koin-androidx-compose = { module = "io.insert-koin:koin-androidx-compose" }
koin-androidx-workmanager = { module = "io.insert-koin:koin-androidx-workmanager" }

# Room
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }

# Network
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
retrofit-converter-kotlinx-serialization = { group = "com.squareup.retrofit2", name = "converter-kotlinx-serialization", version.ref = "retrofit" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }

# Image Loading
coil-kt-compose = { group = "io.coil-kt", name = "coil-compose", version.ref = "coil" }

# Other
javax-inject = { group = "javax.inject", name = "javax.inject", version = "1" }
reorderable = { module = "sh.calvin.reorderable:reorderable", version.ref = "reorderable" }
places = { module = "com.google.android.libraries.places:places", version.ref = "places" }
play-services-location = { module = "com.google.android.gms:play-services-location", version.ref = "playServicesLocation" }

# About Libraries
aboutlibrary-core = { group = "com.mikepenz", name = "aboutlibraries-core", version.ref = "aboutLibrary" }
aboutlibrary-compose = { group = "com.mikepenz", name = "aboutlibraries-compose", version.ref = "aboutLibrary" }

# Testing
junit5-api = { group = "org.junit.jupiter", name = "junit-jupiter-api", version.ref = "junit5" }
junit5-engine = { group = "org.junit.jupiter", name = "junit-jupiter-engine", version.ref = "junit5" }
turbine = { group = "app.cash.turbine", name = "turbine", version.ref = "turbine" }
kotest-runner = { group = "io.kotest", name = "kotest-runner-junit5", version.ref = "kotest" }
kotest-assertions = { group = "io.kotest", name = "kotest-assertions-core", version.ref = "kotest" }
mockk = { group = "io.mockk", name = "mockk", version.ref = "mockk" }
kotlinx-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "kotlinxCoroutines" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
room = { id = "androidx.room", version.ref = "room" }
about-library = { id = "com.mikepenz.aboutlibraries.plugin", version.ref = "aboutLibrary" }

# Code Quality
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
ktlint = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "ktlint" }
spotless = { id = "com.diffplug.spotless", version.ref = "spotless" }

[bundles]
compose = ["compose-ui", "compose-icons", "compose-material3"]
arrow = ["arrow-core", "arrow-fx-coroutines"]
koin = ["koin-core", "koin-android", "koin-androidx-compose"]
room = ["room-runtime", "room-ktx"]
network = ["retrofit", "retrofit-converter-kotlinx-serialization", "okhttp-logging"]
testing = ["junit5-api", "turbine", "kotest-runner", "kotest-assertions", "mockk", "kotlinx-coroutines-test"]
```


***

## Dependency Rules
- Use **latest stable versions**
- Group related dependencies in **bundles**
- Apply **consistent versioning** across modules
- Use **BOM** for dependency alignment
- Minimize **transitive dependencies**

## Module Dependencies
```

// Feature module dependencies
implementation(projects.core.domain)
implementation(projects.core.ui)
implementation(libs.bundles.koin)
implementation(libs.bundles.arrow)
