[{"key": "androidx/compose/ui/tooling/ComposableInvoker.class", "name": "androidx/compose/ui/tooling/ComposableInvoker.class", "size": 16692, "crc": -1116998977}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-1$1.class", "size": 2268, "crc": -1554212757}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-2$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-2$1.class", "size": 2269, "crc": -478571097}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-3$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-3$1.class", "size": 2269, "crc": 1421136478}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt.class", "size": 2346, "crc": 1632096754}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt$lambda-1$1.class", "size": 2744, "crc": 259752987}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt.class", "size": 1573, "crc": 249353802}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1$activityResultRegistry$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1$activityResultRegistry$1.class", "size": 1762, "crc": -1601997421}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1.class", "size": 1628, "crc": 1198295319}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeOnBackPressedDispatcherOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeOnBackPressedDispatcherOwner$1.class", "size": 2149, "crc": -1074882994}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeSavedStateRegistryOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeSavedStateRegistryOwner$1.class", "size": 3481, "crc": 1396994250}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeViewModelStoreOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeViewModelStoreOwner$1.class", "size": 1224, "crc": 61985820}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$1.class", "size": 3090, "crc": 395336347}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$2.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$2.class", "size": 2221, "crc": 248061551}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$1.class", "size": 1414, "crc": 868700773}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$2.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$2.class", "size": 1261, "crc": **********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findDesignInfoProviders$1$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findDesignInfoProviders$1$1.class", "size": 3542, "crc": **********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$1.class", "size": 1357, "crc": 628402741}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$2.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$2.class", "size": 1357, "crc": 592774753}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$1$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$1$1.class", "size": 2186, "crc": **********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$composable$1$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$composable$1$1.class", "size": 3247, "crc": -796186464}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1.class", "size": 6346, "crc": **********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3.class", "size": 4071, "crc": 1038929280}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$onDraw$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$onDraw$1.class", "size": 1171, "crc": -427123336}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter.class", "size": 36078, "crc": 1260768028}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter_androidKt.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter_androidKt.class", "size": 1485, "crc": 419502464}, {"key": "androidx/compose/ui/tooling/CompositionDataRecord$Companion.class", "name": "androidx/compose/ui/tooling/CompositionDataRecord$Companion.class", "size": 1114, "crc": 1433711266}, {"key": "androidx/compose/ui/tooling/CompositionDataRecord.class", "name": "androidx/compose/ui/tooling/CompositionDataRecord.class", "size": 1042, "crc": -580272927}, {"key": "androidx/compose/ui/tooling/CompositionDataRecordImpl.class", "name": "androidx/compose/ui/tooling/CompositionDataRecordImpl.class", "size": 1348, "crc": 1414920018}, {"key": "androidx/compose/ui/tooling/InspectableKt$InInspectionModeOnly$1.class", "name": "androidx/compose/ui/tooling/InspectableKt$InInspectionModeOnly$1.class", "size": 1881, "crc": 2126546504}, {"key": "androidx/compose/ui/tooling/InspectableKt$Inspectable$1.class", "name": "androidx/compose/ui/tooling/InspectableKt$Inspectable$1.class", "size": 2123, "crc": -2037582839}, {"key": "androidx/compose/ui/tooling/InspectableKt.class", "name": "androidx/compose/ui/tooling/InspectableKt.class", "size": 6937, "crc": -741690002}, {"key": "androidx/compose/ui/tooling/LayoutlibFontResourceLoader.class", "name": "androidx/compose/ui/tooling/LayoutlibFontResourceLoader.class", "size": 2737, "crc": -724091174}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setComposableContent$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setComposableContent$2.class", "size": 2568, "crc": -26954666}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1$1$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1$1$1.class", "size": 1592, "crc": -1798608071}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1.class", "size": 5256, "crc": 1638199000}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$2.class", "size": 10165, "crc": -1060580635}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1.class", "size": 5624, "crc": 2016485491}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$2.class", "size": 2807, "crc": -416579673}, {"key": "androidx/compose/ui/tooling/PreviewActivity.class", "name": "androidx/compose/ui/tooling/PreviewActivity.class", "size": 5214, "crc": 1568415903}, {"key": "androidx/compose/ui/tooling/PreviewLogger$Companion.class", "name": "androidx/compose/ui/tooling/PreviewLogger$Companion.class", "size": 1992, "crc": -1932842879}, {"key": "androidx/compose/ui/tooling/PreviewLogger.class", "name": "androidx/compose/ui/tooling/PreviewLogger.class", "size": 1025, "crc": -386466525}, {"key": "androidx/compose/ui/tooling/PreviewLogger_androidKt.class", "name": "androidx/compose/ui/tooling/PreviewLogger_androidKt.class", "size": 528, "crc": -472304868}, {"key": "androidx/compose/ui/tooling/PreviewUtils_androidKt.class", "name": "androidx/compose/ui/tooling/PreviewUtils_androidKt.class", "size": 11613, "crc": 2007102174}, {"key": "androidx/compose/ui/tooling/ResourceFontHelper.class", "name": "androidx/compose/ui/tooling/ResourceFontHelper.class", "size": 1503, "crc": 268027733}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo$allNodes$1.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo$allNodes$1.class", "size": 5949, "crc": -2077005507}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo.class", "size": 6390, "crc": -485721884}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$1.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$1.class", "size": 2579, "crc": -852925077}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$2.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$2.class", "size": 2061, "crc": -466652161}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$3.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$3.class", "size": 1882, "crc": -625592091}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt.class", "size": 8097, "crc": 644335510}, {"key": "androidx/compose/ui/tooling/ThreadSafeException.class", "name": "androidx/compose/ui/tooling/ThreadSafeException.class", "size": 2296, "crc": 288964875}, {"key": "androidx/compose/ui/tooling/ViewInfo.class", "name": "androidx/compose/ui/tooling/ViewInfo.class", "size": 8402, "crc": 1210190508}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$filterTree$1.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$filterTree$1.class", "size": 1551, "crc": 1817793619}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$1.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$1.class", "size": 1563, "crc": -1286896119}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$2.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$2.class", "size": 1629, "crc": -1556944723}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$3.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$3.class", "size": 1685, "crc": -2024384228}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$4.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$4.class", "size": 1746, "crc": 786397435}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt.class", "size": 7718, "crc": 552459992}, {"key": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation$Companion.class", "size": 3226, "crc": -1353718758}, {"key": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation.class", "size": 7529, "crc": 1359841508}, {"key": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation$Companion.class", "size": 3287, "crc": -736120775}, {"key": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation.class", "size": 5466, "crc": 662459236}, {"key": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation.class", "size": 4120, "crc": 406670108}, {"key": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation_androidKt.class", "name": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation_androidKt.class", "size": 1510, "crc": 1584890635}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$addAnimations$2$1$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$addAnimations$2$1$1.class", "size": 2330, "crc": 1097152085}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$hasAnimation$1$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$hasAnimation$1$1.class", "size": 2045, "crc": -1826093284}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch.class", "size": 5669, "crc": -2014118257}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearch.class", "size": 20169, "crc": -526858425}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearchInfo.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearchInfo.class", "size": 5544, "crc": -2073364782}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedContentSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedContentSearch.class", "size": 10060, "crc": 1009981025}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedVisibilitySearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedVisibilitySearch.class", "size": 10098, "crc": -671498073}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$DecaySearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$DecaySearch.class", "size": 1853, "crc": 668479895}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearch.class", "size": 12585, "crc": -674593735}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearchInfo.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearchInfo.class", "size": 4373, "crc": 81987962}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$RememberSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$RememberSearch.class", "size": 7188, "crc": -2001703310}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$Search.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$Search.class", "size": 4394, "crc": 1569117420}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$TargetBasedSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$TargetBasedSearch.class", "size": 1895, "crc": 1312538341}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$TransitionSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$TransitionSearch.class", "size": 9446, "crc": -524521500}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$animateXAsStateSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$animateXAsStateSearch$1.class", "size": 2189, "crc": 322588237}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedContentSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedContentSearch$1.class", "size": 1981, "crc": -1414902774}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedVisibilitySearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedVisibilitySearch$1.class", "size": 2114, "crc": 1536412354}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$attachAllAnimations$1$groups$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$attachAllAnimations$1$groups$1.class", "size": 1548, "crc": -2043493800}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$infiniteTransitionSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$infiniteTransitionSearch$1.class", "size": 2098, "crc": 2140827108}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$searchAny$1$groups$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$searchAny$1$groups$1.class", "size": 1518, "crc": -2013955920}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$transitionSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$transitionSearch$1.class", "size": 1966, "crc": 48785117}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$1.class", "size": 1736, "crc": 870033537}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$2.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$2.class", "size": 2012, "crc": 1462928739}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$3.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$3.class", "size": 1976, "crc": -1904684110}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch.class", "size": 12404, "crc": 1588021842}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt$findRememberedData$rememberCalls$1$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt$findRememberedData$rememberCalls$1$1.class", "size": 1787, "crc": -1587970889}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt.class", "size": 14497, "crc": 98047781}, {"key": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation$Companion.class", "size": 2664, "crc": -217213631}, {"key": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation.class", "size": 5834, "crc": 687229148}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$1.class", "size": 1226, "crc": 2117279019}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimateXAsState$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimateXAsState$1.class", "size": 3693, "crc": 430665771}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedContent$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedContent$1.class", "size": 3266, "crc": -145958388}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$1.class", "size": 1385, "crc": -854322725}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$2.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$2.class", "size": 4142, "crc": 688693516}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1$1$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1$1$1.class", "size": 3869, "crc": -881623065}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1.class", "size": 3464, "crc": -1685562024}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackTransition$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackTransition$1.class", "size": 3089, "crc": 962781459}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackUnsupported$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackUnsupported$1.class", "size": 1602, "crc": -1910695868}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock.class", "size": 24009, "crc": -949040390}, {"key": "androidx/compose/ui/tooling/animation/ToolingState.class", "name": "androidx/compose/ui/tooling/animation/ToolingState.class", "size": 2767, "crc": 247151571}, {"key": "androidx/compose/ui/tooling/animation/TransitionBasedAnimation.class", "name": "androidx/compose/ui/tooling/animation/TransitionBasedAnimation.class", "size": 1042, "crc": 862271984}, {"key": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation.class", "size": 3128, "crc": 1347926385}, {"key": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation_androidKt.class", "name": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation_androidKt.class", "size": 2230, "crc": 25912280}, {"key": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation$Companion.class", "size": 1966, "crc": -321069141}, {"key": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation.class", "size": 4576, "crc": 724413634}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimateXAsStateClock.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimateXAsStateClock.class", "size": 8771, "crc": -681558328}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getAnimatedProperties$lambda$8$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getAnimatedProperties$lambda$8$$inlined$sortedBy$1.class", "size": 2541, "crc": 1581599972}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getTransitions$lambda$4$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getTransitions$lambda$4$$inlined$sortedBy$1.class", "size": 2496, "crc": 1995705323}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock.class", "size": 11934, "crc": 248771359}, {"key": "androidx/compose/ui/tooling/animation/clock/ComposeAnimationClock.class", "name": "androidx/compose/ui/tooling/animation/clock/ComposeAnimationClock.class", "size": 2747, "crc": 1558064123}, {"key": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock$1.class", "name": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock$1.class", "size": 1376, "crc": 400207363}, {"key": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock.class", "name": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock.class", "size": 13051, "crc": 828153846}, {"key": "androidx/compose/ui/tooling/animation/clock/TransitionClock.class", "name": "androidx/compose/ui/tooling/animation/clock/TransitionClock.class", "size": 10155, "crc": -757431300}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$startTimeMs$2.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$startTimeMs$2.class", "size": 3246, "crc": -248534611}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$2.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$2.class", "size": 2645, "crc": -1587305609}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$5.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$5.class", "size": 2835, "crc": -1808004183}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt.class", "size": 17492, "crc": -1412776222}, {"key": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState$Companion.class", "name": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState$Companion.class", "size": 1484, "crc": -1308068440}, {"key": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState.class", "name": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState.class", "size": 3113, "crc": 63836371}, {"key": "androidx/compose/ui/tooling/animation/states/ComposeAnimationState.class", "name": "androidx/compose/ui/tooling/animation/states/ComposeAnimationState.class", "size": 469, "crc": 1680259192}, {"key": "androidx/compose/ui/tooling/animation/states/TargetState.class", "name": "androidx/compose/ui/tooling/animation/states/TargetState.class", "size": 3357, "crc": 2000314636}, {"key": "META-INF/androidx.compose.ui_ui-tooling.version", "name": "META-INF/androidx.compose.ui_ui-tooling.version", "size": 6, "crc": 333960004}, {"key": "META-INF/ui-tooling_release.kotlin_module", "name": "META-INF/ui-tooling_release.kotlin_module", "size": 417, "crc": 496746948}]