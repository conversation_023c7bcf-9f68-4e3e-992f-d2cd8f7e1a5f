---
type: "always_apply"
---

# Testing Strategy & Guidelines

## Test Pyramid
- **70% Unit Tests**: Domain layer, ViewModels, Use Cases
- **20% Integration Tests**: Repository, Database, Network
- **10% UI Tests**: Critical user journeys

## Unit Testing with Arrow
```

class ChatUseCaseTest {
@Test
fun `should return success when repository succeeds`() = runTest {
// Given
coEvery { repository.getMessages() } returns messages.right()

        // When
        val result = useCase()
        
        // Then
        result.shouldBeRight(messages)
    }
    }

```

## Testing Libraries
- **JUnit5**: Modern testing framework
- **Turbine**: Flow testing
- **Kotest**: Assertion library
- **MockK**: Mocking framework
- **Coroutines Test**: Async testing

## Test Organization
- Use **Given-When-Then** structure
- Test **happy paths** and **error cases**
- Mock **external dependencies**
- Use **test doubles** appropriately
- Implement **screenshot testing** for UI
