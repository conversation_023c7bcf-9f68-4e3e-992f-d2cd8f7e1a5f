0io.github.simplenote.data.local.dao.NoteDao_Impl:io.github.simplenote.data.local.database.NoteDatabase_Impl!io.github.simplenote.MainActivity*io.github.simplenote.SimpleNoteApplication5io.github.simplenote.data.local.database.NoteDatabase7io.github.simplenote.data.repository.NoteRepositoryImpl+io.github.simplenote.domain.model.NoteColor9io.github.simplenote.domain.model.NoteError.DatabaseErrorBio.github.simplenote.domain.model.NoteError.DatabaseError.NotFoundFio.github.simplenote.domain.model.NoteError.DatabaseError.InsertFailedFio.github.simplenote.domain.model.NoteError.DatabaseError.UpdateFailedFio.github.simplenote.domain.model.NoteError.DatabaseError.DeleteFailedEio.github.simplenote.domain.model.NoteError.DatabaseError.QueryFailed;io.github.simplenote.domain.model.NoteError.ValidationErrorFio.github.simplenote.domain.model.NoteError.ValidationError.EmptyTitleHio.github.simplenote.domain.model.NoteError.ValidationError.EmptyContentHio.github.simplenote.domain.model.NoteError.ValidationError.TitleTooLongJio.github.simplenote.domain.model.NoteError.ValidationError.ContentTooLongHio.github.simplenote.domain.model.NoteError.ValidationError.InvalidColor4io.github.simplenote.domain.model.NoteError.AppError<io.github.simplenote.domain.model.NoteError.AppError.UnknownGio.github.simplenote.domain.model.NoteError.AppError.NetworkUnavailableGio.github.simplenote.domain.model.NoteError.AppError.StorageUnavailable+io.github.simplenote.domain.model.ThemeMode:io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy4io.github.simplenote.navigation.AppDestination.Notes9io.github.simplenote.navigation.AppDestination.NoteEditorEio.github.simplenote.navigation.AppDestination.NoteEditor.$serializer7io.github.simplenote.navigation.AppDestination.Settings;io.github.simplenote.presentation.model.NotesUiState.SortBy>io.github.simplenote.presentation.model.NotesUiEvent.LoadNotesBio.github.simplenote.presentation.model.NotesUiEvent.CreateNewNote?io.github.simplenote.presentation.model.NotesUiEvent.SelectNote?<EMAIL>?io.github.simplenote.presentation.model.NotesUiEvent.ClearErrorBio.github.simplenote.presentation.model.NoteEditorUiEvent.LoadNoteEio.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateTitleGio.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateContentEio.github.simplenote.presentation.model.NoteEditorUiEvent.ChangeColorBio.github.simplenote.presentation.model.NoteEditorUiEvent.SaveNoteDio.github.simplenote.presentation.model.NoteEditorUiEvent.DeleteNoteFio.github.simplenote.presentation.model.NoteEditorUiEvent.NavigateBackDio.github.simplenote.presentation.model.NoteEditorUiEvent.ClearErrorDio.github.simplenote.presentation.model.NavigationEvent.NavigateBackLio.github.simplenote.presentation.model.NavigationEvent.NavigateToNoteEditorNio.github.simplenote.presentation.model.NavigationEvent.ShowDeleteConfirmationAio.github.simplenote.presentation.model.NavigationEvent.ShowError?io.github.simplenote.presentation.viewmodel.NoteEditorViewModel:io.github.simplenote.presentation.viewmodel.NotesViewModel=io.github.simplenote.presentation.viewmodel.SettingsViewModel                                                                                                                                                                                                                                                                                                                                                                                                                            