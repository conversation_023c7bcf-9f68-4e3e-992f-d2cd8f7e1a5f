[{"key": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationEffect$onCommit$1.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationEffect$onCommit$1.class", "size": 4410, "crc": -914532733}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationEffect.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationEffect.class", "size": 5443, "crc": 940237492}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationInfo.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$AnimationInfo.class", "size": 3112, "crc": 1307813583}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$AnimatorEffect$onStart$1.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$AnimatorEffect$onStart$1.class", "size": 3540, "crc": -1164282766}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$AnimatorEffect.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$AnimatorEffect.class", "size": 7455, "crc": -1681828910}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$Api24Impl.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$Api24Impl.class", "size": 1436, "crc": -649358622}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$Api26Impl.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$Api26Impl.class", "size": 1658, "crc": -335330554}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$SpecialEffectsInfo.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$SpecialEffectsInfo.class", "size": 2507, "crc": 689962281}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect$onCommit$4.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect$onCommit$4.class", "size": 1877, "crc": 1963287866}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect$onStart$4$1.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect$onStart$4$1.class", "size": 7393, "crc": 1617064660}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect$onStart$4.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect$onStart$4.class", "size": 3778, "crc": 154243611}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionEffect.class", "size": 27453, "crc": 1886772810}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionInfo.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$TransitionInfo.class", "size": 5078, "crc": -371527296}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController$retainMatchingViews$1.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController$retainMatchingViews$1.class", "size": 2443, "crc": 1978034312}, {"key": "androidx/fragment/app/DefaultSpecialEffectsController.class", "name": "androidx/fragment/app/DefaultSpecialEffectsController.class", "size": 22984, "crc": -1307948824}, {"key": "androidx/fragment/app/FragmentContainerView$Api20Impl.class", "name": "androidx/fragment/app/FragmentContainerView$Api20Impl.class", "size": 1870, "crc": 501262824}, {"key": "androidx/fragment/app/FragmentContainerView.class", "name": "androidx/fragment/app/FragmentContainerView.class", "size": 14433, "crc": -825267084}, {"key": "androidx/fragment/app/FragmentHostCallback.class", "name": "androidx/fragment/app/FragmentHostCallback.class", "size": 8726, "crc": 331700255}, {"key": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher$FragmentLifecycleCallbacksHolder.class", "name": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher$FragmentLifecycleCallbacksHolder.class", "size": 1774, "crc": -770776415}, {"key": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher.class", "name": "androidx/fragment/app/FragmentLifecycleCallbacksDispatcher.class", "size": 10236, "crc": -1450025524}, {"key": "androidx/fragment/app/FragmentTransition.class", "name": "androidx/fragment/app/FragmentTransition.class", "size": 9004, "crc": 852833368}, {"key": "androidx/fragment/app/PredictiveBackControl.class", "name": "androidx/fragment/app/PredictiveBackControl.class", "size": 1056, "crc": 1791906543}, {"key": "androidx/fragment/app/SpecialEffectsController$Companion.class", "name": "androidx/fragment/app/SpecialEffectsController$Companion.class", "size": 2860, "crc": -696951667}, {"key": "androidx/fragment/app/SpecialEffectsController$Effect.class", "name": "androidx/fragment/app/SpecialEffectsController$Effect.class", "size": 2184, "crc": -1542684364}, {"key": "androidx/fragment/app/SpecialEffectsController$FragmentStateManagerOperation.class", "name": "androidx/fragment/app/SpecialEffectsController$FragmentStateManagerOperation.class", "size": 4712, "crc": 87400136}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$LifecycleImpact.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$LifecycleImpact.class", "size": 1846, "crc": 714672513}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$State$Companion.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$State$Companion.class", "size": 2522, "crc": -1805314590}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$State$WhenMappings.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$State$WhenMappings.class", "size": 1110, "crc": -2093217594}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$State.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$State.class", "size": 4571, "crc": -1131955060}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation$WhenMappings.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation$WhenMappings.class", "size": 1095, "crc": -1495401057}, {"key": "androidx/fragment/app/SpecialEffectsController$Operation.class", "name": "androidx/fragment/app/SpecialEffectsController$Operation.class", "size": 9706, "crc": 208875042}, {"key": "androidx/fragment/app/SpecialEffectsController$WhenMappings.class", "name": "androidx/fragment/app/SpecialEffectsController$WhenMappings.class", "size": 991, "crc": -1735925192}, {"key": "androidx/fragment/app/SpecialEffectsController.class", "name": "androidx/fragment/app/SpecialEffectsController.class", "size": 23694, "crc": -1108127263}, {"key": "androidx/fragment/app/strictmode/FragmentReuseViolation.class", "name": "androidx/fragment/app/strictmode/FragmentReuseViolation.class", "size": 1580, "crc": 1927247262}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Flag.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Flag.class", "size": 2183, "crc": -1202574102}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$OnViolationListener.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$OnViolationListener.class", "size": 845, "crc": -1223641283}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Builder.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Builder.class", "size": 6091, "crc": -902154631}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Companion.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy$Companion.class", "size": 1067, "crc": -2079268844}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode$Policy.class", "size": 4430, "crc": -1592250528}, {"key": "androidx/fragment/app/strictmode/FragmentStrictMode.class", "name": "androidx/fragment/app/strictmode/FragmentStrictMode.class", "size": 12554, "crc": 619912561}, {"key": "androidx/fragment/app/strictmode/FragmentTagUsageViolation.class", "name": "androidx/fragment/app/strictmode/FragmentTagUsageViolation.class", "size": 1727, "crc": -151741538}, {"key": "androidx/fragment/app/strictmode/GetRetainInstanceUsageViolation.class", "name": "androidx/fragment/app/strictmode/GetRetainInstanceUsageViolation.class", "size": 1384, "crc": -505990103}, {"key": "androidx/fragment/app/strictmode/GetTargetFragmentRequestCodeUsageViolation.class", "name": "androidx/fragment/app/strictmode/GetTargetFragmentRequestCodeUsageViolation.class", "size": 1422, "crc": -886686165}, {"key": "androidx/fragment/app/strictmode/GetTargetFragmentUsageViolation.class", "name": "androidx/fragment/app/strictmode/GetTargetFragmentUsageViolation.class", "size": 1385, "crc": 1469552144}, {"key": "androidx/fragment/app/strictmode/RetainInstanceUsageViolation.class", "name": "androidx/fragment/app/strictmode/RetainInstanceUsageViolation.class", "size": 1316, "crc": 378640997}, {"key": "androidx/fragment/app/strictmode/SetRetainInstanceUsageViolation.class", "name": "androidx/fragment/app/strictmode/SetRetainInstanceUsageViolation.class", "size": 1384, "crc": 203001874}, {"key": "androidx/fragment/app/strictmode/SetTargetFragmentUsageViolation.class", "name": "androidx/fragment/app/strictmode/SetTargetFragmentUsageViolation.class", "size": 2004, "crc": -1965257009}, {"key": "androidx/fragment/app/strictmode/SetUserVisibleHintViolation.class", "name": "androidx/fragment/app/strictmode/SetUserVisibleHintViolation.class", "size": 1573, "crc": -1974598797}, {"key": "androidx/fragment/app/strictmode/TargetFragmentUsageViolation.class", "name": "androidx/fragment/app/strictmode/TargetFragmentUsageViolation.class", "size": 1316, "crc": -856855041}, {"key": "androidx/fragment/app/strictmode/Violation.class", "name": "androidx/fragment/app/strictmode/Violation.class", "size": 1523, "crc": 1163390328}, {"key": "androidx/fragment/app/strictmode/WrongFragmentContainerViolation.class", "name": "androidx/fragment/app/strictmode/WrongFragmentContainerViolation.class", "size": 1725, "crc": 3797976}, {"key": "androidx/fragment/app/strictmode/WrongNestedHierarchyViolation.class", "name": "androidx/fragment/app/strictmode/WrongNestedHierarchyViolation.class", "size": 2059, "crc": 141687659}, {"key": "androidx/fragment/app/BackStackRecord.class", "name": "androidx/fragment/app/BackStackRecord.class", "size": 16298, "crc": 1172948854}, {"key": "androidx/fragment/app/BackStackRecordState$1.class", "name": "androidx/fragment/app/BackStackRecordState$1.class", "size": 1260, "crc": -1119449387}, {"key": "androidx/fragment/app/BackStackRecordState.class", "name": "androidx/fragment/app/BackStackRecordState.class", "size": 7485, "crc": 2054571500}, {"key": "androidx/fragment/app/BackStackState$1.class", "name": "androidx/fragment/app/BackStackState$1.class", "size": 1218, "crc": 1610501766}, {"key": "androidx/fragment/app/BackStackState.class", "name": "androidx/fragment/app/BackStackState.class", "size": 5544, "crc": -553979045}, {"key": "androidx/fragment/app/DialogFragment$1.class", "name": "androidx/fragment/app/DialogFragment$1.class", "size": 1069, "crc": -1522930745}, {"key": "androidx/fragment/app/DialogFragment$2.class", "name": "androidx/fragment/app/DialogFragment$2.class", "size": 1093, "crc": 1717594222}, {"key": "androidx/fragment/app/DialogFragment$3.class", "name": "androidx/fragment/app/DialogFragment$3.class", "size": 1096, "crc": 1452852741}, {"key": "androidx/fragment/app/DialogFragment$4.class", "name": "androidx/fragment/app/DialogFragment$4.class", "size": 2179, "crc": 2111419204}, {"key": "androidx/fragment/app/DialogFragment$5.class", "name": "androidx/fragment/app/DialogFragment$5.class", "size": 1204, "crc": 440153820}, {"key": "androidx/fragment/app/DialogFragment.class", "name": "androidx/fragment/app/DialogFragment.class", "size": 14979, "crc": 55132994}, {"key": "androidx/fragment/app/Fragment$1.class", "name": "androidx/fragment/app/Fragment$1.class", "size": 679, "crc": 355716266}, {"key": "androidx/fragment/app/Fragment$10.class", "name": "androidx/fragment/app/Fragment$10.class", "size": 2492, "crc": -864734802}, {"key": "androidx/fragment/app/Fragment$2.class", "name": "androidx/fragment/app/Fragment$2.class", "size": 1479, "crc": 1118085775}, {"key": "androidx/fragment/app/Fragment$3.class", "name": "androidx/fragment/app/Fragment$3.class", "size": 722, "crc": 2045609948}, {"key": "androidx/fragment/app/Fragment$4.class", "name": "androidx/fragment/app/Fragment$4.class", "size": 1005, "crc": -572238005}, {"key": "androidx/fragment/app/Fragment$5.class", "name": "androidx/fragment/app/Fragment$5.class", "size": 1424, "crc": -108424422}, {"key": "androidx/fragment/app/Fragment$6.class", "name": "androidx/fragment/app/Fragment$6.class", "size": 1261, "crc": -1499712907}, {"key": "androidx/fragment/app/Fragment$7.class", "name": "androidx/fragment/app/Fragment$7.class", "size": 1641, "crc": 1618009136}, {"key": "androidx/fragment/app/Fragment$8.class", "name": "androidx/fragment/app/Fragment$8.class", "size": 1436, "crc": 1694242453}, {"key": "androidx/fragment/app/Fragment$9.class", "name": "androidx/fragment/app/Fragment$9.class", "size": 2247, "crc": 354071853}, {"key": "androidx/fragment/app/Fragment$AnimationInfo.class", "name": "androidx/fragment/app/Fragment$AnimationInfo.class", "size": 1622, "crc": -1392837736}, {"key": "androidx/fragment/app/Fragment$InstantiationException.class", "name": "androidx/fragment/app/Fragment$InstantiationException.class", "size": 733, "crc": 1288201394}, {"key": "androidx/fragment/app/Fragment$OnPreAttachedListener.class", "name": "androidx/fragment/app/Fragment$OnPreAttachedListener.class", "size": 656, "crc": -583528536}, {"key": "androidx/fragment/app/Fragment$SavedState$1.class", "name": "androidx/fragment/app/Fragment$SavedState$1.class", "size": 1720, "crc": -638246550}, {"key": "androidx/fragment/app/Fragment$SavedState.class", "name": "androidx/fragment/app/Fragment$SavedState.class", "size": 1856, "crc": 1541037674}, {"key": "androidx/fragment/app/Fragment.class", "name": "androidx/fragment/app/Fragment.class", "size": 58344, "crc": 1110318038}, {"key": "androidx/fragment/app/FragmentActivity$HostCallbacks.class", "name": "androidx/fragment/app/FragmentActivity$HostCallbacks.class", "size": 8517, "crc": -1936320910}, {"key": "androidx/fragment/app/FragmentActivity.class", "name": "androidx/fragment/app/FragmentActivity.class", "size": 14073, "crc": -1272002993}, {"key": "androidx/fragment/app/FragmentAnim$AnimationOrAnimator.class", "name": "androidx/fragment/app/FragmentAnim$AnimationOrAnimator.class", "size": 1309, "crc": -630361940}, {"key": "androidx/fragment/app/FragmentAnim$EndViewTransitionAnimation.class", "name": "androidx/fragment/app/FragmentAnim$EndViewTransitionAnimation.class", "size": 2188, "crc": -1743218793}, {"key": "androidx/fragment/app/FragmentAnim.class", "name": "androidx/fragment/app/FragmentAnim.class", "size": 4952, "crc": -692129161}, {"key": "androidx/fragment/app/FragmentContainer.class", "name": "androidx/fragment/app/FragmentContainer.class", "size": 1063, "crc": -1992326252}, {"key": "androidx/fragment/app/FragmentController.class", "name": "androidx/fragment/app/FragmentController.class", "size": 10102, "crc": -1837291495}, {"key": "androidx/fragment/app/FragmentFactory.class", "name": "androidx/fragment/app/FragmentFactory.class", "size": 4280, "crc": 236483861}, {"key": "androidx/fragment/app/FragmentLayoutInflaterFactory$1.class", "name": "androidx/fragment/app/FragmentLayoutInflaterFactory$1.class", "size": 2089, "crc": 1913211678}, {"key": "androidx/fragment/app/FragmentLayoutInflaterFactory.class", "name": "androidx/fragment/app/FragmentLayoutInflaterFactory.class", "size": 6709, "crc": 963003043}, {"key": "androidx/fragment/app/FragmentManager$1.class", "name": "androidx/fragment/app/FragmentManager$1.class", "size": 3549, "crc": 508681039}, {"key": "androidx/fragment/app/FragmentManager$10.class", "name": "androidx/fragment/app/FragmentManager$10.class", "size": 3583, "crc": 1860422604}, {"key": "androidx/fragment/app/FragmentManager$2.class", "name": "androidx/fragment/app/FragmentManager$2.class", "size": 1588, "crc": -1271131552}, {"key": "androidx/fragment/app/FragmentManager$3.class", "name": "androidx/fragment/app/FragmentManager$3.class", "size": 1275, "crc": -336219370}, {"key": "androidx/fragment/app/FragmentManager$4.class", "name": "androidx/fragment/app/FragmentManager$4.class", "size": 1075, "crc": 1865179008}, {"key": "androidx/fragment/app/FragmentManager$5.class", "name": "androidx/fragment/app/FragmentManager$5.class", "size": 719, "crc": -1299546506}, {"key": "androidx/fragment/app/FragmentManager$6.class", "name": "androidx/fragment/app/FragmentManager$6.class", "size": 2267, "crc": 371972915}, {"key": "androidx/fragment/app/FragmentManager$7.class", "name": "androidx/fragment/app/FragmentManager$7.class", "size": 1218, "crc": -1114852837}, {"key": "androidx/fragment/app/FragmentManager$8.class", "name": "androidx/fragment/app/FragmentManager$8.class", "size": 2754, "crc": -1200927530}, {"key": "androidx/fragment/app/FragmentManager$9.class", "name": "androidx/fragment/app/FragmentManager$9.class", "size": 2752, "crc": -241835390}, {"key": "androidx/fragment/app/FragmentManager$BackStackEntry.class", "name": "androidx/fragment/app/FragmentManager$BackStackEntry.class", "size": 758, "crc": 395123910}, {"key": "androidx/fragment/app/FragmentManager$ClearBackStackState.class", "name": "androidx/fragment/app/FragmentManager$ClearBackStackState.class", "size": 1471, "crc": -1782937633}, {"key": "androidx/fragment/app/FragmentManager$FragmentIntentSenderContract.class", "name": "androidx/fragment/app/FragmentManager$FragmentIntentSenderContract.class", "size": 4105, "crc": 314651040}, {"key": "androidx/fragment/app/FragmentManager$FragmentLifecycleCallbacks.class", "name": "androidx/fragment/app/FragmentManager$FragmentLifecycleCallbacks.class", "size": 3043, "crc": 1923990172}, {"key": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo$1.class", "name": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo$1.class", "size": 1425, "crc": 887160521}, {"key": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo.class", "name": "androidx/fragment/app/FragmentManager$LaunchedFragmentInfo.class", "size": 1793, "crc": 16608686}, {"key": "androidx/fragment/app/FragmentManager$LifecycleAwareResultListener.class", "name": "androidx/fragment/app/FragmentManager$LifecycleAwareResultListener.class", "size": 1803, "crc": 1358675973}, {"key": "androidx/fragment/app/FragmentManager$OnBackStackChangedListener.class", "name": "androidx/fragment/app/FragmentManager$OnBackStackChangedListener.class", "size": 1244, "crc": -141010384}, {"key": "androidx/fragment/app/FragmentManager$OpGenerator.class", "name": "androidx/fragment/app/FragmentManager$OpGenerator.class", "size": 523, "crc": 1071547650}, {"key": "androidx/fragment/app/FragmentManager$PopBackStackState.class", "name": "androidx/fragment/app/FragmentManager$PopBackStackState.class", "size": 1952, "crc": 843396341}, {"key": "androidx/fragment/app/FragmentManager$PrepareBackStackTransitionState.class", "name": "androidx/fragment/app/FragmentManager$PrepareBackStackTransitionState.class", "size": 2778, "crc": -292439560}, {"key": "androidx/fragment/app/FragmentManager$RestoreBackStackState.class", "name": "androidx/fragment/app/FragmentManager$RestoreBackStackState.class", "size": 1479, "crc": -321243976}, {"key": "androidx/fragment/app/FragmentManager$SaveBackStackState.class", "name": "androidx/fragment/app/FragmentManager$SaveBackStackState.class", "size": 1467, "crc": -1651507982}, {"key": "androidx/fragment/app/FragmentManager.class", "name": "androidx/fragment/app/FragmentManager.class", "size": 80273, "crc": -1502204291}, {"key": "androidx/fragment/app/FragmentManagerImpl.class", "name": "androidx/fragment/app/FragmentManagerImpl.class", "size": 347, "crc": -1416298462}, {"key": "androidx/fragment/app/FragmentManagerNonConfig.class", "name": "androidx/fragment/app/FragmentManagerNonConfig.class", "size": 2295, "crc": -369784958}, {"key": "androidx/fragment/app/FragmentManagerState$1.class", "name": "androidx/fragment/app/FragmentManagerState$1.class", "size": 1260, "crc": 1282690881}, {"key": "androidx/fragment/app/FragmentManagerState.class", "name": "androidx/fragment/app/FragmentManagerState.class", "size": 2886, "crc": -210326284}, {"key": "androidx/fragment/app/FragmentManagerViewModel$1.class", "name": "androidx/fragment/app/FragmentManagerViewModel$1.class", "size": 1111, "crc": 1741407979}, {"key": "androidx/fragment/app/FragmentManagerViewModel.class", "name": "androidx/fragment/app/FragmentManagerViewModel.class", "size": 10319, "crc": 1154363494}, {"key": "androidx/fragment/app/FragmentOnAttachListener.class", "name": "androidx/fragment/app/FragmentOnAttachListener.class", "size": 422, "crc": -709317468}, {"key": "androidx/fragment/app/FragmentPagerAdapter.class", "name": "androidx/fragment/app/FragmentPagerAdapter.class", "size": 5478, "crc": 493634549}, {"key": "androidx/fragment/app/FragmentResultListener.class", "name": "androidx/fragment/app/FragmentResultListener.class", "size": 307, "crc": -1095203914}, {"key": "androidx/fragment/app/FragmentResultOwner.class", "name": "androidx/fragment/app/FragmentResultOwner.class", "size": 586, "crc": -1593590975}, {"key": "androidx/fragment/app/FragmentState$1.class", "name": "androidx/fragment/app/FragmentState$1.class", "size": 1211, "crc": -28342433}, {"key": "androidx/fragment/app/FragmentState.class", "name": "androidx/fragment/app/FragmentState.class", "size": 5125, "crc": 79145166}, {"key": "androidx/fragment/app/FragmentStateManager$1.class", "name": "androidx/fragment/app/FragmentStateManager$1.class", "size": 1251, "crc": -1145675173}, {"key": "androidx/fragment/app/FragmentStateManager$2.class", "name": "androidx/fragment/app/FragmentStateManager$2.class", "size": 910, "crc": -514192580}, {"key": "androidx/fragment/app/FragmentStateManager.class", "name": "androidx/fragment/app/FragmentStateManager.class", "size": 24216, "crc": 935282117}, {"key": "androidx/fragment/app/FragmentStatePagerAdapter.class", "name": "androidx/fragment/app/FragmentStatePagerAdapter.class", "size": 8006, "crc": 1184517014}, {"key": "androidx/fragment/app/FragmentStore.class", "name": "androidx/fragment/app/FragmentStore.class", "size": 12329, "crc": 1716691632}, {"key": "androidx/fragment/app/FragmentTabHost$DummyTabFactory.class", "name": "androidx/fragment/app/FragmentTabHost$DummyTabFactory.class", "size": 986, "crc": 2144460761}, {"key": "androidx/fragment/app/FragmentTabHost$SavedState$1.class", "name": "androidx/fragment/app/FragmentTabHost$SavedState$1.class", "size": 1355, "crc": -1551796423}, {"key": "androidx/fragment/app/FragmentTabHost$SavedState.class", "name": "androidx/fragment/app/FragmentTabHost$SavedState.class", "size": 1939, "crc": -1892450561}, {"key": "androidx/fragment/app/FragmentTabHost$TabInfo.class", "name": "androidx/fragment/app/FragmentTabHost$TabInfo.class", "size": 1102, "crc": -882579545}, {"key": "androidx/fragment/app/FragmentTabHost.class", "name": "androidx/fragment/app/FragmentTabHost.class", "size": 9415, "crc": -1390181508}, {"key": "androidx/fragment/app/FragmentTransaction$Op.class", "name": "androidx/fragment/app/FragmentTransaction$Op.class", "size": 1903, "crc": 1770481513}, {"key": "androidx/fragment/app/FragmentTransaction.class", "name": "androidx/fragment/app/FragmentTransaction.class", "size": 15576, "crc": -105727049}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$1.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$1.class", "size": 1088, "crc": -2115042628}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$2.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$2.class", "size": 2025, "crc": 1277239223}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$3.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$3.class", "size": 2273, "crc": 1940246455}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$4.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$4.class", "size": 1608, "crc": 1265915732}, {"key": "androidx/fragment/app/FragmentTransitionCompat21$5.class", "name": "androidx/fragment/app/FragmentTransitionCompat21$5.class", "size": 1198, "crc": 787534642}, {"key": "androidx/fragment/app/FragmentTransitionCompat21.class", "name": "androidx/fragment/app/FragmentTransitionCompat21.class", "size": 10615, "crc": -1940428319}, {"key": "androidx/fragment/app/FragmentTransitionImpl$1.class", "name": "androidx/fragment/app/FragmentTransitionImpl$1.class", "size": 1562, "crc": 537838280}, {"key": "androidx/fragment/app/FragmentTransitionImpl.class", "name": "androidx/fragment/app/FragmentTransitionImpl.class", "size": 9798, "crc": -1472982070}, {"key": "androidx/fragment/app/FragmentViewLifecycleOwner.class", "name": "androidx/fragment/app/FragmentViewLifecycleOwner.class", "size": 5504, "crc": -371790542}, {"key": "androidx/fragment/app/ListFragment$1.class", "name": "androidx/fragment/app/ListFragment$1.class", "size": 806, "crc": 1174214966}, {"key": "androidx/fragment/app/ListFragment$2.class", "name": "androidx/fragment/app/ListFragment$2.class", "size": 1216, "crc": -1752401187}, {"key": "androidx/fragment/app/ListFragment.class", "name": "androidx/fragment/app/ListFragment.class", "size": 7947, "crc": -1054388108}, {"key": "androidx/fragment/app/LogWriter.class", "name": "androidx/fragment/app/LogWriter.class", "size": 1330, "crc": -114730018}, {"key": "androidx/fragment/app/SpecialEffectsControllerFactory.class", "name": "androidx/fragment/app/SpecialEffectsControllerFactory.class", "size": 395, "crc": 1160283280}, {"key": "androidx/fragment/app/SuperNotCalledException.class", "name": "androidx/fragment/app/SuperNotCalledException.class", "size": 418, "crc": 1391521167}, {"key": "META-INF/androidx.fragment_fragment.version", "name": "META-INF/androidx.fragment_fragment.version", "size": 6, "crc": -267187569}, {"key": "META-INF/fragment_release.kotlin_module", "name": "META-INF/fragment_release.kotlin_module", "size": 24, "crc": 1613429616}]