[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/kotlinx-collections-immutable.kotlin_module", "name": "META-INF/kotlinx-collections-immutable.kotlin_module", "size": 406, "crc": 580108028}, {"key": "kotlinx/collections/immutable/ExtensionsKt.class", "name": "kotlinx/collections/immutable/ExtensionsKt.class", "size": 38995, "crc": -467513185}, {"key": "kotlinx/collections/immutable/ImmutableCollection.class", "name": "kotlinx/collections/immutable/ImmutableCollection.class", "size": 651, "crc": -265551771}, {"key": "kotlinx/collections/immutable/ImmutableList$DefaultImpls.class", "name": "kotlinx/collections/immutable/ImmutableList$DefaultImpls.class", "size": 1124, "crc": -1444448487}, {"key": "kotlinx/collections/immutable/ImmutableList$SubList.class", "name": "kotlinx/collections/immutable/ImmutableList$SubList.class", "size": 2681, "crc": -1062494356}, {"key": "kotlinx/collections/immutable/ImmutableList.class", "name": "kotlinx/collections/immutable/ImmutableList.class", "size": 1286, "crc": 1198973840}, {"key": "kotlinx/collections/immutable/ImmutableMap.class", "name": "kotlinx/collections/immutable/ImmutableMap.class", "size": 1492, "crc": -490305113}, {"key": "kotlinx/collections/immutable/ImmutableSet.class", "name": "kotlinx/collections/immutable/ImmutableSet.class", "size": 800, "crc": -919493432}, {"key": "kotlinx/collections/immutable/PersistentCollection$Builder.class", "name": "kotlinx/collections/immutable/PersistentCollection$Builder.class", "size": 1080, "crc": -491622777}, {"key": "kotlinx/collections/immutable/PersistentCollection.class", "name": "kotlinx/collections/immutable/PersistentCollection.class", "size": 2434, "crc": 325386481}, {"key": "kotlinx/collections/immutable/PersistentList$Builder.class", "name": "kotlinx/collections/immutable/PersistentList$Builder.class", "size": 1289, "crc": -2058520598}, {"key": "kotlinx/collections/immutable/PersistentList$DefaultImpls.class", "name": "kotlinx/collections/immutable/PersistentList$DefaultImpls.class", "size": 1208, "crc": 207924047}, {"key": "kotlinx/collections/immutable/PersistentList.class", "name": "kotlinx/collections/immutable/PersistentList.class", "size": 3328, "crc": -363165295}, {"key": "kotlinx/collections/immutable/PersistentMap$Builder.class", "name": "kotlinx/collections/immutable/PersistentMap$Builder.class", "size": 1055, "crc": -492797868}, {"key": "kotlinx/collections/immutable/PersistentMap.class", "name": "kotlinx/collections/immutable/PersistentMap.class", "size": 2121, "crc": 1335712947}, {"key": "kotlinx/collections/immutable/PersistentSet$Builder.class", "name": "kotlinx/collections/immutable/PersistentSet$Builder.class", "size": 1278, "crc": 569464742}, {"key": "kotlinx/collections/immutable/PersistentSet.class", "name": "kotlinx/collections/immutable/PersistentSet.class", "size": 2495, "crc": 831609331}, {"key": "kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "name": "kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "size": 4107, "crc": -975547057}, {"key": "kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "name": "kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "size": 6012, "crc": 1873414173}, {"key": "kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "name": "kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "size": 7043, "crc": 487013940}, {"key": "kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "name": "kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "size": 1399, "crc": -769822803}, {"key": "kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "size": 2782, "crc": -1937803728}, {"key": "kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "name": "kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "size": 8491, "crc": 2016591030}, {"key": "kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "size": 1948, "crc": -1340568376}, {"key": "kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "name": "kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "size": 1087, "crc": -1622467431}, {"key": "kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "name": "kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "size": 14176, "crc": 455002014}, {"key": "kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "name": "kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "size": 27111, "crc": 920061291}, {"key": "kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "size": 2897, "crc": -663740713}, {"key": "kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "size": 5468, "crc": 106991865}, {"key": "kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "size": 1610, "crc": -1535598118}, {"key": "kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "name": "kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "size": 1398, "crc": 1668336975}, {"key": "kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "name": "kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "size": 11209, "crc": 802523507}, {"key": "kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "size": 3880, "crc": -1511336201}, {"key": "kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "name": "kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "size": 2211, "crc": 1390570936}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "size": 2153, "crc": 1362767770}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "size": 3441, "crc": -384280104}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "size": 2885, "crc": 496230635}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "size": 2050, "crc": 1012079945}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$1.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$1.class", "size": 1930, "crc": -2084227151}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$2.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$2.class", "size": 1930, "crc": -1569384778}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$3.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$3.class", "size": 1483, "crc": -681378298}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$4.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$equals$4.class", "size": 1483, "crc": 649442288}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "size": 12822, "crc": 1844353780}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "size": 5138, "crc": -409861130}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$1.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$1.class", "size": 1511, "crc": 1131207384}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$2.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$2.class", "size": 1511, "crc": 983683382}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$3.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$3.class", "size": 1958, "crc": 1751409330}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$4.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder$equals$4.class", "size": 1958, "crc": 1525611603}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "size": 12497, "crc": -26251901}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "size": 7619, "crc": 1654100981}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "size": 3794, "crc": 511958063}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "size": 3665, "crc": -1342692517}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "size": 3039, "crc": -813450746}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "size": 2258, "crc": 1565939480}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "size": 2877, "crc": 1132379711}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "size": 2264, "crc": -1877517941}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "size": 490, "crc": -1600825632}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "size": 3351, "crc": 1323883691}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "size": 2275, "crc": -2030658501}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "size": 2670, "crc": 621392892}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "size": 2133, "crc": 207687655}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "size": 2718, "crc": -183993975}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "size": 2139, "crc": 1644768197}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "size": 1355, "crc": 1906127757}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "size": 3819, "crc": -772656030}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "size": 39689, "crc": 161646922}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "size": 4323, "crc": 172841575}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "size": 1874, "crc": -1793377504}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "size": 1383, "crc": -1968404431}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "size": 4227, "crc": 1035903153}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "size": 2759, "crc": 784270739}, {"key": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "size": 1386, "crc": -940974034}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "size": 1699, "crc": 1382673392}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "size": 9844, "crc": 384300244}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "size": 9473, "crc": -171257371}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "size": 5090, "crc": 1647830488}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "size": 5569, "crc": -1475604558}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "size": 1349, "crc": -1527228627}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "size": 32007, "crc": 132144983}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "size": 3419, "crc": -1910912654}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "size": 1766, "crc": 577274003}, {"key": "kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "name": "kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "size": 3811, "crc": 679637325}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "size": 3199, "crc": 1181592060}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "size": 3164, "crc": 532849857}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "size": 2152, "crc": 1924376002}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$1.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$1.class", "size": 2207, "crc": -1652091496}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$2.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$2.class", "size": 2207, "crc": 817149117}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$3.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$3.class", "size": 2047, "crc": 1424686165}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$4.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$equals$4.class", "size": 2047, "crc": -762666934}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "size": 14414, "crc": -1667635439}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$1.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$1.class", "size": 2235, "crc": -1272786544}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$2.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$2.class", "size": 2235, "crc": -1258420954}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$3.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$3.class", "size": 2075, "crc": -39465327}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$4.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder$equals$4.class", "size": 2075, "crc": -1116047011}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "size": 10872, "crc": -956449092}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "size": 3885, "crc": 2001756889}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "size": 3784, "crc": -1128950295}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "size": 3130, "crc": 592856081}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "size": 2870, "crc": 1570980790}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "size": 6058, "crc": -191197206}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "size": 2968, "crc": 306207836}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "size": 2908, "crc": 788511514}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "size": 3240, "crc": 1360030364}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "size": 3527, "crc": 1283213743}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "size": 2559, "crc": -1000277282}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "size": 3006, "crc": 698172556}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "size": 4174, "crc": -672932530}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "size": 2607, "crc": -1936803685}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "size": 3001, "crc": -1069252534}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "size": 2303, "crc": -1577610602}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "size": 1757, "crc": 911670255}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "size": 13562, "crc": 943880214}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "size": 9187, "crc": 1553801332}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "size": 3817, "crc": -1183802218}, {"key": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "name": "kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "size": 3884, "crc": -1217016257}, {"key": "kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "name": "kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "size": 713, "crc": 984076897}, {"key": "kotlinx/collections/immutable/internal/DeltaCounter.class", "name": "kotlinx/collections/immutable/internal/DeltaCounter.class", "size": 2494, "crc": 681607849}, {"key": "kotlinx/collections/immutable/internal/EndOfChain.class", "name": "kotlinx/collections/immutable/internal/EndOfChain.class", "size": 730, "crc": 455967929}, {"key": "kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "name": "kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "size": 1500, "crc": 45039259}, {"key": "kotlinx/collections/immutable/internal/ListImplementation.class", "name": "kotlinx/collections/immutable/internal/ListImplementation.class", "size": 3546, "crc": -1218139308}, {"key": "kotlinx/collections/immutable/internal/MapImplementation.class", "name": "kotlinx/collections/immutable/internal/MapImplementation.class", "size": 4223, "crc": -1004047145}, {"key": "kotlinx/collections/immutable/internal/MutabilityOwnership.class", "name": "kotlinx/collections/immutable/internal/MutabilityOwnership.class", "size": 592, "crc": -1982002739}]