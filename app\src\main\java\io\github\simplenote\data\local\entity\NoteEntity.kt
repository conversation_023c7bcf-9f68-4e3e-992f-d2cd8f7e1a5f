package io.github.simplenote.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import kotlinx.datetime.Instant

/**
 * Room entity representing a Note in the database.
 * This is the data layer representation that maps to the database table.
 */
@Entity(tableName = "notes")
data class NoteEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0L,
    
    @ColumnInfo(name = "title")
    val title: String,
    
    @ColumnInfo(name = "content")
    val content: String,
    
    @ColumnInfo(name = "color_id")
    val colorId: Int,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long, // Stored as epoch milliseconds
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long  // Stored as epoch milliseconds
)

/**
 * Extension functions to convert between domain model and entity
 */
fun NoteEntity.toDomain(): Note = Note(
    id = id,
    title = title,
    content = content,
    color = NoteColor.fromId(colorId),
    createdAt = Instant.fromEpochMilliseconds(createdAt),
    updatedAt = Instant.fromEpochMilliseconds(updatedAt)
)

fun Note.toEntity(): NoteEntity = NoteEntity(
    id = id,
    title = title,
    content = content,
    colorId = color.id,
    createdAt = createdAt.toEpochMilliseconds(),
    updatedAt = updatedAt.toEpochMilliseconds()
)
