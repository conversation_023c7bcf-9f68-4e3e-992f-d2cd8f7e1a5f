{"formatVersion": 1, "database": {"version": 1, "identityHash": "36a672eeccf2b5443cc47b14f0f4e06b", "entities": [{"tableName": "notes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL, `color_id` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "colorId", "columnName": "color_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updated_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '36a672eeccf2b5443cc47b14f0f4e06b')"]}}