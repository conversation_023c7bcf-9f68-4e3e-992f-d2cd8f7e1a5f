[{"key": "androidx/datastore/core/Actual_jvmKt.class", "name": "androidx/datastore/core/Actual_jvmKt.class", "size": 943, "crc": 1622053985}, {"key": "androidx/datastore/core/Api26Impl.class", "name": "androidx/datastore/core/Api26Impl.class", "size": 1631, "crc": 265391255}, {"key": "androidx/datastore/core/AtomicBoolean.class", "name": "androidx/datastore/core/AtomicBoolean.class", "size": 1084, "crc": 281145531}, {"key": "androidx/datastore/core/AtomicInt.class", "name": "androidx/datastore/core/AtomicInt.class", "size": 1401, "crc": 1091169861}, {"key": "androidx/datastore/core/Closeable.class", "name": "androidx/datastore/core/Closeable.class", "size": 432, "crc": -543122357}, {"key": "androidx/datastore/core/CloseableKt.class", "name": "androidx/datastore/core/CloseableKt.class", "size": 2347, "crc": 1595969217}, {"key": "androidx/datastore/core/CorruptionException.class", "name": "androidx/datastore/core/CorruptionException.class", "size": 1233, "crc": -707053664}, {"key": "androidx/datastore/core/CorruptionHandler.class", "name": "androidx/datastore/core/CorruptionHandler.class", "size": 1010, "crc": 1999603299}, {"key": "androidx/datastore/core/Data.class", "name": "androidx/datastore/core/Data.class", "size": 1670, "crc": 325258244}, {"key": "androidx/datastore/core/DataMigration.class", "name": "androidx/datastore/core/DataMigration.class", "size": 1257, "crc": 1429712458}, {"key": "androidx/datastore/core/DataMigrationInitializer$Companion$getInitializer$1.class", "name": "androidx/datastore/core/DataMigrationInitializer$Companion$getInitializer$1.class", "size": 4032, "crc": -1652378874}, {"key": "androidx/datastore/core/DataMigrationInitializer$Companion$runMigrations$1.class", "name": "androidx/datastore/core/DataMigrationInitializer$Companion$runMigrations$1.class", "size": 2334, "crc": -1152150821}, {"key": "androidx/datastore/core/DataMigrationInitializer$Companion$runMigrations$2$1$1.class", "name": "androidx/datastore/core/DataMigrationInitializer$Companion$runMigrations$2$1$1.class", "size": 3396, "crc": 2127456709}, {"key": "androidx/datastore/core/DataMigrationInitializer$Companion$runMigrations$2.class", "name": "androidx/datastore/core/DataMigrationInitializer$Companion$runMigrations$2.class", "size": 6310, "crc": 617032936}, {"key": "androidx/datastore/core/DataMigrationInitializer$Companion.class", "name": "androidx/datastore/core/DataMigrationInitializer$Companion.class", "size": 6845, "crc": 221718962}, {"key": "androidx/datastore/core/DataMigrationInitializer.class", "name": "androidx/datastore/core/DataMigrationInitializer.class", "size": 1032, "crc": -1435957000}, {"key": "androidx/datastore/core/DataStore.class", "name": "androidx/datastore/core/DataStore.class", "size": 1340, "crc": 1787783560}, {"key": "androidx/datastore/core/DataStoreFactory.class", "name": "androidx/datastore/core/DataStoreFactory.class", "size": 8663, "crc": 780963651}, {"key": "androidx/datastore/core/DataStoreImpl$Companion.class", "name": "androidx/datastore/core/DataStoreImpl$Companion.class", "size": 874, "crc": 1986325549}, {"key": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$1.class", "name": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$1.class", "size": 1921, "crc": -1481487159}, {"key": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$initData$1$api$1$updateData$1.class", "name": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$initData$1$api$1$updateData$1.class", "size": 2477, "crc": 595086078}, {"key": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$initData$1$api$1.class", "name": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$initData$1$api$1.class", "size": 6639, "crc": -1455081680}, {"key": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$initData$1.class", "name": "androidx/datastore/core/DataStoreImpl$InitDataStore$doRun$initData$1.class", "size": 8815, "crc": -908667302}, {"key": "androidx/datastore/core/DataStoreImpl$InitDataStore.class", "name": "androidx/datastore/core/DataStoreImpl$InitDataStore.class", "size": 4993, "crc": -1892342636}, {"key": "androidx/datastore/core/DataStoreImpl$coordinator$2.class", "name": "androidx/datastore/core/DataStoreImpl$coordinator$2.class", "size": 1755, "crc": -1887887220}, {"key": "androidx/datastore/core/DataStoreImpl$data$1$1.class", "name": "androidx/datastore/core/DataStoreImpl$data$1$1.class", "size": 3700, "crc": 48809343}, {"key": "androidx/datastore/core/DataStoreImpl$data$1$2.class", "name": "androidx/datastore/core/DataStoreImpl$data$1$2.class", "size": 3273, "crc": 1816416417}, {"key": "androidx/datastore/core/DataStoreImpl$data$1$3.class", "name": "androidx/datastore/core/DataStoreImpl$data$1$3.class", "size": 3480, "crc": 1078793515}, {"key": "androidx/datastore/core/DataStoreImpl$data$1$5.class", "name": "androidx/datastore/core/DataStoreImpl$data$1$5.class", "size": 3445, "crc": 669503152}, {"key": "androidx/datastore/core/DataStoreImpl$data$1$invokeSuspend$$inlined$map$1$2$1.class", "name": "androidx/datastore/core/DataStoreImpl$data$1$invokeSuspend$$inlined$map$1$2$1.class", "size": 2134, "crc": -351466242}, {"key": "androidx/datastore/core/DataStoreImpl$data$1$invokeSuspend$$inlined$map$1$2.class", "name": "androidx/datastore/core/DataStoreImpl$data$1$invokeSuspend$$inlined$map$1$2.class", "size": 4227, "crc": 126194062}, {"key": "androidx/datastore/core/DataStoreImpl$data$1$invokeSuspend$$inlined$map$1.class", "name": "androidx/datastore/core/DataStoreImpl$data$1$invokeSuspend$$inlined$map$1.class", "size": 3141, "crc": 534879682}, {"key": "androidx/datastore/core/DataStoreImpl$data$1.class", "name": "androidx/datastore/core/DataStoreImpl$data$1.class", "size": 7588, "crc": 1127684265}, {"key": "androidx/datastore/core/DataStoreImpl$decrementCollector$1.class", "name": "androidx/datastore/core/DataStoreImpl$decrementCollector$1.class", "size": 1944, "crc": -1500844919}, {"key": "androidx/datastore/core/DataStoreImpl$doWithWriteFileLock$3.class", "name": "androidx/datastore/core/DataStoreImpl$doWithWriteFileLock$3.class", "size": 3232, "crc": 2040946057}, {"key": "androidx/datastore/core/DataStoreImpl$handleUpdate$1.class", "name": "androidx/datastore/core/DataStoreImpl$handleUpdate$1.class", "size": 2137, "crc": 1052641040}, {"key": "androidx/datastore/core/DataStoreImpl$incrementCollector$1.class", "name": "androidx/datastore/core/DataStoreImpl$incrementCollector$1.class", "size": 1944, "crc": -1385704261}, {"key": "androidx/datastore/core/DataStoreImpl$incrementCollector$2$1$1.class", "name": "androidx/datastore/core/DataStoreImpl$incrementCollector$2$1$1.class", "size": 2705, "crc": -1189954685}, {"key": "androidx/datastore/core/DataStoreImpl$incrementCollector$2$1.class", "name": "androidx/datastore/core/DataStoreImpl$incrementCollector$2$1.class", "size": 4514, "crc": 387407606}, {"key": "androidx/datastore/core/DataStoreImpl$readAndInitOrPropagateAndThrowFailure$1.class", "name": "androidx/datastore/core/DataStoreImpl$readAndInitOrPropagateAndThrowFailure$1.class", "size": 2042, "crc": 810469696}, {"key": "androidx/datastore/core/DataStoreImpl$readDataAndUpdateCache$1.class", "name": "androidx/datastore/core/DataStoreImpl$readDataAndUpdateCache$1.class", "size": 2031, "crc": 1688851253}, {"key": "androidx/datastore/core/DataStoreImpl$readDataAndUpdateCache$3.class", "name": "androidx/datastore/core/DataStoreImpl$readDataAndUpdateCache$3.class", "size": 4381, "crc": -1004460174}, {"key": "androidx/datastore/core/DataStoreImpl$readDataAndUpdateCache$4.class", "name": "androidx/datastore/core/DataStoreImpl$readDataAndUpdateCache$4.class", "size": 5065, "crc": 909036092}, {"key": "androidx/datastore/core/DataStoreImpl$readDataOrHandleCorruption$1.class", "name": "androidx/datastore/core/DataStoreImpl$readDataOrHandleCorruption$1.class", "size": 2232, "crc": 1701275624}, {"key": "androidx/datastore/core/DataStoreImpl$readDataOrHandleCorruption$2.class", "name": "androidx/datastore/core/DataStoreImpl$readDataOrHandleCorruption$2.class", "size": 4597, "crc": 911883147}, {"key": "androidx/datastore/core/DataStoreImpl$readDataOrHandleCorruption$3.class", "name": "androidx/datastore/core/DataStoreImpl$readDataOrHandleCorruption$3.class", "size": 4662, "crc": 1291079420}, {"key": "androidx/datastore/core/DataStoreImpl$readState$2.class", "name": "androidx/datastore/core/DataStoreImpl$readState$2.class", "size": 4494, "crc": -1275502548}, {"key": "androidx/datastore/core/DataStoreImpl$storageConnectionDelegate$1.class", "name": "androidx/datastore/core/DataStoreImpl$storageConnectionDelegate$1.class", "size": 1834, "crc": 1917388262}, {"key": "androidx/datastore/core/DataStoreImpl$transformAndWrite$2$newData$1.class", "name": "androidx/datastore/core/DataStoreImpl$transformAndWrite$2$newData$1.class", "size": 3777, "crc": 360323376}, {"key": "androidx/datastore/core/DataStoreImpl$transformAndWrite$2.class", "name": "androidx/datastore/core/DataStoreImpl$transformAndWrite$2.class", "size": 4904, "crc": -1517642294}, {"key": "androidx/datastore/core/DataStoreImpl$updateData$2.class", "name": "androidx/datastore/core/DataStoreImpl$updateData$2.class", "size": 5155, "crc": 267509738}, {"key": "androidx/datastore/core/DataStoreImpl$writeActor$1.class", "name": "androidx/datastore/core/DataStoreImpl$writeActor$1.class", "size": 2555, "crc": 1688546204}, {"key": "androidx/datastore/core/DataStoreImpl$writeActor$2.class", "name": "androidx/datastore/core/DataStoreImpl$writeActor$2.class", "size": 2477, "crc": 365923161}, {"key": "androidx/datastore/core/DataStoreImpl$writeActor$3.class", "name": "androidx/datastore/core/DataStoreImpl$writeActor$3.class", "size": 3839, "crc": -1065370002}, {"key": "androidx/datastore/core/DataStoreImpl$writeData$1.class", "name": "androidx/datastore/core/DataStoreImpl$writeData$1.class", "size": 1784, "crc": 817349546}, {"key": "androidx/datastore/core/DataStoreImpl$writeData$2.class", "name": "androidx/datastore/core/DataStoreImpl$writeData$2.class", "size": 5116, "crc": 1081215118}, {"key": "androidx/datastore/core/DataStoreImpl.class", "name": "androidx/datastore/core/DataStoreImpl.class", "size": 30161, "crc": -825258245}, {"key": "androidx/datastore/core/DataStoreInMemoryCache.class", "name": "androidx/datastore/core/DataStoreInMemoryCache.class", "size": 4169, "crc": -2105515116}, {"key": "androidx/datastore/core/FileMoves_androidKt.class", "name": "androidx/datastore/core/FileMoves_androidKt.class", "size": 1175, "crc": 1404911589}, {"key": "androidx/datastore/core/FileReadScope$readData$1.class", "name": "androidx/datastore/core/FileReadScope$readData$1.class", "size": 1884, "crc": -324268820}, {"key": "androidx/datastore/core/FileReadScope.class", "name": "androidx/datastore/core/FileReadScope.class", "size": 5906, "crc": -1348120421}, {"key": "androidx/datastore/core/FileStorage$1.class", "name": "androidx/datastore/core/FileStorage$1.class", "size": 1828, "crc": 1565585428}, {"key": "androidx/datastore/core/FileStorage$Companion.class", "name": "androidx/datastore/core/FileStorage$Companion.class", "size": 1424, "crc": -1033162239}, {"key": "androidx/datastore/core/FileStorage$createConnection$2.class", "name": "androidx/datastore/core/FileStorage$createConnection$2.class", "size": 1808, "crc": -685415958}, {"key": "androidx/datastore/core/FileStorage.class", "name": "androidx/datastore/core/FileStorage.class", "size": 5270, "crc": -236107750}, {"key": "androidx/datastore/core/FileStorageConnection$readScope$1.class", "name": "androidx/datastore/core/FileStorageConnection$readScope$1.class", "size": 1984, "crc": -2015813614}, {"key": "androidx/datastore/core/FileStorageConnection$writeScope$1.class", "name": "androidx/datastore/core/FileStorageConnection$writeScope$1.class", "size": 2006, "crc": -1537100435}, {"key": "androidx/datastore/core/FileStorageConnection.class", "name": "androidx/datastore/core/FileStorageConnection.class", "size": 13316, "crc": 1920706340}, {"key": "androidx/datastore/core/FileWriteScope$writeData$1.class", "name": "androidx/datastore/core/FileWriteScope$writeData$1.class", "size": 1776, "crc": 497686096}, {"key": "androidx/datastore/core/FileWriteScope.class", "name": "androidx/datastore/core/FileWriteScope.class", "size": 3959, "crc": 1615448392}, {"key": "androidx/datastore/core/Final.class", "name": "androidx/datastore/core/Final.class", "size": 1234, "crc": 1796962094}, {"key": "androidx/datastore/core/InitializerApi.class", "name": "androidx/datastore/core/InitializerApi.class", "size": 1154, "crc": -1116167888}, {"key": "androidx/datastore/core/InterProcessCoordinator.class", "name": "androidx/datastore/core/InterProcessCoordinator.class", "size": 2057, "crc": 242868864}, {"key": "androidx/datastore/core/InterProcessCoordinatorKt.class", "name": "androidx/datastore/core/InterProcessCoordinatorKt.class", "size": 1323, "crc": 1740643587}, {"key": "androidx/datastore/core/InterProcessCoordinator_jvmKt.class", "name": "androidx/datastore/core/InterProcessCoordinator_jvmKt.class", "size": 1269, "crc": -1085479840}, {"key": "androidx/datastore/core/Message$Read.class", "name": "androidx/datastore/core/Message$Read.class", "size": 1353, "crc": -400573172}, {"key": "androidx/datastore/core/Message$Update.class", "name": "androidx/datastore/core/Message$Update.class", "size": 3223, "crc": 1147647925}, {"key": "androidx/datastore/core/Message.class", "name": "androidx/datastore/core/Message.class", "size": 1326, "crc": 1712840020}, {"key": "androidx/datastore/core/MultiProcessCoordinator$Companion$getExclusiveFileLockWithRetryIfDeadlock$1.class", "name": "androidx/datastore/core/MultiProcessCoordinator$Companion$getExclusiveFileLockWithRetryIfDeadlock$1.class", "size": 2313, "crc": -1392567798}, {"key": "androidx/datastore/core/MultiProcessCoordinator$Companion.class", "name": "androidx/datastore/core/MultiProcessCoordinator$Companion.class", "size": 4025, "crc": 536617787}, {"key": "androidx/datastore/core/MultiProcessCoordinator$getVersion$$inlined$withLazyCounter$1.class", "name": "androidx/datastore/core/MultiProcessCoordinator$getVersion$$inlined$withLazyCounter$1.class", "size": 4600, "crc": -300187011}, {"key": "androidx/datastore/core/MultiProcessCoordinator$incrementAndGetVersion$$inlined$withLazyCounter$1.class", "name": "androidx/datastore/core/MultiProcessCoordinator$incrementAndGetVersion$$inlined$withLazyCounter$1.class", "size": 4674, "crc": 2055090433}, {"key": "androidx/datastore/core/MultiProcessCoordinator$lazySharedCounter$1$1.class", "name": "androidx/datastore/core/MultiProcessCoordinator$lazySharedCounter$1$1.class", "size": 1801, "crc": -1343515775}, {"key": "androidx/datastore/core/MultiProcessCoordinator$lazySharedCounter$1.class", "name": "androidx/datastore/core/MultiProcessCoordinator$lazySharedCounter$1.class", "size": 1849, "crc": -1602123432}, {"key": "androidx/datastore/core/MultiProcessCoordinator$lock$1.class", "name": "androidx/datastore/core/MultiProcessCoordinator$lock$1.class", "size": 1997, "crc": 1372788723}, {"key": "androidx/datastore/core/MultiProcessCoordinator$lockFile$2.class", "name": "androidx/datastore/core/MultiProcessCoordinator$lockFile$2.class", "size": 1704, "crc": 1273811206}, {"key": "androidx/datastore/core/MultiProcessCoordinator$tryLock$1.class", "name": "androidx/datastore/core/MultiProcessCoordinator$tryLock$1.class", "size": 2015, "crc": 665250414}, {"key": "androidx/datastore/core/MultiProcessCoordinator$withLazyCounter$2.class", "name": "androidx/datastore/core/MultiProcessCoordinator$withLazyCounter$2.class", "size": 4632, "crc": -1165203990}, {"key": "androidx/datastore/core/MultiProcessCoordinator.class", "name": "androidx/datastore/core/MultiProcessCoordinator.class", "size": 17710, "crc": -1641692078}, {"key": "androidx/datastore/core/MultiProcessCoordinatorKt.class", "name": "androidx/datastore/core/MultiProcessCoordinatorKt.class", "size": 1320, "crc": 126248610}, {"key": "androidx/datastore/core/MultiProcessDataStoreFactory$create$1.class", "name": "androidx/datastore/core/MultiProcessDataStoreFactory$create$1.class", "size": 2222, "crc": -2041807583}, {"key": "androidx/datastore/core/MultiProcessDataStoreFactory.class", "name": "androidx/datastore/core/MultiProcessDataStoreFactory.class", "size": 9109, "crc": 744108644}, {"key": "androidx/datastore/core/MulticastFileObserver$Companion$observe$1$1.class", "name": "androidx/datastore/core/MulticastFileObserver$Companion$observe$1$1.class", "size": 1489, "crc": 1714347236}, {"key": "androidx/datastore/core/MulticastFileObserver$Companion$observe$1$flowObserver$1.class", "name": "androidx/datastore/core/MulticastFileObserver$Companion$observe$1$flowObserver$1.class", "size": 2334, "crc": 1616316979}, {"key": "androidx/datastore/core/MulticastFileObserver$Companion$observe$1.class", "name": "androidx/datastore/core/MulticastFileObserver$Companion$observe$1.class", "size": 5048, "crc": 2080110335}, {"key": "androidx/datastore/core/MulticastFileObserver$Companion.class", "name": "androidx/datastore/core/MulticastFileObserver$Companion.class", "size": 7758, "crc": -539995196}, {"key": "androidx/datastore/core/MulticastFileObserver.class", "name": "androidx/datastore/core/MulticastFileObserver.class", "size": 4279, "crc": 1531425875}, {"key": "androidx/datastore/core/MulticastFileObserver_androidKt.class", "name": "androidx/datastore/core/MulticastFileObserver_androidKt.class", "size": 464, "crc": 251161866}, {"key": "androidx/datastore/core/MutexUtilsKt.class", "name": "androidx/datastore/core/MutexUtilsKt.class", "size": 2492, "crc": 1922233397}, {"key": "androidx/datastore/core/NativeSharedCounter.class", "name": "androidx/datastore/core/NativeSharedCounter.class", "size": 848, "crc": -886128644}, {"key": "androidx/datastore/core/ReadException.class", "name": "androidx/datastore/core/ReadException.class", "size": 1290, "crc": -1144202307}, {"key": "androidx/datastore/core/ReadScope.class", "name": "androidx/datastore/core/ReadScope.class", "size": 925, "crc": -1906775323}, {"key": "androidx/datastore/core/RunOnce$runIfNeeded$1.class", "name": "androidx/datastore/core/RunOnce$runIfNeeded$1.class", "size": 1707, "crc": -1372482783}, {"key": "androidx/datastore/core/RunOnce.class", "name": "androidx/datastore/core/RunOnce.class", "size": 4514, "crc": 2047923908}, {"key": "androidx/datastore/core/Serializer.class", "name": "androidx/datastore/core/Serializer.class", "size": 1381, "crc": -513651225}, {"key": "androidx/datastore/core/SharedCounter$Factory.class", "name": "androidx/datastore/core/SharedCounter$Factory.class", "size": 3325, "crc": -1856628565}, {"key": "androidx/datastore/core/SharedCounter.class", "name": "androidx/datastore/core/SharedCounter.class", "size": 1810, "crc": -819902634}, {"key": "androidx/datastore/core/SimpleActor$1.class", "name": "androidx/datastore/core/SimpleActor$1.class", "size": 2877, "crc": -439833891}, {"key": "androidx/datastore/core/SimpleActor$offer$2.class", "name": "androidx/datastore/core/SimpleActor$offer$2.class", "size": 4405, "crc": 1519200002}, {"key": "androidx/datastore/core/SimpleActor.class", "name": "androidx/datastore/core/SimpleActor.class", "size": 6403, "crc": 135152654}, {"key": "androidx/datastore/core/SingleProcessCoordinator$lock$1.class", "name": "androidx/datastore/core/SingleProcessCoordinator$lock$1.class", "size": 1940, "crc": 1779912129}, {"key": "androidx/datastore/core/SingleProcessCoordinator$tryLock$1.class", "name": "androidx/datastore/core/SingleProcessCoordinator$tryLock$1.class", "size": 1946, "crc": 275740937}, {"key": "androidx/datastore/core/SingleProcessCoordinator$updateNotifications$1.class", "name": "androidx/datastore/core/SingleProcessCoordinator$updateNotifications$1.class", "size": 3199, "crc": -385554682}, {"key": "androidx/datastore/core/SingleProcessCoordinator.class", "name": "androidx/datastore/core/SingleProcessCoordinator.class", "size": 8005, "crc": -1917119630}, {"key": "androidx/datastore/core/State.class", "name": "androidx/datastore/core/State.class", "size": 1173, "crc": 1457679313}, {"key": "androidx/datastore/core/Storage.class", "name": "androidx/datastore/core/Storage.class", "size": 753, "crc": 1576227059}, {"key": "androidx/datastore/core/StorageConnection.class", "name": "androidx/datastore/core/StorageConnection.class", "size": 2202, "crc": 698061721}, {"key": "androidx/datastore/core/StorageConnectionKt$readData$2.class", "name": "androidx/datastore/core/StorageConnectionKt$readData$2.class", "size": 3217, "crc": -730182230}, {"key": "androidx/datastore/core/StorageConnectionKt$writeData$2.class", "name": "androidx/datastore/core/StorageConnectionKt$writeData$2.class", "size": 3557, "crc": 1316184677}, {"key": "androidx/datastore/core/StorageConnectionKt.class", "name": "androidx/datastore/core/StorageConnectionKt.class", "size": 2409, "crc": 732503518}, {"key": "androidx/datastore/core/UnInitialized.class", "name": "androidx/datastore/core/UnInitialized.class", "size": 883, "crc": -41801921}, {"key": "androidx/datastore/core/UncloseableOutputStream.class", "name": "androidx/datastore/core/UncloseableOutputStream.class", "size": 1858, "crc": -794852431}, {"key": "androidx/datastore/core/UpdatingDataContextElement$Companion$Key.class", "name": "androidx/datastore/core/UpdatingDataContextElement$Companion$Key.class", "size": 1266, "crc": -299466748}, {"key": "androidx/datastore/core/UpdatingDataContextElement$Companion.class", "name": "androidx/datastore/core/UpdatingDataContextElement$Companion.class", "size": 1304, "crc": 1391612978}, {"key": "androidx/datastore/core/UpdatingDataContextElement.class", "name": "androidx/datastore/core/UpdatingDataContextElement.class", "size": 5107, "crc": -2083083625}, {"key": "androidx/datastore/core/WriteScope.class", "name": "androidx/datastore/core/WriteScope.class", "size": 1004, "crc": -1802350200}, {"key": "androidx/datastore/core/handlers/NoOpCorruptionHandler.class", "name": "androidx/datastore/core/handlers/NoOpCorruptionHandler.class", "size": 1512, "crc": -1876143019}, {"key": "androidx/datastore/core/handlers/ReplaceFileCorruptionHandler.class", "name": "androidx/datastore/core/handlers/ReplaceFileCorruptionHandler.class", "size": 2162, "crc": 301271595}, {"key": "META-INF/androidx.datastore_datastore-core.version", "name": "META-INF/androidx.datastore_datastore-core.version", "size": 6, "crc": 176380362}, {"key": "META-INF/datastore-core_release.kotlin_module", "name": "META-INF/datastore-core_release.kotlin_module", "size": 253, "crc": -1701857903}]