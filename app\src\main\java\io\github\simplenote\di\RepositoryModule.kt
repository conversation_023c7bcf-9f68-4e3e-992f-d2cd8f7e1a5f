package io.github.simplenote.di

import io.github.simplenote.data.repository.NoteRepositoryImpl
import io.github.simplenote.domain.repository.NoteRepository
import org.koin.dsl.module

/**
 * Koin module for repository dependencies.
 * Provides repository implementations.
 */
val repositoryModule = module {
    
    // Note Repository - Singleton
    single<NoteRepository> {
        NoteRepositoryImpl(
            noteDao = get()
        )
    }
}
