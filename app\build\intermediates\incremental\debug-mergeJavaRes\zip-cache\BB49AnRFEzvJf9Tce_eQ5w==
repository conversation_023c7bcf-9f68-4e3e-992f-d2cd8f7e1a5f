[{"key": "androidx/compose/runtime/AbstractApplier.class", "name": "androidx/compose/runtime/AbstractApplier.class", "size": 3872, "crc": -111683372}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt.class", "size": 634, "crc": 1275150599}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt__MonotonicFrameClock_androidKt$DefaultMonotonicFrameClock$2.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt__MonotonicFrameClock_androidKt$DefaultMonotonicFrameClock$2.class", "size": 1748, "crc": 184021977}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt__MonotonicFrameClock_androidKt.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt__MonotonicFrameClock_androidKt.class", "size": 1843, "crc": 992581962}, {"key": "androidx/compose/runtime/ActualJvm_jvmKt.class", "name": "androidx/compose/runtime/ActualJvm_jvmKt.class", "size": 1047, "crc": -367336357}, {"key": "androidx/compose/runtime/ActualJvm_jvmKt__OldSynchronized_androidKt.class", "name": "androidx/compose/runtime/ActualJvm_jvmKt__OldSynchronized_androidKt.class", "size": 2581, "crc": -1965669389}, {"key": "androidx/compose/runtime/Anchor.class", "name": "androidx/compose/runtime/Anchor.class", "size": 2201, "crc": 599803457}, {"key": "androidx/compose/runtime/AnchoredGroupPath.class", "name": "androidx/compose/runtime/AnchoredGroupPath.class", "size": 1315, "crc": -503871617}, {"key": "androidx/compose/runtime/Applier$DefaultImpls.class", "name": "androidx/compose/runtime/Applier$DefaultImpls.class", "size": 1636, "crc": -1114323555}, {"key": "androidx/compose/runtime/Applier.class", "name": "androidx/compose/runtime/Applier.class", "size": 2898, "crc": -664658536}, {"key": "androidx/compose/runtime/BitVector.class", "name": "androidx/compose/runtime/BitVector.class", "size": 8454, "crc": 1558235181}, {"key": "androidx/compose/runtime/BitwiseOperatorsKt.class", "name": "androidx/compose/runtime/BitwiseOperatorsKt.class", "size": 760, "crc": 302323995}, {"key": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "name": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "size": 3242, "crc": 1270627470}, {"key": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$1.class", "size": 3711, "crc": -870063566}, {"key": "androidx/compose/runtime/BroadcastFrameClock.class", "name": "androidx/compose/runtime/BroadcastFrameClock.class", "size": 12598, "crc": -1366545791}, {"key": "androidx/compose/runtime/CancelledCoroutineContext$Key.class", "name": "androidx/compose/runtime/CancelledCoroutineContext$Key.class", "size": 1172, "crc": -1091070265}, {"key": "androidx/compose/runtime/CancelledCoroutineContext.class", "name": "androidx/compose/runtime/CancelledCoroutineContext.class", "size": 3309, "crc": -792065069}, {"key": "androidx/compose/runtime/CheckResultKt.class", "name": "androidx/compose/runtime/CheckResultKt.class", "size": 384, "crc": -80176519}, {"key": "androidx/compose/runtime/Composable.class", "name": "androidx/compose/runtime/Composable.class", "size": 969, "crc": -2143352274}, {"key": "androidx/compose/runtime/ComposableInferredTarget.class", "name": "androidx/compose/runtime/ComposableInferredTarget.class", "size": 1045, "crc": -358309323}, {"key": "androidx/compose/runtime/ComposableOpenTarget.class", "name": "androidx/compose/runtime/ComposableOpenTarget.class", "size": 980, "crc": -1886432699}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-1$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-1$1.class", "size": 2152, "crc": 1193044938}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-2$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-2$1.class", "size": 2152, "crc": 764309896}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "size": 1838, "crc": 990617523}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda-1$1.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda-1$1.class", "size": 2145, "crc": -1662317991}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "size": 1493, "crc": -1466508134}, {"key": "androidx/compose/runtime/ComposableTarget.class", "name": "androidx/compose/runtime/ComposableTarget.class", "size": 1009, "crc": -177990591}, {"key": "androidx/compose/runtime/ComposableTargetMarker.class", "name": "androidx/compose/runtime/ComposableTargetMarker.class", "size": 979, "crc": 177650531}, {"key": "androidx/compose/runtime/ComposablesKt.class", "name": "androidx/compose/runtime/ComposablesKt.class", "size": 18588, "crc": 1526281324}, {"key": "androidx/compose/runtime/ComposeCompilerApi.class", "name": "androidx/compose/runtime/ComposeCompilerApi.class", "size": 798, "crc": -267243505}, {"key": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "name": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "size": 556, "crc": -1172920673}, {"key": "androidx/compose/runtime/ComposeRuntimeError.class", "name": "androidx/compose/runtime/ComposeRuntimeError.class", "size": 1155, "crc": 191559452}, {"key": "androidx/compose/runtime/ComposeRuntimeFlags.class", "name": "androidx/compose/runtime/ComposeRuntimeFlags.class", "size": 1143, "crc": 177182088}, {"key": "androidx/compose/runtime/ComposeVersion.class", "name": "androidx/compose/runtime/ComposeVersion.class", "size": 882, "crc": -35351567}, {"key": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "name": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "size": 832, "crc": -1087790129}, {"key": "androidx/compose/runtime/Composer$Companion.class", "name": "androidx/compose/runtime/Composer$Companion.class", "size": 1536, "crc": -1842247595}, {"key": "androidx/compose/runtime/Composer.class", "name": "androidx/compose/runtime/Composer.class", "size": 9841, "crc": -454389440}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "size": 1905, "crc": 1282677844}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "size": 15911, "crc": 1712319564}, {"key": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "name": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "size": 1804, "crc": 620942302}, {"key": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$1$1.class", "name": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$1$1.class", "size": 6046, "crc": 1466118893}, {"key": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$2$1$1$1$1.class", "name": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$2$1$1$1$1.class", "size": 1947, "crc": 314152905}, {"key": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "name": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "size": 2886, "crc": 1668253799}, {"key": "androidx/compose/runtime/ComposerImpl.class", "name": "androidx/compose/runtime/ComposerImpl.class", "size": 109708, "crc": -662832801}, {"key": "androidx/compose/runtime/ComposerKt$extractMovableContentAtCurrent$movableContentRecomposeScopeOwner$1.class", "name": "androidx/compose/runtime/ComposerKt$extractMovableContentAtCurrent$movableContentRecomposeScopeOwner$1.class", "size": 2957, "crc": -305996923}, {"key": "androidx/compose/runtime/ComposerKt.class", "name": "androidx/compose/runtime/ComposerKt.class", "size": 32563, "crc": -84859379}, {"key": "androidx/compose/runtime/Composition.class", "name": "androidx/compose/runtime/Composition.class", "size": 991, "crc": -1213119087}, {"key": "androidx/compose/runtime/CompositionContext.class", "name": "androidx/compose/runtime/CompositionContext.class", "size": 7585, "crc": 856047612}, {"key": "androidx/compose/runtime/CompositionContextKt.class", "name": "androidx/compose/runtime/CompositionContextKt.class", "size": 1044, "crc": 1046706402}, {"key": "androidx/compose/runtime/CompositionDataImpl.class", "name": "androidx/compose/runtime/CompositionDataImpl.class", "size": 7813, "crc": -489311693}, {"key": "androidx/compose/runtime/CompositionImpl$observe$2.class", "name": "androidx/compose/runtime/CompositionImpl$observe$2.class", "size": 3159, "crc": -1884987091}, {"key": "androidx/compose/runtime/CompositionImpl.class", "name": "androidx/compose/runtime/CompositionImpl.class", "size": 90107, "crc": -835027084}, {"key": "androidx/compose/runtime/CompositionKt$CompositionImplServiceKey$1.class", "name": "androidx/compose/runtime/CompositionKt$CompositionImplServiceKey$1.class", "size": 966, "crc": -758166684}, {"key": "androidx/compose/runtime/CompositionKt.class", "name": "androidx/compose/runtime/CompositionKt.class", "size": 7462, "crc": 1044431753}, {"key": "androidx/compose/runtime/CompositionLocal.class", "name": "androidx/compose/runtime/CompositionLocal.class", "size": 3398, "crc": 686892745}, {"key": "androidx/compose/runtime/CompositionLocalAccessorScope.class", "name": "androidx/compose/runtime/CompositionLocalAccessorScope.class", "size": 837, "crc": **********}, {"key": "androidx/compose/runtime/CompositionLocalContext.class", "name": "androidx/compose/runtime/CompositionLocalContext.class", "size": 1195, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$1.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$1.class", "size": 2267, "crc": 779898534}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$2.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$2.class", "size": 2162, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$4.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$4.class", "size": 2147, "crc": 422134210}, {"key": "androidx/compose/runtime/CompositionLocalKt.class", "name": "androidx/compose/runtime/CompositionLocalKt.class", "size": 10425, "crc": -403219414}, {"key": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "name": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "size": 1289, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalMap.class", "name": "androidx/compose/runtime/CompositionLocalMap.class", "size": 1183, "crc": 795781295}, {"key": "androidx/compose/runtime/CompositionLocalMapKt.class", "name": "androidx/compose/runtime/CompositionLocalMapKt.class", "size": 6847, "crc": **********}, {"key": "androidx/compose/runtime/CompositionObserverHolder.class", "name": "androidx/compose/runtime/CompositionObserverHolder.class", "size": 2030, "crc": **********}, {"key": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "name": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "size": 2124, "crc": -219097093}, {"key": "androidx/compose/runtime/CompositionServiceKey.class", "name": "androidx/compose/runtime/CompositionServiceKey.class", "size": 488, "crc": -315792851}, {"key": "androidx/compose/runtime/CompositionServices.class", "name": "androidx/compose/runtime/CompositionServices.class", "size": 905, "crc": -272696927}, {"key": "androidx/compose/runtime/CompositionTracer.class", "name": "androidx/compose/runtime/CompositionTracer.class", "size": 863, "crc": 1185774143}, {"key": "androidx/compose/runtime/ComputedProvidableCompositionLocal$1.class", "name": "androidx/compose/runtime/ComputedProvidableCompositionLocal$1.class", "size": 1273, "crc": -1279058807}, {"key": "androidx/compose/runtime/ComputedProvidableCompositionLocal.class", "name": "androidx/compose/runtime/ComputedProvidableCompositionLocal.class", "size": 3224, "crc": 703474941}, {"key": "androidx/compose/runtime/ComputedValueHolder.class", "name": "androidx/compose/runtime/ComputedValueHolder.class", "size": 4634, "crc": 949613100}, {"key": "androidx/compose/runtime/ControlledComposition.class", "name": "androidx/compose/runtime/ControlledComposition.class", "size": 3402, "crc": -215500971}, {"key": "androidx/compose/runtime/DataIterator.class", "name": "androidx/compose/runtime/DataIterator.class", "size": 3856, "crc": -1025768098}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "size": 3109, "crc": 768309394}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "size": 1744, "crc": -101828571}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "size": 3047, "crc": -2036226579}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "size": 7050, "crc": -769979914}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "size": 1200, "crc": 1212524853}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "size": 13291, "crc": 241350176}, {"key": "androidx/compose/runtime/DerivedSnapshotState$currentRecord$result$1$1$result$1.class", "name": "androidx/compose/runtime/DerivedSnapshotState$currentRecord$result$1$1$result$1.class", "size": 3037, "crc": -505627463}, {"key": "androidx/compose/runtime/DerivedSnapshotState.class", "name": "androidx/compose/runtime/DerivedSnapshotState.class", "size": 18474, "crc": 1288654346}, {"key": "androidx/compose/runtime/DerivedState$Record.class", "name": "androidx/compose/runtime/DerivedState$Record.class", "size": 1079, "crc": 1782872350}, {"key": "androidx/compose/runtime/DerivedState.class", "name": "androidx/compose/runtime/DerivedState.class", "size": 1319, "crc": 755196201}, {"key": "androidx/compose/runtime/DerivedStateObserver.class", "name": "androidx/compose/runtime/DerivedStateObserver.class", "size": 793, "crc": -1673462835}, {"key": "androidx/compose/runtime/DisallowComposableCalls.class", "name": "androidx/compose/runtime/DisallowComposableCalls.class", "size": 928, "crc": 1063203462}, {"key": "androidx/compose/runtime/DisposableEffectImpl.class", "name": "androidx/compose/runtime/DisposableEffectImpl.class", "size": 2205, "crc": -1999229906}, {"key": "androidx/compose/runtime/DisposableEffectResult.class", "name": "androidx/compose/runtime/DisposableEffectResult.class", "size": 453, "crc": 268640175}, {"key": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "name": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "size": 1309, "crc": -965641650}, {"key": "androidx/compose/runtime/DisposableEffectScope.class", "name": "androidx/compose/runtime/DisposableEffectScope.class", "size": 1527, "crc": 1294402732}, {"key": "androidx/compose/runtime/DontMemoize.class", "name": "androidx/compose/runtime/DontMemoize.class", "size": 754, "crc": 74531887}, {"key": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "size": 1033, "crc": -1909754821}, {"key": "androidx/compose/runtime/DoubleState.class", "name": "androidx/compose/runtime/DoubleState.class", "size": 1494, "crc": -459239180}, {"key": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "name": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "size": 2481, "crc": -1274952746}, {"key": "androidx/compose/runtime/DynamicValueHolder.class", "name": "androidx/compose/runtime/DynamicValueHolder.class", "size": 4271, "crc": -79201362}, {"key": "androidx/compose/runtime/EffectsKt$LaunchedEffect$1.class", "name": "androidx/compose/runtime/EffectsKt$LaunchedEffect$1.class", "size": 1897, "crc": -1292758463}, {"key": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "name": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "size": 1274, "crc": 413619273}, {"key": "androidx/compose/runtime/EffectsKt.class", "name": "androidx/compose/runtime/EffectsKt.class", "size": 19337, "crc": -770931568}, {"key": "androidx/compose/runtime/ExperimentalComposeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeApi.class", "size": 1223, "crc": -511101709}, {"key": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "size": 1247, "crc": 1888576465}, {"key": "androidx/compose/runtime/ExplicitGroupsComposable.class", "name": "androidx/compose/runtime/ExplicitGroupsComposable.class", "size": 865, "crc": 957680310}, {"key": "androidx/compose/runtime/FallbackFrameClock$withFrameNanos$2.class", "name": "androidx/compose/runtime/FallbackFrameClock$withFrameNanos$2.class", "size": 3581, "crc": 1827701348}, {"key": "androidx/compose/runtime/FallbackFrameClock.class", "name": "androidx/compose/runtime/FallbackFrameClock.class", "size": 4193, "crc": 1715911875}, {"key": "androidx/compose/runtime/FloatState$DefaultImpls.class", "name": "androidx/compose/runtime/FloatState$DefaultImpls.class", "size": 1023, "crc": -605803671}, {"key": "androidx/compose/runtime/FloatState.class", "name": "androidx/compose/runtime/FloatState.class", "size": 1483, "crc": -1827190565}, {"key": "androidx/compose/runtime/ForgottenCoroutineScopeException.class", "name": "androidx/compose/runtime/ForgottenCoroutineScopeException.class", "size": 766, "crc": -718373244}, {"key": "androidx/compose/runtime/GroupInfo.class", "name": "androidx/compose/runtime/GroupInfo.class", "size": 1349, "crc": -255569287}, {"key": "androidx/compose/runtime/GroupIterator.class", "name": "androidx/compose/runtime/GroupIterator.class", "size": 2809, "crc": -1946997700}, {"key": "androidx/compose/runtime/GroupKind$Companion.class", "name": "androidx/compose/runtime/GroupKind$Companion.class", "size": 1372, "crc": -2069592825}, {"key": "androidx/compose/runtime/GroupKind.class", "name": "androidx/compose/runtime/GroupKind.class", "size": 3210, "crc": 1699570718}, {"key": "androidx/compose/runtime/GroupSourceInformation.class", "name": "androidx/compose/runtime/GroupSourceInformation.class", "size": 8365, "crc": -1522116576}, {"key": "androidx/compose/runtime/HotReloader$Companion.class", "name": "androidx/compose/runtime/HotReloader$Companion.class", "size": 2558, "crc": -2125900290}, {"key": "androidx/compose/runtime/HotReloader.class", "name": "androidx/compose/runtime/HotReloader.class", "size": 883, "crc": 596399683}, {"key": "androidx/compose/runtime/HotReloaderKt.class", "name": "androidx/compose/runtime/HotReloaderKt.class", "size": 5641, "crc": 1835615644}, {"key": "androidx/compose/runtime/Immutable.class", "name": "androidx/compose/runtime/Immutable.class", "size": 959, "crc": 479953821}, {"key": "androidx/compose/runtime/IntStack.class", "name": "androidx/compose/runtime/IntStack.class", "size": 2807, "crc": 586406777}, {"key": "androidx/compose/runtime/IntState$DefaultImpls.class", "name": "androidx/compose/runtime/IntState$DefaultImpls.class", "size": 1015, "crc": -612768950}, {"key": "androidx/compose/runtime/IntState.class", "name": "androidx/compose/runtime/IntState.class", "size": 1477, "crc": -704613906}, {"key": "androidx/compose/runtime/InternalComposeApi.class", "name": "androidx/compose/runtime/InternalComposeApi.class", "size": 1189, "crc": -1684373844}, {"key": "androidx/compose/runtime/InternalComposeTracingApi.class", "name": "androidx/compose/runtime/InternalComposeTracingApi.class", "size": 955, "crc": 1579020570}, {"key": "androidx/compose/runtime/Invalidation.class", "name": "androidx/compose/runtime/Invalidation.class", "size": 1818, "crc": 97403466}, {"key": "androidx/compose/runtime/InvalidationResult.class", "name": "androidx/compose/runtime/InvalidationResult.class", "size": 1976, "crc": -377820250}, {"key": "androidx/compose/runtime/JoinedKey.class", "name": "androidx/compose/runtime/JoinedKey.class", "size": 3023, "crc": 223089134}, {"key": "androidx/compose/runtime/KeyInfo.class", "name": "androidx/compose/runtime/KeyInfo.class", "size": 1702, "crc": -77109642}, {"key": "androidx/compose/runtime/Latch$await$2$2.class", "name": "androidx/compose/runtime/Latch$await$2$2.class", "size": 2937, "crc": 639967682}, {"key": "androidx/compose/runtime/Latch.class", "name": "androidx/compose/runtime/Latch.class", "size": 6825, "crc": -1045207677}, {"key": "androidx/compose/runtime/LaunchedEffectImpl.class", "name": "androidx/compose/runtime/LaunchedEffectImpl.class", "size": 3227, "crc": -304000862}, {"key": "androidx/compose/runtime/LazyValueHolder.class", "name": "androidx/compose/runtime/LazyValueHolder.class", "size": 2655, "crc": 609406037}, {"key": "androidx/compose/runtime/LeftCompositionCancellationException.class", "name": "androidx/compose/runtime/LeftCompositionCancellationException.class", "size": 771, "crc": 1867830748}, {"key": "androidx/compose/runtime/LongState$DefaultImpls.class", "name": "androidx/compose/runtime/LongState$DefaultImpls.class", "size": 1013, "crc": 1107990236}, {"key": "androidx/compose/runtime/LongState.class", "name": "androidx/compose/runtime/LongState.class", "size": 1472, "crc": 1360172776}, {"key": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "name": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "size": 3397, "crc": 1483458275}, {"key": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "name": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "size": 1051, "crc": -376719679}, {"key": "androidx/compose/runtime/MonotonicFrameClock.class", "name": "androidx/compose/runtime/MonotonicFrameClock.class", "size": 2118, "crc": 1610391038}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "size": 1693, "crc": -505037878}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt.class", "size": 4524, "crc": 1769248}, {"key": "androidx/compose/runtime/MovableContent.class", "name": "androidx/compose/runtime/MovableContent.class", "size": 1788, "crc": 1319292336}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "size": 2363, "crc": -506380880}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "size": 2859, "crc": -305562726}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "size": 3203, "crc": 451261110}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "size": 3457, "crc": 151946528}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "size": 3607, "crc": -2089598496}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "size": 2558, "crc": 1825273558}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "size": 3284, "crc": -933156704}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "size": 3378, "crc": -1159932042}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "size": 3134, "crc": 265678037}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "size": 2909, "crc": 1489774410}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "size": 3253, "crc": -450540937}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "size": 3502, "crc": -2027656529}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "size": 3658, "crc": 77203548}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "size": 3031, "crc": -1607120648}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "size": 3324, "crc": 1198223953}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "size": 3421, "crc": -726679989}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "size": 3178, "crc": -1455332494}, {"key": "androidx/compose/runtime/MovableContentKt.class", "name": "androidx/compose/runtime/MovableContentKt.class", "size": 8593, "crc": 1187759212}, {"key": "androidx/compose/runtime/MovableContentState$extractNestedStates$referencesToExtract$2.class", "name": "androidx/compose/runtime/MovableContentState$extractNestedStates$referencesToExtract$2.class", "size": 2017, "crc": 1874236343}, {"key": "androidx/compose/runtime/MovableContentState.class", "name": "androidx/compose/runtime/MovableContentState.class", "size": 9169, "crc": -1019361587}, {"key": "androidx/compose/runtime/MovableContentStateReference.class", "name": "androidx/compose/runtime/MovableContentStateReference.class", "size": 4938, "crc": 1557400807}, {"key": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "size": 1300, "crc": -582416815}, {"key": "androidx/compose/runtime/MutableDoubleState.class", "name": "androidx/compose/runtime/MutableDoubleState.class", "size": 2177, "crc": 214386118}, {"key": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "size": 1289, "crc": 1818458771}, {"key": "androidx/compose/runtime/MutableFloatState.class", "name": "androidx/compose/runtime/MutableFloatState.class", "size": 2161, "crc": 1871252333}, {"key": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "size": 1279, "crc": 132268817}, {"key": "androidx/compose/runtime/MutableIntState.class", "name": "androidx/compose/runtime/MutableIntState.class", "size": 2145, "crc": 335110288}, {"key": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "size": 1278, "crc": -1032523175}, {"key": "androidx/compose/runtime/MutableLongState.class", "name": "androidx/compose/runtime/MutableLongState.class", "size": 2145, "crc": 1620515517}, {"key": "androidx/compose/runtime/MutableState.class", "name": "androidx/compose/runtime/MutableState.class", "size": 1124, "crc": -1807567960}, {"key": "androidx/compose/runtime/NestedContentMap$usedContainer$1$1.class", "name": "androidx/compose/runtime/NestedContentMap$usedContainer$1$1.class", "size": 1733, "crc": -4237530}, {"key": "androidx/compose/runtime/NestedContentMap.class", "name": "androidx/compose/runtime/NestedContentMap.class", "size": 6205, "crc": 1712116237}, {"key": "androidx/compose/runtime/NestedMovableContent.class", "name": "androidx/compose/runtime/NestedMovableContent.class", "size": 1338, "crc": 917192583}, {"key": "androidx/compose/runtime/NeverEqualPolicy.class", "name": "androidx/compose/runtime/NeverEqualPolicy.class", "size": 1398, "crc": -64106612}, {"key": "androidx/compose/runtime/NoLiveLiterals.class", "name": "androidx/compose/runtime/NoLiveLiterals.class", "size": 865, "crc": -540016753}, {"key": "androidx/compose/runtime/NonRestartableComposable.class", "name": "androidx/compose/runtime/NonRestartableComposable.class", "size": 865, "crc": -690896654}, {"key": "androidx/compose/runtime/NonSkippableComposable.class", "name": "androidx/compose/runtime/NonSkippableComposable.class", "size": 859, "crc": 494713255}, {"key": "androidx/compose/runtime/OffsetApplier.class", "name": "androidx/compose/runtime/OffsetApplier.class", "size": 3968, "crc": -328740992}, {"key": "androidx/compose/runtime/OpaqueKey.class", "name": "androidx/compose/runtime/OpaqueKey.class", "size": 2287, "crc": 28257742}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "size": 1854, "crc": -1510977037}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "size": 1145, "crc": -127848627}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "size": 2169, "crc": 1989597086}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "size": 1845, "crc": 1837142111}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "size": 1140, "crc": -484774680}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "size": 2158, "crc": 983993010}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "size": 1827, "crc": -1575311667}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "size": 1130, "crc": 23916344}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "size": 2116, "crc": 480424887}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "size": 1836, "crc": 1761901042}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "size": 1135, "crc": -490175960}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "size": 2147, "crc": -1892175107}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "size": 3917, "crc": 489614729}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "size": 1275, "crc": -119863565}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "size": 3433, "crc": -978611137}, {"key": "androidx/compose/runtime/PausableComposition.class", "name": "androidx/compose/runtime/PausableComposition.class", "size": 1294, "crc": -851099016}, {"key": "androidx/compose/runtime/PausableCompositionKt.class", "name": "androidx/compose/runtime/PausableCompositionKt.class", "size": 1399, "crc": -765174412}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "size": 1977, "crc": 1140270636}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "size": 5468, "crc": -75026992}, {"key": "androidx/compose/runtime/PausedComposition.class", "name": "androidx/compose/runtime/PausedComposition.class", "size": 869, "crc": -1487493942}, {"key": "androidx/compose/runtime/PausedCompositionImpl$WhenMappings.class", "name": "androidx/compose/runtime/PausedCompositionImpl$WhenMappings.class", "size": 1065, "crc": -153889117}, {"key": "androidx/compose/runtime/PausedCompositionImpl.class", "name": "androidx/compose/runtime/PausedCompositionImpl.class", "size": 10328, "crc": -932040469}, {"key": "androidx/compose/runtime/PausedCompositionState.class", "name": "androidx/compose/runtime/PausedCompositionState.class", "size": 2157, "crc": -2044597194}, {"key": "androidx/compose/runtime/Pending$keyMap$2.class", "name": "androidx/compose/runtime/Pending$keyMap$2.class", "size": 2421, "crc": -1774351236}, {"key": "androidx/compose/runtime/Pending.class", "name": "androidx/compose/runtime/Pending.class", "size": 13830, "crc": 1807872622}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "size": 1449, "crc": 1840434137}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "size": 2777, "crc": 1175297251}, {"key": "androidx/compose/runtime/PreconditionsKt.class", "name": "androidx/compose/runtime/PreconditionsKt.class", "size": 1878, "crc": 459946676}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "size": 1641, "crc": 225891809}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "size": 2169, "crc": 606570798}, {"key": "androidx/compose/runtime/PrioritySet.class", "name": "androidx/compose/runtime/PrioritySet.class", "size": 7464, "crc": -332819666}, {"key": "androidx/compose/runtime/ProduceFrameSignal.class", "name": "androidx/compose/runtime/ProduceFrameSignal.class", "size": 6271, "crc": 1048143900}, {"key": "androidx/compose/runtime/ProduceStateScope.class", "name": "androidx/compose/runtime/ProduceStateScope.class", "size": 1244, "crc": -1410383939}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "size": 1864, "crc": 663876092}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl.class", "size": 5646, "crc": 1432672591}, {"key": "androidx/compose/runtime/ProvidableCompositionLocal.class", "name": "androidx/compose/runtime/ProvidableCompositionLocal.class", "size": 5481, "crc": 1950898315}, {"key": "androidx/compose/runtime/ProvidedValue.class", "name": "androidx/compose/runtime/ProvidedValue.class", "size": 5840, "crc": -1958859489}, {"key": "androidx/compose/runtime/ReadOnlyComposable.class", "name": "androidx/compose/runtime/ReadOnlyComposable.class", "size": 938, "crc": 1940531248}, {"key": "androidx/compose/runtime/RecomposeScope.class", "name": "androidx/compose/runtime/RecomposeScope.class", "size": 451, "crc": -1718686348}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "size": 4938, "crc": -899331304}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$end$1$2.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$end$1$2.class", "size": 5671, "crc": -120543468}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$observe$2.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$observe$2.class", "size": 3059, "crc": -616201679}, {"key": "androidx/compose/runtime/RecomposeScopeImpl.class", "name": "androidx/compose/runtime/RecomposeScopeImpl.class", "size": 21094, "crc": -1377669537}, {"key": "androidx/compose/runtime/RecomposeScopeImplKt.class", "name": "androidx/compose/runtime/RecomposeScopeImplKt.class", "size": 2625, "crc": -889717514}, {"key": "androidx/compose/runtime/RecomposeScopeOwner.class", "name": "androidx/compose/runtime/RecomposeScopeOwner.class", "size": 1116, "crc": 1738840822}, {"key": "androidx/compose/runtime/Recomposer$Companion.class", "name": "androidx/compose/runtime/Recomposer$Companion.class", "size": 11219, "crc": -860113040}, {"key": "androidx/compose/runtime/Recomposer$HotReloadable.class", "name": "androidx/compose/runtime/Recomposer$HotReloadable.class", "size": 2056, "crc": -25793491}, {"key": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "name": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "size": 1342, "crc": 158554491}, {"key": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "name": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "size": 9019, "crc": -1030517152}, {"key": "androidx/compose/runtime/Recomposer$State.class", "name": "androidx/compose/runtime/Recomposer$State.class", "size": 2183, "crc": -340347638}, {"key": "androidx/compose/runtime/Recomposer$addCompositionRegistrationObserver$2.class", "name": "androidx/compose/runtime/Recomposer$addCompositionRegistrationObserver$2.class", "size": 3122, "crc": -2145899758}, {"key": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "name": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "size": 3193, "crc": 1202060896}, {"key": "androidx/compose/runtime/Recomposer$broadcastFrameClock$1.class", "name": "androidx/compose/runtime/Recomposer$broadcastFrameClock$1.class", "size": 3882, "crc": 1038147247}, {"key": "androidx/compose/runtime/Recomposer$effectJob$1$1$1$1.class", "name": "androidx/compose/runtime/Recomposer$effectJob$1$1$1$1.class", "size": 3878, "crc": 98932311}, {"key": "androidx/compose/runtime/Recomposer$effectJob$1$1.class", "name": "androidx/compose/runtime/Recomposer$effectJob$1$1.class", "size": 4753, "crc": -71795159}, {"key": "androidx/compose/runtime/Recomposer$join$2.class", "name": "androidx/compose/runtime/Recomposer$join$2.class", "size": 3102, "crc": 210203521}, {"key": "androidx/compose/runtime/Recomposer$performRecompose$1$1.class", "name": "androidx/compose/runtime/Recomposer$performRecompose$1$1.class", "size": 4112, "crc": -12519839}, {"key": "androidx/compose/runtime/Recomposer$readObserverOf$1.class", "name": "androidx/compose/runtime/Recomposer$readObserverOf$1.class", "size": 1477, "crc": 51203694}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2$3.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2$3.class", "size": 3836, "crc": -360817862}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2$unregisterApplyObserver$1.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2$unregisterApplyObserver$1.class", "size": 8539, "crc": 511571246}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "size": 9362, "crc": -1615610522}, {"key": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "size": 2165, "crc": -273939658}, {"key": "androidx/compose/runtime/Recomposer$runFrameLoop$2.class", "name": "androidx/compose/runtime/Recomposer$runFrameLoop$2.class", "size": 9565, "crc": -924521094}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2$1.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2$1.class", "size": 18088, "crc": 1282318251}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "size": 14904, "crc": 577223178}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "size": 6127, "crc": 393670084}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "size": 3913, "crc": -1009840309}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "size": 14920, "crc": -2040523675}, {"key": "androidx/compose/runtime/Recomposer$writeObserverOf$1.class", "name": "androidx/compose/runtime/Recomposer$writeObserverOf$1.class", "size": 1980, "crc": -836197491}, {"key": "androidx/compose/runtime/Recomposer.class", "name": "androidx/compose/runtime/Recomposer.class", "size": 88617, "crc": -1684239587}, {"key": "androidx/compose/runtime/RecomposerErrorInfo.class", "name": "androidx/compose/runtime/RecomposerErrorInfo.class", "size": 725, "crc": -2112726515}, {"key": "androidx/compose/runtime/RecomposerInfo.class", "name": "androidx/compose/runtime/RecomposerInfo.class", "size": 1076, "crc": 1210872765}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "size": 3286, "crc": 1048146978}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "size": 4741, "crc": 400547848}, {"key": "androidx/compose/runtime/RecomposerKt.class", "name": "androidx/compose/runtime/RecomposerKt.class", "size": 2342, "crc": -1054548551}, {"key": "androidx/compose/runtime/RecordingApplier$Companion.class", "name": "androidx/compose/runtime/RecordingApplier$Companion.class", "size": 1137, "crc": 576012155}, {"key": "androidx/compose/runtime/RecordingApplier.class", "name": "androidx/compose/runtime/RecordingApplier.class", "size": 7678, "crc": 962349133}, {"key": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "name": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "size": 1461, "crc": -1016362586}, {"key": "androidx/compose/runtime/RelativeGroupPath.class", "name": "androidx/compose/runtime/RelativeGroupPath.class", "size": 1651, "crc": 1321864790}, {"key": "androidx/compose/runtime/RememberManager.class", "name": "androidx/compose/runtime/RememberManager.class", "size": 1708, "crc": -1001949028}, {"key": "androidx/compose/runtime/RememberObserver.class", "name": "androidx/compose/runtime/RememberObserver.class", "size": 526, "crc": 1706384725}, {"key": "androidx/compose/runtime/RememberObserverHolder.class", "name": "androidx/compose/runtime/RememberObserverHolder.class", "size": 1818, "crc": -1999073946}, {"key": "androidx/compose/runtime/RememberedCoroutineScope$Companion.class", "name": "androidx/compose/runtime/RememberedCoroutineScope$Companion.class", "size": 952, "crc": 1322990431}, {"key": "androidx/compose/runtime/RememberedCoroutineScope.class", "name": "androidx/compose/runtime/RememberedCoroutineScope.class", "size": 5731, "crc": 1044964274}, {"key": "androidx/compose/runtime/ReusableComposition.class", "name": "androidx/compose/runtime/ReusableComposition.class", "size": 1076, "crc": -752462214}, {"key": "androidx/compose/runtime/ReusableRememberObserver.class", "name": "androidx/compose/runtime/ReusableRememberObserver.class", "size": 502, "crc": -191479134}, {"key": "androidx/compose/runtime/ScopeInvalidated.class", "name": "androidx/compose/runtime/ScopeInvalidated.class", "size": 804, "crc": -887159039}, {"key": "androidx/compose/runtime/ScopeUpdateScope.class", "name": "androidx/compose/runtime/ScopeUpdateScope.class", "size": 869, "crc": -1117005563}, {"key": "androidx/compose/runtime/ShouldPauseCallback.class", "name": "androidx/compose/runtime/ShouldPauseCallback.class", "size": 467, "crc": 256877608}, {"key": "androidx/compose/runtime/SkippableUpdater.class", "name": "androidx/compose/runtime/SkippableUpdater.class", "size": 3961, "crc": -2091231572}, {"key": "androidx/compose/runtime/SlotReader.class", "name": "androidx/compose/runtime/SlotReader.class", "size": 20430, "crc": 1171678931}, {"key": "androidx/compose/runtime/SlotTable.class", "name": "androidx/compose/runtime/SlotTable.class", "size": 40055, "crc": 1100925116}, {"key": "androidx/compose/runtime/SlotTableGroup.class", "name": "androidx/compose/runtime/SlotTableGroup.class", "size": 9679, "crc": 1062798877}, {"key": "androidx/compose/runtime/SlotTableKt.class", "name": "androidx/compose/runtime/SlotTableKt.class", "size": 21405, "crc": 654511503}, {"key": "androidx/compose/runtime/SlotWriter$Companion.class", "name": "androidx/compose/runtime/SlotWriter$Companion.class", "size": 12395, "crc": -383072023}, {"key": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "name": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "size": 2056, "crc": -1944533695}, {"key": "androidx/compose/runtime/SlotWriter.class", "name": "androidx/compose/runtime/SlotWriter.class", "size": 74279, "crc": -1803061180}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "size": 1645, "crc": 1002412468}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "size": 2179, "crc": 1727651443}, {"key": "androidx/compose/runtime/SnapshotDoubleState_androidKt.class", "name": "androidx/compose/runtime/SnapshotDoubleState_androidKt.class", "size": 871, "crc": 2094797868}, {"key": "androidx/compose/runtime/SnapshotFloatState_androidKt.class", "name": "androidx/compose/runtime/SnapshotFloatState_androidKt.class", "size": 864, "crc": -2109040409}, {"key": "androidx/compose/runtime/SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt.class", "size": 1609, "crc": -132995982}, {"key": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "size": 2121, "crc": 2010140985}, {"key": "androidx/compose/runtime/SnapshotIntState_androidKt.class", "name": "androidx/compose/runtime/SnapshotIntState_androidKt.class", "size": 850, "crc": 1645175068}, {"key": "androidx/compose/runtime/SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt.class", "size": 1621, "crc": 443065313}, {"key": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "size": 2143, "crc": -451979984}, {"key": "androidx/compose/runtime/SnapshotLongState_androidKt.class", "name": "androidx/compose/runtime/SnapshotLongState_androidKt.class", "size": 857, "crc": 887472508}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "size": 2136, "crc": 184689577}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$component2$1.class", "size": 1499, "crc": 1240094638}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "size": 9907, "crc": -332008376}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "size": 2291, "crc": -899880242}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$component2$1.class", "size": 1490, "crc": 230913877}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "size": 9861, "crc": 1694199933}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "size": 2273, "crc": 1330161381}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl$component2$1.class", "size": 1469, "crc": -1120739298}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "size": 9496, "crc": 920205686}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "size": 2255, "crc": 1261847597}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl$component2$1.class", "size": 1481, "crc": 1970995938}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "size": 9030, "crc": 2059134359}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "size": 3064, "crc": -273691087}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl$component2$1.class", "size": 1561, "crc": -883968893}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "size": 10182, "crc": -550811658}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "size": 1143, "crc": 801956204}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy.class", "size": 1462, "crc": 1723465144}, {"key": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "name": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "size": 2671, "crc": -84004476}, {"key": "androidx/compose/runtime/SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt.class", "size": 11717, "crc": -587610677}, {"key": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "size": 8124, "crc": 1592342456}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1$1.class", "size": 4117, "crc": -1760613687}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2$1.class", "size": 4135, "crc": 1464143753}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3$1.class", "size": 4153, "crc": 170381960}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4$1.class", "size": 4171, "crc": -1776398964}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5$1.class", "size": 4136, "crc": -1934709782}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "size": 13091, "crc": 1349004539}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$1.class", "size": 1736, "crc": 723243761}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2$1.class", "size": 1831, "crc": -565145952}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2.class", "size": 3911, "crc": -2139651547}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "size": 4816, "crc": 353106502}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$readObserver$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$readObserver$1.class", "size": 3036, "crc": -425665947}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$unregisterApplyObserver$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$unregisterApplyObserver$1.class", "size": 7368, "crc": 1608223161}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "size": 9702, "crc": 514666235}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "size": 8752, "crc": -1215432256}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "size": 2137, "crc": -300737736}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "size": 10056, "crc": 1162538010}, {"key": "androidx/compose/runtime/SnapshotState_androidKt.class", "name": "androidx/compose/runtime/SnapshotState_androidKt.class", "size": 1369, "crc": 218073612}, {"key": "androidx/compose/runtime/SourceInformationGroupDataIterator.class", "name": "androidx/compose/runtime/SourceInformationGroupDataIterator.class", "size": 5346, "crc": 1221383073}, {"key": "androidx/compose/runtime/SourceInformationGroupIterator.class", "name": "androidx/compose/runtime/SourceInformationGroupIterator.class", "size": 4664, "crc": 1565796563}, {"key": "androidx/compose/runtime/SourceInformationGroupPath.class", "name": "androidx/compose/runtime/SourceInformationGroupPath.class", "size": 1145, "crc": 2027431769}, {"key": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "name": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "size": 4685, "crc": -1999717621}, {"key": "androidx/compose/runtime/SourceInformationSlotTableGroupIdentity.class", "name": "androidx/compose/runtime/SourceInformationSlotTableGroupIdentity.class", "size": 2820, "crc": -494251202}, {"key": "androidx/compose/runtime/Stable.class", "name": "androidx/compose/runtime/Stable.class", "size": 1019, "crc": -1871315059}, {"key": "androidx/compose/runtime/StableMarker.class", "name": "androidx/compose/runtime/StableMarker.class", "size": 931, "crc": -234614667}, {"key": "androidx/compose/runtime/Stack.class", "name": "androidx/compose/runtime/Stack.class", "size": 4949, "crc": -1302048859}, {"key": "androidx/compose/runtime/State.class", "name": "androidx/compose/runtime/State.class", "size": 637, "crc": 1638048454}, {"key": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "name": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "size": 2100, "crc": 1178453205}, {"key": "androidx/compose/runtime/StaticValueHolder.class", "name": "androidx/compose/runtime/StaticValueHolder.class", "size": 3939, "crc": 2043653489}, {"key": "androidx/compose/runtime/StructuralEqualityPolicy.class", "name": "androidx/compose/runtime/StructuralEqualityPolicy.class", "size": 1483, "crc": -935817394}, {"key": "androidx/compose/runtime/TestOnly_jvmKt.class", "name": "androidx/compose/runtime/TestOnly_jvmKt.class", "size": 389, "crc": 704266305}, {"key": "androidx/compose/runtime/UnboxedDoubleState.class", "name": "androidx/compose/runtime/UnboxedDoubleState.class", "size": 2159, "crc": 1733541975}, {"key": "androidx/compose/runtime/UnboxedFloatState.class", "name": "androidx/compose/runtime/UnboxedFloatState.class", "size": 2148, "crc": -2061820467}, {"key": "androidx/compose/runtime/UnboxedIntState.class", "name": "androidx/compose/runtime/UnboxedIntState.class", "size": 2136, "crc": -1749803458}, {"key": "androidx/compose/runtime/UnboxedLongState.class", "name": "androidx/compose/runtime/UnboxedLongState.class", "size": 2137, "crc": 950462046}, {"key": "androidx/compose/runtime/Updater$init$1.class", "name": "androidx/compose/runtime/Updater$init$1.class", "size": 1640, "crc": -1910138033}, {"key": "androidx/compose/runtime/Updater$reconcile$1.class", "name": "androidx/compose/runtime/Updater$reconcile$1.class", "size": 1655, "crc": 1896375279}, {"key": "androidx/compose/runtime/Updater.class", "name": "androidx/compose/runtime/Updater.class", "size": 6382, "crc": -593637307}, {"key": "androidx/compose/runtime/ValueHolder.class", "name": "androidx/compose/runtime/ValueHolder.class", "size": 1266, "crc": -147919766}, {"key": "androidx/compose/runtime/changelist/ChangeList.class", "name": "androidx/compose/runtime/changelist/ChangeList.class", "size": 39650, "crc": 615718156}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "size": 956, "crc": 77222547}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "size": 20431, "crc": -388880601}, {"key": "androidx/compose/runtime/changelist/FixupList.class", "name": "androidx/compose/runtime/changelist/FixupList.class", "size": 10859, "crc": 1759682890}, {"key": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "name": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "size": 3324, "crc": -1378434143}, {"key": "androidx/compose/runtime/changelist/Operation$AppendValue.class", "name": "androidx/compose/runtime/changelist/Operation$AppendValue.class", "size": 4467, "crc": -780562144}, {"key": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "name": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "size": 4787, "crc": 968394170}, {"key": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "size": 5367, "crc": -1350219209}, {"key": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "size": 6829, "crc": -1360680901}, {"key": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "size": 2118, "crc": 1878329426}, {"key": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "name": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "size": 4761, "crc": 1490775031}, {"key": "androidx/compose/runtime/changelist/Operation$Downs.class", "name": "androidx/compose/runtime/changelist/Operation$Downs.class", "size": 4029, "crc": -4046006}, {"key": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "name": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "size": 4288, "crc": -1581770455}, {"key": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "size": 2003, "crc": -1238004044}, {"key": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "name": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "size": 2424, "crc": 1662101126}, {"key": "androidx/compose/runtime/changelist/Operation$EndResumingScope.class", "name": "androidx/compose/runtime/changelist/Operation$EndResumingScope.class", "size": 3811, "crc": -14079874}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "size": 3764, "crc": 2057287839}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "size": 2030, "crc": -1413650955}, {"key": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "size": 5349, "crc": 1857268066}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "size": 4414, "crc": 191671436}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "size": 6282, "crc": -1735794552}, {"key": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "size": 3330, "crc": -1559819076}, {"key": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "size": 3816, "crc": -1565371480}, {"key": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "name": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "size": 2412, "crc": -1224218607}, {"key": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "size": 4834, "crc": 1115606411}, {"key": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "name": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "size": 5526, "crc": -596110491}, {"key": "androidx/compose/runtime/changelist/Operation$Remember.class", "name": "androidx/compose/runtime/changelist/Operation$Remember.class", "size": 3736, "crc": -378629594}, {"key": "androidx/compose/runtime/changelist/Operation$RememberPausingScope.class", "name": "androidx/compose/runtime/changelist/Operation$RememberPausingScope.class", "size": 3843, "crc": 1223891728}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "size": 2102, "crc": -1010136148}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "size": 3594, "crc": -571138515}, {"key": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "name": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "size": 1978, "crc": -951931971}, {"key": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "name": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "size": 3702, "crc": -1595945430}, {"key": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "size": 2026, "crc": -791196895}, {"key": "androidx/compose/runtime/changelist/Operation$StartResumingScope.class", "name": "androidx/compose/runtime/changelist/Operation$StartResumingScope.class", "size": 3827, "crc": 1255615160}, {"key": "androidx/compose/runtime/changelist/Operation$TestOperation$1.class", "name": "androidx/compose/runtime/changelist/Operation$TestOperation$1.class", "size": 2172, "crc": -769949608}, {"key": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "name": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "size": 6160, "crc": -992680881}, {"key": "androidx/compose/runtime/changelist/Operation$TrimParentValues.class", "name": "androidx/compose/runtime/changelist/Operation$TrimParentValues.class", "size": 5746, "crc": -1217046790}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateAnchoredValue.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateAnchoredValue.class", "size": 6374, "crc": 1229876279}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "size": 3629, "crc": 416957466}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "size": 4226, "crc": 1526279980}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "size": 5248, "crc": -60336978}, {"key": "androidx/compose/runtime/changelist/Operation$Ups.class", "name": "androidx/compose/runtime/changelist/Operation$Ups.class", "size": 3423, "crc": -2056793568}, {"key": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "name": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "size": 1989, "crc": 1404081508}, {"key": "androidx/compose/runtime/changelist/Operation.class", "name": "androidx/compose/runtime/changelist/Operation.class", "size": 10137, "crc": 1116794705}, {"key": "androidx/compose/runtime/changelist/OperationArgContainer.class", "name": "androidx/compose/runtime/changelist/OperationArgContainer.class", "size": 937, "crc": -1883615960}, {"key": "androidx/compose/runtime/changelist/OperationKt.class", "name": "androidx/compose/runtime/changelist/OperationKt.class", "size": 4147, "crc": -77341128}, {"key": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "name": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "size": 3489, "crc": 1919342254}, {"key": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "name": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "size": 9636, "crc": 592797594}, {"key": "androidx/compose/runtime/changelist/Operations$toCollectionString$1.class", "name": "androidx/compose/runtime/changelist/Operations$toCollectionString$1.class", "size": 1705, "crc": 664171699}, {"key": "androidx/compose/runtime/changelist/Operations.class", "name": "androidx/compose/runtime/changelist/Operations.class", "size": 20398, "crc": -253856529}, {"key": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "name": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "size": 1414, "crc": 497842952}, {"key": "androidx/compose/runtime/changelist/OperationsKt.class", "name": "androidx/compose/runtime/changelist/OperationsKt.class", "size": 494, "crc": 1532901772}, {"key": "androidx/compose/runtime/collection/ArrayUtils_androidKt.class", "name": "androidx/compose/runtime/collection/ArrayUtils_androidKt.class", "size": 1137, "crc": -1132941659}, {"key": "androidx/compose/runtime/collection/ExtensionsKt$sortBy$$inlined$sortBy$1.class", "name": "androidx/compose/runtime/collection/ExtensionsKt$sortBy$$inlined$sortBy$1.class", "size": 1737, "crc": -13014198}, {"key": "androidx/compose/runtime/collection/ExtensionsKt.class", "name": "androidx/compose/runtime/collection/ExtensionsKt.class", "size": 8753, "crc": -1684607895}, {"key": "androidx/compose/runtime/collection/MultiValueMap.class", "name": "androidx/compose/runtime/collection/MultiValueMap.class", "size": 14793, "crc": -1948782404}, {"key": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "name": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "size": 6988, "crc": -56326164}, {"key": "androidx/compose/runtime/collection/MutableVector$SubList.class", "name": "androidx/compose/runtime/collection/MutableVector$SubList.class", "size": 8237, "crc": 1515182478}, {"key": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "name": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "size": 2711, "crc": 560871228}, {"key": "androidx/compose/runtime/collection/MutableVector.class", "name": "androidx/compose/runtime/collection/MutableVector.class", "size": 29517, "crc": -2081915656}, {"key": "androidx/compose/runtime/collection/MutableVectorKt.class", "name": "androidx/compose/runtime/collection/MutableVectorKt.class", "size": 5522, "crc": 843309367}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapper$iterator$1.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapper$iterator$1.class", "size": 6744, "crc": 33951746}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapper.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapper.class", "size": 5176, "crc": 630136228}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapperKt.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapperKt.class", "size": 6505, "crc": 2030388901}, {"key": "androidx/compose/runtime/collection/ScopeMap.class", "name": "androidx/compose/runtime/collection/ScopeMap.class", "size": 19340, "crc": 348414468}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "size": 44671, "crc": 526587926}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "size": 724, "crc": -1330896259}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "size": 2887, "crc": -1489824509}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "size": 1844, "crc": 709034399}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "size": 1799, "crc": 1203045542}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "size": 975, "crc": -527597744}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "size": 1289, "crc": 410761866}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "size": 3016, "crc": -593486278}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "size": 1634, "crc": 132963841}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "size": 4131, "crc": -403425594}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "size": 1264, "crc": 602088875}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "size": 2703, "crc": -2066276844}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "size": 1623, "crc": 2057484153}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "size": 3179, "crc": -1836824335}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "size": 4299, "crc": -862608094}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "size": 6270, "crc": 33978916}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "size": 7711, "crc": 806410838}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "size": 1721, "crc": -444531978}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "size": 2990, "crc": 1914023240}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$removeAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$removeAll$1.class", "size": 1822, "crc": -2008325840}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$retainAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$retainAll$1.class", "size": 1857, "crc": -1943204653}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "size": 8659, "crc": 1304269455}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "size": 2135, "crc": -271025564}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "size": 1297, "crc": -10707268}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "size": 16456, "crc": -958695486}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder$removeAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder$removeAll$1.class", "size": 1747, "crc": -2101074665}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "size": 30072, "crc": 1543511528}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "size": 3183, "crc": 853074822}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "size": 5890, "crc": 1592322994}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "size": 1905, "crc": 1937198254}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "size": 1549, "crc": 307004129}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "size": 12744, "crc": 1946770810}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "size": 4194, "crc": -286209344}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "size": 2508, "crc": -1548044353}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "size": 2256, "crc": -407881115}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "size": 3697, "crc": 2000112081}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "size": 3085, "crc": -1827621672}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "size": 2323, "crc": -1698285933}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "size": 13005, "crc": -292295886}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "size": 5682, "crc": -763684292}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "size": 11551, "crc": 1957087294}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "size": 7506, "crc": -207101096}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "size": 4976, "crc": 72789693}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "size": 4196, "crc": -339126248}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "size": 3333, "crc": 955017374}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "size": 2775, "crc": 694673842}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "size": 3171, "crc": -561765057}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "size": 2781, "crc": 438760827}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "size": 515, "crc": -1521162543}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "size": 4646, "crc": 1741935338}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "size": 2792, "crc": -1302886320}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "size": 3119, "crc": 2025137739}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "size": 2650, "crc": 1420953479}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "size": 3167, "crc": 474776863}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "size": 2656, "crc": 1171882944}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "size": 1492, "crc": -311840909}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "size": 4371, "crc": -2033179866}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "size": 43745, "crc": -722025548}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "size": 4783, "crc": 274768049}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "size": 2244, "crc": -547041879}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "size": 1749, "crc": -1343562511}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "size": 4354, "crc": -409810907}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "size": 3188, "crc": -1060035969}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "size": 1752, "crc": 1980799687}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "size": 1972, "crc": -234967390}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "size": 11252, "crc": 1729551057}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "size": 10465, "crc": 1411121381}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "size": 5603, "crc": -160307053}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "size": 6258, "crc": -672217814}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "size": 1486, "crc": 1829777517}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "size": 37105, "crc": 389726210}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "size": 3909, "crc": 257258168}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "size": 2133, "crc": -1676499835}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "size": 3938, "crc": 1344578086}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "size": 3575, "crc": -1668566924}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "size": 3457, "crc": 4655441}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "size": 2425, "crc": 1716742516}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "size": 14518, "crc": -959271196}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "size": 9543, "crc": 1014906118}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "size": 5100, "crc": -446480775}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "size": 4293, "crc": -550799347}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "size": 3424, "crc": 1054452009}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "size": 3237, "crc": 896675288}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "size": 6585, "crc": -25272822}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "size": 3262, "crc": -1434945609}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "size": 3323, "crc": -845050901}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "size": 4518, "crc": -1148758066}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "size": 3944, "crc": -1919013932}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "size": 2954, "crc": -1120837828}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "size": 3355, "crc": 1138234922}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "size": 4437, "crc": 2066772057}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "size": 3002, "crc": 1641971784}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "size": 3402, "crc": -1572422400}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "size": 2611, "crc": 1458547066}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "size": 2030, "crc": 1575798331}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "size": 13264, "crc": 254788215}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "size": 8267, "crc": 1690915529}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "size": 4026, "crc": -1057440431}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "size": 4239, "crc": -238499536}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "size": 548, "crc": 314899694}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "size": 2802, "crc": -1823405891}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "size": 887, "crc": -518927940}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "size": 1387, "crc": 1927902920}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "size": 3528, "crc": -1729971301}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "size": 817, "crc": 1257661052}, {"key": "androidx/compose/runtime/internal/AtomicInt.class", "name": "androidx/compose/runtime/internal/AtomicInt.class", "size": 2606, "crc": 1908899490}, {"key": "androidx/compose/runtime/internal/Atomic_jvmKt.class", "name": "androidx/compose/runtime/internal/Atomic_jvmKt.class", "size": 526, "crc": 611601495}, {"key": "androidx/compose/runtime/internal/ComposableLambda.class", "name": "androidx/compose/runtime/internal/ComposableLambda.class", "size": 9084, "crc": 995936582}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "size": 1634, "crc": -580708052}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$10.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$10.class", "size": 2568, "crc": 1738003965}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$11.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$11.class", "size": 2562, "crc": 1149219909}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$12.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$12.class", "size": 2845, "crc": 1586925607}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$13.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$13.class", "size": 2930, "crc": 859415565}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$14.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$14.class", "size": 3015, "crc": 1966786250}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$15.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$15.class", "size": 3100, "crc": -1067983927}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$16.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$16.class", "size": 3185, "crc": 850381966}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$17.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$17.class", "size": 3270, "crc": 2062541356}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$18.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$18.class", "size": 3355, "crc": -813611861}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$19.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$19.class", "size": 3440, "crc": -441167240}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$2.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$2.class", "size": 1926, "crc": -1786674231}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$3.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$3.class", "size": 2006, "crc": 166659699}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$4.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$4.class", "size": 2086, "crc": 1049188870}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$5.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$5.class", "size": 2166, "crc": 2005817674}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$6.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$6.class", "size": 2246, "crc": -2138726260}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$7.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$7.class", "size": 2326, "crc": -2128160678}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$8.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$8.class", "size": 2406, "crc": -434261125}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$9.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$9.class", "size": 2486, "crc": -506868199}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "size": 49754, "crc": 1402542117}, {"key": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "size": 6406, "crc": 1545703730}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN.class", "size": 739, "crc": 224275867}, {"key": "androidx/compose/runtime/internal/ComposableLambdaNImpl$invoke$1.class", "name": "androidx/compose/runtime/internal/ComposableLambdaNImpl$invoke$1.class", "size": 3892, "crc": -1662683110}, {"key": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "size": 6893, "crc": 400251413}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "size": 5122, "crc": -576153720}, {"key": "androidx/compose/runtime/internal/Decoy.class", "name": "androidx/compose/runtime/internal/Decoy.class", "size": 1029, "crc": -159454887}, {"key": "androidx/compose/runtime/internal/DecoyImplementation.class", "name": "androidx/compose/runtime/internal/DecoyImplementation.class", "size": 1006, "crc": -149501284}, {"key": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "name": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "size": 961, "crc": 1561541589}, {"key": "androidx/compose/runtime/internal/DecoyKt.class", "name": "androidx/compose/runtime/internal/DecoyKt.class", "size": 1071, "crc": -1421800139}, {"key": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "name": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "size": 1501, "crc": 627222620}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "size": 913, "crc": -773996642}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "size": 1297, "crc": -1907059599}, {"key": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "name": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "size": 971, "crc": -1680379156}, {"key": "androidx/compose/runtime/internal/IntRef.class", "name": "androidx/compose/runtime/internal/IntRef.class", "size": 1876, "crc": -394776406}, {"key": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 560, "crc": -683601055}, {"key": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "size": 965, "crc": -152902829}, {"key": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "size": 1026, "crc": -879718616}, {"key": "androidx/compose/runtime/internal/LiveLiteralKt.class", "name": "androidx/compose/runtime/internal/LiveLiteralKt.class", "size": 4358, "crc": -1428597118}, {"key": "androidx/compose/runtime/internal/PausedCompositionRemembers.class", "name": "androidx/compose/runtime/internal/PausedCompositionRemembers.class", "size": 4064, "crc": -2064064204}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "size": 6608, "crc": 293267640}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "size": 1382, "crc": -1180406802}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "size": 9590, "crc": -1952193927}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "size": 3332, "crc": -1827818696}, {"key": "androidx/compose/runtime/internal/PlatformOptimizedCancellationException.class", "name": "androidx/compose/runtime/internal/PlatformOptimizedCancellationException.class", "size": 1610, "crc": -1248259008}, {"key": "androidx/compose/runtime/internal/RememberEventDispatcher.class", "name": "androidx/compose/runtime/internal/RememberEventDispatcher.class", "size": 16189, "crc": 358803126}, {"key": "androidx/compose/runtime/internal/RememberEventDispatcherKt.class", "name": "androidx/compose/runtime/internal/RememberEventDispatcherKt.class", "size": 1435, "crc": -2044514271}, {"key": "androidx/compose/runtime/internal/SnapshotThreadLocal.class", "name": "androidx/compose/runtime/internal/SnapshotThreadLocal.class", "size": 4043, "crc": 1154784583}, {"key": "androidx/compose/runtime/internal/SnapshotThreadLocalKt.class", "name": "androidx/compose/runtime/internal/SnapshotThreadLocalKt.class", "size": 845, "crc": -1907171287}, {"key": "androidx/compose/runtime/internal/StabilityInferred.class", "name": "androidx/compose/runtime/internal/StabilityInferred.class", "size": 955, "crc": -1323770174}, {"key": "androidx/compose/runtime/internal/ThreadMap.class", "name": "androidx/compose/runtime/internal/ThreadMap.class", "size": 3956, "crc": 207125677}, {"key": "androidx/compose/runtime/internal/Thread_androidKt.class", "name": "androidx/compose/runtime/internal/Thread_androidKt.class", "size": 894, "crc": -990547827}, {"key": "androidx/compose/runtime/internal/Thread_jvmKt.class", "name": "androidx/compose/runtime/internal/Thread_jvmKt.class", "size": 738, "crc": 1022066331}, {"key": "androidx/compose/runtime/internal/Trace.class", "name": "androidx/compose/runtime/internal/Trace.class", "size": 1353, "crc": -38994030}, {"key": "androidx/compose/runtime/internal/TraceKt.class", "name": "androidx/compose/runtime/internal/TraceKt.class", "size": 1535, "crc": -394917616}, {"key": "androidx/compose/runtime/internal/Utils_androidKt.class", "name": "androidx/compose/runtime/internal/Utils_androidKt.class", "size": 949, "crc": 433436888}, {"key": "androidx/compose/runtime/internal/Utils_jvmKt.class", "name": "androidx/compose/runtime/internal/Utils_jvmKt.class", "size": 1888, "crc": 1793638910}, {"key": "androidx/compose/runtime/internal/WeakReference.class", "name": "androidx/compose/runtime/internal/WeakReference.class", "size": 1077, "crc": 1696808082}, {"key": "androidx/compose/runtime/platform/Synchronization_androidKt.class", "name": "androidx/compose/runtime/platform/Synchronization_androidKt.class", "size": 2090, "crc": 799630460}, {"key": "androidx/compose/runtime/reflect/ComposableInfo.class", "name": "androidx/compose/runtime/reflect/ComposableInfo.class", "size": 3352, "crc": 190496649}, {"key": "androidx/compose/runtime/reflect/ComposableMethod.class", "name": "androidx/compose/runtime/reflect/ComposableMethod.class", "size": 8341, "crc": -106487431}, {"key": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "name": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "size": 10613, "crc": -1205880425}, {"key": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "name": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "size": 1009, "crc": 1443387148}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$1.class", "size": 3412, "crc": -430520762}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1$1.class", "size": 4150, "crc": 1752006979}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1$1.class", "size": 3847, "crc": 1807993085}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "size": 8714, "crc": -103309819}, {"key": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "name": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "size": 12843, "crc": 668326060}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "size": 903, "crc": 531845569}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "size": 40661, "crc": 1680868161}, {"key": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "size": 8172, "crc": 1462767281}, {"key": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "size": 8733, "crc": -346087118}, {"key": "androidx/compose/runtime/snapshots/ObserverHandle.class", "name": "androidx/compose/runtime/snapshots/ObserverHandle.class", "size": 462, "crc": 1215084186}, {"key": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "name": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "size": 1543, "crc": -1518549272}, {"key": "androidx/compose/runtime/snapshots/ReaderKind.class", "name": "androidx/compose/runtime/snapshots/ReaderKind.class", "size": 3098, "crc": 1409496289}, {"key": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "size": 8325, "crc": -1634708313}, {"key": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "size": 18615, "crc": 1082725580}, {"key": "androidx/compose/runtime/snapshots/Snapshot.class", "name": "androidx/compose/runtime/snapshots/Snapshot.class", "size": 13026, "crc": -73451391}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "size": 1251, "crc": 1245544572}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "size": 1775, "crc": 11152124}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "size": 1261, "crc": -1248455069}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "size": 1403, "crc": -699003289}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "size": 3157, "crc": -1149010736}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "size": 1119, "crc": -127431128}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "size": 1048, "crc": 1238942698}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElementImpl.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElementImpl.class", "size": 4999, "crc": 808578173}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "size": 1082, "crc": 982017218}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "size": 6187, "crc": -1007853564}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "size": 438, "crc": -904283024}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdArrayBuilder.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdArrayBuilder.class", "size": 3941, "crc": -1668362105}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "size": 1193, "crc": 1809220006}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "size": 5891, "crc": 1318064899}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "size": 18928, "crc": 1962024761}, {"key": "androidx/compose/runtime/snapshots/SnapshotId_jvmKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotId_jvmKt.class", "size": 5581, "crc": -1461453020}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$emptyLambda$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$emptyLambda$1.class", "size": 1404, "crc": -259793504}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$mergedReadObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$mergedReadObserver$1.class", "size": 1767, "crc": 1936917436}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$mergedWriteObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$mergedWriteObserver$1.class", "size": 1770, "crc": 1512312508}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$takeNewSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$takeNewSnapshot$1.class", "size": 3719, "crc": 812517071}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt.class", "size": 42474, "crc": -1753302396}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "size": 13340, "crc": -2033457814}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "size": 12251, "crc": 977881282}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "size": 2505, "crc": -4914346}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "size": 14114, "crc": -295682293}, {"key": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "name": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "size": 979, "crc": 746176173}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$StateListStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$StateListStateRecord.class", "size": 5446, "crc": -458448948}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$addAll$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$addAll$1.class", "size": 1617, "crc": -2125393168}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$retainAll$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$retainAll$1.class", "size": 1573, "crc": 2033675310}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "size": 51051, "crc": -533167264}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "size": 4697, "crc": 1426775157}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "size": 5269, "crc": 752713335}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "size": 30403, "crc": -365275878}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "size": 1762, "crc": 647286590}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "size": 1924, "crc": -864871204}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "size": 41209, "crc": -425890148}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$applyObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$applyObserver$1.class", "size": 2160, "crc": -1941558631}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$readObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$readObserver$1.class", "size": 3301, "crc": 1039915527}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$sendNotifications$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$sendNotifications$1.class", "size": 4160, "crc": 1489567386}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "size": 20775, "crc": 675216335}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSet$StateSetStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSet$StateSetStateRecord.class", "size": 5106, "crc": -1637652652}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSet$retainAll$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSet$retainAll$1.class", "size": 1719, "crc": 1848592374}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSet.class", "size": 30229, "crc": -216540805}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSetKt.class", "size": 1595, "crc": -1419264552}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "size": 7150, "crc": 608742950}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "size": 422, "crc": 557347695}, {"key": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "name": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "size": 935, "crc": -827599409}, {"key": "androidx/compose/runtime/snapshots/StateListIterator.class", "name": "androidx/compose/runtime/snapshots/StateListIterator.class", "size": 4442, "crc": -1161789937}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "size": 4051, "crc": 958482094}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "size": 2286, "crc": 1859394165}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "size": 5998, "crc": 1510117490}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "size": 1980, "crc": -1484055079}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "size": 1985, "crc": 506142422}, {"key": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "size": 1192, "crc": -2035115455}, {"key": "androidx/compose/runtime/snapshots/StateObject.class", "name": "androidx/compose/runtime/snapshots/StateObject.class", "size": 1859, "crc": -1606148052}, {"key": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "name": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "size": 2735, "crc": -92415460}, {"key": "androidx/compose/runtime/snapshots/StateRecord.class", "name": "androidx/compose/runtime/snapshots/StateRecord.class", "size": 3406, "crc": 50405110}, {"key": "androidx/compose/runtime/snapshots/StateSetIterator.class", "name": "androidx/compose/runtime/snapshots/StateSetIterator.class", "size": 5545, "crc": -1538597897}, {"key": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "name": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "size": 3234, "crc": 1293957262}, {"key": "androidx/compose/runtime/snapshots/SubList.class", "name": "androidx/compose/runtime/snapshots/SubList.class", "size": 9821, "crc": -206979259}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "size": 10165, "crc": -182403182}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "size": 8269, "crc": 1581586538}, {"key": "androidx/compose/runtime/snapshots/tooling/SnapshotInstanceObservers.class", "name": "androidx/compose/runtime/snapshots/tooling/SnapshotInstanceObservers.class", "size": 2177, "crc": 1916979942}, {"key": "androidx/compose/runtime/snapshots/tooling/SnapshotObserver.class", "name": "androidx/compose/runtime/snapshots/tooling/SnapshotObserver.class", "size": 2724, "crc": 1692037995}, {"key": "androidx/compose/runtime/snapshots/tooling/SnapshotObserverKt$mergeObservers$2.class", "name": "androidx/compose/runtime/snapshots/tooling/SnapshotObserverKt$mergeObservers$2.class", "size": 1783, "crc": -296547886}, {"key": "androidx/compose/runtime/snapshots/tooling/SnapshotObserverKt.class", "name": "androidx/compose/runtime/snapshots/tooling/SnapshotObserverKt.class", "size": 14161, "crc": -1589100902}, {"key": "androidx/compose/runtime/tooling/CompositionData.class", "name": "androidx/compose/runtime/tooling/CompositionData.class", "size": 1211, "crc": 362275900}, {"key": "androidx/compose/runtime/tooling/CompositionDataKt.class", "name": "androidx/compose/runtime/tooling/CompositionDataKt.class", "size": 1012, "crc": -777293339}, {"key": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "name": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "size": 1561, "crc": -773783041}, {"key": "androidx/compose/runtime/tooling/CompositionGroup.class", "name": "androidx/compose/runtime/tooling/CompositionGroup.class", "size": 2403, "crc": 838933755}, {"key": "androidx/compose/runtime/tooling/CompositionInstance.class", "name": "androidx/compose/runtime/tooling/CompositionInstance.class", "size": 1026, "crc": -514491664}, {"key": "androidx/compose/runtime/tooling/CompositionObserver.class", "name": "androidx/compose/runtime/tooling/CompositionObserver.class", "size": 1192, "crc": 1243005749}, {"key": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "name": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "size": 588, "crc": 863686226}, {"key": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "name": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "size": 3145, "crc": 349460427}, {"key": "androidx/compose/runtime/tooling/CompositionRegistrationObserver.class", "name": "androidx/compose/runtime/tooling/CompositionRegistrationObserver.class", "size": 1022, "crc": -1818472655}, {"key": "androidx/compose/runtime/tooling/InspectionTablesKt$LocalInspectionTables$1.class", "name": "androidx/compose/runtime/tooling/InspectionTablesKt$LocalInspectionTables$1.class", "size": 1320, "crc": 815831486}, {"key": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "name": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "size": 1567, "crc": 149869147}, {"key": "androidx/compose/runtime/tooling/RecomposeScopeObserver.class", "name": "androidx/compose/runtime/tooling/RecomposeScopeObserver.class", "size": 929, "crc": -2004111840}, {"key": "META-INF/androidx.compose.runtime_runtime.version", "name": "META-INF/androidx.compose.runtime_runtime.version", "size": 6, "crc": 333960004}, {"key": "META-INF/runtime_release.kotlin_module", "name": "META-INF/runtime_release.kotlin_module", "size": 2725, "crc": 1865887588}]