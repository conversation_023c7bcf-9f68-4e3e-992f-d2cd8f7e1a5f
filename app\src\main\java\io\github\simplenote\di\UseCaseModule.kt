package io.github.simplenote.di

import io.github.simplenote.domain.usecase.CreateNoteUseCase
import io.github.simplenote.domain.usecase.DeleteNoteUseCase
import io.github.simplenote.domain.usecase.GetNotesUseCase
import io.github.simplenote.domain.usecase.GetThemeSettingsUseCase
import io.github.simplenote.domain.usecase.PopulateSampleDataUseCase
import io.github.simplenote.domain.usecase.UpdateNoteUseCase
import io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase
import org.koin.dsl.module

/**
 * Koin module for use case dependencies.
 * Provides use case instances with repository dependencies.
 */
val useCaseModule = module {
    
    // Get Notes Use Case - Factory (new instance each time)
    factory {
        GetNotesUseCase(
            repository = get()
        )
    }
    
    // Create Note Use Case - Factory
    factory {
        CreateNoteUseCase(
            repository = get()
        )
    }
    
    // Update Note Use Case - Factory
    factory {
        UpdateNoteUseCase(
            repository = get()
        )
    }
    
    // Delete Note Use Case - Factory
    factory {
        DeleteNoteUseCase(
            repository = get()
        )
    }

    // Get Theme Settings Use Case - Factory
    factory {
        GetThemeSettingsUseCase(
            themePreferences = get()
        )
    }

    // Update Theme Settings Use Case - Factory
    factory {
        UpdateThemeSettingsUseCase(
            themePreferences = get()
        )
    }

    // Populate Sample Data Use Case - Factory
    factory {
        PopulateSampleDataUseCase(
            repository = get()
        )
    }
}
