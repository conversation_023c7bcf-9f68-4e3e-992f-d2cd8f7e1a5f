-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:2:1-28:12
MERGED from [io.insert-koin:koin-androidx-compose:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\936747fdeaa32ae23e669d48666c960d\transformed\koin-androidx-compose-4.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-compose-viewmodel-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\896857d7d8846d3319a384615d9e1984\transformed\koin-compose-viewmodel-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-compose-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1038bb0c03e2b8e802afbd9bda8e0f1c\transformed\koin-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943df7b2a5ffd54142f8e507e12e7d20\transformed\koin-android-4.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\759f44b938018b86b4c9fb3e5df6883b\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d7b96133fd54175221e748800b0b16e\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5916c99aabdefec4ef03690d7a2a3c\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\55ccf585c648384d6d3306f9dd19b331\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a52cd15d91b3b91eba876f6457455010\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\efd0c7f813c432e2c0785136f0b2016e\transformed\fragment-ktx-1.8.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2f04f83541c4522bc76f261d3ab1327\transformed\fragment-1.8.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cf858d13e6a5e1f5b119836a8b04239\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d61245d3231dfae84075323e154f2e0\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f6b7cfc7dddc58c7ce7adfd537c1781\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4126eedf04e080472ca9b82cdac66986\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b666e5e24eb30aa3f982dbadc803c1c4\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f5dcfdd8f75e3f8cbb984085971c6e9\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\84aef995668f3afb4a1cd22a31387706\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\96ccc887d394ddaa1dd6d0e3b9de1bba\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a595bc9036bece929a4901376504e526\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a381dee28a0246f2ac113bec67c9bdd7\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\381547b80a35280790ec30a8cc1a672e\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc60f3403f29b909ebc5a5bdad6b8e61\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\54bf382fb7c1129431b3418c21685dda\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed21dc111fd125e1031bf6e008f544fd\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9191134d19d2f1df3d192d102235a2e1\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e0f7b0c5e3f30cad1b2eb0430adb40f\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fbdca4cff0337663acf0a2aafd5ff6\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc74e441d430e274cfa22082fef79643\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9a9d86ce5ee999d30e52d89e77165c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a811b42383e45363f6ebbbd567269d72\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\630c6e46c5782c01df0ef5a225935695\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5560dff54341d35c4484e55ec4abc162\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd303e6c2d4f9c936158afcf677a41cf\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b649523b489aab6eb4df3f50cf82e90\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\edb49963624f80a748f5dc5659581840\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0226efcf44c5e5d49af1b0dc8369cf79\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af31a73ad5c0b7922c384dc7240c9e38\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [io.insert-koin:koin-core-viewmodel-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f15cd42e1e861e70f25f48f8cee5438\transformed\koin-core-viewmodel-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f7e35418771ab73cd674216e8ea897\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e6dfe0612115486eaa52457b6b985db\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b155b834be774cb3a094090b68ee5962\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\71a734cd1832c1a454590ad921e7204f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af7a71bc77c40ff487916a8f0efe29c\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee3e6af81af78cd6a62020dc6325c477\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0ffb6d4d4d2356baf020d19e3081802\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\36de0711b8724c92786f0bf89a4d43c7\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43dd482a7c96b77d6cc6ccc24a17e047\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4ac2fe99ec024b24b5c440a2cc6511b\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb8b0930d663c4970257b91c063e82d5\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\863ed73f3c29931470523f99024b69e7\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\312f557dc9df8ea6cbb049518740c4f6\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\250d8cc481173cc0c0524c32c74c00eb\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f8129004ccff9ecbc01885518a2d0c\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f92d728c24f0eb7aa621a3c78cd669f0\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\186e30df85fe320f7190373ad5d1ca2c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b3727a0af9938b2b4f53392a3dce9bb\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8835490be1a29b5f40d11535f6927e40\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b073acf1deccb16be6bbe2264189300d\transformed\room-ktx-2.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd49277c138bc5ea9b5faa44fe8b0bb1\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c522a1397267ebfa7112d0505a40ec28\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae9198a09dc580746a955a921bc37a89\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\be56c65c9154680c10169f24ca1efe81\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.arrow-kt:arrow-fx-coroutines-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c055263b104b3ec3fa3eaa2845be421\transformed\arrow-fx-coroutines-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\44072d45c1c5c9f9fbbd156a68477164\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3032d8c69540cf626cf55a11c7eb6ae7\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.arrow-kt:arrow-core-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bf0237c627db9f614deb1dda7e56fe5\transformed\arrow-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43b10ea5a82b477a24598e9b953b9eba\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae9817b57a187e60b0aad2683452b55\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f700c4f4ce05aa09b22b934581e7ab9f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fd5e6e4172b7c73b7467af17cfed33f\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dc26759a30652cfb6cd64cf0456e462\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c30a28a2b4c3895a9a01eab52b571f33\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3616f75cb42e500dc3168f59df520b04\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\409cd7c9c3057a3e7553a6631d5e1b7b\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac58664721739588eccfba72ea0587d\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.arrow-kt:arrow-autoclose-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe3da7a1107455f0ab8c7c7aa2e7da3\transformed\arrow-autoclose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.arrow-kt:arrow-atomic-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9488b95ec6fe6a94a44bdaafd1218ab3\transformed\arrow-atomic-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.arrow-kt:arrow-annotations-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f6b95641ab9abc61cec554e5ca38362\transformed\arrow-annotations-release\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:5:5-26:19
INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:5:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dc26759a30652cfb6cd64cf0456e462\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dc26759a30652cfb6cd64cf0456e462\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:9:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:12:9-54
	android:icon
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:10:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:14:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:8:9-65
	android:name
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:6:9-46
activity#io.github.simplenote.MainActivity
ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:19:13-52
	android:name
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:23:27-74
uses-sdk
INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
MERGED from [io.insert-koin:koin-androidx-compose:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\936747fdeaa32ae23e669d48666c960d\transformed\koin-androidx-compose-4.1.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-androidx-compose:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\936747fdeaa32ae23e669d48666c960d\transformed\koin-androidx-compose-4.1.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-compose-viewmodel-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\896857d7d8846d3319a384615d9e1984\transformed\koin-compose-viewmodel-release\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-compose-viewmodel-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\896857d7d8846d3319a384615d9e1984\transformed\koin-compose-viewmodel-release\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-compose-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1038bb0c03e2b8e802afbd9bda8e0f1c\transformed\koin-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-compose-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1038bb0c03e2b8e802afbd9bda8e0f1c\transformed\koin-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943df7b2a5ffd54142f8e507e12e7d20\transformed\koin-android-4.1.0\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\943df7b2a5ffd54142f8e507e12e7d20\transformed\koin-android-4.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\759f44b938018b86b4c9fb3e5df6883b\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\759f44b938018b86b4c9fb3e5df6883b\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d7b96133fd54175221e748800b0b16e\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d7b96133fd54175221e748800b0b16e\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5916c99aabdefec4ef03690d7a2a3c\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e5916c99aabdefec4ef03690d7a2a3c\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\55ccf585c648384d6d3306f9dd19b331\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\55ccf585c648384d6d3306f9dd19b331\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a52cd15d91b3b91eba876f6457455010\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a52cd15d91b3b91eba876f6457455010\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\efd0c7f813c432e2c0785136f0b2016e\transformed\fragment-ktx-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\efd0c7f813c432e2c0785136f0b2016e\transformed\fragment-ktx-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2f04f83541c4522bc76f261d3ab1327\transformed\fragment-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2f04f83541c4522bc76f261d3ab1327\transformed\fragment-1.8.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cf858d13e6a5e1f5b119836a8b04239\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2cf858d13e6a5e1f5b119836a8b04239\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d61245d3231dfae84075323e154f2e0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d61245d3231dfae84075323e154f2e0\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f6b7cfc7dddc58c7ce7adfd537c1781\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f6b7cfc7dddc58c7ce7adfd537c1781\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4126eedf04e080472ca9b82cdac66986\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4126eedf04e080472ca9b82cdac66986\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b666e5e24eb30aa3f982dbadc803c1c4\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b666e5e24eb30aa3f982dbadc803c1c4\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f5dcfdd8f75e3f8cbb984085971c6e9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f5dcfdd8f75e3f8cbb984085971c6e9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\84aef995668f3afb4a1cd22a31387706\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\84aef995668f3afb4a1cd22a31387706\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\96ccc887d394ddaa1dd6d0e3b9de1bba\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\96ccc887d394ddaa1dd6d0e3b9de1bba\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a595bc9036bece929a4901376504e526\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a595bc9036bece929a4901376504e526\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a381dee28a0246f2ac113bec67c9bdd7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\a381dee28a0246f2ac113bec67c9bdd7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\381547b80a35280790ec30a8cc1a672e\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\381547b80a35280790ec30a8cc1a672e\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc60f3403f29b909ebc5a5bdad6b8e61\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc60f3403f29b909ebc5a5bdad6b8e61\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\54bf382fb7c1129431b3418c21685dda\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\54bf382fb7c1129431b3418c21685dda\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed21dc111fd125e1031bf6e008f544fd\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed21dc111fd125e1031bf6e008f544fd\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9191134d19d2f1df3d192d102235a2e1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\9191134d19d2f1df3d192d102235a2e1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e0f7b0c5e3f30cad1b2eb0430adb40f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e0f7b0c5e3f30cad1b2eb0430adb40f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fbdca4cff0337663acf0a2aafd5ff6\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0fbdca4cff0337663acf0a2aafd5ff6\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc74e441d430e274cfa22082fef79643\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc74e441d430e274cfa22082fef79643\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9a9d86ce5ee999d30e52d89e77165c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9a9d86ce5ee999d30e52d89e77165c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a811b42383e45363f6ebbbd567269d72\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a811b42383e45363f6ebbbd567269d72\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\630c6e46c5782c01df0ef5a225935695\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\630c6e46c5782c01df0ef5a225935695\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5560dff54341d35c4484e55ec4abc162\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5560dff54341d35c4484e55ec4abc162\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd303e6c2d4f9c936158afcf677a41cf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd303e6c2d4f9c936158afcf677a41cf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b649523b489aab6eb4df3f50cf82e90\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b649523b489aab6eb4df3f50cf82e90\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\edb49963624f80a748f5dc5659581840\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\edb49963624f80a748f5dc5659581840\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0226efcf44c5e5d49af1b0dc8369cf79\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0226efcf44c5e5d49af1b0dc8369cf79\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af31a73ad5c0b7922c384dc7240c9e38\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af31a73ad5c0b7922c384dc7240c9e38\transformed\lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-core-viewmodel-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f15cd42e1e861e70f25f48f8cee5438\transformed\koin-core-viewmodel-release\AndroidManifest.xml:5:5-44
MERGED from [io.insert-koin:koin-core-viewmodel-android:4.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f15cd42e1e861e70f25f48f8cee5438\transformed\koin-core-viewmodel-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f7e35418771ab73cd674216e8ea897\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f7e35418771ab73cd674216e8ea897\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e6dfe0612115486eaa52457b6b985db\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e6dfe0612115486eaa52457b6b985db\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b155b834be774cb3a094090b68ee5962\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b155b834be774cb3a094090b68ee5962\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\71a734cd1832c1a454590ad921e7204f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\71a734cd1832c1a454590ad921e7204f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af7a71bc77c40ff487916a8f0efe29c\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af7a71bc77c40ff487916a8f0efe29c\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee3e6af81af78cd6a62020dc6325c477\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee3e6af81af78cd6a62020dc6325c477\transformed\savedstate-ktx-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0ffb6d4d4d2356baf020d19e3081802\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0ffb6d4d4d2356baf020d19e3081802\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\36de0711b8724c92786f0bf89a4d43c7\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\36de0711b8724c92786f0bf89a4d43c7\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43dd482a7c96b77d6cc6ccc24a17e047\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\43dd482a7c96b77d6cc6ccc24a17e047\transformed\lifecycle-viewmodel-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4ac2fe99ec024b24b5c440a2cc6511b\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4ac2fe99ec024b24b5c440a2cc6511b\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb8b0930d663c4970257b91c063e82d5\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb8b0930d663c4970257b91c063e82d5\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\863ed73f3c29931470523f99024b69e7\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\863ed73f3c29931470523f99024b69e7\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\312f557dc9df8ea6cbb049518740c4f6\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\312f557dc9df8ea6cbb049518740c4f6\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\250d8cc481173cc0c0524c32c74c00eb\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\250d8cc481173cc0c0524c32c74c00eb\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f8129004ccff9ecbc01885518a2d0c\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\61f8129004ccff9ecbc01885518a2d0c\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f92d728c24f0eb7aa621a3c78cd669f0\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f92d728c24f0eb7aa621a3c78cd669f0\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\186e30df85fe320f7190373ad5d1ca2c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\186e30df85fe320f7190373ad5d1ca2c\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b3727a0af9938b2b4f53392a3dce9bb\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b3727a0af9938b2b4f53392a3dce9bb\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8835490be1a29b5f40d11535f6927e40\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8835490be1a29b5f40d11535f6927e40\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b073acf1deccb16be6bbe2264189300d\transformed\room-ktx-2.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b073acf1deccb16be6bbe2264189300d\transformed\room-ktx-2.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd49277c138bc5ea9b5faa44fe8b0bb1\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd49277c138bc5ea9b5faa44fe8b0bb1\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c522a1397267ebfa7112d0505a40ec28\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c522a1397267ebfa7112d0505a40ec28\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae9198a09dc580746a955a921bc37a89\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae9198a09dc580746a955a921bc37a89\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\be56c65c9154680c10169f24ca1efe81\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\be56c65c9154680c10169f24ca1efe81\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [io.arrow-kt:arrow-fx-coroutines-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c055263b104b3ec3fa3eaa2845be421\transformed\arrow-fx-coroutines-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-fx-coroutines-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c055263b104b3ec3fa3eaa2845be421\transformed\arrow-fx-coroutines-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\44072d45c1c5c9f9fbbd156a68477164\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\44072d45c1c5c9f9fbbd156a68477164\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3032d8c69540cf626cf55a11c7eb6ae7\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3032d8c69540cf626cf55a11c7eb6ae7\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-core-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bf0237c627db9f614deb1dda7e56fe5\transformed\arrow-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-core-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bf0237c627db9f614deb1dda7e56fe5\transformed\arrow-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43b10ea5a82b477a24598e9b953b9eba\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\43b10ea5a82b477a24598e9b953b9eba\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae9817b57a187e60b0aad2683452b55\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae9817b57a187e60b0aad2683452b55\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f700c4f4ce05aa09b22b934581e7ab9f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f700c4f4ce05aa09b22b934581e7ab9f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fd5e6e4172b7c73b7467af17cfed33f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fd5e6e4172b7c73b7467af17cfed33f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dc26759a30652cfb6cd64cf0456e462\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dc26759a30652cfb6cd64cf0456e462\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c30a28a2b4c3895a9a01eab52b571f33\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c30a28a2b4c3895a9a01eab52b571f33\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3616f75cb42e500dc3168f59df520b04\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3616f75cb42e500dc3168f59df520b04\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\409cd7c9c3057a3e7553a6631d5e1b7b\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\409cd7c9c3057a3e7553a6631d5e1b7b\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac58664721739588eccfba72ea0587d\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ac58664721739588eccfba72ea0587d\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [io.arrow-kt:arrow-autoclose-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe3da7a1107455f0ab8c7c7aa2e7da3\transformed\arrow-autoclose-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-autoclose-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe3da7a1107455f0ab8c7c7aa2e7da3\transformed\arrow-autoclose-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-atomic-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9488b95ec6fe6a94a44bdaafd1218ab3\transformed\arrow-atomic-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-atomic-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9488b95ec6fe6a94a44bdaafd1218ab3\transformed\arrow-atomic-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-annotations-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f6b95641ab9abc61cec554e5ca38362\transformed\arrow-annotations-release\AndroidManifest.xml:5:5-44
MERGED from [io.arrow-kt:arrow-annotations-android:2.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f6b95641ab9abc61cec554e5ca38362\transformed\arrow-annotations-release\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c209c48bc5c39759a1bfd4c2f5ff390\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#io.github.simplenote.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#io.github.simplenote.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
