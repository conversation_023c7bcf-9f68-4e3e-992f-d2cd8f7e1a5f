package io.github.simplenote.presentation.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import kotlin.math.roundToInt



/**
 * Simplified version for basic scroll behavior without pull-to-refresh
 */
@Composable
fun SimpleScrollableSearchBarLayout(
    searchBarContent: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    gridState: LazyGridState = rememberLazyGridState(),
    content: @Composable () -> Unit
) {
    val density = LocalDensity.current
    val searchBarHeight = with(density) { 72.dp.toPx() }
    
    // Search bar visibility state
    var lastScrollPosition by remember { mutableStateOf(0) }
    var isSearchBarVisible by remember { mutableStateOf(true) }
    
    // Animated search bar offset
    val animatedSearchBarOffset by animateFloatAsState(
        targetValue = if (isSearchBarVisible) 0f else -searchBarHeight,
        animationSpec = tween(300),
        label = "searchBarOffset"
    )
    
    // Detect scroll direction
    val isScrollingUp by remember {
        derivedStateOf {
            val currentScrollPosition = gridState.firstVisibleItemScrollOffset
            val scrollDelta = currentScrollPosition - lastScrollPosition
            lastScrollPosition = currentScrollPosition
            scrollDelta > 0
        }
    }
    
    // Update search bar visibility based on scroll
    LaunchedEffect(
        isScrollingUp,
        gridState.firstVisibleItemIndex
    ) {
        when {
            // Always show when at the top
            gridState.firstVisibleItemIndex == 0 && gridState.firstVisibleItemScrollOffset < 100 -> {
                isSearchBarVisible = true
            }
            // Hide when scrolling up (content moving up)
            isScrollingUp && gridState.firstVisibleItemIndex > 0 -> {
                isSearchBarVisible = false
            }
            // Show when scrolling down (content moving down)
            !isScrollingUp -> {
                isSearchBarVisible = true
            }
        }
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        Column(modifier = Modifier.fillMaxSize()) {
            // Search bar with animated offset and status bar padding
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .windowInsetsPadding(WindowInsets.statusBars)
                    .offset { IntOffset(0, animatedSearchBarOffset.roundToInt()) }
            ) {
                searchBarContent()
            }
            
            // Content
            content()
        }
    }
}

/**
 * Improved scrollable search bar that works as a TopAppBar replacement
 * with proper scroll synchronization and edge-to-edge support
 */
@Composable
fun ScrollableSearchBarTopAppBar(
    searchBarContent: @Composable () -> Unit,
    gridState: LazyStaggeredGridState,
    modifier: Modifier = Modifier
): @Composable () -> Unit {
    val density = LocalDensity.current
    val searchBarHeight = with(density) { 72.dp.toPx() }

    // Search bar visibility state
    var lastScrollPosition by remember { mutableStateOf(0) }
    var isSearchBarVisible by remember { mutableStateOf(true) }

    // Animated search bar offset
    val animatedSearchBarOffset by animateFloatAsState(
        targetValue = if (isSearchBarVisible) 0f else -searchBarHeight,
        animationSpec = tween(durationMillis = 300),
        label = "searchBarOffset"
    )

    // Improved scroll detection with better performance
    LaunchedEffect(gridState.firstVisibleItemIndex, gridState.firstVisibleItemScrollOffset) {
        val currentScrollPosition = gridState.firstVisibleItemScrollOffset
        val currentIndex = gridState.firstVisibleItemIndex

        // Calculate scroll delta
        val scrollDelta = currentScrollPosition - lastScrollPosition
        lastScrollPosition = currentScrollPosition

        when {
            // Always show when at the very top
            currentIndex == 0 && currentScrollPosition <= 50 -> {
                isSearchBarVisible = true
            }
            // Hide when scrolling up significantly (content moving up)
            scrollDelta > 20 && currentIndex > 0 -> {
                isSearchBarVisible = false
            }
            // Show when scrolling down significantly (content moving down)
            scrollDelta < -20 -> {
                isSearchBarVisible = true
            }
            // Keep current state for small movements
        }
    }

    // Return the TopAppBar composable
    return {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .windowInsetsPadding(WindowInsets.statusBars)
                .offset { IntOffset(0, animatedSearchBarOffset.roundToInt()) }
        ) {
            searchBarContent()
        }
    }
}
