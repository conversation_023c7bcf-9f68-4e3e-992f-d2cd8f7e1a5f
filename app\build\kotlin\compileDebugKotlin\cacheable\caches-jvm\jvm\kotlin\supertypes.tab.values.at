/ Header Record For PersistentHashMapValueStorage, +io.github.simplenote.data.local.dao.NoteDao6 5io.github.simplenote.data.local.database.NoteDatabase$ #androidx.activity.ComponentActivity android.app.Application androidx.room.RoomDatabase6 5io.github.simplenote.domain.repository.NoteRepository kotlin.Enum, +io.github.simplenote.domain.model.NoteError: 9io.github.simplenote.domain.model.NoteError.DatabaseError: 9io.github.simplenote.domain.model.NoteError.DatabaseError: 9io.github.simplenote.domain.model.NoteError.DatabaseError: 9io.github.simplenote.domain.model.NoteError.DatabaseError: 9io.github.simplenote.domain.model.NoteError.DatabaseError, +io.github.simplenote.domain.model.NoteError< ;io.github.simplenote.domain.model.NoteError.ValidationError< ;io.github.simplenote.domain.model.NoteError.ValidationError< ;io.github.simplenote.domain.model.NoteError.ValidationError< ;io.github.simplenote.domain.model.NoteError.ValidationError< ;io.github.simplenote.domain.model.NoteError.ValidationError, +io.github.simplenote.domain.model.NoteError5 4io.github.simplenote.domain.model.NoteError.AppError5 4io.github.simplenote.domain.model.NoteError.AppError5 4io.github.simplenote.domain.model.NoteError.AppError kotlin.Enum kotlin.Enum/ .io.github.simplenote.navigation.AppDestination/ .io.github.simplenote.navigation.AppDestination3 2kotlinx.serialization.internal.GeneratedSerializer/ .io.github.simplenote.navigation.AppDestination kotlin.Enum5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent5 4io.github.simplenote.presentation.model.NotesUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent: 9io.github.simplenote.presentation.model.NoteEditorUiEvent8 7io.github.simplenote.presentation.model.NavigationEvent8 7io.github.simplenote.presentation.model.NavigationEvent8 7io.github.simplenote.presentation.model.NavigationEvent8 7io.github.simplenote.presentation.model.NavigationEvent androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel