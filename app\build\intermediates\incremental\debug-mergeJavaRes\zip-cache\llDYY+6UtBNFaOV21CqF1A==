[{"key": "androidx/core/app/ComponentActivity$ExtraData.class", "name": "androidx/core/app/ComponentActivity$ExtraData.class", "size": 1065, "crc": -**********}, {"key": "androidx/core/app/ComponentActivity.class", "name": "androidx/core/app/ComponentActivity.class", "size": 6625, "crc": 201353956}, {"key": "androidx/core/app/MultiWindowModeChangedInfo.class", "name": "androidx/core/app/MultiWindowModeChangedInfo.class", "size": 2073, "crc": 925803428}, {"key": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "name": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "size": 955, "crc": 44087049}, {"key": "androidx/core/app/OnNewIntentProvider.class", "name": "androidx/core/app/OnNewIntentProvider.class", "size": 846, "crc": -**********}, {"key": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "name": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "size": 990, "crc": -**********}, {"key": "androidx/core/app/OnUserLeaveHintProvider.class", "name": "androidx/core/app/OnUserLeaveHintProvider.class", "size": 713, "crc": 16295445}, {"key": "androidx/core/app/PictureInPictureModeChangedInfo.class", "name": "androidx/core/app/PictureInPictureModeChangedInfo.class", "size": 2103, "crc": -**********}, {"key": "androidx/core/content/OnConfigurationChangedProvider.class", "name": "androidx/core/content/OnConfigurationChangedProvider.class", "size": 931, "crc": 353118657}, {"key": "androidx/core/content/OnTrimMemoryProvider.class", "name": "androidx/core/content/OnTrimMemoryProvider.class", "size": 827, "crc": **********}, {"key": "androidx/core/hardware/display/ExperimentalDisplayApi.class", "name": "androidx/core/hardware/display/ExperimentalDisplayApi.class", "size": 954, "crc": -**********}, {"key": "androidx/core/os/BufferFillPolicy$Companion.class", "name": "androidx/core/os/BufferFillPolicy$Companion.class", "size": 923, "crc": -903949222}, {"key": "androidx/core/os/BufferFillPolicy$Discard.class", "name": "androidx/core/os/BufferFillPolicy$Discard.class", "size": 672, "crc": **********}, {"key": "androidx/core/os/BufferFillPolicy$RingBuffer.class", "name": "androidx/core/os/BufferFillPolicy$RingBuffer.class", "size": 681, "crc": -**********}, {"key": "androidx/core/os/BufferFillPolicy.class", "name": "androidx/core/os/BufferFillPolicy.class", "size": 1765, "crc": -**********}, {"key": "androidx/core/os/BuildCompat$Api30Impl.class", "name": "androidx/core/os/BuildCompat$Api30Impl.class", "size": 1023, "crc": **********}, {"key": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "name": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "size": 781, "crc": -1758429442}, {"key": "androidx/core/os/BuildCompat.class", "name": "androidx/core/os/BuildCompat.class", "size": 8431, "crc": 205634576}, {"key": "androidx/core/os/HeapProfileRequestBuilder.class", "name": "androidx/core/os/HeapProfileRequestBuilder.class", "size": 2797, "crc": -1067804288}, {"key": "androidx/core/os/JavaHeapDumpRequestBuilder.class", "name": "androidx/core/os/JavaHeapDumpRequestBuilder.class", "size": 2014, "crc": 1077565897}, {"key": "androidx/core/os/Profiling$registerForAllProfilingResults$1$2.class", "name": "androidx/core/os/Profiling$registerForAllProfilingResults$1$2.class", "size": 1623, "crc": 1826417670}, {"key": "androidx/core/os/Profiling$registerForAllProfilingResults$1.class", "name": "androidx/core/os/Profiling$registerForAllProfilingResults$1.class", "size": 5214, "crc": 1352344796}, {"key": "androidx/core/os/Profiling.class", "name": "androidx/core/os/Profiling.class", "size": 4465, "crc": -1061401170}, {"key": "androidx/core/os/ProfilingRequest.class", "name": "androidx/core/os/ProfilingRequest.class", "size": 1900, "crc": 204398187}, {"key": "androidx/core/os/ProfilingRequestBuilder.class", "name": "androidx/core/os/ProfilingRequestBuilder.class", "size": 2651, "crc": 1065412446}, {"key": "androidx/core/os/StackSamplingRequestBuilder.class", "name": "androidx/core/os/StackSamplingRequestBuilder.class", "size": 2372, "crc": -1048869707}, {"key": "androidx/core/os/SystemTraceRequestBuilder.class", "name": "androidx/core/os/SystemTraceRequestBuilder.class", "size": 2744, "crc": -160819412}, {"key": "androidx/core/util/Consumer.class", "name": "androidx/core/util/Consumer.class", "size": 559, "crc": -1040353088}, {"key": "androidx/core/util/Function.class", "name": "androidx/core/util/Function.class", "size": 601, "crc": -1585725341}, {"key": "androidx/core/util/Pools$Pool.class", "name": "androidx/core/util/Pools$Pool.class", "size": 886, "crc": 1271013518}, {"key": "androidx/core/util/Pools$SimplePool.class", "name": "androidx/core/util/Pools$SimplePool.class", "size": 3066, "crc": -1900672299}, {"key": "androidx/core/util/Pools$SynchronizedPool.class", "name": "androidx/core/util/Pools$SynchronizedPool.class", "size": 1924, "crc": -69752980}, {"key": "androidx/core/util/Pools.class", "name": "androidx/core/util/Pools.class", "size": 710, "crc": 1614321609}, {"key": "androidx/core/util/Supplier.class", "name": "androidx/core/util/Supplier.class", "size": 525, "crc": 1418066445}, {"key": "android/support/v4/app/INotificationSideChannel$Default.class", "name": "android/support/v4/app/INotificationSideChannel$Default.class", "size": 1135, "crc": -395248935}, {"key": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "name": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "size": 2463, "crc": -839898487}, {"key": "android/support/v4/app/INotificationSideChannel$Stub.class", "name": "android/support/v4/app/INotificationSideChannel$Stub.class", "size": 2803, "crc": -1214087703}, {"key": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "name": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "size": 1753, "crc": -1278251948}, {"key": "android/support/v4/app/INotificationSideChannel.class", "name": "android/support/v4/app/INotificationSideChannel.class", "size": 1205, "crc": 1388408453}, {"key": "android/support/v4/app/RemoteActionCompatParcelizer.class", "name": "android/support/v4/app/RemoteActionCompatParcelizer.class", "size": 1104, "crc": -587702565}, {"key": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "name": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "size": 1132, "crc": 1368227130}, {"key": "android/support/v4/os/IResultReceiver$Default.class", "name": "android/support/v4/os/IResultReceiver$Default.class", "size": 727, "crc": -1405212393}, {"key": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "size": 1698, "crc": -1328091256}, {"key": "android/support/v4/os/IResultReceiver$Stub.class", "name": "android/support/v4/os/IResultReceiver$Stub.class", "size": 2268, "crc": 649748008}, {"key": "android/support/v4/os/IResultReceiver$_Parcel.class", "name": "android/support/v4/os/IResultReceiver$_Parcel.class", "size": 1714, "crc": 1885126233}, {"key": "android/support/v4/os/IResultReceiver.class", "name": "android/support/v4/os/IResultReceiver.class", "size": 977, "crc": 305690791}, {"key": "android/support/v4/os/IResultReceiver2$Default.class", "name": "android/support/v4/os/IResultReceiver2$Default.class", "size": 731, "crc": -1958681237}, {"key": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "size": 1704, "crc": 876312633}, {"key": "android/support/v4/os/IResultReceiver2$Stub.class", "name": "android/support/v4/os/IResultReceiver2$Stub.class", "size": 2275, "crc": 124292307}, {"key": "android/support/v4/os/IResultReceiver2$_Parcel.class", "name": "android/support/v4/os/IResultReceiver2$_Parcel.class", "size": 1718, "crc": -149676984}, {"key": "android/support/v4/os/IResultReceiver2.class", "name": "android/support/v4/os/IResultReceiver2.class", "size": 983, "crc": 890230104}, {"key": "android/support/v4/os/ResultReceiver$1.class", "name": "android/support/v4/os/ResultReceiver$1.class", "size": 1218, "crc": -1040475466}, {"key": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "name": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "size": 1211, "crc": 1005788381}, {"key": "android/support/v4/os/ResultReceiver$MyRunnable.class", "name": "android/support/v4/os/ResultReceiver$MyRunnable.class", "size": 924, "crc": -191193726}, {"key": "android/support/v4/os/ResultReceiver.class", "name": "android/support/v4/os/ResultReceiver.class", "size": 2976, "crc": -2069896512}, {"key": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "name": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "size": 3301, "crc": 1610425752}, {"key": "androidx/core/app/ActivityCompat$1.class", "name": "androidx/core/app/ActivityCompat$1.class", "size": 1622, "crc": -1174975872}, {"key": "androidx/core/app/ActivityCompat$Api21Impl.class", "name": "androidx/core/app/ActivityCompat$Api21Impl.class", "size": 1289, "crc": 1660212579}, {"key": "androidx/core/app/ActivityCompat$Api22Impl.class", "name": "androidx/core/app/ActivityCompat$Api22Impl.class", "size": 717, "crc": 344518849}, {"key": "androidx/core/app/ActivityCompat$Api23Impl.class", "name": "androidx/core/app/ActivityCompat$Api23Impl.class", "size": 1370, "crc": -4915554}, {"key": "androidx/core/app/ActivityCompat$Api28Impl.class", "name": "androidx/core/app/ActivityCompat$Api28Impl.class", "size": 819, "crc": 2103680737}, {"key": "androidx/core/app/ActivityCompat$Api30Impl.class", "name": "androidx/core/app/ActivityCompat$Api30Impl.class", "size": 1496, "crc": 995355397}, {"key": "androidx/core/app/ActivityCompat$Api31Impl.class", "name": "androidx/core/app/ActivityCompat$Api31Impl.class", "size": 2023, "crc": 1424974982}, {"key": "androidx/core/app/ActivityCompat$Api32Impl.class", "name": "androidx/core/app/ActivityCompat$Api32Impl.class", "size": 791, "crc": -1709662383}, {"key": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "name": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "size": 424, "crc": 1525460251}, {"key": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "name": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "size": 691, "crc": -2091290924}, {"key": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "name": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "size": 584, "crc": 780710089}, {"key": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "name": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "size": 4428, "crc": 2075133486}, {"key": "androidx/core/app/ActivityCompat.class", "name": "androidx/core/app/ActivityCompat.class", "size": 10781, "crc": 1864551780}, {"key": "androidx/core/app/ActivityManagerCompat.class", "name": "androidx/core/app/ActivityManagerCompat.class", "size": 797, "crc": -1705456260}, {"key": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "name": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "size": 3052, "crc": -97065832}, {"key": "androidx/core/app/ActivityOptionsCompat$BackgroundActivityStartMode.class", "name": "androidx/core/app/ActivityOptionsCompat$BackgroundActivityStartMode.class", "size": 686, "crc": -1463159492}, {"key": "androidx/core/app/ActivityOptionsCompat.class", "name": "androidx/core/app/ActivityOptionsCompat.class", "size": 5582, "crc": 2036382413}, {"key": "androidx/core/app/ActivityRecreator$1.class", "name": "androidx/core/app/ActivityRecreator$1.class", "size": 964, "crc": -338343361}, {"key": "androidx/core/app/ActivityRecreator$2.class", "name": "androidx/core/app/ActivityRecreator$2.class", "size": 1176, "crc": -568525450}, {"key": "androidx/core/app/ActivityRecreator$3.class", "name": "androidx/core/app/ActivityRecreator$3.class", "size": 1787, "crc": -879425643}, {"key": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "name": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "size": 2055, "crc": -1616267938}, {"key": "androidx/core/app/ActivityRecreator.class", "name": "androidx/core/app/ActivityRecreator.class", "size": 6249, "crc": 653288581}, {"key": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "size": 1249, "crc": 334438665}, {"key": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "size": 1034, "crc": 944016811}, {"key": "androidx/core/app/AlarmManagerCompat$Api31Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api31Impl.class", "size": 728, "crc": -1943292532}, {"key": "androidx/core/app/AlarmManagerCompat.class", "name": "androidx/core/app/AlarmManagerCompat.class", "size": 2587, "crc": -780113557}, {"key": "androidx/core/app/AppComponentFactory.class", "name": "androidx/core/app/AppComponentFactory.class", "size": 4298, "crc": -93918636}, {"key": "androidx/core/app/AppLaunchChecker.class", "name": "androidx/core/app/AppLaunchChecker.class", "size": 2273, "crc": 1221859131}, {"key": "androidx/core/app/AppLocalesStorageHelper.class", "name": "androidx/core/app/AppLocalesStorageHelper.class", "size": 5090, "crc": 1511653288}, {"key": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "size": 1595, "crc": -1822782310}, {"key": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "size": 1522, "crc": 967207321}, {"key": "androidx/core/app/AppOpsManagerCompat.class", "name": "androidx/core/app/AppOpsManagerCompat.class", "size": 3129, "crc": 1167178665}, {"key": "androidx/core/app/BundleCompat.class", "name": "androidx/core/app/BundleCompat.class", "size": 1232, "crc": 1972167974}, {"key": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "name": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "size": 541, "crc": -1240361783}, {"key": "androidx/core/app/CoreComponentFactory.class", "name": "androidx/core/app/CoreComponentFactory.class", "size": 2948, "crc": 1494675766}, {"key": "androidx/core/app/DialogCompat$Api28Impl.class", "name": "androidx/core/app/DialogCompat$Api28Impl.class", "size": 801, "crc": 2069199098}, {"key": "androidx/core/app/DialogCompat.class", "name": "androidx/core/app/DialogCompat.class", "size": 1147, "crc": -185806469}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "size": 1889, "crc": 455752564}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "size": 4121, "crc": 1358986076}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "size": 981, "crc": 992552112}, {"key": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "name": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "size": 668, "crc": -1630363674}, {"key": "androidx/core/app/FrameMetricsAggregator.class", "name": "androidx/core/app/FrameMetricsAggregator.class", "size": 2547, "crc": -450773080}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat$Api34Impl.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat$Api34Impl.class", "size": 1288, "crc": 638227594}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat$GrammaticalGender.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat$GrammaticalGender.class", "size": 705, "crc": -768123921}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat.class", "size": 1739, "crc": -101847125}, {"key": "androidx/core/app/JobIntentService$CommandProcessor.class", "name": "androidx/core/app/JobIntentService$CommandProcessor.class", "size": 1888, "crc": 1566113380}, {"key": "androidx/core/app/JobIntentService$CompatJobEngine.class", "name": "androidx/core/app/JobIntentService$CompatJobEngine.class", "size": 448, "crc": 1210592104}, {"key": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "size": 2923, "crc": 2126471408}, {"key": "androidx/core/app/JobIntentService$CompatWorkItem.class", "name": "androidx/core/app/JobIntentService$CompatWorkItem.class", "size": 1059, "crc": 1421458690}, {"key": "androidx/core/app/JobIntentService$GenericWorkItem.class", "name": "androidx/core/app/JobIntentService$GenericWorkItem.class", "size": 310, "crc": 683365043}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "size": 1512, "crc": -1693114504}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "size": 2592, "crc": -782143900}, {"key": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "size": 1852, "crc": 1877092176}, {"key": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "size": 1443, "crc": 86240402}, {"key": "androidx/core/app/JobIntentService.class", "name": "androidx/core/app/JobIntentService.class", "size": 7139, "crc": -1930501259}, {"key": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "size": 723, "crc": 1588145806}, {"key": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "size": 1008, "crc": 644887433}, {"key": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "size": 1001, "crc": 1667324748}, {"key": "androidx/core/app/LocaleManagerCompat.class", "name": "androidx/core/app/LocaleManagerCompat.class", "size": 3032, "crc": 307582746}, {"key": "androidx/core/app/NavUtils.class", "name": "androidx/core/app/NavUtils.class", "size": 5773, "crc": -947779094}, {"key": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "name": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "size": 597, "crc": -1985100633}, {"key": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "size": 4038, "crc": -1589082301}, {"key": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "size": 767, "crc": -1294422338}, {"key": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "size": 1333, "crc": 1118088892}, {"key": "androidx/core/app/NotificationChannelCompat$Builder.class", "name": "androidx/core/app/NotificationChannelCompat$Builder.class", "size": 3480, "crc": 422155092}, {"key": "androidx/core/app/NotificationChannelCompat.class", "name": "androidx/core/app/NotificationChannelCompat.class", "size": 7016, "crc": 1010650408}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "size": 1838, "crc": -355277085}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "size": 1203, "crc": -268813334}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "size": 1380, "crc": -291700344}, {"key": "androidx/core/app/NotificationChannelGroupCompat.class", "name": "androidx/core/app/NotificationChannelGroupCompat.class", "size": 4904, "crc": -1198276078}, {"key": "androidx/core/app/NotificationCompat$1.class", "name": "androidx/core/app/NotificationCompat$1.class", "size": 238, "crc": -598238096}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "size": 1155, "crc": -1937915801}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "size": 995, "crc": 460978622}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "size": 950, "crc": 1968739977}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "size": 943, "crc": 436442048}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "size": 938, "crc": -538753392}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "size": 950, "crc": 1447456100}, {"key": "androidx/core/app/NotificationCompat$Action$Builder.class", "name": "androidx/core/app/NotificationCompat$Action$Builder.class", "size": 9194, "crc": 1407500987}, {"key": "androidx/core/app/NotificationCompat$Action$Extender.class", "name": "androidx/core/app/NotificationCompat$Action$Extender.class", "size": 594, "crc": 941757456}, {"key": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "name": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "size": 492, "crc": 271952193}, {"key": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "size": 5026, "crc": 1197185246}, {"key": "androidx/core/app/NotificationCompat$Action.class", "name": "androidx/core/app/NotificationCompat$Action.class", "size": 5696, "crc": 272383432}, {"key": "androidx/core/app/NotificationCompat$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Api20Impl.class", "size": 2061, "crc": 1148619638}, {"key": "androidx/core/app/NotificationCompat$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Api23Impl.class", "size": 833, "crc": -1525641747}, {"key": "androidx/core/app/NotificationCompat$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Api24Impl.class", "size": 788, "crc": -880374876}, {"key": "androidx/core/app/NotificationCompat$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$Api26Impl.class", "size": 1358, "crc": 77163447}, {"key": "androidx/core/app/NotificationCompat$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Api28Impl.class", "size": 781, "crc": -736689074}, {"key": "androidx/core/app/NotificationCompat$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Api29Impl.class", "size": 1624, "crc": 1748413076}, {"key": "androidx/core/app/NotificationCompat$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Api31Impl.class", "size": 788, "crc": 1197323786}, {"key": "androidx/core/app/NotificationCompat$BadgeIconType.class", "name": "androidx/core/app/NotificationCompat$BadgeIconType.class", "size": 662, "crc": -1600074890}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "size": 1111, "crc": 272375061}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "size": 1668, "crc": -1573123284}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "size": 7417, "crc": -321491029}, {"key": "androidx/core/app/NotificationCompat$BigTextStyle.class", "name": "androidx/core/app/NotificationCompat$BigTextStyle.class", "size": 3850, "crc": -158713259}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "size": 3547, "crc": 1665397818}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "size": 3624, "crc": 274812560}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "size": 4763, "crc": 1266297987}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "size": 4349, "crc": -1905113207}, {"key": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "size": 1503, "crc": 2080029461}, {"key": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "size": 946, "crc": 482486944}, {"key": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "size": 1356, "crc": 1476158005}, {"key": "androidx/core/app/NotificationCompat$Builder.class", "name": "androidx/core/app/NotificationCompat$Builder.class", "size": 28383, "crc": -947103670}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "size": 1989, "crc": -1076450246}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "size": 1121, "crc": -278366764}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "size": 1687, "crc": 2055028797}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "size": 1085, "crc": 43943028}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "size": 1148, "crc": -2137030556}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "size": 3376, "crc": -1268300084}, {"key": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "name": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "size": 734, "crc": 2136238121}, {"key": "androidx/core/app/NotificationCompat$CallStyle.class", "name": "androidx/core/app/NotificationCompat$CallStyle.class", "size": 16387, "crc": 1417828041}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "size": 3168, "crc": 1064169266}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "size": 825, "crc": 1556403842}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "size": 2864, "crc": 1797862573}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "size": 2327, "crc": -1429973617}, {"key": "androidx/core/app/NotificationCompat$CarExtender.class", "name": "androidx/core/app/NotificationCompat$CarExtender.class", "size": 9219, "crc": -1950054729}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "size": 934, "crc": 140076654}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "size": 9766, "crc": 413361634}, {"key": "androidx/core/app/NotificationCompat$Extender.class", "name": "androidx/core/app/NotificationCompat$Extender.class", "size": 500, "crc": -45559143}, {"key": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "name": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "size": 672, "crc": 900946520}, {"key": "androidx/core/app/NotificationCompat$InboxStyle.class", "name": "androidx/core/app/NotificationCompat$InboxStyle.class", "size": 4075, "crc": 1658679102}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "size": 1710, "crc": -819139740}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "size": 1239, "crc": 910409086}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "size": 1258, "crc": -1888016414}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "size": 1663, "crc": 1181749095}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "size": 1354, "crc": -2121017148}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "size": 8047, "crc": 1006731628}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle.class", "size": 14155, "crc": -615015107}, {"key": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "name": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "size": 680, "crc": 1088614023}, {"key": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "name": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "size": 690, "crc": 1857766983}, {"key": "androidx/core/app/NotificationCompat$StreamType.class", "name": "androidx/core/app/NotificationCompat$StreamType.class", "size": 656, "crc": -1750087649}, {"key": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "size": 867, "crc": -549277025}, {"key": "androidx/core/app/NotificationCompat$Style.class", "name": "androidx/core/app/NotificationCompat$Style.class", "size": 16066, "crc": 1801310707}, {"key": "androidx/core/app/NotificationCompat$TvExtender.class", "name": "androidx/core/app/NotificationCompat$TvExtender.class", "size": 4481, "crc": -417663337}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "size": 2659, "crc": -352436861}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "size": 1230, "crc": 1330262692}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "size": 1109, "crc": 118467851}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "size": 1111, "crc": 277423310}, {"key": "androidx/core/app/NotificationCompat$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$WearableExtender.class", "size": 18047, "crc": -1250616918}, {"key": "androidx/core/app/NotificationCompat.class", "name": "androidx/core/app/NotificationCompat.class", "size": 24153, "crc": -2100675414}, {"key": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "size": 3235, "crc": 1792654946}, {"key": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "size": 2084, "crc": -1196623425}, {"key": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "size": 1752, "crc": 1977635469}, {"key": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "size": 1941, "crc": -1982675874}, {"key": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "size": 2344, "crc": 1048995611}, {"key": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "size": 1368, "crc": 211638304}, {"key": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "size": 2059, "crc": -591088525}, {"key": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "size": 1348, "crc": -1366778517}, {"key": "androidx/core/app/NotificationCompatBuilder.class", "name": "androidx/core/app/NotificationCompatBuilder.class", "size": 20239, "crc": -2046026103}, {"key": "androidx/core/app/NotificationCompatExtras.class", "name": "androidx/core/app/NotificationCompatExtras.class", "size": 780, "crc": 1230724101}, {"key": "androidx/core/app/NotificationCompatJellybean.class", "name": "androidx/core/app/NotificationCompatJellybean.class", "size": 12786, "crc": 594665231}, {"key": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "name": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "size": 2096, "crc": -297013415}, {"key": "androidx/core/app/NotificationCompatSideChannelService.class", "name": "androidx/core/app/NotificationCompatSideChannelService.class", "size": 2299, "crc": -1251234116}, {"key": "androidx/core/app/NotificationManagerCompat$Api23Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api23Impl.class", "size": 1370, "crc": 391841863}, {"key": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "size": 913, "crc": 1010958628}, {"key": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "size": 3436, "crc": 898599105}, {"key": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "size": 944, "crc": 550715644}, {"key": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "size": 1254, "crc": -1615888729}, {"key": "androidx/core/app/NotificationManagerCompat$Api34Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api34Impl.class", "size": 780, "crc": 391167540}, {"key": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "name": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "size": 1911, "crc": 350749370}, {"key": "androidx/core/app/NotificationManagerCompat$InterruptionFilter.class", "name": "androidx/core/app/NotificationManagerCompat$InterruptionFilter.class", "size": 693, "crc": 1507578914}, {"key": "androidx/core/app/NotificationManagerCompat$NotificationWithIdAndTag.class", "name": "androidx/core/app/NotificationManagerCompat$NotificationWithIdAndTag.class", "size": 1043, "crc": -1183023852}, {"key": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "name": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "size": 1721, "crc": -1099592384}, {"key": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "name": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "size": 702, "crc": -1546210805}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "size": 1146, "crc": 2082083199}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "size": 10593, "crc": -2094048904}, {"key": "androidx/core/app/NotificationManagerCompat$Task.class", "name": "androidx/core/app/NotificationManagerCompat$Task.class", "size": 366, "crc": 1719925620}, {"key": "androidx/core/app/NotificationManagerCompat.class", "name": "androidx/core/app/NotificationManagerCompat.class", "size": 18792, "crc": 1303887317}, {"key": "androidx/core/app/PendingIntentCompat$Api23Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api23Impl.class", "size": 1662, "crc": 650721696}, {"key": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "size": 861, "crc": 340318974}, {"key": "androidx/core/app/PendingIntentCompat$Flags.class", "name": "androidx/core/app/PendingIntentCompat$Flags.class", "size": 636, "crc": -1100352364}, {"key": "androidx/core/app/PendingIntentCompat$GatedCallback.class", "name": "androidx/core/app/PendingIntentCompat$GatedCallback.class", "size": 2730, "crc": 1316680308}, {"key": "androidx/core/app/PendingIntentCompat.class", "name": "androidx/core/app/PendingIntentCompat.class", "size": 5939, "crc": 685275768}, {"key": "androidx/core/app/Person$Api22Impl.class", "name": "androidx/core/app/Person$Api22Impl.class", "size": 2031, "crc": -390350765}, {"key": "androidx/core/app/Person$Api28Impl.class", "name": "androidx/core/app/Person$Api28Impl.class", "size": 2423, "crc": 822558955}, {"key": "androidx/core/app/Person$Builder.class", "name": "androidx/core/app/Person$Builder.class", "size": 2128, "crc": 1315491807}, {"key": "androidx/core/app/Person.class", "name": "androidx/core/app/Person.class", "size": 6380, "crc": 2015119602}, {"key": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "size": 2006, "crc": 962029633}, {"key": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "size": 870, "crc": 1534514586}, {"key": "androidx/core/app/RemoteActionCompat.class", "name": "androidx/core/app/RemoteActionCompat.class", "size": 4608, "crc": 1025213360}, {"key": "androidx/core/app/RemoteActionCompatParcelizer.class", "name": "androidx/core/app/RemoteActionCompatParcelizer.class", "size": 2304, "crc": -505423669}, {"key": "androidx/core/app/RemoteInput$Api20Impl.class", "name": "androidx/core/app/RemoteInput$Api20Impl.class", "size": 4200, "crc": 1500486877}, {"key": "androidx/core/app/RemoteInput$Api26Impl.class", "name": "androidx/core/app/RemoteInput$Api26Impl.class", "size": 2166, "crc": -2037165290}, {"key": "androidx/core/app/RemoteInput$Api28Impl.class", "name": "androidx/core/app/RemoteInput$Api28Impl.class", "size": 826, "crc": 615281271}, {"key": "androidx/core/app/RemoteInput$Api29Impl.class", "name": "androidx/core/app/RemoteInput$Api29Impl.class", "size": 1045, "crc": -381936367}, {"key": "androidx/core/app/RemoteInput$Builder.class", "name": "androidx/core/app/RemoteInput$Builder.class", "size": 2967, "crc": 1558759301}, {"key": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "name": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "size": 663, "crc": 137218255}, {"key": "androidx/core/app/RemoteInput$Source.class", "name": "androidx/core/app/RemoteInput$Source.class", "size": 627, "crc": 1225634699}, {"key": "androidx/core/app/RemoteInput.class", "name": "androidx/core/app/RemoteInput.class", "size": 10706, "crc": -1108039798}, {"key": "androidx/core/app/ServiceCompat$Api24Impl.class", "name": "androidx/core/app/ServiceCompat$Api24Impl.class", "size": 709, "crc": 2029128675}, {"key": "androidx/core/app/ServiceCompat$Api29Impl.class", "name": "androidx/core/app/ServiceCompat$Api29Impl.class", "size": 945, "crc": 2026720460}, {"key": "androidx/core/app/ServiceCompat$Api34Impl.class", "name": "androidx/core/app/ServiceCompat$Api34Impl.class", "size": 949, "crc": -38842484}, {"key": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "name": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "size": 659, "crc": 655432038}, {"key": "androidx/core/app/ServiceCompat.class", "name": "androidx/core/app/ServiceCompat.class", "size": 1774, "crc": -1101942020}, {"key": "androidx/core/app/ShareCompat$IntentBuilder.class", "name": "androidx/core/app/ShareCompat$IntentBuilder.class", "size": 8649, "crc": -129418730}, {"key": "androidx/core/app/ShareCompat$IntentReader.class", "name": "androidx/core/app/ShareCompat$IntentReader.class", "size": 7028, "crc": -1574706802}, {"key": "androidx/core/app/ShareCompat.class", "name": "androidx/core/app/ShareCompat.class", "size": 5920, "crc": -702129277}, {"key": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "name": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "size": 317, "crc": 1217450772}, {"key": "androidx/core/app/SharedElementCallback.class", "name": "androidx/core/app/SharedElementCallback.class", "size": 7188, "crc": 376381531}, {"key": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "name": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "size": 394, "crc": -1915356188}, {"key": "androidx/core/app/TaskStackBuilder.class", "name": "androidx/core/app/TaskStackBuilder.class", "size": 6961, "crc": -460153903}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "size": 868, "crc": 1227855709}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "size": 1775, "crc": -1351993899}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "size": 2239, "crc": -900432979}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "size": 1116, "crc": 1361675367}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "size": 992, "crc": -957843627}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "size": 1945, "crc": **********}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "size": 2617, "crc": 216898320}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "size": 1181, "crc": -**********}, {"key": "androidx/core/content/ContentProviderCompat.class", "name": "androidx/core/content/ContentProviderCompat.class", "size": 950, "crc": -**********}, {"key": "androidx/core/content/ContentResolverCompat.class", "name": "androidx/core/content/ContentResolverCompat.class", "size": 2328, "crc": -740477848}, {"key": "androidx/core/content/ContextCompat$Api21Impl.class", "name": "androidx/core/content/ContextCompat$Api21Impl.class", "size": 1034, "crc": -923536734}, {"key": "androidx/core/content/ContextCompat$Api23Impl.class", "name": "androidx/core/content/ContextCompat$Api23Impl.class", "size": 1449, "crc": -**********}, {"key": "androidx/core/content/ContextCompat$Api24Impl.class", "name": "androidx/core/content/ContextCompat$Api24Impl.class", "size": 1051, "crc": -565598928}, {"key": "androidx/core/content/ContextCompat$Api26Impl.class", "name": "androidx/core/content/ContextCompat$Api26Impl.class", "size": 1901, "crc": -177393960}, {"key": "androidx/core/content/ContextCompat$Api28Impl.class", "name": "androidx/core/content/ContextCompat$Api28Impl.class", "size": 761, "crc": 1478074448}, {"key": "androidx/core/content/ContextCompat$Api30Impl.class", "name": "androidx/core/content/ContextCompat$Api30Impl.class", "size": 2089, "crc": 469095007}, {"key": "androidx/core/content/ContextCompat$Api33Impl.class", "name": "androidx/core/content/ContextCompat$Api33Impl.class", "size": 1279, "crc": -1053967695}, {"key": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "name": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "size": 4358, "crc": **********}, {"key": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "name": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "size": 658, "crc": 794825751}, {"key": "androidx/core/content/ContextCompat.class", "name": "androidx/core/content/ContextCompat.class", "size": 13460, "crc": **********}, {"key": "androidx/core/content/FileProvider$Api21Impl.class", "name": "androidx/core/content/FileProvider$Api21Impl.class", "size": 734, "crc": -931847650}, {"key": "androidx/core/content/FileProvider$PathStrategy.class", "name": "androidx/core/content/FileProvider$PathStrategy.class", "size": 346, "crc": 770878075}, {"key": "androidx/core/content/FileProvider$SimplePathStrategy.class", "name": "androidx/core/content/FileProvider$SimplePathStrategy.class", "size": 4719, "crc": -813009308}, {"key": "androidx/core/content/FileProvider.class", "name": "androidx/core/content/FileProvider.class", "size": 13750, "crc": **********}, {"key": "androidx/core/content/IntentCompat$Api33Impl.class", "name": "androidx/core/content/IntentCompat$Api33Impl.class", "size": 2493, "crc": -314216379}, {"key": "androidx/core/content/IntentCompat.class", "name": "androidx/core/content/IntentCompat.class", "size": 5533, "crc": -**********}, {"key": "androidx/core/content/IntentSanitizer$1.class", "name": "androidx/core/content/IntentSanitizer$1.class", "size": 237, "crc": -61702665}, {"key": "androidx/core/content/IntentSanitizer$Api29Impl.class", "name": "androidx/core/content/IntentSanitizer$Api29Impl.class", "size": 991, "crc": 821829029}, {"key": "androidx/core/content/IntentSanitizer$Api31Impl.class", "name": "androidx/core/content/IntentSanitizer$Api31Impl.class", "size": 1693, "crc": 626577617}, {"key": "androidx/core/content/IntentSanitizer$Builder.class", "name": "androidx/core/content/IntentSanitizer$Builder.class", "size": 13867, "crc": **********}, {"key": "androidx/core/content/IntentSanitizer.class", "name": "androidx/core/content/IntentSanitizer.class", "size": 13102, "crc": 173240952}, {"key": "androidx/core/content/LocusIdCompat$Api29Impl.class", "name": "androidx/core/content/LocusIdCompat$Api29Impl.class", "size": 1014, "crc": 180322650}, {"key": "androidx/core/content/LocusIdCompat.class", "name": "androidx/core/content/LocusIdCompat.class", "size": 2809, "crc": 230114922}, {"key": "androidx/core/content/MimeTypeFilter.class", "name": "androidx/core/content/MimeTypeFilter.class", "size": 2712, "crc": 145487149}, {"key": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "name": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "size": 1002, "crc": 1028433083}, {"key": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "name": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "size": 691, "crc": 2041315114}, {"key": "androidx/core/content/PackageManagerCompat.class", "name": "androidx/core/content/PackageManagerCompat.class", "size": 6199, "crc": 182105302}, {"key": "androidx/core/content/PermissionChecker$PermissionResult.class", "name": "androidx/core/content/PermissionChecker$PermissionResult.class", "size": 673, "crc": -666488629}, {"key": "androidx/core/content/PermissionChecker.class", "name": "androidx/core/content/PermissionChecker.class", "size": 2875, "crc": 589896192}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "size": 1116, "crc": -1988023331}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "size": 1381, "crc": -1689204474}, {"key": "androidx/core/content/SharedPreferencesCompat.class", "name": "androidx/core/content/SharedPreferencesCompat.class", "size": 532, "crc": 1567352910}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "size": 1290, "crc": 886441463}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "size": 1634, "crc": -619020854}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "size": 1592, "crc": -776689373}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "size": 1892, "crc": 1638763422}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "size": 4203, "crc": 995541241}, {"key": "androidx/core/content/UnusedAppRestrictionsConstants.class", "name": "androidx/core/content/UnusedAppRestrictionsConstants.class", "size": 588, "crc": -1244214169}, {"key": "androidx/core/content/UriMatcherCompat.class", "name": "androidx/core/content/UriMatcherCompat.class", "size": 1488, "crc": 735789542}, {"key": "androidx/core/content/pm/ActivityInfoCompat.class", "name": "androidx/core/content/pm/ActivityInfoCompat.class", "size": 493, "crc": -1364477614}, {"key": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "size": 1843, "crc": -115077841}, {"key": "androidx/core/content/pm/PackageInfoCompat.class", "name": "androidx/core/content/pm/PackageInfoCompat.class", "size": 6120, "crc": -2007868392}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "size": 869, "crc": -1267724071}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "size": 663, "crc": 1629059509}, {"key": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "name": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "size": 739, "crc": 1522065108}, {"key": "androidx/core/content/pm/PermissionInfoCompat.class", "name": "androidx/core/content/pm/PermissionInfoCompat.class", "size": 1366, "crc": -544089825}, {"key": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "name": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "size": 1709, "crc": -1952581504}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "size": 998, "crc": -1004518602}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "size": 12667, "crc": -700864668}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "size": 664, "crc": 282044856}, {"key": "androidx/core/content/pm/ShortcutInfoCompat.class", "name": "androidx/core/content/pm/ShortcutInfoCompat.class", "size": 12949, "crc": 1012136900}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "size": 1759, "crc": 2002495096}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "size": 1544, "crc": -976705994}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "size": 1316, "crc": -1899661881}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "size": 1498, "crc": -1242999488}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "size": 695, "crc": 856990973}, {"key": "androidx/core/content/pm/ShortcutManagerCompat.class", "name": "androidx/core/content/pm/ShortcutManagerCompat.class", "size": 20155, "crc": -129299186}, {"key": "androidx/core/content/pm/ShortcutXmlParser.class", "name": "androidx/core/content/pm/ShortcutXmlParser.class", "size": 5961, "crc": 1487523664}, {"key": "androidx/core/content/res/CamColor.class", "name": "androidx/core/content/res/CamColor.class", "size": 10302, "crc": -452418118}, {"key": "androidx/core/content/res/CamUtils.class", "name": "androidx/core/content/res/CamUtils.class", "size": 3150, "crc": -1606624112}, {"key": "androidx/core/content/res/ColorStateListInflaterCompat.class", "name": "androidx/core/content/res/ColorStateListInflaterCompat.class", "size": 8240, "crc": 1969455639}, {"key": "androidx/core/content/res/ComplexColorCompat.class", "name": "androidx/core/content/res/ComplexColorCompat.class", "size": 5290, "crc": 955734427}, {"key": "androidx/core/content/res/ConfigurationHelper.class", "name": "androidx/core/content/res/ConfigurationHelper.class", "size": 755, "crc": -1696505131}, {"key": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "name": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "size": 806, "crc": 1254746132}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "size": 287, "crc": -39793732}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "size": 454, "crc": 1823731370}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "size": 1239, "crc": -263360887}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "size": 1626, "crc": **********}, {"key": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "size": 2241, "crc": 227403463}, {"key": "androidx/core/content/res/FontResourcesParserCompat.class", "name": "androidx/core/content/res/FontResourcesParserCompat.class", "size": 9016, "crc": **********}, {"key": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "name": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "size": 1775, "crc": 48552735}, {"key": "androidx/core/content/res/GradientColorInflaterCompat.class", "name": "androidx/core/content/res/GradientColorInflaterCompat.class", "size": 7810, "crc": -**********}, {"key": "androidx/core/content/res/GrowingArrayUtils.class", "name": "androidx/core/content/res/GrowingArrayUtils.class", "size": 2719, "crc": **********}, {"key": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "size": 1352, "crc": **********}, {"key": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "size": 1484, "crc": -2063483255}, {"key": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "size": 925, "crc": 1538535462}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "size": 1314, "crc": 1544562731}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "size": 1501, "crc": 254353684}, {"key": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "name": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "size": 2665, "crc": 1299013137}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "size": 2301, "crc": -1511549074}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "size": 990, "crc": 1012874049}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "size": 1133, "crc": 1034709495}, {"key": "androidx/core/content/res/ResourcesCompat.class", "name": "androidx/core/content/res/ResourcesCompat.class", "size": 14466, "crc": -508022053}, {"key": "androidx/core/content/res/TypedArrayUtils.class", "name": "androidx/core/content/res/TypedArrayUtils.class", "size": 9217, "crc": 1952953540}, {"key": "androidx/core/content/res/ViewingConditions.class", "name": "androidx/core/content/res/ViewingConditions.class", "size": 3496, "crc": -524541016}, {"key": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "name": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "size": 807, "crc": -497307517}, {"key": "androidx/core/database/CursorWindowCompat.class", "name": "androidx/core/database/CursorWindowCompat.class", "size": 987, "crc": -1333299183}, {"key": "androidx/core/database/DatabaseUtilsCompat.class", "name": "androidx/core/database/DatabaseUtilsCompat.class", "size": 1371, "crc": -2044651927}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "size": 841, "crc": -2046928020}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "size": 901, "crc": 193607055}, {"key": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "size": 2497, "crc": -1293704289}, {"key": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "size": 830, "crc": 355444069}, {"key": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "size": 1034, "crc": -728687595}, {"key": "androidx/core/graphics/BitmapCompat.class", "name": "androidx/core/graphics/BitmapCompat.class", "size": 6299, "crc": -662288887}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "size": 889, "crc": 1145598921}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat.class", "size": 1804, "crc": -50451621}, {"key": "androidx/core/graphics/BlendModeCompat.class", "name": "androidx/core/graphics/BlendModeCompat.class", "size": 2933, "crc": 1687048290}, {"key": "androidx/core/graphics/BlendModeUtils$1.class", "name": "androidx/core/graphics/BlendModeUtils$1.class", "size": 2083, "crc": -392654013}, {"key": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "name": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "size": 2125, "crc": 160875147}, {"key": "androidx/core/graphics/BlendModeUtils.class", "name": "androidx/core/graphics/BlendModeUtils.class", "size": 1689, "crc": -381923774}, {"key": "androidx/core/graphics/ColorUtils$Api26Impl.class", "name": "androidx/core/graphics/ColorUtils$Api26Impl.class", "size": 2085, "crc": 859281138}, {"key": "androidx/core/graphics/ColorUtils.class", "name": "androidx/core/graphics/ColorUtils.class", "size": 11534, "crc": 564048506}, {"key": "androidx/core/graphics/Insets$Api29Impl.class", "name": "androidx/core/graphics/Insets$Api29Impl.class", "size": 693, "crc": -65060369}, {"key": "androidx/core/graphics/Insets.class", "name": "androidx/core/graphics/Insets.class", "size": 3803, "crc": -51805920}, {"key": "androidx/core/graphics/PaintCompat$Api23Impl.class", "name": "androidx/core/graphics/PaintCompat$Api23Impl.class", "size": 765, "crc": -1481367347}, {"key": "androidx/core/graphics/PaintCompat$Api29Impl.class", "name": "androidx/core/graphics/PaintCompat$Api29Impl.class", "size": 821, "crc": -224682413}, {"key": "androidx/core/graphics/PaintCompat.class", "name": "androidx/core/graphics/PaintCompat.class", "size": 4199, "crc": 1573026376}, {"key": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "name": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "size": 492, "crc": -984632330}, {"key": "androidx/core/graphics/PathParser$PathDataNode.class", "name": "androidx/core/graphics/PathParser$PathDataNode.class", "size": 9637, "crc": 123540718}, {"key": "androidx/core/graphics/PathParser.class", "name": "androidx/core/graphics/PathParser.class", "size": 8093, "crc": 20075510}, {"key": "androidx/core/graphics/PathSegment.class", "name": "androidx/core/graphics/PathSegment.class", "size": 2404, "crc": -1473567995}, {"key": "androidx/core/graphics/PathUtils$Api26Impl.class", "name": "androidx/core/graphics/PathUtils$Api26Impl.class", "size": 716, "crc": 1420785047}, {"key": "androidx/core/graphics/PathUtils.class", "name": "androidx/core/graphics/PathUtils.class", "size": 2222, "crc": -4451307}, {"key": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "name": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "size": 1720, "crc": -1188119896}, {"key": "androidx/core/graphics/TypefaceCompat.class", "name": "androidx/core/graphics/TypefaceCompat.class", "size": 11776, "crc": 688140795}, {"key": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "size": 9909, "crc": 1118438688}, {"key": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "size": 8157, "crc": -2143397204}, {"key": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "size": 13653, "crc": -202077350}, {"key": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "size": 3161, "crc": 1497520765}, {"key": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "size": 10087, "crc": 824092295}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "size": 1732, "crc": 345003103}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "size": 2041, "crc": 561875496}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "size": 2042, "crc": -1459310074}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "size": 446, "crc": 1807309830}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "size": 11331, "crc": 944645025}, {"key": "androidx/core/graphics/TypefaceCompatUtil.class", "name": "androidx/core/graphics/TypefaceCompatUtil.class", "size": 7726, "crc": 573941828}, {"key": "androidx/core/graphics/WeightTypefaceApi14.class", "name": "androidx/core/graphics/WeightTypefaceApi14.class", "size": 5222, "crc": -273406757}, {"key": "androidx/core/graphics/WeightTypefaceApi21.class", "name": "androidx/core/graphics/WeightTypefaceApi21.class", "size": 6055, "crc": 275438467}, {"key": "androidx/core/graphics/WeightTypefaceApi26.class", "name": "androidx/core/graphics/WeightTypefaceApi26.class", "size": 5540, "crc": 1603553730}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "size": 3146, "crc": -730980645}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "size": 951, "crc": 1216427941}, {"key": "androidx/core/graphics/drawable/DrawableCompat.class", "name": "androidx/core/graphics/drawable/DrawableCompat.class", "size": 8479, "crc": 1216306546}, {"key": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "size": 7782, "crc": 11313052}, {"key": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "size": 1200, "crc": 498120204}, {"key": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "size": 1092, "crc": 1131212206}, {"key": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "size": 754, "crc": -1302580962}, {"key": "androidx/core/graphics/drawable/IconCompat$IconType.class", "name": "androidx/core/graphics/drawable/IconCompat$IconType.class", "size": 643, "crc": 1843019074}, {"key": "androidx/core/graphics/drawable/IconCompat.class", "name": "androidx/core/graphics/drawable/IconCompat.class", "size": 23888, "crc": -1676503509}, {"key": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "name": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "size": 2391, "crc": -1370437693}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "size": 7877, "crc": -1128034670}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "size": 1864, "crc": 2137532621}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "size": 1631, "crc": -1721205194}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "size": 2617, "crc": -870313236}, {"key": "androidx/core/graphics/drawable/TintAwareDrawable.class", "name": "androidx/core/graphics/drawable/TintAwareDrawable.class", "size": 745, "crc": 1348044187}, {"key": "androidx/core/graphics/drawable/WrappedDrawable.class", "name": "androidx/core/graphics/drawable/WrappedDrawable.class", "size": 560, "crc": -747113056}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "size": 8979, "crc": -1890717726}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "size": 4152, "crc": -1520239930}, {"key": "androidx/core/graphics/drawable/WrappedDrawableState.class", "name": "androidx/core/graphics/drawable/WrappedDrawableState.class", "size": 2124, "crc": 132384012}, {"key": "androidx/core/hardware/display/DisplayManagerCompat.class", "name": "androidx/core/hardware/display/DisplayManagerCompat.class", "size": 4359, "crc": 1964134302}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "size": 2815, "crc": -1203016599}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "size": 4055, "crc": -2046050835}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "size": 1435, "crc": -1304719722}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "size": 1108, "crc": -1511417080}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "size": 1507, "crc": 1834709790}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "size": 5422, "crc": 1605172615}, {"key": "androidx/core/internal/package-info.class", "name": "androidx/core/internal/package-info.class", "size": 404, "crc": -1162714476}, {"key": "androidx/core/internal/view/SupportMenu.class", "name": "androidx/core/internal/view/SupportMenu.class", "size": 765, "crc": 1726273822}, {"key": "androidx/core/internal/view/SupportMenuItem.class", "name": "androidx/core/internal/view/SupportMenuItem.class", "size": 2870, "crc": -660853870}, {"key": "androidx/core/internal/view/SupportSubMenu.class", "name": "androidx/core/internal/view/SupportSubMenu.class", "size": 488, "crc": 341992331}, {"key": "androidx/core/location/GnssStatusCompat$Callback.class", "name": "androidx/core/location/GnssStatusCompat$Callback.class", "size": 1050, "crc": 1292473593}, {"key": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "name": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "size": 661, "crc": 600754332}, {"key": "androidx/core/location/GnssStatusCompat.class", "name": "androidx/core/location/GnssStatusCompat.class", "size": 2836, "crc": 1028378963}, {"key": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "size": 928, "crc": -2119839397}, {"key": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "size": 922, "crc": -1647988692}, {"key": "androidx/core/location/GnssStatusWrapper.class", "name": "androidx/core/location/GnssStatusWrapper.class", "size": 3112, "crc": -2122357746}, {"key": "androidx/core/location/GpsStatusWrapper.class", "name": "androidx/core/location/GpsStatusWrapper.class", "size": 4956, "crc": 1004775428}, {"key": "androidx/core/location/LocationCompat$Api26Impl.class", "name": "androidx/core/location/LocationCompat$Api26Impl.class", "size": 3257, "crc": -311540079}, {"key": "androidx/core/location/LocationCompat$Api28Impl.class", "name": "androidx/core/location/LocationCompat$Api28Impl.class", "size": 4044, "crc": 1775510598}, {"key": "androidx/core/location/LocationCompat$Api29Impl.class", "name": "androidx/core/location/LocationCompat$Api29Impl.class", "size": 1404, "crc": -1799543993}, {"key": "androidx/core/location/LocationCompat$Api31Impl.class", "name": "androidx/core/location/LocationCompat$Api31Impl.class", "size": 710, "crc": -1319650082}, {"key": "androidx/core/location/LocationCompat$Api33Impl.class", "name": "androidx/core/location/LocationCompat$Api33Impl.class", "size": 916, "crc": -871802774}, {"key": "androidx/core/location/LocationCompat$Api34Impl.class", "name": "androidx/core/location/LocationCompat$Api34Impl.class", "size": 1683, "crc": -1326418635}, {"key": "androidx/core/location/LocationCompat.class", "name": "androidx/core/location/LocationCompat.class", "size": 10383, "crc": 745378312}, {"key": "androidx/core/location/LocationListenerCompat.class", "name": "androidx/core/location/LocationListenerCompat.class", "size": 1563, "crc": -1475807042}, {"key": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "size": 3767, "crc": -261176859}, {"key": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "size": 3981, "crc": -223916297}, {"key": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "size": 1096, "crc": 255996146}, {"key": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "size": 5798, "crc": -899058146}, {"key": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "size": 2265, "crc": 1939678433}, {"key": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "name": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "size": 4706, "crc": -102020126}, {"key": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "name": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "size": 1188, "crc": 1084022117}, {"key": "androidx/core/location/LocationManagerCompat$GnssMeasurementsTransport.class", "name": "androidx/core/location/LocationManagerCompat$GnssMeasurementsTransport.class", "size": 2944, "crc": -1772502240}, {"key": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "size": 1738, "crc": 1406909359}, {"key": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "size": 4336, "crc": 1251424449}, {"key": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "name": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "size": 1597, "crc": 101042904}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "size": 1398, "crc": 988611971}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "size": 5158, "crc": 131966662}, {"key": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "size": 4097, "crc": 590112483}, {"key": "androidx/core/location/LocationManagerCompat.class", "name": "androidx/core/location/LocationManagerCompat.class", "size": 22730, "crc": 702380167}, {"key": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "size": 3015, "crc": 1927983784}, {"key": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "size": 1606, "crc": -793517752}, {"key": "androidx/core/location/LocationRequestCompat$Builder.class", "name": "androidx/core/location/LocationRequestCompat$Builder.class", "size": 3795, "crc": -684974547}, {"key": "androidx/core/location/LocationRequestCompat$Quality.class", "name": "androidx/core/location/LocationRequestCompat$Quality.class", "size": 656, "crc": -1304604809}, {"key": "androidx/core/location/LocationRequestCompat.class", "name": "androidx/core/location/LocationRequestCompat.class", "size": 4940, "crc": 178330590}, {"key": "androidx/core/math/MathUtils.class", "name": "androidx/core/math/MathUtils.class", "size": 2973, "crc": -1763529000}, {"key": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "name": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "size": 789, "crc": -1256022342}, {"key": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "name": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "size": 705, "crc": 716344203}, {"key": "androidx/core/net/ConnectivityManagerCompat.class", "name": "androidx/core/net/ConnectivityManagerCompat.class", "size": 2353, "crc": 1577470037}, {"key": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "size": 2647, "crc": 1217154937}, {"key": "androidx/core/net/DatagramSocketWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper.class", "size": 694, "crc": 1450467363}, {"key": "androidx/core/net/MailTo.class", "name": "androidx/core/net/MailTo.class", "size": 4760, "crc": -2053611098}, {"key": "androidx/core/net/ParseException.class", "name": "androidx/core/net/ParseException.class", "size": 503, "crc": 40838154}, {"key": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "name": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "size": 873, "crc": 745720342}, {"key": "androidx/core/net/TrafficStatsCompat.class", "name": "androidx/core/net/TrafficStatsCompat.class", "size": 2386, "crc": -1075377607}, {"key": "androidx/core/net/UriCompat.class", "name": "androidx/core/net/UriCompat.class", "size": 1825, "crc": 292042931}, {"key": "androidx/core/os/BundleCompat$Api33Impl.class", "name": "androidx/core/os/BundleCompat$Api33Impl.class", "size": 2851, "crc": -37861567}, {"key": "androidx/core/os/BundleCompat.class", "name": "androidx/core/os/BundleCompat.class", "size": 4370, "crc": 2070272812}, {"key": "androidx/core/os/CancellationSignal$OnCancelListener.class", "name": "androidx/core/os/CancellationSignal$OnCancelListener.class", "size": 267, "crc": 1768443177}, {"key": "androidx/core/os/CancellationSignal.class", "name": "androidx/core/os/CancellationSignal.class", "size": 2532, "crc": -465984517}, {"key": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "name": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "size": 1246, "crc": -1012191153}, {"key": "androidx/core/os/ConfigurationCompat.class", "name": "androidx/core/os/ConfigurationCompat.class", "size": 1638, "crc": 866049606}, {"key": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "name": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "size": 699, "crc": 225023687}, {"key": "androidx/core/os/EnvironmentCompat.class", "name": "androidx/core/os/EnvironmentCompat.class", "size": 1017, "crc": -1775750373}, {"key": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "name": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "size": 1396, "crc": -429742436}, {"key": "androidx/core/os/ExecutorCompat.class", "name": "androidx/core/os/ExecutorCompat.class", "size": 696, "crc": 1204857246}, {"key": "androidx/core/os/HandlerCompat$Api28Impl.class", "name": "androidx/core/os/HandlerCompat$Api28Impl.class", "size": 1266, "crc": 1188911207}, {"key": "androidx/core/os/HandlerCompat$Api29Impl.class", "name": "androidx/core/os/HandlerCompat$Api29Impl.class", "size": 750, "crc": -97291405}, {"key": "androidx/core/os/HandlerCompat.class", "name": "androidx/core/os/HandlerCompat.class", "size": 4933, "crc": 390955248}, {"key": "androidx/core/os/LocaleListCompat$Api21Impl.class", "name": "androidx/core/os/LocaleListCompat$Api21Impl.class", "size": 1938, "crc": 1103120099}, {"key": "androidx/core/os/LocaleListCompat$Api24Impl.class", "name": "androidx/core/os/LocaleListCompat$Api24Impl.class", "size": 897, "crc": 843497505}, {"key": "androidx/core/os/LocaleListCompat.class", "name": "androidx/core/os/LocaleListCompat.class", "size": 5763, "crc": -1523958033}, {"key": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "name": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "size": 736, "crc": -1773013710}, {"key": "androidx/core/os/LocaleListCompatWrapper.class", "name": "androidx/core/os/LocaleListCompatWrapper.class", "size": 6926, "crc": -1137754749}, {"key": "androidx/core/os/LocaleListInterface.class", "name": "androidx/core/os/LocaleListInterface.class", "size": 672, "crc": 49744619}, {"key": "androidx/core/os/LocaleListPlatformWrapper.class", "name": "androidx/core/os/LocaleListPlatformWrapper.class", "size": 1936, "crc": 480437374}, {"key": "androidx/core/os/MessageCompat$Api22Impl.class", "name": "androidx/core/os/MessageCompat$Api22Impl.class", "size": 824, "crc": 494949187}, {"key": "androidx/core/os/MessageCompat.class", "name": "androidx/core/os/MessageCompat.class", "size": 1393, "crc": 1839607600}, {"key": "androidx/core/os/OperationCanceledException.class", "name": "androidx/core/os/OperationCanceledException.class", "size": 734, "crc": -1776484526}, {"key": "androidx/core/os/OutcomeReceiverCompat.class", "name": "androidx/core/os/OutcomeReceiverCompat.class", "size": 729, "crc": -527939181}, {"key": "androidx/core/os/ParcelCompat$Api29Impl.class", "name": "androidx/core/os/ParcelCompat$Api29Impl.class", "size": 1361, "crc": -1079494510}, {"key": "androidx/core/os/ParcelCompat$Api30Impl.class", "name": "androidx/core/os/ParcelCompat$Api30Impl.class", "size": 1131, "crc": -1978835523}, {"key": "androidx/core/os/ParcelCompat$Api33Impl.class", "name": "androidx/core/os/ParcelCompat$Api33Impl.class", "size": 5708, "crc": 579793570}, {"key": "androidx/core/os/ParcelCompat.class", "name": "androidx/core/os/ParcelCompat.class", "size": 9103, "crc": -238900465}, {"key": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "name": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "size": 1865, "crc": -1411295453}, {"key": "androidx/core/os/ParcelableCompat.class", "name": "androidx/core/os/ParcelableCompat.class", "size": 1172, "crc": 276111255}, {"key": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "name": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "size": 521, "crc": -1661855711}, {"key": "androidx/core/os/ProcessCompat$Api19Impl.class", "name": "androidx/core/os/ProcessCompat$Api19Impl.class", "size": 1688, "crc": -612991505}, {"key": "androidx/core/os/ProcessCompat$Api24Impl.class", "name": "androidx/core/os/ProcessCompat$Api24Impl.class", "size": 628, "crc": -36967095}, {"key": "androidx/core/os/ProcessCompat.class", "name": "androidx/core/os/ProcessCompat.class", "size": 705, "crc": 1617640224}, {"key": "androidx/core/os/TraceCompat$Api29Impl.class", "name": "androidx/core/os/TraceCompat$Api29Impl.class", "size": 1020, "crc": 1269643219}, {"key": "androidx/core/os/TraceCompat.class", "name": "androidx/core/os/TraceCompat.class", "size": 3632, "crc": -490500308}, {"key": "androidx/core/os/UserHandleCompat$Api24Impl.class", "name": "androidx/core/os/UserHandleCompat$Api24Impl.class", "size": 751, "crc": -880975193}, {"key": "androidx/core/os/UserHandleCompat.class", "name": "androidx/core/os/UserHandleCompat.class", "size": 3013, "crc": **********}, {"key": "androidx/core/os/UserManagerCompat$Api24Impl.class", "name": "androidx/core/os/UserManagerCompat$Api24Impl.class", "size": 810, "crc": -489943301}, {"key": "androidx/core/os/UserManagerCompat.class", "name": "androidx/core/os/UserManagerCompat.class", "size": 785, "crc": **********}, {"key": "androidx/core/provider/CallbackWrapper$1.class", "name": "androidx/core/provider/CallbackWrapper$1.class", "size": 1191, "crc": -703054601}, {"key": "androidx/core/provider/CallbackWrapper$2.class", "name": "androidx/core/provider/CallbackWrapper$2.class", "size": 1115, "crc": **********}, {"key": "androidx/core/provider/CallbackWrapper.class", "name": "androidx/core/provider/CallbackWrapper.class", "size": 2686, "crc": **********}, {"key": "androidx/core/provider/CalleeHandler.class", "name": "androidx/core/provider/CalleeHandler.class", "size": 757, "crc": -463463726}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "size": 539, "crc": -744260981}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "size": 2125, "crc": -**********}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "size": 1183, "crc": 519384825}, {"key": "androidx/core/provider/DocumentsContractCompat.class", "name": "androidx/core/provider/DocumentsContractCompat.class", "size": 4179, "crc": 692727352}, {"key": "androidx/core/provider/FontProvider$ContentQueryWrapper.class", "name": "androidx/core/provider/FontProvider$ContentQueryWrapper.class", "size": 1164, "crc": -**********}, {"key": "androidx/core/provider/FontProvider$ContentQueryWrapperApi16Impl.class", "name": "androidx/core/provider/FontProvider$ContentQueryWrapperApi16Impl.class", "size": 1965, "crc": 538412157}, {"key": "androidx/core/provider/FontProvider$ContentQueryWrapperApi24Impl.class", "name": "androidx/core/provider/FontProvider$ContentQueryWrapperApi24Impl.class", "size": 2044, "crc": **********}, {"key": "androidx/core/provider/FontProvider$ProviderCacheKey.class", "name": "androidx/core/provider/FontProvider$ProviderCacheKey.class", "size": 1405, "crc": 965921492}, {"key": "androidx/core/provider/FontProvider.class", "name": "androidx/core/provider/FontProvider.class", "size": 11877, "crc": -169929584}, {"key": "androidx/core/provider/FontRequest.class", "name": "androidx/core/provider/FontRequest.class", "size": 4148, "crc": -13551059}, {"key": "androidx/core/provider/FontRequestWorker$1.class", "name": "androidx/core/provider/FontRequestWorker$1.class", "size": 1681, "crc": 80139681}, {"key": "androidx/core/provider/FontRequestWorker$2.class", "name": "androidx/core/provider/FontRequestWorker$2.class", "size": 1625, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker$3.class", "name": "androidx/core/provider/FontRequestWorker$3.class", "size": 1857, "crc": **********}, {"key": "androidx/core/provider/FontRequestWorker$4.class", "name": "androidx/core/provider/FontRequestWorker$4.class", "size": 2073, "crc": **********}, {"key": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "name": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "size": 1197, "crc": 633404888}, {"key": "androidx/core/provider/FontRequestWorker.class", "name": "androidx/core/provider/FontRequestWorker.class", "size": 9679, "crc": -32110707}, {"key": "androidx/core/provider/FontsContractCompat$Columns.class", "name": "androidx/core/provider/FontsContractCompat$Columns.class", "size": 1073, "crc": **********}, {"key": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "name": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "size": 3082, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat$FontInfo.class", "name": "androidx/core/provider/FontsContractCompat$FontInfo.class", "size": 2203, "crc": -278636375}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "size": 809, "crc": -566122054}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "size": 1726, "crc": **********}, {"key": "androidx/core/provider/FontsContractCompat$TypefaceStyle.class", "name": "androidx/core/provider/FontsContractCompat$TypefaceStyle.class", "size": 662, "crc": -606944210}, {"key": "androidx/core/provider/FontsContractCompat.class", "name": "androidx/core/provider/FontsContractCompat.class", "size": 9332, "crc": -835257085}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "size": 989, "crc": **********}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "size": 1106, "crc": 184198979}, {"key": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "name": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "size": 1418, "crc": -36851192}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "size": 1172, "crc": 426403311}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "size": 2064, "crc": -**********}, {"key": "androidx/core/provider/RequestExecutor.class", "name": "androidx/core/provider/RequestExecutor.class", "size": 4100, "crc": 658049436}, {"key": "androidx/core/provider/SelfDestructiveThread$1.class", "name": "androidx/core/provider/SelfDestructiveThread$1.class", "size": 1128, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$2$1.class", "name": "androidx/core/provider/SelfDestructiveThread$2$1.class", "size": 1118, "crc": -680123024}, {"key": "androidx/core/provider/SelfDestructiveThread$2.class", "name": "androidx/core/provider/SelfDestructiveThread$2.class", "size": 1877, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$3.class", "name": "androidx/core/provider/SelfDestructiveThread$3.class", "size": 1993, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "name": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "size": 379, "crc": -538938372}, {"key": "androidx/core/provider/SelfDestructiveThread.class", "name": "androidx/core/provider/SelfDestructiveThread.class", "size": 6254, "crc": **********}, {"key": "androidx/core/service/quicksettings/PendingIntentActivityWrapper.class", "name": "androidx/core/service/quicksettings/PendingIntentActivityWrapper.class", "size": 2525, "crc": -388118143}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$Api24Impl.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$Api24Impl.class", "size": 921, "crc": -**********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$Api34Impl.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$Api34Impl.class", "size": 937, "crc": -**********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$TileServiceWrapper.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$TileServiceWrapper.class", "size": 387, "crc": -**********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat.class", "name": "androidx/core/service/quicksettings/TileServiceCompat.class", "size": 2417, "crc": -1059257499}, {"key": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "name": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "size": 710, "crc": 1852964716}, {"key": "androidx/core/telephony/SubscriptionManagerCompat.class", "name": "androidx/core/telephony/SubscriptionManagerCompat.class", "size": 1698, "crc": 1530975204}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "size": 1094, "crc": -549144488}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "size": 1061, "crc": -1907109432}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "size": 787, "crc": -1006670485}, {"key": "androidx/core/telephony/TelephonyManagerCompat.class", "name": "androidx/core/telephony/TelephonyManagerCompat.class", "size": 2915, "crc": -762125207}, {"key": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "name": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "size": 2186, "crc": -1878492408}, {"key": "androidx/core/telephony/mbms/MbmsHelper.class", "name": "androidx/core/telephony/mbms/MbmsHelper.class", "size": 974, "crc": 1390669200}, {"key": "androidx/core/text/BidiFormatter$Builder.class", "name": "androidx/core/text/BidiFormatter$Builder.class", "size": 2036, "crc": 1378760591}, {"key": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "name": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "size": 4094, "crc": -1902417185}, {"key": "androidx/core/text/BidiFormatter.class", "name": "androidx/core/text/BidiFormatter.class", "size": 5789, "crc": -838162369}, {"key": "androidx/core/text/HtmlCompat$Api24Impl.class", "name": "androidx/core/text/HtmlCompat$Api24Impl.class", "size": 1290, "crc": -760785277}, {"key": "androidx/core/text/HtmlCompat.class", "name": "androidx/core/text/HtmlCompat.class", "size": 2541, "crc": 1563923224}, {"key": "androidx/core/text/ICUCompat$Api21Impl.class", "name": "androidx/core/text/ICUCompat$Api21Impl.class", "size": 686, "crc": 1158237831}, {"key": "androidx/core/text/ICUCompat$Api24Impl.class", "name": "androidx/core/text/ICUCompat$Api24Impl.class", "size": 1056, "crc": 1312437245}, {"key": "androidx/core/text/ICUCompat.class", "name": "androidx/core/text/ICUCompat.class", "size": 3455, "crc": 1098822262}, {"key": "androidx/core/text/PrecomputedTextCompat$Api28Impl.class", "name": "androidx/core/text/PrecomputedTextCompat$Api28Impl.class", "size": 721, "crc": -1998498070}, {"key": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "name": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "size": 2098, "crc": 1300802269}, {"key": "androidx/core/text/PrecomputedTextCompat$Params.class", "name": "androidx/core/text/PrecomputedTextCompat$Params.class", "size": 6441, "crc": 1877316849}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "size": 1632, "crc": -1348446544}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "size": 1137, "crc": 1620675989}, {"key": "androidx/core/text/PrecomputedTextCompat.class", "name": "androidx/core/text/PrecomputedTextCompat.class", "size": 9274, "crc": 1610120165}, {"key": "androidx/core/text/TextDirectionHeuristicCompat.class", "name": "androidx/core/text/TextDirectionHeuristicCompat.class", "size": 222, "crc": -118200751}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "size": 1358, "crc": -116397953}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "size": 1162, "crc": 1465235069}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "size": 342, "crc": 964437877}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "size": 1745, "crc": 2136242730}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "size": 1131, "crc": -1371447970}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "size": 1258, "crc": 1502971490}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat.class", "size": 2235, "crc": 1050206764}, {"key": "androidx/core/text/TextUtilsCompat.class", "name": "androidx/core/text/TextUtilsCompat.class", "size": 789, "crc": 1388806511}, {"key": "androidx/core/text/method/LinkMovementMethodCompat.class", "name": "androidx/core/text/method/LinkMovementMethodCompat.class", "size": 2179, "crc": -1732838766}, {"key": "androidx/core/text/util/FindAddress$ZipRange.class", "name": "androidx/core/text/util/FindAddress$ZipRange.class", "size": 1027, "crc": 1759049123}, {"key": "androidx/core/text/util/FindAddress.class", "name": "androidx/core/text/util/FindAddress.class", "size": 11163, "crc": 106913627}, {"key": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "name": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "size": 1608, "crc": 746309326}, {"key": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "name": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "size": 548, "crc": 978455525}, {"key": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "name": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "size": 655, "crc": -1830119234}, {"key": "androidx/core/text/util/LinkifyCompat.class", "name": "androidx/core/text/util/LinkifyCompat.class", "size": 12469, "crc": -71077493}, {"key": "androidx/core/text/util/LocalePreferences$1.class", "name": "androidx/core/text/util/LocalePreferences$1.class", "size": 940, "crc": 273592195}, {"key": "androidx/core/text/util/LocalePreferences$Api24Impl.class", "name": "androidx/core/text/util/LocalePreferences$Api24Impl.class", "size": 1200, "crc": 323753318}, {"key": "androidx/core/text/util/LocalePreferences$Api33Impl.class", "name": "androidx/core/text/util/LocalePreferences$Api33Impl.class", "size": 2890, "crc": -1462793771}, {"key": "androidx/core/text/util/LocalePreferences$CalendarType$CalendarTypes.class", "name": "androidx/core/text/util/LocalePreferences$CalendarType$CalendarTypes.class", "size": 754, "crc": 2111337730}, {"key": "androidx/core/text/util/LocalePreferences$CalendarType.class", "name": "androidx/core/text/util/LocalePreferences$CalendarType.class", "size": 1131, "crc": -985713320}, {"key": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek$Days.class", "name": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek$Days.class", "size": 742, "crc": -925039115}, {"key": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek.class", "name": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek.class", "size": 892, "crc": 1409549697}, {"key": "androidx/core/text/util/LocalePreferences$HourCycle$HourCycleTypes.class", "name": "androidx/core/text/util/LocalePreferences$HourCycle$HourCycleTypes.class", "size": 747, "crc": -1451601347}, {"key": "androidx/core/text/util/LocalePreferences$HourCycle.class", "name": "androidx/core/text/util/LocalePreferences$HourCycle.class", "size": 770, "crc": 2050120332}, {"key": "androidx/core/text/util/LocalePreferences$TemperatureUnit$TemperatureUnits.class", "name": "androidx/core/text/util/LocalePreferences$TemperatureUnit$TemperatureUnits.class", "size": 769, "crc": 372884259}, {"key": "androidx/core/text/util/LocalePreferences$TemperatureUnit.class", "name": "androidx/core/text/util/LocalePreferences$TemperatureUnit.class", "size": 793, "crc": -1507507255}, {"key": "androidx/core/text/util/LocalePreferences.class", "name": "androidx/core/text/util/LocalePreferences.class", "size": 5430, "crc": -958948147}, {"key": "androidx/core/util/AtomicFile.class", "name": "androidx/core/util/AtomicFile.class", "size": 4454, "crc": -2095879844}, {"key": "androidx/core/util/DebugUtils.class", "name": "androidx/core/util/DebugUtils.class", "size": 1525, "crc": -788472052}, {"key": "androidx/core/util/LogWriter.class", "name": "androidx/core/util/LogWriter.class", "size": 1684, "crc": -707190681}, {"key": "androidx/core/util/ObjectsCompat.class", "name": "androidx/core/util/ObjectsCompat.class", "size": 1828, "crc": 994611083}, {"key": "androidx/core/util/Pair.class", "name": "androidx/core/util/Pair.class", "size": 1929, "crc": -1292297626}, {"key": "androidx/core/util/PatternsCompat.class", "name": "androidx/core/util/PatternsCompat.class", "size": 58674, "crc": 176158261}, {"key": "androidx/core/util/Preconditions.class", "name": "androidx/core/util/Preconditions.class", "size": 6155, "crc": -277946906}, {"key": "androidx/core/util/Predicate.class", "name": "androidx/core/util/Predicate.class", "size": 3398, "crc": 1422148262}, {"key": "androidx/core/util/SizeFCompat$Api21Impl.class", "name": "androidx/core/util/SizeFCompat$Api21Impl.class", "size": 1179, "crc": 1597369424}, {"key": "androidx/core/util/SizeFCompat.class", "name": "androidx/core/util/SizeFCompat.class", "size": 2002, "crc": -97395590}, {"key": "androidx/core/util/TimeUtils.class", "name": "androidx/core/util/TimeUtils.class", "size": 4432, "crc": 943155114}, {"key": "androidx/core/util/TypedValueCompat$Api34Impl.class", "name": "androidx/core/util/TypedValueCompat$Api34Impl.class", "size": 769, "crc": 309038580}, {"key": "androidx/core/util/TypedValueCompat$ComplexDimensionUnit.class", "name": "androidx/core/util/TypedValueCompat$ComplexDimensionUnit.class", "size": 659, "crc": 1797910148}, {"key": "androidx/core/util/TypedValueCompat.class", "name": "androidx/core/util/TypedValueCompat.class", "size": 2398, "crc": **********}, {"key": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "name": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "size": 4776, "crc": -727071470}, {"key": "androidx/core/view/AccessibilityDelegateCompat.class", "name": "androidx/core/view/AccessibilityDelegateCompat.class", "size": 7269, "crc": -161213129}, {"key": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "name": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "size": 543, "crc": -**********}, {"key": "androidx/core/view/ActionProvider$VisibilityListener.class", "name": "androidx/core/view/ActionProvider$VisibilityListener.class", "size": 289, "crc": -**********}, {"key": "androidx/core/view/ActionProvider.class", "name": "androidx/core/view/ActionProvider.class", "size": 3587, "crc": 604207025}, {"key": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "size": 3034, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$Builder.class", "name": "androidx/core/view/ContentInfoCompat$Builder.class", "size": 2668, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "size": 647, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "size": 2648, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "size": 2434, "crc": 708621611}, {"key": "androidx/core/view/ContentInfoCompat$Compat.class", "name": "androidx/core/view/ContentInfoCompat$Compat.class", "size": 619, "crc": 1683951257}, {"key": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "size": 1995, "crc": -547685902}, {"key": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "size": 2883, "crc": -109527353}, {"key": "androidx/core/view/ContentInfoCompat$Flags.class", "name": "androidx/core/view/ContentInfoCompat$Flags.class", "size": 645, "crc": 103647730}, {"key": "androidx/core/view/ContentInfoCompat$Source.class", "name": "androidx/core/view/ContentInfoCompat$Source.class", "size": 647, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat.class", "name": "androidx/core/view/ContentInfoCompat.class", "size": 7590, "crc": -512176165}, {"key": "androidx/core/view/DifferentialMotionFlingController$DifferentialVelocityProvider.class", "name": "androidx/core/view/DifferentialMotionFlingController$DifferentialVelocityProvider.class", "size": 491, "crc": **********}, {"key": "androidx/core/view/DifferentialMotionFlingController$FlingVelocityThresholdCalculator.class", "name": "androidx/core/view/DifferentialMotionFlingController$FlingVelocityThresholdCalculator.class", "size": 510, "crc": 573487127}, {"key": "androidx/core/view/DifferentialMotionFlingController.class", "name": "androidx/core/view/DifferentialMotionFlingController.class", "size": 5507, "crc": -349865449}, {"key": "androidx/core/view/DifferentialMotionFlingTarget.class", "name": "androidx/core/view/DifferentialMotionFlingTarget.class", "size": 286, "crc": -504038964}, {"key": "androidx/core/view/DisplayCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$Api23Impl.class", "size": 3226, "crc": **********}, {"key": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "size": 924, "crc": -**********}, {"key": "androidx/core/view/DisplayCompat$ModeCompat.class", "name": "androidx/core/view/DisplayCompat$ModeCompat.class", "size": 2244, "crc": -1016362034}, {"key": "androidx/core/view/DisplayCompat.class", "name": "androidx/core/view/DisplayCompat.class", "size": 5323, "crc": 1351769568}, {"key": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "size": 1845, "crc": 193963218}, {"key": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "size": 1241, "crc": -1809069616}, {"key": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "size": 1555, "crc": -1335472089}, {"key": "androidx/core/view/DisplayCutoutCompat$Api31Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api31Impl.class", "size": 861, "crc": -1819583349}, {"key": "androidx/core/view/DisplayCutoutCompat$Api33Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api33Impl.class", "size": 1982, "crc": -970922767}, {"key": "androidx/core/view/DisplayCutoutCompat.class", "name": "androidx/core/view/DisplayCutoutCompat.class", "size": 6802, "crc": -1715349718}, {"key": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "name": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "size": 1162, "crc": 1545111542}, {"key": "androidx/core/view/DragAndDropPermissionsCompat.class", "name": "androidx/core/view/DragAndDropPermissionsCompat.class", "size": 1694, "crc": -470099473}, {"key": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "name": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "size": 417, "crc": 1783244556}, {"key": "androidx/core/view/DragStartHelper.class", "name": "androidx/core/view/DragStartHelper.class", "size": 3444, "crc": 1231000365}, {"key": "androidx/core/view/GestureDetectorCompat.class", "name": "androidx/core/view/GestureDetectorCompat.class", "size": 2092, "crc": 423357046}, {"key": "androidx/core/view/GravityCompat.class", "name": "androidx/core/view/GravityCompat.class", "size": 1465, "crc": 2088497660}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackFlags.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackFlags.class", "size": 696, "crc": 172944499}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackType.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackType.class", "size": 694, "crc": 1352997797}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat.class", "size": 2165, "crc": -233855463}, {"key": "androidx/core/view/InputDeviceCompat.class", "name": "androidx/core/view/InputDeviceCompat.class", "size": 1178, "crc": -1611156102}, {"key": "androidx/core/view/KeyEventDispatcher$Component.class", "name": "androidx/core/view/KeyEventDispatcher$Component.class", "size": 377, "crc": -1365176840}, {"key": "androidx/core/view/KeyEventDispatcher.class", "name": "androidx/core/view/KeyEventDispatcher.class", "size": 5628, "crc": 699347486}, {"key": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "name": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "size": 1833, "crc": 1472753057}, {"key": "androidx/core/view/LayoutInflaterCompat.class", "name": "androidx/core/view/LayoutInflaterCompat.class", "size": 3450, "crc": 2141986043}, {"key": "androidx/core/view/LayoutInflaterFactory.class", "name": "androidx/core/view/LayoutInflaterFactory.class", "size": 362, "crc": 1217133356}, {"key": "androidx/core/view/MarginLayoutParamsCompat.class", "name": "androidx/core/view/MarginLayoutParamsCompat.class", "size": 2348, "crc": -955217261}, {"key": "androidx/core/view/MenuCompat$Api28Impl.class", "name": "androidx/core/view/MenuCompat$Api28Impl.class", "size": 715, "crc": 668173913}, {"key": "androidx/core/view/MenuCompat.class", "name": "androidx/core/view/MenuCompat.class", "size": 1264, "crc": 625380981}, {"key": "androidx/core/view/MenuHost.class", "name": "androidx/core/view/MenuHost.class", "size": 811, "crc": -1524134716}, {"key": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "name": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "size": 1048, "crc": 1142414138}, {"key": "androidx/core/view/MenuHostHelper.class", "name": "androidx/core/view/MenuHostHelper.class", "size": 6035, "crc": 1058622383}, {"key": "androidx/core/view/MenuItemCompat$1.class", "name": "androidx/core/view/MenuItemCompat$1.class", "size": 1202, "crc": -806903813}, {"key": "androidx/core/view/MenuItemCompat$Api26Impl.class", "name": "androidx/core/view/MenuItemCompat$Api26Impl.class", "size": 3142, "crc": -297334692}, {"key": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "name": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "size": 427, "crc": -**********}, {"key": "androidx/core/view/MenuItemCompat.class", "name": "androidx/core/view/MenuItemCompat.class", "size": 8193, "crc": 383080423}, {"key": "androidx/core/view/MenuProvider.class", "name": "androidx/core/view/MenuProvider.class", "size": 701, "crc": **********}, {"key": "androidx/core/view/MotionEventCompat.class", "name": "androidx/core/view/MotionEventCompat.class", "size": 5523, "crc": **********}, {"key": "androidx/core/view/NestedScrollingChild.class", "name": "androidx/core/view/NestedScrollingChild.class", "size": 603, "crc": -219765865}, {"key": "androidx/core/view/NestedScrollingChild2.class", "name": "androidx/core/view/NestedScrollingChild2.class", "size": 499, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingChild3.class", "name": "androidx/core/view/NestedScrollingChild3.class", "size": 365, "crc": **********}, {"key": "androidx/core/view/NestedScrollingChildHelper.class", "name": "androidx/core/view/NestedScrollingChildHelper.class", "size": 5684, "crc": 617690646}, {"key": "androidx/core/view/NestedScrollingParent.class", "name": "androidx/core/view/NestedScrollingParent.class", "size": 797, "crc": -225666609}, {"key": "androidx/core/view/NestedScrollingParent2.class", "name": "androidx/core/view/NestedScrollingParent2.class", "size": 680, "crc": -511141332}, {"key": "androidx/core/view/NestedScrollingParent3.class", "name": "androidx/core/view/NestedScrollingParent3.class", "size": 341, "crc": -873812005}, {"key": "androidx/core/view/NestedScrollingParentHelper.class", "name": "androidx/core/view/NestedScrollingParentHelper.class", "size": 1543, "crc": 183451244}, {"key": "androidx/core/view/OnApplyWindowInsetsListener.class", "name": "androidx/core/view/OnApplyWindowInsetsListener.class", "size": 383, "crc": 2065677997}, {"key": "androidx/core/view/OnReceiveContentListener.class", "name": "androidx/core/view/OnReceiveContentListener.class", "size": 410, "crc": 1045863421}, {"key": "androidx/core/view/OnReceiveContentViewBehavior.class", "name": "androidx/core/view/OnReceiveContentViewBehavior.class", "size": 392, "crc": 1117318141}, {"key": "androidx/core/view/OneShotPreDrawListener.class", "name": "androidx/core/view/OneShotPreDrawListener.class", "size": 2276, "crc": 1411995672}, {"key": "androidx/core/view/PointerIconCompat$Api24Impl.class", "name": "androidx/core/view/PointerIconCompat$Api24Impl.class", "size": 1187, "crc": -**********}, {"key": "androidx/core/view/PointerIconCompat.class", "name": "androidx/core/view/PointerIconCompat.class", "size": 3076, "crc": 542273241}, {"key": "androidx/core/view/ScaleGestureDetectorCompat.class", "name": "androidx/core/view/ScaleGestureDetectorCompat.class", "size": 1388, "crc": **********}, {"key": "androidx/core/view/ScrollFeedbackProviderCompat$1.class", "name": "androidx/core/view/ScrollFeedbackProviderCompat$1.class", "size": 270, "crc": **********}, {"key": "androidx/core/view/ScrollFeedbackProviderCompat$ScrollFeedbackProviderApi35Impl.class", "name": "androidx/core/view/ScrollFeedbackProviderCompat$ScrollFeedbackProviderApi35Impl.class", "size": 1531, "crc": -**********}, {"key": "androidx/core/view/ScrollFeedbackProviderCompat$ScrollFeedbackProviderBaseImpl.class", "name": "androidx/core/view/ScrollFeedbackProviderCompat$ScrollFeedbackProviderBaseImpl.class", "size": 1326, "crc": **********}, {"key": "androidx/core/view/ScrollFeedbackProviderCompat$ScrollFeedbackProviderImpl.class", "name": "androidx/core/view/ScrollFeedbackProviderCompat$ScrollFeedbackProviderImpl.class", "size": 399, "crc": **********}, {"key": "androidx/core/view/ScrollFeedbackProviderCompat.class", "name": "androidx/core/view/ScrollFeedbackProviderCompat.class", "size": 2023, "crc": -117904931}, {"key": "androidx/core/view/ScrollingView.class", "name": "androidx/core/view/ScrollingView.class", "size": 364, "crc": -218140119}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl.class", "size": 596, "crc": **********}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl20.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl20.class", "size": 2598, "crc": 989144849}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl30.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl30.class", "size": 4063, "crc": -925019018}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat.class", "size": 1687, "crc": -*********}, {"key": "androidx/core/view/TintableBackgroundView.class", "name": "androidx/core/view/TintableBackgroundView.class", "size": 699, "crc": -1080820594}, {"key": "androidx/core/view/VelocityTrackerCompat$Api34Impl.class", "name": "androidx/core/view/VelocityTrackerCompat$Api34Impl.class", "size": 1063, "crc": -*********}, {"key": "androidx/core/view/VelocityTrackerCompat$VelocityTrackableMotionEventAxis.class", "name": "androidx/core/view/VelocityTrackerCompat$VelocityTrackableMotionEventAxis.class", "size": 711, "crc": 1429362118}, {"key": "androidx/core/view/VelocityTrackerCompat.class", "name": "androidx/core/view/VelocityTrackerCompat.class", "size": 4136, "crc": -1787249390}, {"key": "androidx/core/view/VelocityTrackerFallback.class", "name": "androidx/core/view/VelocityTrackerFallback.class", "size": 2833, "crc": *********}, {"key": "androidx/core/view/ViewCompat$1.class", "name": "androidx/core/view/ViewCompat$1.class", "size": 2409, "crc": -1117730373}, {"key": "androidx/core/view/ViewCompat$2.class", "name": "androidx/core/view/ViewCompat$2.class", "size": 2220, "crc": 1178745152}, {"key": "androidx/core/view/ViewCompat$3.class", "name": "androidx/core/view/ViewCompat$3.class", "size": 2215, "crc": -2142899074}, {"key": "androidx/core/view/ViewCompat$4.class", "name": "androidx/core/view/ViewCompat$4.class", "size": 2277, "crc": 1939575791}, {"key": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "name": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "size": 3791, "crc": -1163831242}, {"key": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "name": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "size": 3143, "crc": 1069049609}, {"key": "androidx/core/view/ViewCompat$Api20Impl.class", "name": "androidx/core/view/ViewCompat$Api20Impl.class", "size": 1237, "crc": -1540529748}, {"key": "androidx/core/view/ViewCompat$Api21Impl$1.class", "name": "androidx/core/view/ViewCompat$Api21Impl$1.class", "size": 2042, "crc": 1758198521}, {"key": "androidx/core/view/ViewCompat$Api21Impl.class", "name": "androidx/core/view/ViewCompat$Api21Impl.class", "size": 7000, "crc": 28958877}, {"key": "androidx/core/view/ViewCompat$Api23Impl.class", "name": "androidx/core/view/ViewCompat$Api23Impl.class", "size": 1870, "crc": -520135111}, {"key": "androidx/core/view/ViewCompat$Api24Impl.class", "name": "androidx/core/view/ViewCompat$Api24Impl.class", "size": 2049, "crc": -541155245}, {"key": "androidx/core/view/ViewCompat$Api26Impl.class", "name": "androidx/core/view/ViewCompat$Api26Impl.class", "size": 3373, "crc": 675587876}, {"key": "androidx/core/view/ViewCompat$Api28Impl.class", "name": "androidx/core/view/ViewCompat$Api28Impl.class", "size": 4553, "crc": 445614351}, {"key": "androidx/core/view/ViewCompat$Api29Impl.class", "name": "androidx/core/view/ViewCompat$Api29Impl.class", "size": 3178, "crc": 1401919117}, {"key": "androidx/core/view/ViewCompat$Api30Impl.class", "name": "androidx/core/view/ViewCompat$Api30Impl.class", "size": 2369, "crc": 100418862}, {"key": "androidx/core/view/ViewCompat$Api31Impl.class", "name": "androidx/core/view/ViewCompat$Api31Impl.class", "size": 2173, "crc": -1007502504}, {"key": "androidx/core/view/ViewCompat$FocusDirection.class", "name": "androidx/core/view/ViewCompat$FocusDirection.class", "size": 642, "crc": 1155425484}, {"key": "androidx/core/view/ViewCompat$FocusRealDirection.class", "name": "androidx/core/view/ViewCompat$FocusRealDirection.class", "size": 650, "crc": 749280326}, {"key": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "name": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "size": 658, "crc": 1576246077}, {"key": "androidx/core/view/ViewCompat$NestedScrollType.class", "name": "androidx/core/view/ViewCompat$NestedScrollType.class", "size": 646, "crc": 1643799421}, {"key": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "name": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "size": 1718, "crc": -999173100}, {"key": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "name": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "size": 425, "crc": -818575362}, {"key": "androidx/core/view/ViewCompat$ScrollAxis.class", "name": "androidx/core/view/ViewCompat$ScrollAxis.class", "size": 634, "crc": -1481000453}, {"key": "androidx/core/view/ViewCompat$ScrollIndicators.class", "name": "androidx/core/view/ViewCompat$ScrollIndicators.class", "size": 646, "crc": -1203320779}, {"key": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "name": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "size": 5894, "crc": 1716869540}, {"key": "androidx/core/view/ViewCompat.class", "name": "androidx/core/view/ViewCompat.class", "size": 64009, "crc": 1454502414}, {"key": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "size": 886, "crc": 1626987926}, {"key": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "size": 930, "crc": -714012791}, {"key": "androidx/core/view/ViewConfigurationCompat$Api34Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api34Impl.class", "size": 1091, "crc": -1371238798}, {"key": "androidx/core/view/ViewConfigurationCompat.class", "name": "androidx/core/view/ViewConfigurationCompat.class", "size": 7618, "crc": 1686065512}, {"key": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "name": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "size": 983, "crc": 1107509646}, {"key": "androidx/core/view/ViewGroupCompat.class", "name": "androidx/core/view/ViewGroupCompat.class", "size": 6213, "crc": 1836504933}, {"key": "androidx/core/view/ViewParentCompat$Api21Impl.class", "name": "androidx/core/view/ViewParentCompat$Api21Impl.class", "size": 2233, "crc": 222832456}, {"key": "androidx/core/view/ViewParentCompat.class", "name": "androidx/core/view/ViewParentCompat.class", "size": 7449, "crc": 238664230}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "size": 1471, "crc": 1654667030}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "size": 1146, "crc": 2075170206}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat.class", "size": 10387, "crc": 111390826}, {"key": "androidx/core/view/ViewPropertyAnimatorListener.class", "name": "androidx/core/view/ViewPropertyAnimatorListener.class", "size": 375, "crc": 852258404}, {"key": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "name": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "size": 848, "crc": 213232244}, {"key": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "name": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "size": 305, "crc": 648937304}, {"key": "androidx/core/view/ViewStructureCompat$Api23Impl.class", "name": "androidx/core/view/ViewStructureCompat$Api23Impl.class", "size": 1411, "crc": -740838675}, {"key": "androidx/core/view/ViewStructureCompat.class", "name": "androidx/core/view/ViewStructureCompat.class", "size": 2213, "crc": -1770496824}, {"key": "androidx/core/view/WindowCompat$Api16Impl.class", "name": "androidx/core/view/WindowCompat$Api16Impl.class", "size": 1075, "crc": 926591703}, {"key": "androidx/core/view/WindowCompat$Api28Impl.class", "name": "androidx/core/view/WindowCompat$Api28Impl.class", "size": 808, "crc": 1023815862}, {"key": "androidx/core/view/WindowCompat$Api30Impl.class", "name": "androidx/core/view/WindowCompat$Api30Impl.class", "size": 1193, "crc": 727714833}, {"key": "androidx/core/view/WindowCompat$Api35Impl.class", "name": "androidx/core/view/WindowCompat$Api35Impl.class", "size": 819, "crc": -892720300}, {"key": "androidx/core/view/WindowCompat.class", "name": "androidx/core/view/WindowCompat.class", "size": 2300, "crc": 1154304761}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "size": 3033, "crc": 817507816}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "size": 771, "crc": -19343690}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "size": 2044, "crc": -1255215357}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "size": 1787, "crc": -1685860077}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "size": 2637, "crc": -990103993}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "size": 1673, "crc": -2116985540}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "size": 2062, "crc": 952631830}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "size": 6178, "crc": -111727354}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "size": 9606, "crc": 1851247834}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "size": 4821, "crc": -1777781387}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "size": 4019, "crc": -1956419470}, {"key": "androidx/core/view/WindowInsetsAnimationCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat.class", "size": 3550, "crc": 79701192}, {"key": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "size": 526, "crc": -579854131}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "size": 1873, "crc": -259057593}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "size": 2589, "crc": -413945900}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "size": 2516, "crc": 733189971}, {"key": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "name": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "size": 3506, "crc": -1390511902}, {"key": "androidx/core/view/WindowInsetsCompat$Builder.class", "name": "androidx/core/view/WindowInsetsCompat$Builder.class", "size": 3765, "crc": -282447980}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "size": 3177, "crc": 78885965}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "size": 3858, "crc": 1927519703}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "size": 3176, "crc": -1074694099}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "size": 1858, "crc": -423688294}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl34.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl34.class", "size": 1858, "crc": -423321211}, {"key": "androidx/core/view/WindowInsetsCompat$Impl.class", "name": "androidx/core/view/WindowInsetsCompat$Impl.class", "size": 4568, "crc": -765445621}, {"key": "androidx/core/view/WindowInsetsCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsCompat$Impl20.class", "size": 10313, "crc": -301075434}, {"key": "androidx/core/view/WindowInsetsCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsCompat$Impl21.class", "size": 2442, "crc": -145440537}, {"key": "androidx/core/view/WindowInsetsCompat$Impl28.class", "name": "androidx/core/view/WindowInsetsCompat$Impl28.class", "size": 2440, "crc": -117599849}, {"key": "androidx/core/view/WindowInsetsCompat$Impl29.class", "name": "androidx/core/view/WindowInsetsCompat$Impl29.class", "size": 2636, "crc": -694684001}, {"key": "androidx/core/view/WindowInsetsCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsCompat$Impl30.class", "size": 2275, "crc": -905774205}, {"key": "androidx/core/view/WindowInsetsCompat$Impl34.class", "name": "androidx/core/view/WindowInsetsCompat$Impl34.class", "size": 2114, "crc": 1589913137}, {"key": "androidx/core/view/WindowInsetsCompat$Side$InsetsSide.class", "name": "androidx/core/view/WindowInsetsCompat$Side$InsetsSide.class", "size": 719, "crc": -238414125}, {"key": "androidx/core/view/WindowInsetsCompat$Side.class", "name": "androidx/core/view/WindowInsetsCompat$Side.class", "size": 679, "crc": 1533119395}, {"key": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "name": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "size": 719, "crc": 1917876408}, {"key": "androidx/core/view/WindowInsetsCompat$Type.class", "name": "androidx/core/view/WindowInsetsCompat$Type.class", "size": 2502, "crc": 1004470164}, {"key": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "size": 1293, "crc": -553704006}, {"key": "androidx/core/view/WindowInsetsCompat$TypeImpl34.class", "name": "androidx/core/view/WindowInsetsCompat$TypeImpl34.class", "size": 1346, "crc": 666182379}, {"key": "androidx/core/view/WindowInsetsCompat.class", "name": "androidx/core/view/WindowInsetsCompat.class", "size": 11519, "crc": -1755120727}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "size": 2370, "crc": -199051937}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "size": 4471, "crc": 267134734}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "size": 1630, "crc": -304845956}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "size": 1635, "crc": 1713723369}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "size": 2332, "crc": -1581813652}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "size": 7860, "crc": -199328483}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl31.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl31.class", "size": 1785, "crc": -710939727}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl35.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl35.class", "size": 1774, "crc": -946574706}, {"key": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "name": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "size": 492, "crc": 797501602}, {"key": "androidx/core/view/WindowInsetsControllerCompat.class", "name": "androidx/core/view/WindowInsetsControllerCompat.class", "size": 5381, "crc": -994559136}, {"key": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "name": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "size": 1701, "crc": -1814421}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$Api34Impl.class", "size": 1071, "crc": 1071329072}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "size": 718, "crc": 701609424}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "size": 5889, "crc": 409911889}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "size": 477, "crc": 1509031422}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "size": 811, "crc": -379364844}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "size": 1690, "crc": -1088028026}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api34Impl.class", "size": 890, "crc": 1359064398}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "size": 384, "crc": -1837815159}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "size": 1717, "crc": 173471880}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "size": 4834, "crc": -1136661087}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "size": 11557, "crc": 699516060}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api30Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api30Impl.class", "size": 1464, "crc": 373364472}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "size": 4263, "crc": 8755983}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api34Impl.class", "size": 3410, "crc": 978753119}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api35Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api35Impl.class", "size": 2116, "crc": -368762634}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat$Builder.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat$Builder.class", "size": 2800, "crc": -39842949}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "size": 2574, "crc": -1709045273}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat$Builder.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat$Builder.class", "size": 3272, "crc": -179550551}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "size": 2927, "crc": -519550836}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "size": 2028, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "size": 2589, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "size": 46358, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "size": 2633, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "size": 1697, "crc": -449053692}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "size": 2598, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "size": 8926, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "size": 1020, "crc": -264044560}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "size": 1120, "crc": 759849651}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "size": 1023, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "size": 1000, "crc": -389625364}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "size": 1070, "crc": -1161318143}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "size": 932, "crc": -1613502452}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "size": 1022, "crc": 1087470567}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "size": 1033, "crc": -1691903135}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "size": 1353, "crc": 937689886}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "size": 2596, "crc": 1637611120}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "size": 1164, "crc": -1038550972}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api26Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api26Impl.class", "size": 889, "crc": -479776054}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api30Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api30Impl.class", "size": 810, "crc": 2017422290}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "size": 1615, "crc": -2109981991}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api34Impl.class", "size": 1080, "crc": 1271495126}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "size": 8887, "crc": 1045079796}, {"key": "androidx/core/view/animation/PathInterpolatorApi14.class", "name": "androidx/core/view/animation/PathInterpolatorApi14.class", "size": 2333, "crc": 1168129793}, {"key": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "name": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "size": 1217, "crc": 1265270823}, {"key": "androidx/core/view/animation/PathInterpolatorCompat.class", "name": "androidx/core/view/animation/PathInterpolatorCompat.class", "size": 1483, "crc": -939107761}, {"key": "androidx/core/view/autofill/AutofillIdCompat.class", "name": "androidx/core/view/autofill/AutofillIdCompat.class", "size": 1087, "crc": 1083057385}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api23Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api23Impl.class", "size": 842, "crc": -178558652}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api29Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api29Impl.class", "size": 2612, "crc": 796027262}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api34Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api34Impl.class", "size": 1190, "crc": -98234275}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat.class", "size": 5419, "crc": -812619847}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "size": 1619, "crc": 944384493}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat$Api35Impl.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat$Api35Impl.class", "size": 1070, "crc": 520161173}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat.class", "size": 8863, "crc": -138592734}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "size": 1833, "crc": 623798447}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "size": 1649, "crc": -1881400469}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "size": 1086, "crc": -395793993}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "size": 529, "crc": -2058581590}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat.class", "size": 11369, "crc": -67867098}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "size": 2113, "crc": 283373479}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "size": 1764, "crc": -532174469}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "size": 710, "crc": -2042476820}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "size": 2690, "crc": 2015932036}, {"key": "androidx/core/view/insets/ColorProtection.class", "name": "androidx/core/view/insets/ColorProtection.class", "size": 1529, "crc": 46823773}, {"key": "androidx/core/view/insets/GradientProtection.class", "name": "androidx/core/view/insets/GradientProtection.class", "size": 3290, "crc": 1617678780}, {"key": "androidx/core/view/insets/Protection$Attributes$Callback.class", "name": "androidx/core/view/insets/Protection$Attributes$Callback.class", "size": 1484, "crc": 2101270948}, {"key": "androidx/core/view/insets/Protection$Attributes.class", "name": "androidx/core/view/insets/Protection$Attributes.class", "size": 5218, "crc": 1481168617}, {"key": "androidx/core/view/insets/Protection.class", "name": "androidx/core/view/insets/Protection.class", "size": 8942, "crc": 1163914191}, {"key": "androidx/core/view/insets/ProtectionGroup.class", "name": "androidx/core/view/insets/ProtectionGroup.class", "size": 5390, "crc": 1699757804}, {"key": "androidx/core/view/insets/ProtectionLayout$1.class", "name": "androidx/core/view/insets/ProtectionLayout$1.class", "size": 3047, "crc": -910761991}, {"key": "androidx/core/view/insets/ProtectionLayout.class", "name": "androidx/core/view/insets/ProtectionLayout.class", "size": 8275, "crc": 196553517}, {"key": "androidx/core/view/insets/SystemBarStateMonitor$1.class", "name": "androidx/core/view/insets/SystemBarStateMonitor$1.class", "size": 2006, "crc": 236064772}, {"key": "androidx/core/view/insets/SystemBarStateMonitor$2.class", "name": "androidx/core/view/insets/SystemBarStateMonitor$2.class", "size": 5070, "crc": -746709862}, {"key": "androidx/core/view/insets/SystemBarStateMonitor$Callback.class", "name": "androidx/core/view/insets/SystemBarStateMonitor$Callback.class", "size": 533, "crc": -1398397602}, {"key": "androidx/core/view/insets/SystemBarStateMonitor.class", "name": "androidx/core/view/insets/SystemBarStateMonitor.class", "size": 6250, "crc": -1224757922}, {"key": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "name": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "size": 3002, "crc": -1244258957}, {"key": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "name": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "size": 1612, "crc": 72190227}, {"key": "androidx/core/widget/AutoScrollHelper.class", "name": "androidx/core/widget/AutoScrollHelper.class", "size": 8609, "crc": -880152256}, {"key": "androidx/core/widget/AutoSizeableTextView.class", "name": "androidx/core/widget/AutoSizeableTextView.class", "size": 1310, "crc": 1191022615}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "size": 1854, "crc": -357188607}, {"key": "androidx/core/widget/CheckedTextViewCompat.class", "name": "androidx/core/widget/CheckedTextViewCompat.class", "size": 2679, "crc": -2046939796}, {"key": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "size": 1645, "crc": -180275534}, {"key": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "size": 827, "crc": 1577960354}, {"key": "androidx/core/widget/CompoundButtonCompat.class", "name": "androidx/core/widget/CompoundButtonCompat.class", "size": 3516, "crc": -630494798}, {"key": "androidx/core/widget/ContentLoadingProgressBar.class", "name": "androidx/core/widget/ContentLoadingProgressBar.class", "size": 3263, "crc": -1952755727}, {"key": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "size": 779, "crc": 1668432159}, {"key": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "size": 1493, "crc": 1945857777}, {"key": "androidx/core/widget/EdgeEffectCompat.class", "name": "androidx/core/widget/EdgeEffectCompat.class", "size": 2907, "crc": 240255036}, {"key": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "name": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "size": 1586, "crc": -1325285682}, {"key": "androidx/core/widget/ImageViewCompat.class", "name": "androidx/core/widget/ImageViewCompat.class", "size": 2751, "crc": -479313651}, {"key": "androidx/core/widget/ListPopupWindowCompat.class", "name": "androidx/core/widget/ListPopupWindowCompat.class", "size": 1347, "crc": 88502787}, {"key": "androidx/core/widget/ListViewAutoScrollHelper.class", "name": "androidx/core/widget/ListViewAutoScrollHelper.class", "size": 1611, "crc": -729049248}, {"key": "androidx/core/widget/ListViewCompat.class", "name": "androidx/core/widget/ListViewCompat.class", "size": 1027, "crc": -460510677}, {"key": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "name": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "size": 3675, "crc": 2062786170}, {"key": "androidx/core/widget/NestedScrollView$Api21Impl.class", "name": "androidx/core/widget/NestedScrollView$Api21Impl.class", "size": 714, "crc": 124085011}, {"key": "androidx/core/widget/NestedScrollView$Api35Impl.class", "name": "androidx/core/widget/NestedScrollView$Api35Impl.class", "size": 806, "crc": -642086290}, {"key": "androidx/core/widget/NestedScrollView$DifferentialMotionFlingTargetImpl.class", "name": "androidx/core/widget/NestedScrollView$DifferentialMotionFlingTargetImpl.class", "size": 1320, "crc": 1169335022}, {"key": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "name": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "size": 414, "crc": 408199441}, {"key": "androidx/core/widget/NestedScrollView$SavedState$1.class", "name": "androidx/core/widget/NestedScrollView$SavedState$1.class", "size": 1356, "crc": -1196071737}, {"key": "androidx/core/widget/NestedScrollView$SavedState.class", "name": "androidx/core/widget/NestedScrollView$SavedState.class", "size": 1978, "crc": -1003432971}, {"key": "androidx/core/widget/NestedScrollView.class", "name": "androidx/core/widget/NestedScrollView.class", "size": 43020, "crc": -88889528}, {"key": "androidx/core/widget/PopupMenuCompat.class", "name": "androidx/core/widget/PopupMenuCompat.class", "size": 814, "crc": 2039980593}, {"key": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "name": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "size": 1199, "crc": -15177621}, {"key": "androidx/core/widget/PopupWindowCompat.class", "name": "androidx/core/widget/PopupWindowCompat.class", "size": 4129, "crc": -1225815984}, {"key": "androidx/core/widget/ScrollerCompat.class", "name": "androidx/core/widget/ScrollerCompat.class", "size": 3656, "crc": -54539407}, {"key": "androidx/core/widget/TextViewCompat$Api23Impl.class", "name": "androidx/core/widget/TextViewCompat$Api23Impl.class", "size": 2142, "crc": 1832707872}, {"key": "androidx/core/widget/TextViewCompat$Api24Impl.class", "name": "androidx/core/widget/TextViewCompat$Api24Impl.class", "size": 733, "crc": 1744365068}, {"key": "androidx/core/widget/TextViewCompat$Api26Impl.class", "name": "androidx/core/widget/TextViewCompat$Api26Impl.class", "size": 1818, "crc": -991379888}, {"key": "androidx/core/widget/TextViewCompat$Api28Impl.class", "name": "androidx/core/widget/TextViewCompat$Api28Impl.class", "size": 1535, "crc": -758346169}, {"key": "androidx/core/widget/TextViewCompat$Api34Impl.class", "name": "androidx/core/widget/TextViewCompat$Api34Impl.class", "size": 959, "crc": -449396543}, {"key": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "name": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "size": 662, "crc": -1792362665}, {"key": "androidx/core/widget/TextViewCompat$OreoCallback.class", "name": "androidx/core/widget/TextViewCompat$OreoCallback.class", "size": 7387, "crc": -1992776375}, {"key": "androidx/core/widget/TextViewCompat.class", "name": "androidx/core/widget/TextViewCompat.class", "size": 15913, "crc": -1874645538}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener.class", "size": 3898, "crc": 1251884673}, {"key": "androidx/core/widget/TintableCheckedTextView.class", "name": "androidx/core/widget/TintableCheckedTextView.class", "size": 952, "crc": -1157876996}, {"key": "androidx/core/widget/TintableCompoundButton.class", "name": "androidx/core/widget/TintableCompoundButton.class", "size": 685, "crc": 631178640}, {"key": "androidx/core/widget/TintableCompoundDrawablesView.class", "name": "androidx/core/widget/TintableCompoundDrawablesView.class", "size": 743, "crc": -553086992}, {"key": "androidx/core/widget/TintableImageSourceView.class", "name": "androidx/core/widget/TintableImageSourceView.class", "size": 936, "crc": 1797686395}, {"key": "META-INF/androidx.core_core.version", "name": "META-INF/androidx.core_core.version", "size": 7, "crc": -358639726}, {"key": "META-INF/core_release.kotlin_module", "name": "META-INF/core_release.kotlin_module", "size": 55, "crc": 262169874}]