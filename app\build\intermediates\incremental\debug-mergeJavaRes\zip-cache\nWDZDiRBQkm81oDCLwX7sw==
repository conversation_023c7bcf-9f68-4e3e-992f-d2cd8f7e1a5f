[{"key": "androidx/compose/material/ActualJvm_jvmKt.class", "name": "androidx/compose/material/ActualJvm_jvmKt.class", "size": 541, "crc": 602077920}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2$1.class", "size": 2151, "crc": -209692533}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2.class", "size": 7769, "crc": -206891072}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$3.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$3.class", "size": 2379, "crc": -683940957}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1$1.class", "size": 3233, "crc": -1129147782}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "size": 3698, "crc": -1531513668}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1$1.class", "size": 3228, "crc": -1854135473}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "size": 3711, "crc": -1499441042}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1.class", "size": 11410, "crc": -414772726}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$2.class", "size": 2852, "crc": -357467450}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "size": 5395, "crc": -460178767}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1.class", "size": 6987, "crc": 13687410}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$2.class", "size": 1993, "crc": 771168153}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1$1$1.class", "size": 2969, "crc": 231509883}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1.class", "size": 10440, "crc": 1247508561}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$2.class", "size": 3868, "crc": -7950655}, {"key": "androidx/compose/material/AlertDialogKt.class", "name": "androidx/compose/material/AlertDialogKt.class", "size": 27973, "crc": -558081824}, {"key": "androidx/compose/material/AnchoredDragFinishedSignal.class", "name": "androidx/compose/material/AnchoredDragFinishedSignal.class", "size": 1233, "crc": -1432356483}, {"key": "androidx/compose/material/AnchoredDragScope.class", "name": "androidx/compose/material/AnchoredDragScope.class", "size": 1015, "crc": -1330344492}, {"key": "androidx/compose/material/AnchoredDraggableDefaults.class", "name": "androidx/compose/material/AnchoredDraggableDefaults.class", "size": 1567, "crc": -708489562}, {"key": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1$1.class", "size": 3470, "crc": 1840919760}, {"key": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1.class", "size": 3880, "crc": -1234445077}, {"key": "androidx/compose/material/AnchoredDraggableKt$animateTo$2$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$animateTo$2$1.class", "size": 1858, "crc": -39364008}, {"key": "androidx/compose/material/AnchoredDraggableKt$animateTo$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$animateTo$2.class", "size": 4904, "crc": 566723849}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$1.class", "size": 1666, "crc": -1051399036}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4000, "crc": -1114716355}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 1909, "crc": -1974868445}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1.class", "size": 4381, "crc": 961691037}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2.class", "size": 4419, "crc": -2138998580}, {"key": "androidx/compose/material/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$snapTo$2.class", "size": 3658, "crc": -324154607}, {"key": "androidx/compose/material/AnchoredDraggableKt.class", "name": "androidx/compose/material/AnchoredDraggableKt.class", "size": 10212, "crc": -932202424}, {"key": "androidx/compose/material/AnchoredDraggableState$1.class", "name": "androidx/compose/material/AnchoredDraggableState$1.class", "size": 1523, "crc": 993662385}, {"key": "androidx/compose/material/AnchoredDraggableState$2.class", "name": "androidx/compose/material/AnchoredDraggableState$2.class", "size": 1567, "crc": -599367224}, {"key": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$1.class", "name": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$1.class", "size": 2103, "crc": 767788618}, {"key": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$2.class", "name": "androidx/compose/material/AnchoredDraggableState$Companion$Saver$2.class", "size": 2898, "crc": -43137423}, {"key": "androidx/compose/material/AnchoredDraggableState$Companion.class", "name": "androidx/compose/material/AnchoredDraggableState$Companion.class", "size": 3034, "crc": -1498641479}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$1.class", "size": 1920, "crc": -1152058284}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$1.class", "size": 1661, "crc": 372209636}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4261, "crc": -57938964}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2.class", "size": 4240, "crc": -517386605}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$3.class", "size": 1939, "crc": 692836330}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$1.class", "size": 1879, "crc": -666760345}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4491, "crc": -1907661656}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4.class", "size": 4477, "crc": -45012507}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDragScope$1.class", "size": 1606, "crc": -1285444553}, {"key": "androidx/compose/material/AnchoredDraggableState$closestValue$2.class", "name": "androidx/compose/material/AnchoredDraggableState$closestValue$2.class", "size": 2027, "crc": 1116883141}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1$drag$2.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1$drag$2.class", "size": 5154, "crc": 175207113}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1$dragScope$1.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1$dragScope$1.class", "size": 2479, "crc": -1202037878}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1.class", "size": 3521, "crc": -421611331}, {"key": "androidx/compose/material/AnchoredDraggableState$progress$2.class", "name": "androidx/compose/material/AnchoredDraggableState$progress$2.class", "size": 2159, "crc": 984198221}, {"key": "androidx/compose/material/AnchoredDraggableState$targetValue$2.class", "name": "androidx/compose/material/AnchoredDraggableState$targetValue$2.class", "size": 2009, "crc": -387933042}, {"key": "androidx/compose/material/AnchoredDraggableState$trySnapTo$1.class", "name": "androidx/compose/material/AnchoredDraggableState$trySnapTo$1.class", "size": 2417, "crc": 1084083527}, {"key": "androidx/compose/material/AnchoredDraggableState.class", "name": "androidx/compose/material/AnchoredDraggableState.class", "size": 24725, "crc": 1884380992}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$1.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$1.class", "size": 3624, "crc": 1555984730}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$2.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt$AlertDialog$2.class", "size": 3388, "crc": -820833412}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt.class", "size": 12876, "crc": -344783521}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$1.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$1.class", "size": 2810, "crc": -976674974}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$2.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$2.class", "size": 4269, "crc": -658348927}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$3.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$3.class", "size": 3030, "crc": **********}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "size": 2451, "crc": **********}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenuItem$1.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenuItem$1.class", "size": 3097, "crc": **********}, {"key": "androidx/compose/material/AndroidMenu_androidKt.class", "name": "androidx/compose/material/AndroidMenu_androidKt.class", "size": 17137, "crc": **********}, {"key": "androidx/compose/material/AppBarDefaults.class", "name": "androidx/compose/material/AppBarDefaults.class", "size": 5247, "crc": 66870650}, {"key": "androidx/compose/material/AppBarKt$AppBar$1$1.class", "name": "androidx/compose/material/AppBarKt$AppBar$1$1.class", "size": 10316, "crc": -**********}, {"key": "androidx/compose/material/AppBarKt$AppBar$1.class", "name": "androidx/compose/material/AppBarKt$AppBar$1.class", "size": 4308, "crc": **********}, {"key": "androidx/compose/material/AppBarKt$AppBar$2.class", "name": "androidx/compose/material/AppBarKt$AppBar$2.class", "size": 3044, "crc": -193977698}, {"key": "androidx/compose/material/AppBarKt$BottomAppBar$1.class", "name": "androidx/compose/material/AppBarKt$BottomAppBar$1.class", "size": 3038, "crc": -518493970}, {"key": "androidx/compose/material/AppBarKt$BottomAppBar$2.class", "name": "androidx/compose/material/AppBarKt$BottomAppBar$2.class", "size": 2786, "crc": 1369998668}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1$2$1.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1$2$1.class", "size": 3380, "crc": -1582320504}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1$3.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1$3.class", "size": 9426, "crc": -980263079}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1.class", "size": 14669, "crc": 528410360}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$2.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$2.class", "size": 3162, "crc": -1936342537}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$3.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$3.class", "size": 2909, "crc": -1578474339}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$4.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$4.class", "size": 2829, "crc": 1920188492}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$5.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$5.class", "size": 2577, "crc": -861976443}, {"key": "androidx/compose/material/AppBarKt.class", "name": "androidx/compose/material/AppBarKt.class", "size": 29551, "crc": -1824944255}, {"key": "androidx/compose/material/BackdropLayers.class", "name": "androidx/compose/material/BackdropLayers.class", "size": 1833, "crc": 988445131}, {"key": "androidx/compose/material/BackdropScaffoldDefaults.class", "name": "androidx/compose/material/BackdropScaffoldDefaults.class", "size": 6275, "crc": -1286867262}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1$1.class", "size": 1885, "crc": -1072411950}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$1$1.class", "size": 4332, "crc": 2074005609}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$2$1.class", "size": 3313, "crc": 1447190189}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1$1.class", "size": 1886, "crc": 1707214990}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$4$1.class", "size": 4333, "crc": 270764659}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$5$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$1$5$1.class", "size": 3314, "crc": 1844750434}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackLayerTransition$2.class", "size": 2355, "crc": -1461085939}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$1$1.class", "size": 1786, "crc": 267958259}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$WhenMappings.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$WhenMappings.class", "size": 1035, "crc": 1824435633}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$newAnchors$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1$newAnchors$1.class", "size": 2461, "crc": 659561009}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$1.class", "size": 3810, "crc": -275164872}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1$1.class", "size": 3623, "crc": 1070509400}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1.class", "size": 2530, "crc": 395495879}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2$1.class", "size": 3624, "crc": 827461721}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2.class", "size": 2531, "crc": -2008765718}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1.class", "size": 2474, "crc": 2045986919}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1$1.class", "size": 3614, "crc": 1857897801}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1.class", "size": 2556, "crc": 1532339984}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3.class", "size": 11730, "crc": 1948085488}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1.class", "size": 16544, "crc": -628174202}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2.class", "size": 9165, "crc": 274236825}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$3.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$3.class", "size": 4255, "crc": 1976544801}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$backLayer$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$backLayer$1.class", "size": 10957, "crc": -364259169}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$calculateBackLayerConstraints$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$calculateBackLayerConstraints$1$1.class", "size": 2039, "crc": 70116517}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffoldState$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffoldState$1.class", "size": 1758, "crc": -1091174809}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$2.class", "size": 3363, "crc": -731497924}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$placeables$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$placeables$1.class", "size": 3213, "crc": 1323633298}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1.class", "size": 7138, "crc": -503388354}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$2.class", "size": 2948, "crc": 1656866227}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPostFling$1.class", "size": 1895, "crc": 1547093086}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPreFling$1.class", "size": 1889, "crc": 192186704}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1.class", "size": 6476, "crc": 1788762534}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$1$1.class", "size": 2031, "crc": -1774212654}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$2.class", "size": 1880, "crc": -739632506}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1$1.class", "size": 1698, "crc": -1140622894}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1.class", "size": 4000, "crc": -793167358}, {"key": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$1.class", "size": 1651, "crc": -1905136889}, {"key": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$3$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$rememberBackdropScaffoldState$3$1.class", "size": 2919, "crc": 44197648}, {"key": "androidx/compose/material/BackdropScaffoldKt.class", "name": "androidx/compose/material/BackdropScaffoldKt.class", "size": 46358, "crc": 1152033375}, {"key": "androidx/compose/material/BackdropScaffoldState$1.class", "name": "androidx/compose/material/BackdropScaffoldState$1.class", "size": 1637, "crc": -1882108333}, {"key": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$1.class", "name": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$1.class", "size": 2267, "crc": -247248690}, {"key": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$2.class", "name": "androidx/compose/material/BackdropScaffoldState$Companion$Saver$2.class", "size": 3041, "crc": 2115397797}, {"key": "androidx/compose/material/BackdropScaffoldState$Companion.class", "name": "androidx/compose/material/BackdropScaffoldState$Companion.class", "size": 2921, "crc": 524544173}, {"key": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$1.class", "name": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$1.class", "size": 2587, "crc": -774823354}, {"key": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$2.class", "name": "androidx/compose/material/BackdropScaffoldState$anchoredDraggableState$2.class", "size": 2454, "crc": 96933203}, {"key": "androidx/compose/material/BackdropScaffoldState.class", "name": "androidx/compose/material/BackdropScaffoldState.class", "size": 9828, "crc": 2047847458}, {"key": "androidx/compose/material/BackdropValue.class", "name": "androidx/compose/material/BackdropValue.class", "size": 1834, "crc": -401645909}, {"key": "androidx/compose/material/BadgeKt$Badge$1$1$1.class", "name": "androidx/compose/material/BadgeKt$Badge$1$1$1.class", "size": 2939, "crc": -2044454257}, {"key": "androidx/compose/material/BadgeKt$Badge$1$1.class", "name": "androidx/compose/material/BadgeKt$Badge$1$1.class", "size": 4639, "crc": -711395399}, {"key": "androidx/compose/material/BadgeKt$Badge$2.class", "name": "androidx/compose/material/BadgeKt$Badge$2.class", "size": 2244, "crc": 1810443170}, {"key": "androidx/compose/material/BadgeKt$BadgedBox$2$1.class", "name": "androidx/compose/material/BadgeKt$BadgedBox$2$1.class", "size": 2582, "crc": -1685098148}, {"key": "androidx/compose/material/BadgeKt$BadgedBox$2.class", "name": "androidx/compose/material/BadgeKt$BadgedBox$2.class", "size": 5667, "crc": 503506047}, {"key": "androidx/compose/material/BadgeKt$BadgedBox$3.class", "name": "androidx/compose/material/BadgeKt$BadgedBox$3.class", "size": 2394, "crc": -1571344413}, {"key": "androidx/compose/material/BadgeKt.class", "name": "androidx/compose/material/BadgeKt.class", "size": 22028, "crc": -1656049267}, {"key": "androidx/compose/material/BottomAppBarCutoutShape.class", "name": "androidx/compose/material/BottomAppBarCutoutShape.class", "size": 9312, "crc": 637387846}, {"key": "androidx/compose/material/BottomDrawerState$1.class", "name": "androidx/compose/material/BottomDrawerState$1.class", "size": 1624, "crc": 2124924892}, {"key": "androidx/compose/material/BottomDrawerState$Companion$Saver$1.class", "name": "androidx/compose/material/BottomDrawerState$Companion$Saver$1.class", "size": 2200, "crc": -1734033760}, {"key": "androidx/compose/material/BottomDrawerState$Companion$Saver$2.class", "name": "androidx/compose/material/BottomDrawerState$Companion$Saver$2.class", "size": 2602, "crc": 1514179433}, {"key": "androidx/compose/material/BottomDrawerState$Companion.class", "name": "androidx/compose/material/BottomDrawerState$Companion.class", "size": 2691, "crc": -1373679190}, {"key": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$1.class", "name": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$1.class", "size": 2300, "crc": -2001675546}, {"key": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$2.class", "name": "androidx/compose/material/BottomDrawerState$anchoredDraggableState$2.class", "size": 2167, "crc": 1517372880}, {"key": "androidx/compose/material/BottomDrawerState.class", "name": "androidx/compose/material/BottomDrawerState.class", "size": 10592, "crc": 826023376}, {"key": "androidx/compose/material/BottomDrawerValue.class", "name": "androidx/compose/material/BottomDrawerValue.class", "size": 1907, "crc": 699554424}, {"key": "androidx/compose/material/BottomNavigationDefaults.class", "name": "androidx/compose/material/BottomNavigationDefaults.class", "size": 4035, "crc": 1608544264}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigation$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigation$1.class", "size": 10603, "crc": -1519909648}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigation$2.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigation$2.class", "size": 2631, "crc": -1255888213}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigation$3.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigation$3.class", "size": 2379, "crc": -1744919257}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$1$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$1$1.class", "size": 3590, "crc": 1789661841}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$2.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$2.class", "size": 3542, "crc": -428568871}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$styledLabel$1$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$styledLabel$1$1.class", "size": 4532, "crc": 1620849163}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$2$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$2$1.class", "size": 6109, "crc": -542747583}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$3.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$3.class", "size": 2274, "crc": -1115723428}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$1.class", "size": 3196, "crc": 888659907}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$2.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$2.class", "size": 2172, "crc": -977009915}, {"key": "androidx/compose/material/BottomNavigationKt$placeIcon$1.class", "name": "androidx/compose/material/BottomNavigationKt$placeIcon$1.class", "size": 1905, "crc": 1830227753}, {"key": "androidx/compose/material/BottomNavigationKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material/BottomNavigationKt$placeLabelAndIcon$1.class", "size": 2405, "crc": 70732131}, {"key": "androidx/compose/material/BottomNavigationKt.class", "name": "androidx/compose/material/BottomNavigationKt.class", "size": 37753, "crc": -821830345}, {"key": "androidx/compose/material/BottomSheetScaffoldDefaults.class", "name": "androidx/compose/material/BottomSheetScaffoldDefaults.class", "size": 3050, "crc": -1833675449}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$WhenMappings.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$WhenMappings.class", "size": 883, "crc": -908611588}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$newAnchors$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1$newAnchors$1.class", "size": 2058, "crc": -654013733}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$1$1.class", "size": 3763, "crc": -1906494609}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1$1.class", "size": 3406, "crc": -1321892895}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1.class", "size": 2515, "crc": -792978252}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2$1.class", "size": 3408, "crc": -1519536663}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2.class", "size": 2516, "crc": 955120599}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1.class", "size": 2811, "crc": -1188024221}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$3.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$3.class", "size": 9551, "crc": -1535762488}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$4.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$4.class", "size": 2984, "crc": 1041644867}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$1.class", "size": 3115, "crc": 1768202532}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$2.class", "size": 7409, "crc": -467504448}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$3.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$3.class", "size": 3224, "crc": 246985989}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$4$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$4$1.class", "size": 1565, "crc": -1714224306}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "size": 8675, "crc": 974204697}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "size": 4718, "crc": 1904510450}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1$WhenMappings.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1$WhenMappings.class", "size": 1006, "crc": -9040292}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$1.class", "size": 7679, "crc": 5783871}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "size": 12209, "crc": -2010745980}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2.class", "size": 3516, "crc": -747566712}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 2106, "crc": 963068808}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 2100, "crc": 872468050}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 6635, "crc": 1448129234}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$1.class", "size": 1613, "crc": -1181797265}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$2$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$rememberBottomSheetState$2$1.class", "size": 2447, "crc": 537734457}, {"key": "androidx/compose/material/BottomSheetScaffoldKt.class", "name": "androidx/compose/material/BottomSheetScaffoldKt.class", "size": 37775, "crc": 247951225}, {"key": "androidx/compose/material/BottomSheetScaffoldState.class", "name": "androidx/compose/material/BottomSheetScaffoldState.class", "size": 1508, "crc": -1959372403}, {"key": "androidx/compose/material/BottomSheetState$1.class", "name": "androidx/compose/material/BottomSheetState$1.class", "size": 1629, "crc": -1658646069}, {"key": "androidx/compose/material/BottomSheetState$Companion$Saver$1.class", "name": "androidx/compose/material/BottomSheetState$Companion$Saver$1.class", "size": 2202, "crc": -1149368483}, {"key": "androidx/compose/material/BottomSheetState$Companion$Saver$2.class", "name": "androidx/compose/material/BottomSheetState$Companion$Saver$2.class", "size": 2625, "crc": 1803417297}, {"key": "androidx/compose/material/BottomSheetState$Companion.class", "name": "androidx/compose/material/BottomSheetState$Companion.class", "size": 2648, "crc": 284148364}, {"key": "androidx/compose/material/BottomSheetState$anchoredDraggableState$1.class", "name": "androidx/compose/material/BottomSheetState$anchoredDraggableState$1.class", "size": 2384, "crc": -192433239}, {"key": "androidx/compose/material/BottomSheetState$anchoredDraggableState$2.class", "name": "androidx/compose/material/BottomSheetState$anchoredDraggableState$2.class", "size": 2251, "crc": 868402395}, {"key": "androidx/compose/material/BottomSheetState.class", "name": "androidx/compose/material/BottomSheetState.class", "size": 8602, "crc": 1300034366}, {"key": "androidx/compose/material/BottomSheetValue.class", "name": "androidx/compose/material/BottomSheetValue.class", "size": 1858, "crc": 888079759}, {"key": "androidx/compose/material/ButtonColors.class", "name": "androidx/compose/material/ButtonColors.class", "size": 1135, "crc": -1892842338}, {"key": "androidx/compose/material/ButtonDefaults.class", "name": "androidx/compose/material/ButtonDefaults.class", "size": 13131, "crc": 2063708419}, {"key": "androidx/compose/material/ButtonElevation.class", "name": "androidx/compose/material/ButtonElevation.class", "size": 1251, "crc": 1661803732}, {"key": "androidx/compose/material/ButtonKt$Button$1.class", "name": "androidx/compose/material/ButtonKt$Button$1.class", "size": 2183, "crc": -887258493}, {"key": "androidx/compose/material/ButtonKt$Button$2$1$1.class", "name": "androidx/compose/material/ButtonKt$Button$2$1$1.class", "size": 10089, "crc": -1073810269}, {"key": "androidx/compose/material/ButtonKt$Button$2$1.class", "name": "androidx/compose/material/ButtonKt$Button$2$1.class", "size": 3580, "crc": 708833448}, {"key": "androidx/compose/material/ButtonKt$Button$2.class", "name": "androidx/compose/material/ButtonKt$Button$2.class", "size": 4605, "crc": 1286700526}, {"key": "androidx/compose/material/ButtonKt$Button$3.class", "name": "androidx/compose/material/ButtonKt$Button$3.class", "size": 3865, "crc": 2011865700}, {"key": "androidx/compose/material/ButtonKt.class", "name": "androidx/compose/material/ButtonKt.class", "size": 14851, "crc": -2092552285}, {"key": "androidx/compose/material/CardKt.class", "name": "androidx/compose/material/CardKt.class", "size": 6408, "crc": 301031328}, {"key": "androidx/compose/material/CheckDrawingCache.class", "name": "androidx/compose/material/CheckDrawingCache.class", "size": 2131, "crc": 1443951857}, {"key": "androidx/compose/material/CheckboxColors.class", "name": "androidx/compose/material/CheckboxColors.class", "size": 1690, "crc": 215215333}, {"key": "androidx/compose/material/CheckboxDefaults.class", "name": "androidx/compose/material/CheckboxDefaults.class", "size": 5202, "crc": 1586333313}, {"key": "androidx/compose/material/CheckboxKt$Checkbox$1$1.class", "name": "androidx/compose/material/CheckboxKt$Checkbox$1$1.class", "size": 1778, "crc": 1524926049}, {"key": "androidx/compose/material/CheckboxKt$Checkbox$2.class", "name": "androidx/compose/material/CheckboxKt$Checkbox$2.class", "size": 2634, "crc": 268964181}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$1$1.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$1$1.class", "size": 3507, "crc": 1569691647}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$2.class", "size": 2033, "crc": 1983152943}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$2.class", "size": 3686, "crc": 2118371741}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkDrawFraction$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkDrawFraction$2.class", "size": 3676, "crc": -1910798649}, {"key": "androidx/compose/material/CheckboxKt$TriStateCheckbox$1.class", "name": "androidx/compose/material/CheckboxKt$TriStateCheckbox$1.class", "size": 2781, "crc": -177241421}, {"key": "androidx/compose/material/CheckboxKt$WhenMappings.class", "name": "androidx/compose/material/CheckboxKt$WhenMappings.class", "size": 848, "crc": 1309317390}, {"key": "androidx/compose/material/CheckboxKt.class", "name": "androidx/compose/material/CheckboxKt.class", "size": 28123, "crc": 176240512}, {"key": "androidx/compose/material/ChipColors.class", "name": "androidx/compose/material/ChipColors.class", "size": 1295, "crc": -434072199}, {"key": "androidx/compose/material/ChipDefaults.class", "name": "androidx/compose/material/ChipDefaults.class", "size": 10453, "crc": 169978522}, {"key": "androidx/compose/material/ChipKt$Chip$1.class", "name": "androidx/compose/material/ChipKt$Chip$1.class", "size": 2106, "crc": 1630959574}, {"key": "androidx/compose/material/ChipKt$Chip$2$1$1.class", "name": "androidx/compose/material/ChipKt$Chip$2$1$1.class", "size": 13315, "crc": 998005625}, {"key": "androidx/compose/material/ChipKt$Chip$2$1.class", "name": "androidx/compose/material/ChipKt$Chip$2$1.class", "size": 3904, "crc": 1533980875}, {"key": "androidx/compose/material/ChipKt$Chip$2.class", "name": "androidx/compose/material/ChipKt$Chip$2.class", "size": 4885, "crc": 1593448785}, {"key": "androidx/compose/material/ChipKt$Chip$3.class", "name": "androidx/compose/material/ChipKt$Chip$3.class", "size": 3725, "crc": 157999748}, {"key": "androidx/compose/material/ChipKt$FilterChip$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$1.class", "size": 2202, "crc": -1352130684}, {"key": "androidx/compose/material/ChipKt$FilterChip$2$1$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$2$1$1.class", "size": 22193, "crc": -1266424616}, {"key": "androidx/compose/material/ChipKt$FilterChip$2$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$2$1.class", "size": 4682, "crc": 1339670509}, {"key": "androidx/compose/material/ChipKt$FilterChip$2.class", "name": "androidx/compose/material/ChipKt$FilterChip$2.class", "size": 5615, "crc": -76401452}, {"key": "androidx/compose/material/ChipKt$FilterChip$3.class", "name": "androidx/compose/material/ChipKt$FilterChip$3.class", "size": 4351, "crc": -1172511226}, {"key": "androidx/compose/material/ChipKt.class", "name": "androidx/compose/material/ChipKt.class", "size": 17434, "crc": -1965200179}, {"key": "androidx/compose/material/Colors.class", "name": "androidx/compose/material/Colors.class", "size": 12483, "crc": -1903843324}, {"key": "androidx/compose/material/ColorsKt$LocalColors$1.class", "name": "androidx/compose/material/ColorsKt$LocalColors$1.class", "size": 1239, "crc": -707891189}, {"key": "androidx/compose/material/ColorsKt.class", "name": "androidx/compose/material/ColorsKt.class", "size": 9283, "crc": -834428393}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-1$1.class", "size": 2383, "crc": -153827412}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda-2$1.class", "size": 2384, "crc": 546532455}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt.class", "size": 1901, "crc": 935596224}, {"key": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt$lambda-1$1.class", "size": 2940, "crc": -1112700722}, {"key": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt.class", "size": 1618, "crc": -893346532}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "size": 2961, "crc": -566935890}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "size": 2212, "crc": 1982451721}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-3$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda-3$1.class", "size": 2212, "crc": 2021207375}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt.class", "size": 2669, "crc": -1218976845}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "size": 2135, "crc": -1526090263}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "size": 2135, "crc": -2038986938}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "size": 2883, "crc": -1006481777}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "size": 2135, "crc": -1460429038}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-5$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-5$1.class", "size": 2135, "crc": -1355382282}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-6$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-6$1.class", "size": 2135, "crc": -1310423720}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-7$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-7$1.class", "size": 2884, "crc": 1041073084}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-8$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda-8$1.class", "size": 2135, "crc": 1289719650}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt.class", "size": 4255, "crc": 2080941428}, {"key": "androidx/compose/material/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "size": 2968, "crc": -1307886889}, {"key": "androidx/compose/material/ComposableSingletons$SnackbarHostKt.class", "name": "androidx/compose/material/ComposableSingletons$SnackbarHostKt.class", "size": 1588, "crc": 460983815}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-1$1.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-1$1.class", "size": 2450, "crc": -1235433981}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-2$1.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda-2$1.class", "size": 2451, "crc": -11102360}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt.class", "size": 1811, "crc": 285934304}, {"key": "androidx/compose/material/ContentAlpha.class", "name": "androidx/compose/material/ContentAlpha.class", "size": 4957, "crc": -534032710}, {"key": "androidx/compose/material/ContentAlphaKt$LocalContentAlpha$1.class", "name": "androidx/compose/material/ContentAlphaKt$LocalContentAlpha$1.class", "size": 1152, "crc": 1351469970}, {"key": "androidx/compose/material/ContentAlphaKt.class", "name": "androidx/compose/material/ContentAlphaKt.class", "size": 1420, "crc": -1133541213}, {"key": "androidx/compose/material/ContentColorKt$LocalContentColor$1.class", "name": "androidx/compose/material/ContentColorKt$LocalContentColor$1.class", "size": 1410, "crc": 1384365550}, {"key": "androidx/compose/material/ContentColorKt.class", "name": "androidx/compose/material/ContentColorKt.class", "size": 1494, "crc": 528485050}, {"key": "androidx/compose/material/DefaultButtonColors.class", "name": "androidx/compose/material/DefaultButtonColors.class", "size": 3991, "crc": -1151076042}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$1$1$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$1$1$1.class", "size": 3511, "crc": -40014552}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$1$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$1$1.class", "size": 4206, "crc": -1991252262}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$2$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$2$1.class", "size": 5939, "crc": 871103390}, {"key": "androidx/compose/material/DefaultButtonElevation.class", "name": "androidx/compose/material/DefaultButtonElevation.class", "size": 9313, "crc": 531753574}, {"key": "androidx/compose/material/DefaultCheckboxColors$WhenMappings.class", "name": "androidx/compose/material/DefaultCheckboxColors$WhenMappings.class", "size": 870, "crc": 902829749}, {"key": "androidx/compose/material/DefaultCheckboxColors.class", "name": "androidx/compose/material/DefaultCheckboxColors.class", "size": 6802, "crc": -1416861943}, {"key": "androidx/compose/material/DefaultChipColors.class", "name": "androidx/compose/material/DefaultChipColors.class", "size": 4760, "crc": 1422551027}, {"key": "androidx/compose/material/DefaultElevationOverlay.class", "name": "androidx/compose/material/DefaultElevationOverlay.class", "size": 3623, "crc": -1272672207}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$1$1.class", "size": 4128, "crc": -6222496}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1$1.class", "size": 3852, "crc": 987128288}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1.class", "size": 4530, "crc": 1582916462}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1.class", "size": 4443, "crc": -1958789701}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation.class", "size": 8064, "crc": -69323025}, {"key": "androidx/compose/material/DefaultPlatformTextStyle_androidKt.class", "name": "androidx/compose/material/DefaultPlatformTextStyle_androidKt.class", "size": 1108, "crc": -1878901534}, {"key": "androidx/compose/material/DefaultRadioButtonColors.class", "name": "androidx/compose/material/DefaultRadioButtonColors.class", "size": 4270, "crc": 968917136}, {"key": "androidx/compose/material/DefaultSelectableChipColors.class", "name": "androidx/compose/material/DefaultSelectableChipColors.class", "size": 5516, "crc": -470586907}, {"key": "androidx/compose/material/DefaultSliderColors.class", "name": "androidx/compose/material/DefaultSliderColors.class", "size": 5656, "crc": 2135022935}, {"key": "androidx/compose/material/DefaultSwitchColors.class", "name": "androidx/compose/material/DefaultSwitchColors.class", "size": 4798, "crc": 1891012016}, {"key": "androidx/compose/material/DefaultTextFieldColors.class", "name": "androidx/compose/material/DefaultTextFieldColors.class", "size": 13791, "crc": 1223330444}, {"key": "androidx/compose/material/DefaultTextFieldForExposedDropdownMenusColors.class", "name": "androidx/compose/material/DefaultTextFieldForExposedDropdownMenusColors.class", "size": 14136, "crc": 2083646572}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "size": 3621, "crc": -488312731}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateRippleAlpha$1.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateRippleAlpha$1.class", "size": 2713, "crc": 329251200}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode$updateConfiguration$1.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode$updateConfiguration$1.class", "size": 2183, "crc": -1151097877}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode.class", "size": 5191, "crc": 1096718825}, {"key": "androidx/compose/material/DismissDirection.class", "name": "androidx/compose/material/DismissDirection.class", "size": 1856, "crc": 1829772058}, {"key": "androidx/compose/material/DismissState$1.class", "name": "androidx/compose/material/DismissState$1.class", "size": 1511, "crc": -167212634}, {"key": "androidx/compose/material/DismissState$Companion$Saver$1.class", "name": "androidx/compose/material/DismissState$Companion$Saver$1.class", "size": 1905, "crc": -1932001879}, {"key": "androidx/compose/material/DismissState$Companion$Saver$2.class", "name": "androidx/compose/material/DismissState$Companion$Saver$2.class", "size": 1931, "crc": 2093460386}, {"key": "androidx/compose/material/DismissState$Companion.class", "name": "androidx/compose/material/DismissState$Companion.class", "size": 2180, "crc": 1612023089}, {"key": "androidx/compose/material/DismissState.class", "name": "androidx/compose/material/DismissState.class", "size": 4554, "crc": -1845011385}, {"key": "androidx/compose/material/DismissValue.class", "name": "androidx/compose/material/DismissValue.class", "size": 1899, "crc": 1265365344}, {"key": "androidx/compose/material/DividerKt$Divider$1.class", "name": "androidx/compose/material/DividerKt$Divider$1.class", "size": 1802, "crc": -1108507939}, {"key": "androidx/compose/material/DividerKt.class", "name": "androidx/compose/material/DividerKt.class", "size": 6739, "crc": -298731534}, {"key": "androidx/compose/material/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/material/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2087, "crc": -145414835}, {"key": "androidx/compose/material/DragGestureDetectorCopyKt.class", "name": "androidx/compose/material/DragGestureDetectorCopyKt.class", "size": 15764, "crc": -395811762}, {"key": "androidx/compose/material/DraggableAnchors.class", "name": "androidx/compose/material/DraggableAnchors.class", "size": 1232, "crc": -1189443043}, {"key": "androidx/compose/material/DraggableAnchorsConfig.class", "name": "androidx/compose/material/DraggableAnchorsConfig.class", "size": 1724, "crc": 14516088}, {"key": "androidx/compose/material/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "size": 3349, "crc": 496299334}, {"key": "androidx/compose/material/DraggableAnchorsElement.class", "name": "androidx/compose/material/DraggableAnchorsElement.class", "size": 6544, "crc": -405268222}, {"key": "androidx/compose/material/DraggableAnchorsNode$measure$1.class", "name": "androidx/compose/material/DraggableAnchorsNode$measure$1.class", "size": 3334, "crc": 656541713}, {"key": "androidx/compose/material/DraggableAnchorsNode.class", "name": "androidx/compose/material/DraggableAnchorsNode.class", "size": 6476, "crc": -2010926300}, {"key": "androidx/compose/material/DrawerDefaults.class", "name": "androidx/compose/material/DrawerDefaults.class", "size": 5101, "crc": 415743264}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1$1.class", "size": 3320, "crc": 359731529}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1.class", "size": 2241, "crc": 174346709}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$WhenMappings.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$WhenMappings.class", "size": 961, "crc": -1387221817}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$newAnchors$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1$newAnchors$1.class", "size": 2186, "crc": 1547467999}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$2$1.class", "size": 3443, "crc": -1579833902}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$3$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$3$1.class", "size": 1883, "crc": -646704029}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1$1.class", "size": 3417, "crc": 1881373171}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1.class", "size": 2258, "crc": -2136489668}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1.class", "size": 2494, "crc": 1448871098}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$5.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$5.class", "size": 9321, "crc": -120449484}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1.class", "size": 19731, "crc": 1004401946}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$2.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$2.class", "size": 3274, "crc": -1150696167}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$1$1.class", "size": 2039, "crc": -1620328627}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$2.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$2.class", "size": 1888, "crc": -1948029934}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1$1.class", "size": 1694, "crc": -1635636602}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1.class", "size": 4002, "crc": 294150615}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1$1.class", "size": 1459, "crc": -59682814}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$2$1.class", "size": 2359, "crc": -1010953300}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 1989, "crc": -1644822088}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 1983, "crc": -632264078}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 6567, "crc": -1145833620}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1$anchors$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1$anchors$1.class", "size": 1876, "crc": 1422412631}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$1$1.class", "size": 2345, "crc": -382778811}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1$1.class", "size": 3290, "crc": -953484179}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1.class", "size": 2525, "crc": 325000663}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$3$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$3$1.class", "size": 1590, "crc": 1343333571}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$5$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$5$1.class", "size": 1862, "crc": 544768414}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1$1.class", "size": 3386, "crc": -411449710}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1.class", "size": 2514, "crc": 1022062862}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1.class", "size": 2466, "crc": 1978123011}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$7.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$7.class", "size": 9532, "crc": 1809850485}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1.class", "size": 22038, "crc": -703830225}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$2.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$2.class", "size": 3247, "crc": 1594935415}, {"key": "androidx/compose/material/DrawerKt$Scrim$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$1$1.class", "size": 2061, "crc": -292814060}, {"key": "androidx/compose/material/DrawerKt$Scrim$2.class", "name": "androidx/compose/material/DrawerKt$Scrim$2.class", "size": 2061, "crc": -476667778}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1$1.class", "size": 1650, "crc": 1379698320}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1.class", "size": 3950, "crc": 109604638}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1$1.class", "size": 1415, "crc": 340038459}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$2$1.class", "size": 2335, "crc": 804817822}, {"key": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$1.class", "name": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$1.class", "size": 1570, "crc": 471793998}, {"key": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$2$1.class", "name": "androidx/compose/material/DrawerKt$rememberBottomDrawerState$2$1.class", "size": 2385, "crc": -1623736665}, {"key": "androidx/compose/material/DrawerKt$rememberDrawerState$1.class", "name": "androidx/compose/material/DrawerKt$rememberDrawerState$1.class", "size": 1469, "crc": -799698273}, {"key": "androidx/compose/material/DrawerKt$rememberDrawerState$2$1.class", "name": "androidx/compose/material/DrawerKt$rememberDrawerState$2$1.class", "size": 1806, "crc": -1910929912}, {"key": "androidx/compose/material/DrawerKt.class", "name": "androidx/compose/material/DrawerKt.class", "size": 33905, "crc": -1500625289}, {"key": "androidx/compose/material/DrawerState$1.class", "name": "androidx/compose/material/DrawerState$1.class", "size": 1495, "crc": -1237128427}, {"key": "androidx/compose/material/DrawerState$Companion$Saver$1.class", "name": "androidx/compose/material/DrawerState$Companion$Saver$1.class", "size": 1861, "crc": 605621481}, {"key": "androidx/compose/material/DrawerState$Companion$Saver$2.class", "name": "androidx/compose/material/DrawerState$Companion$Saver$2.class", "size": 1909, "crc": -997803309}, {"key": "androidx/compose/material/DrawerState$Companion.class", "name": "androidx/compose/material/DrawerState$Companion.class", "size": 2161, "crc": 571711147}, {"key": "androidx/compose/material/DrawerState$anchoredDraggableState$1.class", "name": "androidx/compose/material/DrawerState$anchoredDraggableState$1.class", "size": 2349, "crc": 1607850524}, {"key": "androidx/compose/material/DrawerState$anchoredDraggableState$2.class", "name": "androidx/compose/material/DrawerState$anchoredDraggableState$2.class", "size": 2216, "crc": 713192645}, {"key": "androidx/compose/material/DrawerState.class", "name": "androidx/compose/material/DrawerState.class", "size": 8051, "crc": **********}, {"key": "androidx/compose/material/DrawerValue.class", "name": "androidx/compose/material/DrawerValue.class", "size": 1803, "crc": **********}, {"key": "androidx/compose/material/DropdownMenuPositionProvider$1.class", "name": "androidx/compose/material/DropdownMenuPositionProvider$1.class", "size": 1618, "crc": 644661315}, {"key": "androidx/compose/material/DropdownMenuPositionProvider.class", "name": "androidx/compose/material/DropdownMenuPositionProvider.class", "size": 10285, "crc": -**********}, {"key": "androidx/compose/material/ElevationDefaults.class", "name": "androidx/compose/material/ElevationDefaults.class", "size": 2385, "crc": -**********}, {"key": "androidx/compose/material/ElevationKt.class", "name": "androidx/compose/material/ElevationKt.class", "size": 4511, "crc": **********}, {"key": "androidx/compose/material/ElevationOverlay.class", "name": "androidx/compose/material/ElevationOverlay.class", "size": 835, "crc": -**********}, {"key": "androidx/compose/material/ElevationOverlayKt$LocalAbsoluteElevation$1.class", "name": "androidx/compose/material/ElevationOverlayKt$LocalAbsoluteElevation$1.class", "size": 2143, "crc": -**********}, {"key": "androidx/compose/material/ElevationOverlayKt$LocalElevationOverlay$1.class", "name": "androidx/compose/material/ElevationOverlayKt$LocalElevationOverlay$1.class", "size": 1375, "crc": **********}, {"key": "androidx/compose/material/ElevationOverlayKt.class", "name": "androidx/compose/material/ElevationOverlayKt.class", "size": 3950, "crc": 1041288002}, {"key": "androidx/compose/material/ExperimentalMaterialApi.class", "name": "androidx/compose/material/ExperimentalMaterialApi.class", "size": 822, "crc": -624179469}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "size": 4705, "crc": -235203904}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "size": 2994, "crc": **********}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "size": 2437, "crc": **********}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope.class", "size": 11955, "crc": -636072501}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "size": 1150, "crc": 865641754}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$2.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$2.class", "size": 1579, "crc": -320057527}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$3.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$3.class", "size": 3351, "crc": -30385135}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$4.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$4.class", "size": 2115, "crc": 780656346}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults.class", "size": 13767, "crc": -55354765}, {"key": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$1$1$invoke$$inlined$onDispose$1.class", "size": 2325, "crc": -416340277}, {"key": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$1$1.class", "size": 3375, "crc": 445868331}, {"key": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$2.class", "name": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$2.class", "size": 1837, "crc": -549262739}, {"key": "androidx/compose/material/ExposedDropdownMenu_android.class", "name": "androidx/compose/material/ExposedDropdownMenu_android.class", "size": 7432, "crc": 2095877350}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1$1.class", "size": 1676, "crc": -2027739494}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$1$1.class", "size": 3283, "crc": 1852243367}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$2$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$2$1.class", "size": 1813, "crc": -527409403}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4$1.class", "size": 1560, "crc": 1605727571}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1$1.class", "size": 1630, "crc": 1516340787}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$5$1.class", "size": 2643, "crc": 428180939}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$6.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$6.class", "size": 2623, "crc": -1992828204}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "size": 2681, "crc": 2072268370}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1$1.class", "size": 4444, "crc": -33034703}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1.class", "size": 3835, "crc": -1439506202}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2$1.class", "size": 1467, "crc": -1809775442}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$2.class", "size": 2410, "crc": -558689046}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt.class", "size": 23865, "crc": 1302450206}, {"key": "androidx/compose/material/FabPlacement.class", "name": "androidx/compose/material/FabPlacement.class", "size": 1301, "crc": 1675497121}, {"key": "androidx/compose/material/FabPosition$Companion.class", "name": "androidx/compose/material/FabPosition$Companion.class", "size": 1364, "crc": 148240663}, {"key": "androidx/compose/material/FabPosition.class", "name": "androidx/compose/material/FabPosition.class", "size": 2702, "crc": 1929250442}, {"key": "androidx/compose/material/FadeInFadeOutAnimationItem.class", "name": "androidx/compose/material/FadeInFadeOutAnimationItem.class", "size": 4391, "crc": 1798341213}, {"key": "androidx/compose/material/FadeInFadeOutState.class", "name": "androidx/compose/material/FadeInFadeOutState.class", "size": 2302, "crc": -906143382}, {"key": "androidx/compose/material/FixedThreshold.class", "name": "androidx/compose/material/FixedThreshold.class", "size": 3364, "crc": -1629648784}, {"key": "androidx/compose/material/FloatingActionButtonDefaults.class", "name": "androidx/compose/material/FloatingActionButtonDefaults.class", "size": 5963, "crc": -725759342}, {"key": "androidx/compose/material/FloatingActionButtonElevation.class", "name": "androidx/compose/material/FloatingActionButtonElevation.class", "size": 1258, "crc": -707964972}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "size": 2008, "crc": 193882926}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "size": 2064, "crc": -1542669478}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable.class", "size": 7126, "crc": 808267664}, {"key": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "size": 11038, "crc": -1474586435}, {"key": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "name": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "size": 3554, "crc": -1090059115}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$1.class", "size": 2172, "crc": -1181737086}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1$1.class", "size": 9369, "crc": -1156417584}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1.class", "size": 3449, "crc": 702872957}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2.class", "size": 4016, "crc": 1916890599}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$3.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$3.class", "size": 3312, "crc": 1732735765}, {"key": "androidx/compose/material/FloatingActionButtonKt.class", "name": "androidx/compose/material/FloatingActionButtonKt.class", "size": 15495, "crc": -1311832059}, {"key": "androidx/compose/material/FractionalThreshold.class", "name": "androidx/compose/material/FractionalThreshold.class", "size": 2887, "crc": 2117406517}, {"key": "androidx/compose/material/HighContrastContentAlpha.class", "name": "androidx/compose/material/HighContrastContentAlpha.class", "size": 903, "crc": 1462899739}, {"key": "androidx/compose/material/IconButtonKt$IconButton$2.class", "name": "androidx/compose/material/IconButtonKt$IconButton$2.class", "size": 2693, "crc": 1275322908}, {"key": "androidx/compose/material/IconButtonKt$IconToggleButton$2.class", "name": "androidx/compose/material/IconButtonKt$IconToggleButton$2.class", "size": 2810, "crc": -1553007447}, {"key": "androidx/compose/material/IconButtonKt.class", "name": "androidx/compose/material/IconButtonKt.class", "size": 17802, "crc": -892036539}, {"key": "androidx/compose/material/IconKt$Icon$1.class", "name": "androidx/compose/material/IconKt$Icon$1.class", "size": 1974, "crc": -1290843477}, {"key": "androidx/compose/material/IconKt$Icon$semantics$1$1.class", "name": "androidx/compose/material/IconKt$Icon$semantics$1$1.class", "size": 2072, "crc": 1263074833}, {"key": "androidx/compose/material/IconKt.class", "name": "androidx/compose/material/IconKt.class", "size": 13070, "crc": -1840098533}, {"key": "androidx/compose/material/InputPhase.class", "name": "androidx/compose/material/InputPhase.class", "size": 1885, "crc": 830059238}, {"key": "androidx/compose/material/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "name": "androidx/compose/material/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "size": 1260, "crc": 565243901}, {"key": "androidx/compose/material/InteractiveComponentSizeKt.class", "name": "androidx/compose/material/InteractiveComponentSizeKt.class", "size": 4005, "crc": -121301541}, {"key": "androidx/compose/material/InteractiveComponentSize_jvmKt.class", "name": "androidx/compose/material/InteractiveComponentSize_jvmKt.class", "size": 736, "crc": -1131477031}, {"key": "androidx/compose/material/InternalMutatorMutex$Mutator.class", "name": "androidx/compose/material/InternalMutatorMutex$Mutator.class", "size": 2052, "crc": -1822413140}, {"key": "androidx/compose/material/InternalMutatorMutex$mutate$2.class", "name": "androidx/compose/material/InternalMutatorMutex$mutate$2.class", "size": 7141, "crc": -837385070}, {"key": "androidx/compose/material/InternalMutatorMutex$mutateWith$2.class", "name": "androidx/compose/material/InternalMutatorMutex$mutateWith$2.class", "size": 7315, "crc": 420340301}, {"key": "androidx/compose/material/InternalMutatorMutex.class", "name": "androidx/compose/material/InternalMutatorMutex.class", "size": 6737, "crc": -84129520}, {"key": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1$2.class", "name": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1$2.class", "size": 3283, "crc": -1692126511}, {"key": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1.class", "name": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1.class", "size": 6507, "crc": -1392236668}, {"key": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$2.class", "name": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$2.class", "size": 2324, "crc": -920037861}, {"key": "androidx/compose/material/ListItemKt$ListItem$1.class", "name": "androidx/compose/material/ListItemKt$ListItem$1.class", "size": 3018, "crc": 608721350}, {"key": "androidx/compose/material/ListItemKt$ListItem$semanticsModifier$1.class", "name": "androidx/compose/material/ListItemKt$ListItem$semanticsModifier$1.class", "size": 1688, "crc": -29233273}, {"key": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1$1.class", "name": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1$1.class", "size": 1916, "crc": -1618535155}, {"key": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1.class", "name": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1.class", "size": 3822, "crc": -279407541}, {"key": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$2.class", "name": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$2.class", "size": 2202, "crc": -1375848282}, {"key": "androidx/compose/material/ListItemKt$applyTextStyle$1$1.class", "name": "androidx/compose/material/ListItemKt$applyTextStyle$1$1.class", "size": 3787, "crc": -664379018}, {"key": "androidx/compose/material/ListItemKt$applyTextStyle$1.class", "name": "androidx/compose/material/ListItemKt$applyTextStyle$1.class", "size": 3975, "crc": 544728193}, {"key": "androidx/compose/material/ListItemKt.class", "name": "androidx/compose/material/ListItemKt.class", "size": 19004, "crc": 883128879}, {"key": "androidx/compose/material/LowContrastContentAlpha.class", "name": "androidx/compose/material/LowContrastContentAlpha.class", "size": 901, "crc": 787145023}, {"key": "androidx/compose/material/MapDraggableAnchors.class", "name": "androidx/compose/material/MapDraggableAnchors.class", "size": 5378, "crc": -794418191}, {"key": "androidx/compose/material/MaterialTextSelectionColorsKt.class", "name": "androidx/compose/material/MaterialTextSelectionColorsKt.class", "size": 7986, "crc": -1151534541}, {"key": "androidx/compose/material/MaterialTheme.class", "name": "androidx/compose/material/MaterialTheme.class", "size": 4642, "crc": 881909294}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1$1.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1$1.class", "size": 2576, "crc": 520683448}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1.class", "size": 3396, "crc": 783119334}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$2.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$2.class", "size": 2487, "crc": -1722305138}, {"key": "androidx/compose/material/MaterialThemeKt.class", "name": "androidx/compose/material/MaterialThemeKt.class", "size": 8641, "crc": 1966445935}, {"key": "androidx/compose/material/MaterialTheme_androidKt$PlatformMaterialTheme$1.class", "name": "androidx/compose/material/MaterialTheme_androidKt$PlatformMaterialTheme$1.class", "size": 1910, "crc": -191197315}, {"key": "androidx/compose/material/MaterialTheme_androidKt.class", "name": "androidx/compose/material/MaterialTheme_androidKt.class", "size": 2654, "crc": 987923073}, {"key": "androidx/compose/material/MenuDefaults.class", "name": "androidx/compose/material/MenuDefaults.class", "size": 2078, "crc": 1954015623}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$1$1.class", "size": 2780, "crc": -980509542}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$2.class", "size": 10480, "crc": -1935701551}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$3.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$3.class", "size": 3137, "crc": 1499646786}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$alpha$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$alpha$2.class", "size": 3446, "crc": -1797245367}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$scale$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$scale$2.class", "size": 3574, "crc": -1864131203}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1$1.class", "size": 3006, "crc": 1871279650}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1.class", "size": 4547, "crc": -1792150109}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$2.class", "size": 3058, "crc": 353075307}, {"key": "androidx/compose/material/MenuKt.class", "name": "androidx/compose/material/MenuKt.class", "size": 27041, "crc": 2041134545}, {"key": "androidx/compose/material/MinimumInteractiveComponentSizeModifier$measure$1.class", "name": "androidx/compose/material/MinimumInteractiveComponentSizeModifier$measure$1.class", "size": 2193, "crc": 17151688}, {"key": "androidx/compose/material/MinimumInteractiveComponentSizeModifier.class", "name": "androidx/compose/material/MinimumInteractiveComponentSizeModifier.class", "size": 3363, "crc": -1345177686}, {"key": "androidx/compose/material/MinimumInteractiveModifier.class", "name": "androidx/compose/material/MinimumInteractiveModifier.class", "size": 4032, "crc": -1385426625}, {"key": "androidx/compose/material/MinimumInteractiveModifierNode$measure$1.class", "name": "androidx/compose/material/MinimumInteractiveModifierNode$measure$1.class", "size": 2166, "crc": 33652172}, {"key": "androidx/compose/material/MinimumInteractiveModifierNode.class", "name": "androidx/compose/material/MinimumInteractiveModifierNode.class", "size": 3586, "crc": 1040612097}, {"key": "androidx/compose/material/ModalBottomSheetDefaults.class", "name": "androidx/compose/material/ModalBottomSheetDefaults.class", "size": 4212, "crc": -1010913331}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 2079, "crc": -1843091598}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 2073, "crc": -1556319191}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 6614, "crc": -1775533922}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1$1.class", "size": 3381, "crc": -884761978}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1.class", "size": 2723, "crc": -617885764}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1$1.class", "size": 3486, "crc": -470414621}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1.class", "size": 2589, "crc": 545420044}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2$1.class", "size": 3505, "crc": -494303069}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2.class", "size": 2599, "crc": 4478366}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3$1.class", "size": 3509, "crc": -542758358}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3.class", "size": 2603, "crc": -1243978734}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1.class", "size": 3167, "crc": -1491178891}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$4.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$4.class", "size": 9661, "crc": -1911703912}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$2.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$2.class", "size": 3359, "crc": 324377827}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$1$1.class", "size": 2094, "crc": -1393498663}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$2.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$2.class", "size": 1880, "crc": 1256959301}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1$1.class", "size": 1698, "crc": 1129192260}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1.class", "size": 4000, "crc": 1806128957}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1$1.class", "size": 1463, "crc": -1174193871}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$2$1.class", "size": 2360, "crc": -313751501}, {"key": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$WhenMappings.class", "name": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$WhenMappings.class", "size": 958, "crc": -2080625192}, {"key": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$newAnchors$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1$newAnchors$1.class", "size": 2495, "crc": 340913700}, {"key": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$modalBottomSheetAnchors$1.class", "size": 4115, "crc": 168557619}, {"key": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$1.class", "size": 1647, "crc": 1325465060}, {"key": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$2$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$rememberModalBottomSheetState$2$1.class", "size": 2574, "crc": -1219944638}, {"key": "androidx/compose/material/ModalBottomSheetKt.class", "name": "androidx/compose/material/ModalBottomSheetKt.class", "size": 37660, "crc": 1944494092}, {"key": "androidx/compose/material/ModalBottomSheetState$1.class", "name": "androidx/compose/material/ModalBottomSheetState$1.class", "size": 1667, "crc": 717162710}, {"key": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$1.class", "name": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$1.class", "size": 2063, "crc": 1019914378}, {"key": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$2.class", "name": "androidx/compose/material/ModalBottomSheetState$Companion$Saver$2.class", "size": 2762, "crc": 499020873}, {"key": "androidx/compose/material/ModalBottomSheetState$Companion.class", "name": "androidx/compose/material/ModalBottomSheetState$Companion.class", "size": 2747, "crc": -335296310}, {"key": "androidx/compose/material/ModalBottomSheetState$WhenMappings.class", "name": "androidx/compose/material/ModalBottomSheetState$WhenMappings.class", "size": 804, "crc": -2122943675}, {"key": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$1.class", "name": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$1.class", "size": 2399, "crc": 644016822}, {"key": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$2.class", "name": "androidx/compose/material/ModalBottomSheetState$anchoredDraggableState$2.class", "size": 2266, "crc": 1614913388}, {"key": "androidx/compose/material/ModalBottomSheetState.class", "name": "androidx/compose/material/ModalBottomSheetState.class", "size": 10459, "crc": -205480660}, {"key": "androidx/compose/material/ModalBottomSheetValue.class", "name": "androidx/compose/material/ModalBottomSheetValue.class", "size": 1953, "crc": -1947567204}, {"key": "androidx/compose/material/MutableWindowInsets.class", "name": "androidx/compose/material/MutableWindowInsets.class", "size": 4126, "crc": 1987037965}, {"key": "androidx/compose/material/NavigationRailDefaults.class", "name": "androidx/compose/material/NavigationRailDefaults.class", "size": 4000, "crc": -400736346}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRail$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRail$1.class", "size": 11660, "crc": 1691299102}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRail$2.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRail$2.class", "size": 2895, "crc": -351455535}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRail$3.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRail$3.class", "size": 2643, "crc": -1898842581}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$1$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$1$1.class", "size": 3522, "crc": -1654130258}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$2.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$2.class", "size": 3209, "crc": -387187714}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "size": 4466, "crc": -1626625325}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$2$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$2$1.class", "size": 6055, "crc": -1815431359}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$3.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$3.class", "size": 2258, "crc": 405184015}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$1.class", "size": 3174, "crc": 1657859016}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$2.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$2.class", "size": 2156, "crc": -1689876240}, {"key": "androidx/compose/material/NavigationRailKt$placeIcon$1.class", "name": "androidx/compose/material/NavigationRailKt$placeIcon$1.class", "size": 1947, "crc": -1195579706}, {"key": "androidx/compose/material/NavigationRailKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material/NavigationRailKt$placeLabelAndIcon$1.class", "size": 2397, "crc": 270028424}, {"key": "androidx/compose/material/NavigationRailKt.class", "name": "androidx/compose/material/NavigationRailKt.class", "size": 37935, "crc": 1955317326}, {"key": "androidx/compose/material/OnGlobalLayoutListener.class", "name": "androidx/compose/material/OnGlobalLayoutListener.class", "size": 3011, "crc": -1977779860}, {"key": "androidx/compose/material/OneLine$ListItem$2.class", "name": "androidx/compose/material/OneLine$ListItem$2.class", "size": 2637, "crc": -1230103758}, {"key": "androidx/compose/material/OneLine.class", "name": "androidx/compose/material/OneLine.class", "size": 19496, "crc": 2083218186}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$1.class", "size": 2256, "crc": -1795796908}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$10.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$10.class", "size": 5274, "crc": 855206719}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$11.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$11.class", "size": 2104, "crc": 79924809}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13$1.class", "size": 3282, "crc": -1099640386}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13.class", "size": 6991, "crc": -1336067596}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$14.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$14.class", "size": 5501, "crc": 1758782617}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$16.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$16.class", "size": 5448, "crc": 1399454267}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3$Decoration$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3$Decoration$1.class", "size": 3303, "crc": 6529461}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3.class", "size": 9742, "crc": -1629481822}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$4.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$4.class", "size": 5916, "crc": 151741088}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$5.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$5.class", "size": 2073, "crc": -946104252}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7$1.class", "size": 3279, "crc": 1299503287}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7.class", "size": 6759, "crc": 504620390}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$8.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$8.class", "size": 5325, "crc": 594901332}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "size": 4168, "crc": 506817316}, {"key": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "name": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "size": 817, "crc": 433527415}, {"key": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$outlineCutout$1.class", "size": 5702, "crc": 637727895}, {"key": "androidx/compose/material/OutlinedTextFieldKt.class", "name": "androidx/compose/material/OutlinedTextFieldKt.class", "size": 81651, "crc": 1602611492}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1785, "crc": 439770682}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1782, "crc": -2014192287}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$measure$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$measure$1.class", "size": 3668, "crc": -1159527325}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1785, "crc": -600160069}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1782, "crc": 1875701297}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy.class", "size": 25476, "crc": 1365414360}, {"key": "androidx/compose/material/ProgressIndicatorDefaults.class", "name": "androidx/compose/material/ProgressIndicatorDefaults.class", "size": 2659, "crc": -1634163791}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$1$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$1$1.class", "size": 2220, "crc": 811046685}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$2.class", "size": 2006, "crc": -1521210245}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$3$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$3$1.class", "size": 3494, "crc": 394123282}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$4.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$4.class", "size": 1955, "crc": 817581497}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$5.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$5.class", "size": 1894, "crc": -954817891}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$6.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$6.class", "size": 1843, "crc": -734057139}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$endAngle$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$endAngle$2.class", "size": 2569, "crc": -1732815624}, {"key": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$startAngle$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$CircularProgressIndicator$startAngle$2.class", "size": 2612, "crc": 1873857880}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$1$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$1$1.class", "size": 2099, "crc": 1862605205}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$2.class", "size": 1945, "crc": 1228940155}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$3$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$3$1.class", "size": 3199, "crc": 1576817292}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$4.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$4.class", "size": 1890, "crc": -324433727}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$5.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$5.class", "size": 1892, "crc": 1603038541}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$6.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$6.class", "size": 1837, "crc": 556575127}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$2.class", "size": 2571, "crc": 1775850043}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$2.class", "size": 2573, "crc": -534032177}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$2.class", "size": 2576, "crc": -1225255844}, {"key": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$2.class", "size": 2576, "crc": -33485283}, {"key": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1$1.class", "size": 1966, "crc": 1988439218}, {"key": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1.class", "name": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$1.class", "size": 2922, "crc": 1053699334}, {"key": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$2.class", "name": "androidx/compose/material/ProgressIndicatorKt$increaseSemanticsBounds$2.class", "size": 1564, "crc": 42763323}, {"key": "androidx/compose/material/ProgressIndicatorKt.class", "name": "androidx/compose/material/ProgressIndicatorKt.class", "size": 41825, "crc": -1566227710}, {"key": "androidx/compose/material/RadioButtonColors.class", "name": "androidx/compose/material/RadioButtonColors.class", "size": 1074, "crc": -1790235093}, {"key": "androidx/compose/material/RadioButtonDefaults.class", "name": "androidx/compose/material/RadioButtonDefaults.class", "size": 4686, "crc": 985295446}, {"key": "androidx/compose/material/RadioButtonKt$RadioButton$1$1.class", "name": "androidx/compose/material/RadioButtonKt$RadioButton$1$1.class", "size": 3972, "crc": 1666964549}, {"key": "androidx/compose/material/RadioButtonKt$RadioButton$2.class", "name": "androidx/compose/material/RadioButtonKt$RadioButton$2.class", "size": 2621, "crc": -318221385}, {"key": "androidx/compose/material/RadioButtonKt.class", "name": "androidx/compose/material/RadioButtonKt.class", "size": 11000, "crc": 1770504397}, {"key": "androidx/compose/material/RangeSliderLogic$captureThumb$1.class", "name": "androidx/compose/material/RangeSliderLogic$captureThumb$1.class", "size": 3807, "crc": -1391333703}, {"key": "androidx/compose/material/RangeSliderLogic.class", "name": "androidx/compose/material/RangeSliderLogic.class", "size": 5218, "crc": -864498662}, {"key": "androidx/compose/material/ResistanceConfig.class", "name": "androidx/compose/material/ResistanceConfig.class", "size": 4017, "crc": 1047708117}, {"key": "androidx/compose/material/RippleConfiguration.class", "name": "androidx/compose/material/RippleConfiguration.class", "size": 3281, "crc": 1735702650}, {"key": "androidx/compose/material/RippleDefaults.class", "name": "androidx/compose/material/RippleDefaults.class", "size": 2157, "crc": 164669038}, {"key": "androidx/compose/material/RippleKt$LocalRippleConfiguration$1.class", "name": "androidx/compose/material/RippleKt$LocalRippleConfiguration$1.class", "size": 1349, "crc": -314967599}, {"key": "androidx/compose/material/RippleKt.class", "name": "androidx/compose/material/RippleKt.class", "size": 4869, "crc": 1115344568}, {"key": "androidx/compose/material/RippleNodeFactory$create$colorProducer$1.class", "name": "androidx/compose/material/RippleNodeFactory$create$colorProducer$1.class", "size": 1193, "crc": -2059289473}, {"key": "androidx/compose/material/RippleNodeFactory.class", "name": "androidx/compose/material/RippleNodeFactory.class", "size": 4316, "crc": 2104163952}, {"key": "androidx/compose/material/ScaffoldDefaults.class", "name": "androidx/compose/material/ScaffoldDefaults.class", "size": 2428, "crc": -538338340}, {"key": "androidx/compose/material/ScaffoldKt$LocalFabPlacement$1.class", "name": "androidx/compose/material/ScaffoldKt$LocalFabPlacement$1.class", "size": 1151, "crc": -642841660}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$1.class", "size": 3267, "crc": 2083783472}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$2.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$2.class", "size": 5163, "crc": -512959672}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$3.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$3.class", "size": 4903, "crc": 513997371}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$1$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$1$1.class", "size": 1999, "crc": -1324851659}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2$1.class", "size": 3173, "crc": -1482852769}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2.class", "size": 5153, "crc": 1779739060}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1.class", "size": 8407, "crc": 767470501}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$1.class", "size": 5782, "crc": 184324488}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "size": 3230, "crc": 1872948311}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "size": 3414, "crc": 841676474}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1.class", "size": 17870, "crc": -127905858}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$2.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$2.class", "size": 3391, "crc": 1220127093}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$contentPadding$1$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$contentPadding$1$1.class", "size": 4364, "crc": 864784400}, {"key": "androidx/compose/material/ScaffoldKt.class", "name": "androidx/compose/material/ScaffoldKt.class", "size": 29322, "crc": -1651733507}, {"key": "androidx/compose/material/ScaffoldLayoutContent.class", "name": "androidx/compose/material/ScaffoldLayoutContent.class", "size": 2064, "crc": -1475203347}, {"key": "androidx/compose/material/ScaffoldState.class", "name": "androidx/compose/material/ScaffoldState.class", "size": 1438, "crc": 448731946}, {"key": "androidx/compose/material/ScrollableTabData$onLaidOut$1$1.class", "name": "androidx/compose/material/ScrollableTabData$onLaidOut$1$1.class", "size": 3712, "crc": 1537202335}, {"key": "androidx/compose/material/ScrollableTabData.class", "name": "androidx/compose/material/ScrollableTabData.class", "size": 4233, "crc": 2026429727}, {"key": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$1.class", "name": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$1.class", "size": 2113, "crc": 369277822}, {"key": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3$Decoration$1.class", "name": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3$Decoration$1.class", "size": 3315, "crc": 1118962870}, {"key": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3.class", "name": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3.class", "size": 6435, "crc": 1524357689}, {"key": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$4.class", "name": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$4.class", "size": 5194, "crc": -295641285}, {"key": "androidx/compose/material/SecureTextFieldKt$SecureTextField$1.class", "name": "androidx/compose/material/SecureTextFieldKt$SecureTextField$1.class", "size": 5793, "crc": 1001199506}, {"key": "androidx/compose/material/SecureTextFieldKt$SecureTextField$2.class", "name": "androidx/compose/material/SecureTextFieldKt$SecureTextField$2.class", "size": 5170, "crc": 920235650}, {"key": "androidx/compose/material/SecureTextFieldKt.class", "name": "androidx/compose/material/SecureTextFieldKt.class", "size": 25024, "crc": 1410066161}, {"key": "androidx/compose/material/SelectableChipColors.class", "name": "androidx/compose/material/SelectableChipColors.class", "size": 1314, "crc": 1590601672}, {"key": "androidx/compose/material/Shapes.class", "name": "androidx/compose/material/Shapes.class", "size": 4855, "crc": -1289294161}, {"key": "androidx/compose/material/ShapesKt$LocalShapes$1.class", "name": "androidx/compose/material/ShapesKt$LocalShapes$1.class", "size": 1380, "crc": 1489677614}, {"key": "androidx/compose/material/ShapesKt.class", "name": "androidx/compose/material/ShapesKt.class", "size": 1371, "crc": -381189672}, {"key": "androidx/compose/material/SliderColors.class", "name": "androidx/compose/material/SliderColors.class", "size": 1445, "crc": 553534104}, {"key": "androidx/compose/material/SliderDefaults.class", "name": "androidx/compose/material/SliderDefaults.class", "size": 4426, "crc": 849866203}, {"key": "androidx/compose/material/SliderDraggableState$drag$2.class", "name": "androidx/compose/material/SliderDraggableState$drag$2.class", "size": 4473, "crc": 1432214477}, {"key": "androidx/compose/material/SliderDraggableState$dragScope$1.class", "name": "androidx/compose/material/SliderDraggableState$dragScope$1.class", "size": 1345, "crc": -1676339928}, {"key": "androidx/compose/material/SliderDraggableState.class", "name": "androidx/compose/material/SliderDraggableState.class", "size": 6197, "crc": 333474390}, {"key": "androidx/compose/material/SliderKt$CorrectValueSideEffect$1$1.class", "name": "androidx/compose/material/SliderKt$CorrectValueSideEffect$1$1.class", "size": 3003, "crc": 1872802349}, {"key": "androidx/compose/material/SliderKt$CorrectValueSideEffect$2.class", "name": "androidx/compose/material/SliderKt$CorrectValueSideEffect$2.class", "size": 2684, "crc": 2117340498}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$2$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$2$1.class", "size": 2336, "crc": 760535954}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$3$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$3$1.class", "size": 2336, "crc": -1653274087}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$endThumbSemantics$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$endThumbSemantics$1$1.class", "size": 2029, "crc": 685125762}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1$1.class", "size": 3935, "crc": 83025913}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1.class", "size": 6024, "crc": -2079708508}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1.class", "size": 4438, "crc": 1865387594}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$onDrag$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$onDrag$1$1.class", "size": 4346, "crc": 1766945011}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$startThumbSemantics$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$startThumbSemantics$1$1.class", "size": 2031, "crc": -848679976}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2.class", "size": 19512, "crc": -804426761}, {"key": "androidx/compose/material/SliderKt$RangeSlider$3.class", "name": "androidx/compose/material/SliderKt$RangeSlider$3.class", "size": 3142, "crc": -1019671071}, {"key": "androidx/compose/material/SliderKt$RangeSliderImpl$1$2$1.class", "name": "androidx/compose/material/SliderKt$RangeSliderImpl$1$2$1.class", "size": 1971, "crc": -619894816}, {"key": "androidx/compose/material/SliderKt$RangeSliderImpl$1$3$1.class", "name": "androidx/compose/material/SliderKt$RangeSliderImpl$1$3$1.class", "size": 1969, "crc": 188680201}, {"key": "androidx/compose/material/SliderKt$RangeSliderImpl$2.class", "name": "androidx/compose/material/SliderKt$RangeSliderImpl$2.class", "size": 3257, "crc": 1105204525}, {"key": "androidx/compose/material/SliderKt$Slider$2$2$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$2$1.class", "size": 2321, "crc": 1118400267}, {"key": "androidx/compose/material/SliderKt$Slider$2$drag$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$drag$1$1.class", "size": 3374, "crc": 1819003309}, {"key": "androidx/compose/material/SliderKt$Slider$2$draggableState$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$draggableState$1$1.class", "size": 3217, "crc": -1579847952}, {"key": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1$1.class", "size": 3972, "crc": -1160201353}, {"key": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1.class", "size": 3661, "crc": 2137972611}, {"key": "androidx/compose/material/SliderKt$Slider$2.class", "name": "androidx/compose/material/SliderKt$Slider$2.class", "size": 17439, "crc": -1208769687}, {"key": "androidx/compose/material/SliderKt$Slider$3.class", "name": "androidx/compose/material/SliderKt$Slider$3.class", "size": 3229, "crc": -210807685}, {"key": "androidx/compose/material/SliderKt$SliderImpl$2.class", "name": "androidx/compose/material/SliderKt$SliderImpl$2.class", "size": 2546, "crc": 1005540337}, {"key": "androidx/compose/material/SliderKt$SliderThumb$1$1$1$1.class", "name": "androidx/compose/material/SliderKt$SliderThumb$1$1$1$1.class", "size": 3182, "crc": -667105446}, {"key": "androidx/compose/material/SliderKt$SliderThumb$1$1$1.class", "name": "androidx/compose/material/SliderKt$SliderThumb$1$1$1.class", "size": 4273, "crc": -96106321}, {"key": "androidx/compose/material/SliderKt$SliderThumb$2.class", "name": "androidx/compose/material/SliderKt$SliderThumb$2.class", "size": 2427, "crc": 1428652424}, {"key": "androidx/compose/material/SliderKt$Track$1$1.class", "name": "androidx/compose/material/SliderKt$Track$1$1.class", "size": 9117, "crc": -1802323759}, {"key": "androidx/compose/material/SliderKt$Track$2.class", "name": "androidx/compose/material/SliderKt$Track$2.class", "size": 2332, "crc": 672354342}, {"key": "androidx/compose/material/SliderKt$animateToTarget$2$1.class", "name": "androidx/compose/material/SliderKt$animateToTarget$2$1.class", "size": 2209, "crc": 1151779513}, {"key": "androidx/compose/material/SliderKt$animateToTarget$2.class", "name": "androidx/compose/material/SliderKt$animateToTarget$2.class", "size": 4373, "crc": 801891906}, {"key": "androidx/compose/material/SliderKt$awaitSlop$1.class", "name": "androidx/compose/material/SliderKt$awaitSlop$1.class", "size": 1559, "crc": 1589436440}, {"key": "androidx/compose/material/SliderKt$awaitSlop$postPointerSlop$1.class", "name": "androidx/compose/material/SliderKt$awaitSlop$postPointerSlop$1.class", "size": 1912, "crc": 2123233324}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "size": 4289, "crc": -470209650}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "size": 3000, "crc": -162273879}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "size": 10774, "crc": -1771537117}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1.class", "size": 5198, "crc": 794026436}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1.class", "size": 6054, "crc": -1868833}, {"key": "androidx/compose/material/SliderKt$slideOnKeyEvents$2.class", "name": "androidx/compose/material/SliderKt$slideOnKeyEvents$2.class", "size": 5413, "crc": 296185520}, {"key": "androidx/compose/material/SliderKt$sliderSemantics$1$1.class", "name": "androidx/compose/material/SliderKt$sliderSemantics$1$1.class", "size": 3109, "crc": 193718551}, {"key": "androidx/compose/material/SliderKt$sliderSemantics$1.class", "name": "androidx/compose/material/SliderKt$sliderSemantics$1.class", "size": 2951, "crc": -533164273}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$$inlined$debugInspectorInfo$1.class", "size": 4136, "crc": 222697108}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$1.class", "size": 4429, "crc": 1349962898}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1$1.class", "size": 3266, "crc": 400527637}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1.class", "size": 4358, "crc": 2143301791}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2.class", "size": 2643, "crc": 917653923}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1.class", "size": 5357, "crc": 246879885}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2.class", "size": 8494, "crc": -405682983}, {"key": "androidx/compose/material/SliderKt.class", "name": "androidx/compose/material/SliderKt.class", "size": 65769, "crc": 162027678}, {"key": "androidx/compose/material/SnackbarData.class", "name": "androidx/compose/material/SnackbarData.class", "size": 958, "crc": 1249382961}, {"key": "androidx/compose/material/SnackbarDefaults.class", "name": "androidx/compose/material/SnackbarDefaults.class", "size": 3222, "crc": 1078157212}, {"key": "androidx/compose/material/SnackbarDuration.class", "name": "androidx/compose/material/SnackbarDuration.class", "size": 1907, "crc": 1178470732}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "size": 1493, "crc": -1243081070}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "size": 2681, "crc": 1615312474}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "size": 2030, "crc": 751087017}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "size": 2434, "crc": 1224490978}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "size": 14344, "crc": -92817017}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "size": 3251, "crc": 1760744987}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "size": 2432, "crc": -977466847}, {"key": "androidx/compose/material/SnackbarHostKt$SnackbarHost$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$SnackbarHost$1$1.class", "size": 4088, "crc": -1762575483}, {"key": "androidx/compose/material/SnackbarHostKt$SnackbarHost$2.class", "name": "androidx/compose/material/SnackbarHostKt$SnackbarHost$2.class", "size": 2388, "crc": -1147374248}, {"key": "androidx/compose/material/SnackbarHostKt$WhenMappings.class", "name": "androidx/compose/material/SnackbarHostKt$WhenMappings.class", "size": 864, "crc": 930310720}, {"key": "androidx/compose/material/SnackbarHostKt$animatedOpacity$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedOpacity$1.class", "size": 1191, "crc": -710212654}, {"key": "androidx/compose/material/SnackbarHostKt$animatedOpacity$2$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedOpacity$2$1.class", "size": 4564, "crc": -723279988}, {"key": "androidx/compose/material/SnackbarHostKt$animatedScale$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedScale$1$1.class", "size": 4236, "crc": -969093257}, {"key": "androidx/compose/material/SnackbarHostKt.class", "name": "androidx/compose/material/SnackbarHostKt.class", "size": 25557, "crc": 1666947559}, {"key": "androidx/compose/material/SnackbarHostState$SnackbarDataImpl.class", "name": "androidx/compose/material/SnackbarHostState$SnackbarDataImpl.class", "size": 3039, "crc": 1301227514}, {"key": "androidx/compose/material/SnackbarHostState$showSnackbar$1.class", "name": "androidx/compose/material/SnackbarHostState$showSnackbar$1.class", "size": 2060, "crc": -2071973449}, {"key": "androidx/compose/material/SnackbarHostState.class", "name": "androidx/compose/material/SnackbarHostState.class", "size": 8244, "crc": 1383997427}, {"key": "androidx/compose/material/SnackbarKt$NewLineButtonSnackbar$2.class", "name": "androidx/compose/material/SnackbarKt$NewLineButtonSnackbar$2.class", "size": 2113, "crc": 346984195}, {"key": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1$2.class", "name": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1$2.class", "size": 2118, "crc": 370776802}, {"key": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1.class", "name": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1.class", "size": 6838, "crc": 1206907249}, {"key": "androidx/compose/material/SnackbarKt$OneRowSnackbar$3.class", "name": "androidx/compose/material/SnackbarKt$OneRowSnackbar$3.class", "size": 2085, "crc": 945124657}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1$1$1.class", "size": 3363, "crc": 873960582}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1$1.class", "size": 3623, "crc": 1209390040}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1.class", "size": 4011, "crc": 1448424870}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$2.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$2.class", "size": 2712, "crc": -1111134998}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$3.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$3.class", "size": 2964, "crc": 1717282665}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$4.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$4.class", "size": 2272, "crc": 971347242}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "size": 1330, "crc": 1114579785}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$2.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$2.class", "size": 3048, "crc": -1743582053}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1.class", "size": 5720, "crc": 33875445}, {"key": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2$2.class", "name": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2$2.class", "size": 3282, "crc": -1731197959}, {"key": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2.class", "name": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2.class", "size": 4687, "crc": -1922672516}, {"key": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$3.class", "name": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$3.class", "size": 1874, "crc": 516964196}, {"key": "androidx/compose/material/SnackbarKt.class", "name": "androidx/compose/material/SnackbarKt.class", "size": 35592, "crc": -279147010}, {"key": "androidx/compose/material/SnackbarResult.class", "name": "androidx/compose/material/SnackbarResult.class", "size": 1844, "crc": 1693675227}, {"key": "androidx/compose/material/Strings$Companion.class", "name": "androidx/compose/material/Strings$Companion.class", "size": 2365, "crc": -1871266865}, {"key": "androidx/compose/material/Strings.class", "name": "androidx/compose/material/Strings.class", "size": 3415, "crc": 1247725478}, {"key": "androidx/compose/material/Strings_androidKt.class", "name": "androidx/compose/material/Strings_androidKt.class", "size": 4357, "crc": 1002412291}, {"key": "androidx/compose/material/SurfaceKt$Surface$1$1.class", "name": "androidx/compose/material/SurfaceKt$Surface$1$1.class", "size": 1620, "crc": 358541568}, {"key": "androidx/compose/material/SurfaceKt$Surface$1$2.class", "name": "androidx/compose/material/SurfaceKt$Surface$1$2.class", "size": 2941, "crc": -1117000884}, {"key": "androidx/compose/material/SurfaceKt$Surface$1.class", "name": "androidx/compose/material/SurfaceKt$Surface$1.class", "size": 11093, "crc": 1563033523}, {"key": "androidx/compose/material/SurfaceKt$Surface$2.class", "name": "androidx/compose/material/SurfaceKt$Surface$2.class", "size": 2627, "crc": -197606881}, {"key": "androidx/compose/material/SurfaceKt$Surface$3.class", "name": "androidx/compose/material/SurfaceKt$Surface$3.class", "size": 11780, "crc": 1030694612}, {"key": "androidx/compose/material/SurfaceKt$Surface$4.class", "name": "androidx/compose/material/SurfaceKt$Surface$4.class", "size": 3262, "crc": 27257776}, {"key": "androidx/compose/material/SurfaceKt$Surface$5.class", "name": "androidx/compose/material/SurfaceKt$Surface$5.class", "size": 11832, "crc": -1087038624}, {"key": "androidx/compose/material/SurfaceKt$Surface$6.class", "name": "androidx/compose/material/SurfaceKt$Surface$6.class", "size": 3372, "crc": -1129232452}, {"key": "androidx/compose/material/SurfaceKt$Surface$7.class", "name": "androidx/compose/material/SurfaceKt$Surface$7.class", "size": 11914, "crc": -1786194129}, {"key": "androidx/compose/material/SurfaceKt$Surface$8.class", "name": "androidx/compose/material/SurfaceKt$Surface$8.class", "size": 3418, "crc": -2099826879}, {"key": "androidx/compose/material/SurfaceKt.class", "name": "androidx/compose/material/SurfaceKt.class", "size": 23000, "crc": -100165047}, {"key": "androidx/compose/material/SwipeProgress.class", "name": "androidx/compose/material/SwipeProgress.class", "size": 2939, "crc": -1652888528}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$1.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$1.class", "size": 1720, "crc": 34059434}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$1$1$1.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$1$1$1.class", "size": 2052, "crc": -1045743176}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$thresholds$1$1.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2$thresholds$1$1.class", "size": 2503, "crc": 1743499451}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2.class", "size": 20015, "crc": 2060220319}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$3.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$3.class", "size": 3314, "crc": -1746763469}, {"key": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$1.class", "name": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$1.class", "size": 1510, "crc": -879988855}, {"key": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$2$1.class", "name": "androidx/compose/material/SwipeToDismissKt$rememberDismissState$2$1.class", "size": 1852, "crc": -543141864}, {"key": "androidx/compose/material/SwipeToDismissKt.class", "name": "androidx/compose/material/SwipeToDismissKt.class", "size": 11074, "crc": -515207392}, {"key": "androidx/compose/material/SwipeableDefaults.class", "name": "androidx/compose/material/SwipeableDefaults.class", "size": 4113, "crc": 1852058568}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPostFling$1.class", "size": 1840, "crc": -1848862899}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPreFling$1.class", "size": 1834, "crc": -102317473}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1.class", "size": 5741, "crc": 160673857}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableState$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableState$1.class", "size": 1409, "crc": 888970834}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableState$2$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableState$2$1.class", "size": 2149, "crc": 1094052477}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$1$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$1$1.class", "size": 3935, "crc": -1099625892}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1$invoke$$inlined$onDispose$1.class", "size": 1955, "crc": 852094622}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$2$1.class", "size": 4078, "crc": 1786811895}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$swipeableState$1$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$swipeableState$1$1.class", "size": 1578, "crc": 882860898}, {"key": "androidx/compose/material/SwipeableKt$swipeable$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$1.class", "size": 2726, "crc": 542890665}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$3$1$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$3$1$1.class", "size": 3060, "crc": 614020603}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$3$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$3$1.class", "size": 5682, "crc": 971493221}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$4$1$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$4$1$1.class", "size": 3431, "crc": -1866860844}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$4$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$4$1.class", "size": 3631, "crc": 252343830}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3.class", "size": 9846, "crc": 861459630}, {"key": "androidx/compose/material/SwipeableKt$swipeable-pPrIpRY$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable-pPrIpRY$$inlined$debugInspectorInfo$1.class", "size": 4406, "crc": 1926655070}, {"key": "androidx/compose/material/SwipeableKt.class", "name": "androidx/compose/material/SwipeableKt.class", "size": 21946, "crc": -1327317680}, {"key": "androidx/compose/material/SwipeableState$1.class", "name": "androidx/compose/material/SwipeableState$1.class", "size": 1427, "crc": -287440101}, {"key": "androidx/compose/material/SwipeableState$Companion$Saver$1.class", "name": "androidx/compose/material/SwipeableState$Companion$Saver$1.class", "size": 1967, "crc": 463003442}, {"key": "androidx/compose/material/SwipeableState$Companion$Saver$2.class", "name": "androidx/compose/material/SwipeableState$Companion$Saver$2.class", "size": 2191, "crc": 357382961}, {"key": "androidx/compose/material/SwipeableState$Companion.class", "name": "androidx/compose/material/SwipeableState$Companion.class", "size": 2395, "crc": 2120016392}, {"key": "androidx/compose/material/SwipeableState$animateInternalToOffset$2$1.class", "name": "androidx/compose/material/SwipeableState$animateInternalToOffset$2$1.class", "size": 2269, "crc": 2065538550}, {"key": "androidx/compose/material/SwipeableState$animateInternalToOffset$2.class", "name": "androidx/compose/material/SwipeableState$animateInternalToOffset$2.class", "size": 5493, "crc": -1729754647}, {"key": "androidx/compose/material/SwipeableState$animateTo$2$emit$1.class", "name": "androidx/compose/material/SwipeableState$animateTo$2$emit$1.class", "size": 1725, "crc": 1117773330}, {"key": "androidx/compose/material/SwipeableState$animateTo$2.class", "name": "androidx/compose/material/SwipeableState$animateTo$2.class", "size": 7055, "crc": 589360624}, {"key": "androidx/compose/material/SwipeableState$draggableState$1.class", "name": "androidx/compose/material/SwipeableState$draggableState$1.class", "size": 2462, "crc": -1775606298}, {"key": "androidx/compose/material/SwipeableState$latestNonEmptyAnchorsFlow$1.class", "name": "androidx/compose/material/SwipeableState$latestNonEmptyAnchorsFlow$1.class", "size": 1522, "crc": -1511243905}, {"key": "androidx/compose/material/SwipeableState$performFling$2.class", "name": "androidx/compose/material/SwipeableState$performFling$2.class", "size": 3869, "crc": -983482930}, {"key": "androidx/compose/material/SwipeableState$processNewAnchors$1.class", "name": "androidx/compose/material/SwipeableState$processNewAnchors$1.class", "size": 1957, "crc": -668715195}, {"key": "androidx/compose/material/SwipeableState$snapInternalToOffset$2.class", "name": "androidx/compose/material/SwipeableState$snapInternalToOffset$2.class", "size": 3647, "crc": 527040843}, {"key": "androidx/compose/material/SwipeableState$snapTo$2$emit$1.class", "name": "androidx/compose/material/SwipeableState$snapTo$2$emit$1.class", "size": 1665, "crc": -178464848}, {"key": "androidx/compose/material/SwipeableState$snapTo$2.class", "name": "androidx/compose/material/SwipeableState$snapTo$2.class", "size": 3960, "crc": 431498250}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2$1.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2$1.class", "size": 1719, "crc": 1333315665}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2.class", "size": 3774, "crc": 1690645798}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1.class", "size": 3203, "crc": 1394323594}, {"key": "androidx/compose/material/SwipeableState$thresholds$2.class", "name": "androidx/compose/material/SwipeableState$thresholds$2.class", "size": 1519, "crc": -2016388164}, {"key": "androidx/compose/material/SwipeableState.class", "name": "androidx/compose/material/SwipeableState.class", "size": 28008, "crc": -1434406890}, {"key": "androidx/compose/material/SwitchColors.class", "name": "androidx/compose/material/SwitchColors.class", "size": 1163, "crc": 1632367191}, {"key": "androidx/compose/material/SwitchDefaults.class", "name": "androidx/compose/material/SwitchDefaults.class", "size": 4085, "crc": -1673889185}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1$1.class", "size": 1488, "crc": 3403100}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1$2.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1$2.class", "size": 4369, "crc": 729451444}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1.class", "size": 4983, "crc": 832508561}, {"key": "androidx/compose/material/SwitchKt$Switch$2$1.class", "name": "androidx/compose/material/SwitchKt$Switch$2$1.class", "size": 3943, "crc": -1295345677}, {"key": "androidx/compose/material/SwitchKt$Switch$3$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$3$1$1.class", "size": 1675, "crc": 1860136609}, {"key": "androidx/compose/material/SwitchKt$Switch$4.class", "name": "androidx/compose/material/SwitchKt$Switch$4.class", "size": 2612, "crc": 418850365}, {"key": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$1.class", "size": 1925, "crc": -767074496}, {"key": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$2.class", "name": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$2.class", "size": 1534, "crc": -421623598}, {"key": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$3.class", "name": "androidx/compose/material/SwitchKt$Switch$anchoredDraggableState$1$3.class", "size": 1366, "crc": 404134082}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$1$1$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$1$1$1.class", "size": 3173, "crc": -221110365}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$1$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$1$1.class", "size": 4215, "crc": 362073149}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$2$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$2$1.class", "size": 2124, "crc": -1329739691}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$3$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$3$1.class", "size": 2109, "crc": 1847864340}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$4.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$4.class", "size": 2611, "crc": 878266335}, {"key": "androidx/compose/material/SwitchKt.class", "name": "androidx/compose/material/SwitchKt.class", "size": 37019, "crc": 1898824081}, {"key": "androidx/compose/material/SystemBarsDefaultInsets_androidKt.class", "name": "androidx/compose/material/SystemBarsDefaultInsets_androidKt.class", "size": 2380, "crc": 104119505}, {"key": "androidx/compose/material/TabKt$LeadingIconTab$1.class", "name": "androidx/compose/material/TabKt$LeadingIconTab$1.class", "size": 13728, "crc": 705293324}, {"key": "androidx/compose/material/TabKt$LeadingIconTab$2.class", "name": "androidx/compose/material/TabKt$LeadingIconTab$2.class", "size": 3092, "crc": 13068029}, {"key": "androidx/compose/material/TabKt$Tab$1.class", "name": "androidx/compose/material/TabKt$Tab$1.class", "size": 3255, "crc": 704165854}, {"key": "androidx/compose/material/TabKt$Tab$2.class", "name": "androidx/compose/material/TabKt$Tab$2.class", "size": 3059, "crc": 1065313483}, {"key": "androidx/compose/material/TabKt$Tab$3.class", "name": "androidx/compose/material/TabKt$Tab$3.class", "size": 10821, "crc": 2090680795}, {"key": "androidx/compose/material/TabKt$Tab$4.class", "name": "androidx/compose/material/TabKt$Tab$4.class", "size": 2938, "crc": -69180089}, {"key": "androidx/compose/material/TabKt$Tab$styledText$1$1.class", "name": "androidx/compose/material/TabKt$Tab$styledText$1$1.class", "size": 4335, "crc": -1325877163}, {"key": "androidx/compose/material/TabKt$TabBaselineLayout$2$1$1.class", "name": "androidx/compose/material/TabKt$TabBaselineLayout$2$1$1.class", "size": 2889, "crc": 2146557804}, {"key": "androidx/compose/material/TabKt$TabBaselineLayout$2$1.class", "name": "androidx/compose/material/TabKt$TabBaselineLayout$2$1.class", "size": 6885, "crc": 1259687112}, {"key": "androidx/compose/material/TabKt$TabBaselineLayout$3.class", "name": "androidx/compose/material/TabKt$TabBaselineLayout$3.class", "size": 2075, "crc": -1655532622}, {"key": "androidx/compose/material/TabKt$TabTransition$1.class", "name": "androidx/compose/material/TabKt$TabTransition$1.class", "size": 2033, "crc": -1396097274}, {"key": "androidx/compose/material/TabKt$TabTransition$color$2.class", "name": "androidx/compose/material/TabKt$TabTransition$color$2.class", "size": 3479, "crc": 1222025830}, {"key": "androidx/compose/material/TabKt.class", "name": "androidx/compose/material/TabKt.class", "size": 37680, "crc": -626042822}, {"key": "androidx/compose/material/TabPosition.class", "name": "androidx/compose/material/TabPosition.class", "size": 3257, "crc": -1088485831}, {"key": "androidx/compose/material/TabRowDefaults$Divider$1.class", "name": "androidx/compose/material/TabRowDefaults$Divider$1.class", "size": 1898, "crc": 15671488}, {"key": "androidx/compose/material/TabRowDefaults$Indicator$1.class", "name": "androidx/compose/material/TabRowDefaults$Indicator$1.class", "size": 1901, "crc": -2142896636}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "size": 2779, "crc": -1248217390}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2$1$1.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2$1$1.class", "size": 2013, "crc": -1152538416}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2.class", "size": 7148, "crc": 1079902096}, {"key": "androidx/compose/material/TabRowDefaults.class", "name": "androidx/compose/material/TabRowDefaults.class", "size": 9574, "crc": 987567080}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$1.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$1.class", "size": 3043, "crc": -1033278417}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2$3.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2$3.class", "size": 3293, "crc": -1447841854}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2.class", "size": 7347, "crc": -240783472}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1.class", "size": 6856, "crc": 1349571818}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2.class", "size": 9487, "crc": 1269788179}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$3.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$3.class", "size": 3011, "crc": 917792080}, {"key": "androidx/compose/material/TabRowKt$TabRow$1.class", "name": "androidx/compose/material/TabRowKt$TabRow$1.class", "size": 3001, "crc": -1837966001}, {"key": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1$3.class", "name": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1$3.class", "size": 3232, "crc": -567752309}, {"key": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1.class", "name": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1.class", "size": 6330, "crc": 1978403247}, {"key": "androidx/compose/material/TabRowKt$TabRow$2$1$1.class", "name": "androidx/compose/material/TabRowKt$TabRow$2$1$1.class", "size": 7143, "crc": -1484692491}, {"key": "androidx/compose/material/TabRowKt$TabRow$2.class", "name": "androidx/compose/material/TabRowKt$TabRow$2.class", "size": 5521, "crc": -887808929}, {"key": "androidx/compose/material/TabRowKt$TabRow$3.class", "name": "androidx/compose/material/TabRowKt$TabRow$3.class", "size": 2921, "crc": 1857674144}, {"key": "androidx/compose/material/TabRowKt.class", "name": "androidx/compose/material/TabRowKt.class", "size": 11245, "crc": 1135923027}, {"key": "androidx/compose/material/TabSlots.class", "name": "androidx/compose/material/TabSlots.class", "size": 1846, "crc": 1823572953}, {"key": "androidx/compose/material/TextFieldColors.class", "name": "androidx/compose/material/TextFieldColors.class", "size": 4340, "crc": -1957829693}, {"key": "androidx/compose/material/TextFieldColorsWithIcons.class", "name": "androidx/compose/material/TextFieldColorsWithIcons.class", "size": 866, "crc": 531427407}, {"key": "androidx/compose/material/TextFieldDefaults$BorderBox$1.class", "name": "androidx/compose/material/TextFieldDefaults$BorderBox$1.class", "size": 2498, "crc": -199935079}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "size": 3499, "crc": 624738139}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$2.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$2.class", "size": 4886, "crc": -662872113}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "size": 3338, "crc": 207056450}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$4.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$4.class", "size": 4692, "crc": 394038860}, {"key": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$1.class", "name": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$1.class", "size": 4639, "crc": -2120540379}, {"key": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$2.class", "name": "androidx/compose/material/TextFieldDefaults$TextFieldDecorationBox$2.class", "size": 4445, "crc": 1388757509}, {"key": "androidx/compose/material/TextFieldDefaults$indicatorLine$2.class", "name": "androidx/compose/material/TextFieldDefaults$indicatorLine$2.class", "size": 3803, "crc": -1829666961}, {"key": "androidx/compose/material/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "size": 3849, "crc": -2052853985}, {"key": "androidx/compose/material/TextFieldDefaults.class", "name": "androidx/compose/material/TextFieldDefaults.class", "size": 43460, "crc": 680872675}, {"key": "androidx/compose/material/TextFieldDefaultsKt.class", "name": "androidx/compose/material/TextFieldDefaultsKt.class", "size": 6168, "crc": -1740793055}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "size": 2297, "crc": 1633002222}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "size": 865, "crc": 1319510671}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "size": 4847, "crc": 1021808818}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "size": 2926, "crc": -458642764}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "size": 10593, "crc": 56658647}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "size": 2929, "crc": 1755122502}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$drawBorder$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$drawBorder$1.class", "size": 10213, "crc": 1771251722}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3.class", "size": 14323, "crc": 683209030}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$4.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$4.class", "size": 4814, "crc": -758771824}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "size": 3799, "crc": -661399560}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$1.class", "size": 2291, "crc": -2116105130}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1$1.class", "size": 3520, "crc": 2020855175}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1.class", "size": 3794, "crc": 1608648692}, {"key": "androidx/compose/material/TextFieldImplKt$defaultErrorSemantics$1.class", "name": "androidx/compose/material/TextFieldImplKt$defaultErrorSemantics$1.class", "size": 1744, "crc": 1652956746}, {"key": "androidx/compose/material/TextFieldImplKt.class", "name": "androidx/compose/material/TextFieldImplKt.class", "size": 21006, "crc": -1630244516}, {"key": "androidx/compose/material/TextFieldKt$TextField$1.class", "name": "androidx/compose/material/TextFieldKt$TextField$1.class", "size": 8949, "crc": -745219894}, {"key": "androidx/compose/material/TextFieldKt$TextField$10.class", "name": "androidx/compose/material/TextFieldKt$TextField$10.class", "size": 5392, "crc": -1451277545}, {"key": "androidx/compose/material/TextFieldKt$TextField$2.class", "name": "androidx/compose/material/TextFieldKt$TextField$2.class", "size": 5860, "crc": -422415604}, {"key": "androidx/compose/material/TextFieldKt$TextField$3.class", "name": "androidx/compose/material/TextFieldKt$TextField$3.class", "size": 6121, "crc": 1135732827}, {"key": "androidx/compose/material/TextFieldKt$TextField$4.class", "name": "androidx/compose/material/TextFieldKt$TextField$4.class", "size": 5269, "crc": 24005598}, {"key": "androidx/compose/material/TextFieldKt$TextField$6.class", "name": "androidx/compose/material/TextFieldKt$TextField$6.class", "size": 5216, "crc": -454443072}, {"key": "androidx/compose/material/TextFieldKt$TextField$7.class", "name": "androidx/compose/material/TextFieldKt$TextField$7.class", "size": 6350, "crc": -2120209928}, {"key": "androidx/compose/material/TextFieldKt$TextField$8.class", "name": "androidx/compose/material/TextFieldKt$TextField$8.class", "size": 5443, "crc": 429181272}, {"key": "androidx/compose/material/TextFieldKt$TextFieldLayout$2.class", "name": "androidx/compose/material/TextFieldKt$TextFieldLayout$2.class", "size": 3500, "crc": -92063433}, {"key": "androidx/compose/material/TextFieldKt$drawIndicatorLine$1.class", "name": "androidx/compose/material/TextFieldKt$drawIndicatorLine$1.class", "size": 2729, "crc": 1819829211}, {"key": "androidx/compose/material/TextFieldKt.class", "name": "androidx/compose/material/TextFieldKt.class", "size": 78228, "crc": 975663944}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1753, "crc": 1329778761}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1750, "crc": -1739237073}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$measure$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$measure$1.class", "size": 4049, "crc": 1983436026}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1753, "crc": -723966275}, {"key": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1750, "crc": 1607806925}, {"key": "androidx/compose/material/TextFieldMeasurePolicy.class", "name": "androidx/compose/material/TextFieldMeasurePolicy.class", "size": 23470, "crc": 473345557}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$1.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$1.class", "size": 3199, "crc": 1676649}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelContentColor$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelContentColor$2.class", "size": 3356, "crc": -1270581727}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelProgress$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelProgress$2.class", "size": 3274, "crc": -352068628}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "size": 3360, "crc": 457374485}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "size": 3901, "crc": 381728669}, {"key": "androidx/compose/material/TextFieldTransitionScope$WhenMappings.class", "name": "androidx/compose/material/TextFieldTransitionScope$WhenMappings.class", "size": 886, "crc": 563916611}, {"key": "androidx/compose/material/TextFieldTransitionScope.class", "name": "androidx/compose/material/TextFieldTransitionScope.class", "size": 17659, "crc": 1220718896}, {"key": "androidx/compose/material/TextFieldType.class", "name": "androidx/compose/material/TextFieldType.class", "size": 1828, "crc": 1436750124}, {"key": "androidx/compose/material/TextKt$LocalTextStyle$1.class", "name": "androidx/compose/material/TextKt$LocalTextStyle$1.class", "size": 1193, "crc": -2080161258}, {"key": "androidx/compose/material/TextKt$ProvideTextStyle$1.class", "name": "androidx/compose/material/TextKt$ProvideTextStyle$1.class", "size": 2020, "crc": 1220060998}, {"key": "androidx/compose/material/TextKt$Text$1$1.class", "name": "androidx/compose/material/TextKt$Text$1$1.class", "size": 1249, "crc": -625560401}, {"key": "androidx/compose/material/TextKt$Text$2.class", "name": "androidx/compose/material/TextKt$Text$2.class", "size": 3947, "crc": -918588582}, {"key": "androidx/compose/material/TextKt$Text$3.class", "name": "androidx/compose/material/TextKt$Text$3.class", "size": 1619, "crc": -1026974569}, {"key": "androidx/compose/material/TextKt$Text$4.class", "name": "androidx/compose/material/TextKt$Text$4.class", "size": 3894, "crc": 1280657718}, {"key": "androidx/compose/material/TextKt$Text$5.class", "name": "androidx/compose/material/TextKt$Text$5.class", "size": 1659, "crc": -1466454898}, {"key": "androidx/compose/material/TextKt$Text$6$1.class", "name": "androidx/compose/material/TextKt$Text$6$1.class", "size": 1288, "crc": -2131416910}, {"key": "androidx/compose/material/TextKt$Text$7.class", "name": "androidx/compose/material/TextKt$Text$7.class", "size": 4331, "crc": 1410404707}, {"key": "androidx/compose/material/TextKt$Text$8.class", "name": "androidx/compose/material/TextKt$Text$8.class", "size": 1658, "crc": 1253955389}, {"key": "androidx/compose/material/TextKt$Text$9.class", "name": "androidx/compose/material/TextKt$Text$9.class", "size": 4278, "crc": -513108099}, {"key": "androidx/compose/material/TextKt.class", "name": "androidx/compose/material/TextKt.class", "size": 33366, "crc": 101952093}, {"key": "androidx/compose/material/ThreeLine$ListItem$1$2.class", "name": "androidx/compose/material/ThreeLine$ListItem$1$2.class", "size": 3355, "crc": -217995894}, {"key": "androidx/compose/material/ThreeLine$ListItem$2.class", "name": "androidx/compose/material/ThreeLine$ListItem$2.class", "size": 3108, "crc": -801203927}, {"key": "androidx/compose/material/ThreeLine.class", "name": "androidx/compose/material/ThreeLine.class", "size": 18582, "crc": -58179410}, {"key": "androidx/compose/material/ThresholdConfig.class", "name": "androidx/compose/material/ThresholdConfig.class", "size": 1040, "crc": 1277605746}, {"key": "androidx/compose/material/TwoLine$ListItem$1$2.class", "name": "androidx/compose/material/TwoLine$ListItem$1$2.class", "size": 3001, "crc": -1824795129}, {"key": "androidx/compose/material/TwoLine$ListItem$1$3.class", "name": "androidx/compose/material/TwoLine$ListItem$1$3.class", "size": 3091, "crc": 1261146221}, {"key": "androidx/compose/material/TwoLine$ListItem$1$4.class", "name": "androidx/compose/material/TwoLine$ListItem$1$4.class", "size": 9369, "crc": 297421526}, {"key": "androidx/compose/material/TwoLine$ListItem$2.class", "name": "androidx/compose/material/TwoLine$ListItem$2.class", "size": 3096, "crc": -577670224}, {"key": "androidx/compose/material/TwoLine.class", "name": "androidx/compose/material/TwoLine.class", "size": 19717, "crc": 1482314040}, {"key": "androidx/compose/material/Typography.class", "name": "androidx/compose/material/Typography.class", "size": 13002, "crc": 1518096410}, {"key": "androidx/compose/material/TypographyKt$LocalTypography$1.class", "name": "androidx/compose/material/TypographyKt$LocalTypography$1.class", "size": 1786, "crc": -395584435}, {"key": "androidx/compose/material/TypographyKt.class", "name": "androidx/compose/material/TypographyKt.class", "size": 4468, "crc": -1318205634}, {"key": "androidx/compose/material/WindowBoundsCalculator.class", "name": "androidx/compose/material/WindowBoundsCalculator.class", "size": 1332, "crc": -413896329}, {"key": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda-1$1.class", "name": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda-1$1.class", "size": 2339, "crc": -1703333322}, {"key": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "size": 1644, "crc": 1071412920}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1$invoke$$inlined$onDispose$1.class", "size": 2458, "crc": 1299720031}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1.class", "size": 4082, "crc": 71168992}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$2$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$2$1.class", "size": 2277, "crc": 954116563}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1$invoke$$inlined$onDispose$1.class", "size": 2178, "crc": 1651773149}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$3$1.class", "size": 3559, "crc": -1510219221}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1.class", "size": 2798, "crc": -2070767306}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1$1.class", "size": 1778, "crc": -1088608556}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1.class", "size": 2644, "crc": 2029872127}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$7.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$7.class", "size": 2570, "crc": 1518588305}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupId$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupId$1.class", "size": 1502, "crc": -80577311}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$1.class", "size": 1770, "crc": -1676704274}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$2$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$2$1.class", "size": 1960, "crc": 1906504063}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "size": 11269, "crc": -945534536}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$LocalPopupTestTag$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$LocalPopupTestTag$1.class", "size": 1211, "crc": -429153350}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "size": 1733, "crc": -311615049}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$2.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$2.class", "size": 1940, "crc": 160103602}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$3.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$3.class", "size": 2346, "crc": -193695875}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1.class", "size": 5250, "crc": 1896968964}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt.class", "size": 21116, "crc": 1653640047}, {"key": "androidx/compose/material/internal/Icons$Filled.class", "name": "androidx/compose/material/internal/Icons$Filled.class", "size": 6213, "crc": -127061710}, {"key": "androidx/compose/material/internal/Icons.class", "name": "androidx/compose/material/internal/Icons.class", "size": 896, "crc": -1233056393}, {"key": "androidx/compose/material/internal/IconsKt.class", "name": "androidx/compose/material/internal/IconsKt.class", "size": 6690, "crc": -1529607191}, {"key": "androidx/compose/material/internal/PopupLayout$2.class", "name": "androidx/compose/material/internal/PopupLayout$2.class", "size": 1360, "crc": 1996845210}, {"key": "androidx/compose/material/internal/PopupLayout$WhenMappings.class", "name": "androidx/compose/material/internal/PopupLayout$WhenMappings.class", "size": 837, "crc": 795505153}, {"key": "androidx/compose/material/internal/PopupLayout$canCalculatePosition$2.class", "name": "androidx/compose/material/internal/PopupLayout$canCalculatePosition$2.class", "size": 1647, "crc": -950148785}, {"key": "androidx/compose/material/internal/PopupLayout$dismissOnOutsideClick$1.class", "name": "androidx/compose/material/internal/PopupLayout$dismissOnOutsideClick$1.class", "size": 2171, "crc": -116002295}, {"key": "androidx/compose/material/internal/PopupLayout.class", "name": "androidx/compose/material/internal/PopupLayout.class", "size": 19903, "crc": -840168426}, {"key": "androidx/compose/material/pullrefresh/ArrowValues.class", "name": "androidx/compose/material/pullrefresh/ArrowValues.class", "size": 1305, "crc": -1424026860}, {"key": "androidx/compose/material/pullrefresh/PullRefreshDefaults.class", "name": "androidx/compose/material/pullrefresh/PullRefreshDefaults.class", "size": 2254, "crc": 1088739824}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$1.class", "size": 1681, "crc": -1222461952}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$2$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$2$1.class", "size": 7118, "crc": -2133714944}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$3.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$3.class", "size": 2034, "crc": 759431302}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$targetAlpha$2$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$CircularArrowIndicator$targetAlpha$2$1.class", "size": 1634, "crc": 313464315}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$1$1.class", "size": 10912, "crc": 1923120095}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$2.class", "size": 2207, "crc": -1221742515}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$showElevation$2$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$showElevation$2$1.class", "size": 1721, "crc": -993795815}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt.class", "size": 28456, "crc": -826388290}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$1.class", "size": 4936, "crc": 1082659712}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt$pullRefreshIndicatorTransform$2.class", "size": 3998, "crc": 1069354465}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt.class", "size": 2120, "crc": -66260287}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$1.class", "size": 1583, "crc": 1206381695}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$2.class", "size": 2061, "crc": 610608445}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt.class", "size": 3917, "crc": -1777934884}, {"key": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection$onPreFling$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection$onPreFling$1.class", "size": 1946, "crc": -1537796792}, {"key": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection.class", "name": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection.class", "size": 5420, "crc": -694679605}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$adjustedDistancePulled$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$adjustedDistancePulled$2.class", "size": 1483, "crc": 1398177654}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1$1.class", "size": 1871, "crc": -366710147}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1.class", "size": 3611, "crc": -308951617}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1.class", "size": 3907, "crc": -767045398}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState.class", "size": 11288, "crc": -1523920582}, {"key": "androidx/compose/material/pullrefresh/PullRefreshStateKt$rememberPullRefreshState$3$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshStateKt$rememberPullRefreshState$3$1.class", "size": 2058, "crc": 1563618555}, {"key": "androidx/compose/material/pullrefresh/PullRefreshStateKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshStateKt.class", "size": 9254, "crc": -1474426233}, {"key": "META-INF/androidx.compose.material_material.version", "name": "META-INF/androidx.compose.material_material.version", "size": 6, "crc": 333960004}, {"key": "META-INF/material_release.kotlin_module", "name": "META-INF/material_release.kotlin_module", "size": 1295, "crc": -129467956}]