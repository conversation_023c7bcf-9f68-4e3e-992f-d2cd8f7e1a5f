{"logs": [{"outputFile": "io.github.simplenote.app-mergeDebugResources-66:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8734d346b2e96ba5091fe03752072eab\\transformed\\core-1.16.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "30,31,32,33,34,35,36,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2733,2825,2926,3020,3114,3207,3301,10322", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "2820,2921,3015,3109,3202,3296,3392,10418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d61245d3231dfae84075323e154f2e0\\transformed\\material3-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,860,963,1078,1160,1256,1340,1429,1535,1649,1750,1860,1968,2076,2192,2299,2400,2504,2610,2695,2790,2895,3004,3094,3192,3290,3400,3515,3615,3706,3779,3869,3958,4051,4134,4216,4308,4388,4470,4568,4662,4755,4850,4934,5030,5126,5223,5331,5411,5503", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "154,257,361,463,555,643,747,855,958,1073,1155,1251,1335,1424,1530,1644,1745,1855,1963,2071,2187,2294,2395,2499,2605,2690,2785,2890,2999,3089,3187,3285,3395,3510,3610,3701,3774,3864,3953,4046,4129,4211,4303,4383,4465,4563,4657,4750,4845,4929,5025,5121,5218,5326,5406,5498,5588"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3954,4058,4161,4265,4367,4459,4547,4651,4759,4862,4977,5059,5155,5239,5328,5434,5548,5649,5759,5867,5975,6091,6198,6299,6403,6509,6594,6689,6794,6903,6993,7091,7189,7299,7414,7514,7605,7678,7768,7857,7950,8033,8115,8207,8287,8369,8467,8561,8654,8749,8833,8929,9025,9122,9230,9310,9402", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "4053,4156,4260,4362,4454,4542,4646,4754,4857,4972,5054,5150,5234,5323,5429,5543,5644,5754,5862,5970,6086,6193,6294,6398,6504,6589,6684,6789,6898,6988,7086,7184,7294,7409,7509,7600,7673,7763,7852,7945,8028,8110,8202,8282,8364,8462,8556,8649,8744,8828,8924,9020,9117,9225,9305,9397,9487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96ccc887d394ddaa1dd6d0e3b9de1bba\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "29,116,117", "startColumns": "4,4,4", "startOffsets": "2662,10678,10759", "endColumns": "70,80,76", "endOffsets": "2728,10754,10831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250d8cc481173cc0c0524c32c74c00eb\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,415,506,583,657,734,812,887,960,1035,1103,1184,1257,1329,1400,1473,1539", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "249,325,410,501,578,652,729,807,882,955,1030,1098,1179,1252,1324,1395,1468,1534,1650"}, "to": {"startLines": "37,38,39,40,41,42,43,102,103,104,105,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3397,3474,3550,3635,3726,3803,3877,9577,9655,9730,9803,9957,10025,10106,10179,10251,10423,10496,10562", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "3469,3545,3630,3721,3798,3872,3949,9650,9725,9798,9873,10020,10101,10174,10246,10317,10491,10557,10673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a52cd15d91b3b91eba876f6457455010\\transformed\\appcompat-1.7.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,9878", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,9952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8f6b7cfc7dddc58c7ce7adfd537c1781\\transformed\\material-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "9492", "endColumns": "84", "endOffsets": "9572"}}]}]}