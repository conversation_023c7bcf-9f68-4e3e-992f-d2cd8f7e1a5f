[{"key": "arrow/AutoCloseScope$DefaultImpls.class", "name": "arrow/AutoCloseScope$DefaultImpls.class", "size": 3484, "crc": -140363822}, {"key": "arrow/AutoCloseScope.class", "name": "arrow/AutoCloseScope.class", "size": 1473, "crc": -822695810}, {"key": "arrow/AutoCloseScopeKt.class", "name": "arrow/AutoCloseScopeKt.class", "size": 1935, "crc": -1480540266}, {"key": "arrow/DefaultAutoCloseScope.class", "name": "arrow/DefaultAutoCloseScope.class", "size": 7346, "crc": 377750655}, {"key": "arrow/ThrowIfFatalKt.class", "name": "arrow/ThrowIfFatalKt.class", "size": 1102, "crc": -1118643345}, {"key": "META-INF/arrow-autoclose_release.kotlin_module", "name": "META-INF/arrow-autoclose_release.kotlin_module", "size": 67, "crc": -1824771866}]