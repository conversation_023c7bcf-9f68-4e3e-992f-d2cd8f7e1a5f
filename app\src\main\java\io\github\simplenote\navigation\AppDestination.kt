package io.github.simplenote.navigation

import kotlinx.serialization.Serializable

/**
 * Type-safe navigation destinations for the app.
 */
@Serializable
sealed class AppDestination {
    
    /**
     * Notes list screen - the main screen of the app
     */
    @Serializable
    data object Notes : AppDestination()
    
    /**
     * Note editor screen for creating or editing a note
     * @param noteId The ID of the note to edit, null for creating a new note
     */
    @Serializable
    data class NoteEditor(val noteId: Long? = null) : AppDestination()

    /**
     * Settings screen for app preferences and theme settings
     */
    @Serializable
    data object Settings : AppDestination()
}
