{"logs": [{"outputFile": "io.github.simplenote.app-mergeDebugResources-66:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8734d346b2e96ba5091fe03752072eab\\transformed\\core-1.16.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "30,31,32,33,34,35,36,112", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2948,3044,3147,3246,3344,3451,3566,11875", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3039,3142,3241,3339,3446,3561,3689,11971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\96ccc887d394ddaa1dd6d0e3b9de1bba\\transformed\\foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,225", "endColumns": "76,92,97", "endOffsets": "127,220,318"}, "to": {"startLines": "29,116,117", "startColumns": "4,4,4", "startOffsets": "2871,12245,12338", "endColumns": "76,92,97", "endOffsets": "2943,12333,12431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250d8cc481173cc0c0524c32c74c00eb\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "183,280,364,458,559,650,733,842,933,1028,1110,1196,1286,1378,1463,1536,1607,1687,1756", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,91,84,72,70,79,68,119", "endOffsets": "275,359,453,554,645,728,837,928,1023,1105,1191,1281,1373,1458,1531,1602,1682,1751,1871"}, "to": {"startLines": "37,38,39,40,41,42,43,102,103,104,105,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3694,3791,3875,3969,4070,4161,4244,11028,11119,11214,11296,11464,11554,11646,11731,11804,11976,12056,12125", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,91,84,72,70,79,68,119", "endOffsets": "3786,3870,3964,4065,4156,4239,4348,11114,11209,11291,11377,11549,11641,11726,11799,11870,12051,12120,12240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d61245d3231dfae84075323e154f2e0\\transformed\\material3-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,307,428,552,653,749,862,1013,1144,1285,1369,1473,1573,1681,1798,1921,2030,2176,2320,2454,2660,2789,2910,3035,3181,3282,3380,3526,3662,3768,3881,3988,4134,4286,4395,4507,4585,4687,4790,4907,4993,5086,5199,5279,5367,5466,5586,5681,5786,5875,5997,6101,6208,6341,6421,6532", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "176,302,423,547,648,744,857,1008,1139,1280,1364,1468,1568,1676,1793,1916,2025,2171,2315,2449,2655,2784,2905,3030,3176,3277,3375,3521,3657,3763,3876,3983,4129,4281,4390,4502,4580,4682,4785,4902,4988,5081,5194,5274,5362,5461,5581,5676,5781,5870,5992,6096,6203,6336,6416,6527,6629"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4353,4479,4605,4726,4850,4951,5047,5160,5311,5442,5583,5667,5771,5871,5979,6096,6219,6328,6474,6618,6752,6958,7087,7208,7333,7479,7580,7678,7824,7960,8066,8179,8286,8432,8584,8693,8805,8883,8985,9088,9205,9291,9384,9497,9577,9665,9764,9884,9979,10084,10173,10295,10399,10506,10639,10719,10830", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "4474,4600,4721,4845,4946,5042,5155,5306,5437,5578,5662,5766,5866,5974,6091,6214,6323,6469,6613,6747,6953,7082,7203,7328,7474,7575,7673,7819,7955,8061,8174,8281,8427,8579,8688,8800,8878,8980,9083,9200,9286,9379,9492,9572,9660,9759,9879,9974,10079,10168,10290,10394,10501,10634,10714,10825,10927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a52cd15d91b3b91eba876f6457455010\\transformed\\appcompat-1.7.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,11382", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,11459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8f6b7cfc7dddc58c7ce7adfd537c1781\\transformed\\material-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "10932", "endColumns": "95", "endOffsets": "11023"}}]}]}