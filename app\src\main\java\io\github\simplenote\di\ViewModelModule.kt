package io.github.simplenote.di

import io.github.simplenote.presentation.viewmodel.NoteEditorViewModel
import io.github.simplenote.presentation.viewmodel.NotesViewModel
import io.github.simplenote.presentation.viewmodel.SettingsViewModel
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module

/**
 * Koin module for ViewModel dependencies.
 * Provides ViewModel instances with use case dependencies.
 */
val viewModelModule = module {
    
    // Notes ViewModel
    viewModel {
        NotesViewModel(
            getNotesUseCase = get(),
            updateNoteUseCase = get(),
            deleteNoteUseCase = get()
        )
    }
    
    // Note Editor ViewModel
    viewModel {
        NoteEditorViewModel(
            getNotesUseCase = get(),
            createNoteUseCase = get(),
            updateNoteUseCase = get(),
            deleteNoteUseCase = get()
        )
    }

    // Settings ViewModel
    viewModel {
        SettingsViewModel(
            getThemeSettingsUseCase = get(),
            updateThemeSettingsUseCase = get()
        )
    }
}
