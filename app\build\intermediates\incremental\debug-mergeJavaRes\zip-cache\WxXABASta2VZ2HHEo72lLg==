[{"key": "androidx/room/BaseRoomConnectionManager$Companion.class", "name": "androidx/room/BaseRoomConnectionManager$Companion.class", "size": 895, "crc": -1729251576}, {"key": "androidx/room/BaseRoomConnectionManager$DriverWrapper$openLocked$2.class", "name": "androidx/room/BaseRoomConnectionManager$DriverWrapper$openLocked$2.class", "size": 1701, "crc": 1768716501}, {"key": "androidx/room/BaseRoomConnectionManager$DriverWrapper.class", "name": "androidx/room/BaseRoomConnectionManager$DriverWrapper.class", "size": 4183, "crc": -2036835161}, {"key": "androidx/room/BaseRoomConnectionManager$WhenMappings.class", "name": "androidx/room/BaseRoomConnectionManager$WhenMappings.class", "size": 876, "crc": -542941555}, {"key": "androidx/room/BaseRoomConnectionManager.class", "name": "androidx/room/BaseRoomConnectionManager.class", "size": 18893, "crc": 262732412}, {"key": "androidx/room/BindOnlySQLiteStatement$Companion.class", "name": "androidx/room/BindOnlySQLiteStatement$Companion.class", "size": 894, "crc": 403771146}, {"key": "androidx/room/BindOnlySQLiteStatement.class", "name": "androidx/room/BindOnlySQLiteStatement.class", "size": 4973, "crc": -2058976427}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$1.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$1.class", "size": 1884, "crc": 1543783118}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$2.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$2.class", "size": 3262, "crc": -969203484}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$3.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$3.class", "size": 2058, "crc": 1319319413}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$4$1.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$4$1.class", "size": 1827, "crc": 1952712323}, {"key": "androidx/room/CoroutinesRoom$Companion$execute$4$job$1.class", "name": "androidx/room/CoroutinesRoom$Companion$execute$4$job$1.class", "size": 3932, "crc": 2005721838}, {"key": "androidx/room/CoroutinesRoom$Companion.class", "name": "androidx/room/CoroutinesRoom$Companion.class", "size": 9403, "crc": -2032589226}, {"key": "androidx/room/CoroutinesRoom.class", "name": "androidx/room/CoroutinesRoom.class", "size": 2976, "crc": -2065756869}, {"key": "androidx/room/DatabaseConfiguration.class", "name": "androidx/room/DatabaseConfiguration.class", "size": 26934, "crc": -1570898087}, {"key": "androidx/room/DelegatingOpenHelper.class", "name": "androidx/room/DelegatingOpenHelper.class", "size": 657, "crc": 491206780}, {"key": "androidx/room/EntityDeleteOrUpdateAdapter.class", "name": "androidx/room/EntityDeleteOrUpdateAdapter.class", "size": 4518, "crc": -301705656}, {"key": "androidx/room/EntityDeletionOrUpdateAdapter.class", "name": "androidx/room/EntityDeletionOrUpdateAdapter.class", "size": 4974, "crc": -1617060698}, {"key": "androidx/room/EntityInsertAdapter.class", "name": "androidx/room/EntityInsertAdapter.class", "size": 11886, "crc": 796404360}, {"key": "androidx/room/EntityInsertionAdapter.class", "name": "androidx/room/EntityInsertionAdapter.class", "size": 9633, "crc": -64377685}, {"key": "androidx/room/EntityUpsertAdapter$Companion.class", "name": "androidx/room/EntityUpsertAdapter$Companion.class", "size": 971, "crc": 870161391}, {"key": "androidx/room/EntityUpsertAdapter.class", "name": "androidx/room/EntityUpsertAdapter.class", "size": 11105, "crc": 1588068384}, {"key": "androidx/room/EntityUpsertionAdapter.class", "name": "androidx/room/EntityUpsertionAdapter.class", "size": 9697, "crc": -130793716}, {"key": "androidx/room/EntityUpsertionAdapter_androidKt.class", "name": "androidx/room/EntityUpsertionAdapter_androidKt.class", "size": 701, "crc": -2020648745}, {"key": "androidx/room/ExperimentalRoomApi.class", "name": "androidx/room/ExperimentalRoomApi.class", "size": 910, "crc": -375310733}, {"key": "androidx/room/InvalidationLiveDataContainer.class", "name": "androidx/room/InvalidationLiveDataContainer.class", "size": 3773, "crc": -421943549}, {"key": "androidx/room/InvalidationTracker$Companion.class", "name": "androidx/room/InvalidationTracker$Companion.class", "size": 833, "crc": 290717640}, {"key": "androidx/room/InvalidationTracker$MultiInstanceClientInitState.class", "name": "androidx/room/InvalidationTracker$MultiInstanceClientInitState.class", "size": 3765, "crc": -1370787065}, {"key": "androidx/room/InvalidationTracker$Observer.class", "name": "androidx/room/InvalidationTracker$Observer.class", "size": 2027, "crc": 1853406447}, {"key": "androidx/room/InvalidationTracker$addObserver$1.class", "name": "androidx/room/InvalidationTracker$addObserver$1.class", "size": 3461, "crc": -1612716272}, {"key": "androidx/room/InvalidationTracker$implementation$1.class", "name": "androidx/room/InvalidationTracker$implementation$1.class", "size": 1706, "crc": 436384354}, {"key": "androidx/room/InvalidationTracker$refreshVersionsSync$1.class", "name": "androidx/room/InvalidationTracker$refreshVersionsSync$1.class", "size": 3678, "crc": -2042273701}, {"key": "androidx/room/InvalidationTracker$removeObserver$1.class", "name": "androidx/room/InvalidationTracker$removeObserver$1.class", "size": 3476, "crc": -1223534355}, {"key": "androidx/room/InvalidationTracker$setAutoCloser$1.class", "name": "androidx/room/InvalidationTracker$setAutoCloser$1.class", "size": 1334, "crc": 985949134}, {"key": "androidx/room/InvalidationTracker$syncBlocking$1.class", "name": "androidx/room/InvalidationTracker$syncBlocking$1.class", "size": 3188, "crc": -1777095053}, {"key": "androidx/room/InvalidationTracker.class", "name": "androidx/room/InvalidationTracker.class", "size": 23808, "crc": 1163417116}, {"key": "androidx/room/MultiInstanceInvalidationClient$createFlow$$inlined$mapNotNull$1$2$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$createFlow$$inlined$mapNotNull$1$2$1.class", "size": 1802, "crc": 132633865}, {"key": "androidx/room/MultiInstanceInvalidationClient$createFlow$$inlined$mapNotNull$1$2.class", "name": "androidx/room/MultiInstanceInvalidationClient$createFlow$$inlined$mapNotNull$1$2.class", "size": 6289, "crc": 1117521997}, {"key": "androidx/room/MultiInstanceInvalidationClient$createFlow$$inlined$mapNotNull$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$createFlow$$inlined$mapNotNull$1.class", "size": 3331, "crc": 2118050286}, {"key": "androidx/room/MultiInstanceInvalidationClient$invalidationCallback$1$onInvalidation$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$invalidationCallback$1$onInvalidation$1.class", "size": 4337, "crc": -1806647284}, {"key": "androidx/room/MultiInstanceInvalidationClient$invalidationCallback$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$invalidationCallback$1.class", "size": 2115, "crc": -399637405}, {"key": "androidx/room/MultiInstanceInvalidationClient$observer$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$observer$1.class", "size": 3822, "crc": -1702949959}, {"key": "androidx/room/MultiInstanceInvalidationClient$serviceConnection$1.class", "name": "androidx/room/MultiInstanceInvalidationClient$serviceConnection$1.class", "size": 2060, "crc": 115134230}, {"key": "androidx/room/MultiInstanceInvalidationClient.class", "name": "androidx/room/MultiInstanceInvalidationClient.class", "size": 9246, "crc": 1104778110}, {"key": "androidx/room/MultiInstanceInvalidationService$binder$1.class", "name": "androidx/room/MultiInstanceInvalidationService$binder$1.class", "size": 4793, "crc": 965025939}, {"key": "androidx/room/MultiInstanceInvalidationService$callbackList$1.class", "name": "androidx/room/MultiInstanceInvalidationService$callbackList$1.class", "size": 1903, "crc": -1014179807}, {"key": "androidx/room/MultiInstanceInvalidationService.class", "name": "androidx/room/MultiInstanceInvalidationService.class", "size": 3063, "crc": -651719897}, {"key": "androidx/room/ObservedTableStates$ObserveOp.class", "name": "androidx/room/ObservedTableStates$ObserveOp.class", "size": 2043, "crc": 527203612}, {"key": "androidx/room/ObservedTableStates.class", "name": "androidx/room/ObservedTableStates.class", "size": 6320, "crc": -1091240676}, {"key": "androidx/room/ObservedTableVersions$collect$1.class", "name": "androidx/room/ObservedTableVersions$collect$1.class", "size": 1661, "crc": 374646660}, {"key": "androidx/room/ObservedTableVersions.class", "name": "androidx/room/ObservedTableVersions.class", "size": 4333, "crc": 916199765}, {"key": "androidx/room/ObserverWrapper.class", "name": "androidx/room/ObserverWrapper.class", "size": 6343, "crc": 1859993409}, {"key": "androidx/room/PooledConnection.class", "name": "androidx/room/PooledConnection.class", "size": 1055, "crc": -1017796357}, {"key": "androidx/room/Room$databaseBuilder$3.class", "name": "androidx/room/Room$databaseBuilder$3.class", "size": 1522, "crc": 796296655}, {"key": "androidx/room/Room$inMemoryDatabaseBuilder$1.class", "name": "androidx/room/Room$inMemoryDatabaseBuilder$1.class", "size": 1528, "crc": -1359554104}, {"key": "androidx/room/Room.class", "name": "androidx/room/Room.class", "size": 6255, "crc": -1598867209}, {"key": "androidx/room/RoomCallableTrackingLiveData.class", "name": "androidx/room/RoomCallableTrackingLiveData.class", "size": 2366, "crc": -201592745}, {"key": "androidx/room/RoomConnectionManager$NoOpOpenDelegate.class", "name": "androidx/room/RoomConnectionManager$NoOpOpenDelegate.class", "size": 2464, "crc": -1852922309}, {"key": "androidx/room/RoomConnectionManager$SupportOpenHelperCallback.class", "name": "androidx/room/RoomConnectionManager$SupportOpenHelperCallback.class", "size": 2472, "crc": -1645748377}, {"key": "androidx/room/RoomConnectionManager$installOnOpenCallback$newCallbacks$1.class", "name": "androidx/room/RoomConnectionManager$installOnOpenCallback$newCallbacks$1.class", "size": 1737, "crc": -1498205442}, {"key": "androidx/room/RoomConnectionManager.class", "name": "androidx/room/RoomConnectionManager.class", "size": 12000, "crc": 1251121802}, {"key": "androidx/room/RoomDatabase$Builder.class", "name": "androidx/room/RoomDatabase$Builder.class", "size": 30384, "crc": -2137327617}, {"key": "androidx/room/RoomDatabase$Callback.class", "name": "androidx/room/RoomDatabase$Callback.class", "size": 1975, "crc": -1948449039}, {"key": "androidx/room/RoomDatabase$Companion.class", "name": "androidx/room/RoomDatabase$Companion.class", "size": 864, "crc": -176675613}, {"key": "androidx/room/RoomDatabase$JournalMode.class", "name": "androidx/room/RoomDatabase$JournalMode.class", "size": 2829, "crc": 1605495027}, {"key": "androidx/room/RoomDatabase$MigrationContainer.class", "name": "androidx/room/RoomDatabase$MigrationContainer.class", "size": 7040, "crc": -1072908652}, {"key": "androidx/room/RoomDatabase$PrepackagedDatabaseCallback.class", "name": "androidx/room/RoomDatabase$PrepackagedDatabaseCallback.class", "size": 1116, "crc": 1210613273}, {"key": "androidx/room/RoomDatabase$QueryCallback.class", "name": "androidx/room/RoomDatabase$QueryCallback.class", "size": 823, "crc": 1937466076}, {"key": "androidx/room/RoomDatabase$closeBarrier$1.class", "name": "androidx/room/RoomDatabase$closeBarrier$1.class", "size": 1187, "crc": 1091992679}, {"key": "androidx/room/RoomDatabase$performClear$1$1$1.class", "name": "androidx/room/RoomDatabase$performClear$1$1$1.class", "size": 5166, "crc": 230596633}, {"key": "androidx/room/RoomDatabase$performClear$1$1.class", "name": "androidx/room/RoomDatabase$performClear$1$1.class", "size": 4934, "crc": 162595608}, {"key": "androidx/room/RoomDatabase$performClear$1.class", "name": "androidx/room/RoomDatabase$performClear$1.class", "size": 3688, "crc": -1996099182}, {"key": "androidx/room/RoomDatabase.class", "name": "androidx/room/RoomDatabase.class", "size": 33456, "crc": 2070788729}, {"key": "androidx/room/RoomDatabaseConstructor.class", "name": "androidx/room/RoomDatabaseConstructor.class", "size": 713, "crc": -438650170}, {"key": "androidx/room/RoomDatabaseKt.class", "name": "androidx/room/RoomDatabaseKt.class", "size": 3764, "crc": 1231365761}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabaseKt$useReaderConnection$2.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabaseKt$useReaderConnection$2.class", "size": 3768, "crc": 681182235}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabaseKt$useWriterConnection$1.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabaseKt$useWriterConnection$1.class", "size": 1738, "crc": -857727177}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabaseKt$useWriterConnection$2.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabaseKt$useWriterConnection$2.class", "size": 3768, "crc": -884683266}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabaseKt.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabaseKt.class", "size": 10331, "crc": 2101308360}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$startTransactionCoroutine$2$1$1.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$startTransactionCoroutine$2$1$1.class", "size": 5279, "crc": -1394765397}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$startTransactionCoroutine$2$1.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$startTransactionCoroutine$2$1.class", "size": 3001, "crc": -111869950}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$withTransaction$2.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$withTransaction$2.class", "size": 3552, "crc": 1238249860}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$withTransactionContext$transactionBlock$1.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt$withTransactionContext$transactionBlock$1.class", "size": 4435, "crc": 1488649633}, {"key": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt.class", "name": "androidx/room/RoomDatabaseKt__RoomDatabase_androidKt.class", "size": 9819, "crc": -628282832}, {"key": "androidx/room/RoomLambdaTrackingLiveData.class", "name": "androidx/room/RoomLambdaTrackingLiveData.class", "size": 2714, "crc": 910872161}, {"key": "androidx/room/RoomOpenDelegate$ValidationResult.class", "name": "androidx/room/RoomOpenDelegate$ValidationResult.class", "size": 1274, "crc": 887297254}, {"key": "androidx/room/RoomOpenDelegate.class", "name": "androidx/room/RoomOpenDelegate.class", "size": 2547, "crc": 870422783}, {"key": "androidx/room/RoomOpenDelegateMarker.class", "name": "androidx/room/RoomOpenDelegateMarker.class", "size": 398, "crc": -527888818}, {"key": "androidx/room/RoomOpenHelper$Companion.class", "name": "androidx/room/RoomOpenHelper$Companion.class", "size": 6480, "crc": -2145273657}, {"key": "androidx/room/RoomOpenHelper$Delegate.class", "name": "androidx/room/RoomOpenHelper$Delegate.class", "size": 2668, "crc": 636063022}, {"key": "androidx/room/RoomOpenHelper$ValidationResult.class", "name": "androidx/room/RoomOpenHelper$ValidationResult.class", "size": 1428, "crc": -2063820545}, {"key": "androidx/room/RoomOpenHelper.class", "name": "androidx/room/RoomOpenHelper.class", "size": 11020, "crc": 1762540628}, {"key": "androidx/room/RoomRawQuery.class", "name": "androidx/room/RoomRawQuery.class", "size": 3225, "crc": 1011326740}, {"key": "androidx/room/RoomSQLiteQuery$Binding.class", "name": "androidx/room/RoomSQLiteQuery$Binding.class", "size": 696, "crc": -1347393934}, {"key": "androidx/room/RoomSQLiteQuery$Companion$copyFrom$1.class", "name": "androidx/room/RoomSQLiteQuery$Companion$copyFrom$1.class", "size": 2123, "crc": -385685578}, {"key": "androidx/room/RoomSQLiteQuery$Companion.class", "name": "androidx/room/RoomSQLiteQuery$Companion.class", "size": 4324, "crc": -1136720817}, {"key": "androidx/room/RoomSQLiteQuery.class", "name": "androidx/room/RoomSQLiteQuery.class", "size": 9212, "crc": -2012492688}, {"key": "androidx/room/RoomTrackingLiveData$invalidated$1.class", "name": "androidx/room/RoomTrackingLiveData$invalidated$1.class", "size": 3253, "crc": -1689948407}, {"key": "androidx/room/RoomTrackingLiveData$observer$1.class", "name": "androidx/room/RoomTrackingLiveData$observer$1.class", "size": 2373, "crc": 334158955}, {"key": "androidx/room/RoomTrackingLiveData$onActive$1.class", "name": "androidx/room/RoomTrackingLiveData$onActive$1.class", "size": 3238, "crc": -595295778}, {"key": "androidx/room/RoomTrackingLiveData$refresh$1.class", "name": "androidx/room/RoomTrackingLiveData$refresh$1.class", "size": 1849, "crc": 85646509}, {"key": "androidx/room/RoomTrackingLiveData.class", "name": "androidx/room/RoomTrackingLiveData.class", "size": 7293, "crc": 1801304370}, {"key": "androidx/room/SharedSQLiteStatement.class", "name": "androidx/room/SharedSQLiteStatement.class", "size": 3661, "crc": -1290503738}, {"key": "androidx/room/TransactionElement$Key.class", "name": "androidx/room/TransactionElement$Key.class", "size": 1105, "crc": -734920764}, {"key": "androidx/room/TransactionElement.class", "name": "androidx/room/TransactionElement.class", "size": 4484, "crc": 837302604}, {"key": "androidx/room/TransactionExecutor.class", "name": "androidx/room/TransactionExecutor.class", "size": 3600, "crc": 825283947}, {"key": "androidx/room/TransactionScope.class", "name": "androidx/room/TransactionScope.class", "size": 1545, "crc": 1208920304}, {"key": "androidx/room/Transactor$SQLiteTransactionType.class", "name": "androidx/room/Transactor$SQLiteTransactionType.class", "size": 2070, "crc": -205589137}, {"key": "androidx/room/Transactor.class", "name": "androidx/room/Transactor.class", "size": 1743, "crc": -1676322153}, {"key": "androidx/room/TransactorKt.class", "name": "androidx/room/TransactorKt.class", "size": 4045, "crc": -413337673}, {"key": "androidx/room/TriggerBasedInvalidationTracker$Companion.class", "name": "androidx/room/TriggerBasedInvalidationTracker$Companion.class", "size": 2004, "crc": -430570629}, {"key": "androidx/room/TriggerBasedInvalidationTracker$checkInvalidatedTables$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$checkInvalidatedTables$1.class", "size": 2045, "crc": 1504447863}, {"key": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1$1.class", "size": 3309, "crc": 1953714030}, {"key": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1$2$emit$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1$2$emit$1.class", "size": 1896, "crc": -1099686998}, {"key": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1$2.class", "name": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1$2.class", "size": 6197, "crc": -1836930161}, {"key": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$createFlow$1.class", "size": 5647, "crc": -832923352}, {"key": "androidx/room/TriggerBasedInvalidationTracker$notifyInvalidation$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$notifyInvalidation$1.class", "size": 1949, "crc": 2100559588}, {"key": "androidx/room/TriggerBasedInvalidationTracker$notifyInvalidation$2$invalidatedTableIds$1$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$notifyInvalidation$2$invalidatedTableIds$1$1.class", "size": 3778, "crc": -666005254}, {"key": "androidx/room/TriggerBasedInvalidationTracker$notifyInvalidation$2$invalidatedTableIds$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$notifyInvalidation$2$invalidatedTableIds$1.class", "size": 4331, "crc": 1245214297}, {"key": "androidx/room/TriggerBasedInvalidationTracker$refreshInvalidation$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$refreshInvalidation$1.class", "size": 1934, "crc": -665197259}, {"key": "androidx/room/TriggerBasedInvalidationTracker$refreshInvalidationAsync$3.class", "name": "androidx/room/TriggerBasedInvalidationTracker$refreshInvalidationAsync$3.class", "size": 3845, "crc": -618005221}, {"key": "androidx/room/TriggerBasedInvalidationTracker$startTrackingTable$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$startTrackingTable$1.class", "size": 2157, "crc": -232937107}, {"key": "androidx/room/TriggerBasedInvalidationTracker$stopTrackingTable$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$stopTrackingTable$1.class", "size": 2056, "crc": 2111305819}, {"key": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$1.class", "size": 1788, "crc": -1486090540}, {"key": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$2$1$1$WhenMappings.class", "name": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$2$1$1$WhenMappings.class", "size": 1055, "crc": 1800871914}, {"key": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$2$1$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$2$1$1.class", "size": 6406, "crc": 1875200572}, {"key": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$2$1.class", "name": "androidx/room/TriggerBasedInvalidationTracker$syncTriggers$2$1.class", "size": 4462, "crc": -1694881546}, {"key": "androidx/room/TriggerBasedInvalidationTracker.class", "name": "androidx/room/TriggerBasedInvalidationTracker.class", "size": 28794, "crc": 1166245814}, {"key": "androidx/room/WeakObserver.class", "name": "androidx/room/WeakObserver.class", "size": 2066, "crc": 162367389}, {"key": "androidx/room/concurrent/AtomicsKt.class", "name": "androidx/room/concurrent/AtomicsKt.class", "size": 1776, "crc": 416818578}, {"key": "androidx/room/concurrent/Atomics_jvmAndroidKt.class", "name": "androidx/room/concurrent/Atomics_jvmAndroidKt.class", "size": 757, "crc": 1222043684}, {"key": "androidx/room/concurrent/CloseBarrier.class", "name": "androidx/room/concurrent/CloseBarrier.class", "size": 4569, "crc": 711376037}, {"key": "androidx/room/concurrent/CloseBarrierKt.class", "name": "androidx/room/concurrent/CloseBarrierKt.class", "size": 1552, "crc": -1766247247}, {"key": "androidx/room/concurrent/ExclusiveLock$Companion.class", "name": "androidx/room/concurrent/ExclusiveLock$Companion.class", "size": 3761, "crc": 1992464084}, {"key": "androidx/room/concurrent/ExclusiveLock.class", "name": "androidx/room/concurrent/ExclusiveLock.class", "size": 3675, "crc": -2105017329}, {"key": "androidx/room/concurrent/FileLock.class", "name": "androidx/room/concurrent/FileLock.class", "size": 2362, "crc": 1438137008}, {"key": "androidx/room/concurrent/ReentrantLockKt.class", "name": "androidx/room/concurrent/ReentrantLockKt.class", "size": 1607, "crc": -2131111688}, {"key": "androidx/room/concurrent/ReentrantLock_jvmAndroidKt.class", "name": "androidx/room/concurrent/ReentrantLock_jvmAndroidKt.class", "size": 419, "crc": -1195467248}, {"key": "androidx/room/concurrent/Synchronized_jvmAndroidKt.class", "name": "androidx/room/concurrent/Synchronized_jvmAndroidKt.class", "size": 1525, "crc": -820186737}, {"key": "androidx/room/concurrent/ThreadLocal_jvmAndroidKt.class", "name": "androidx/room/concurrent/ThreadLocal_jvmAndroidKt.class", "size": 1791, "crc": 839992993}, {"key": "androidx/room/coroutines/AndroidSQLiteDriverConnectionPool.class", "name": "androidx/room/coroutines/AndroidSQLiteDriverConnectionPool.class", "size": 4011, "crc": 2127551892}, {"key": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection$AndroidSQLiteDriverTransactor.class", "name": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection$AndroidSQLiteDriverTransactor.class", "size": 4360, "crc": 1314049526}, {"key": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection$WhenMappings.class", "name": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection$WhenMappings.class", "size": 1009, "crc": 1761450346}, {"key": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection$transaction$1.class", "name": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection$transaction$1.class", "size": 2411, "crc": -1854068829}, {"key": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection.class", "name": "androidx/room/coroutines/AndroidSQLiteDriverPooledConnection.class", "size": 8737, "crc": 2091835542}, {"key": "androidx/room/coroutines/ConnectionElement$Key.class", "name": "androidx/room/coroutines/ConnectionElement$Key.class", "size": 1153, "crc": 679295280}, {"key": "androidx/room/coroutines/ConnectionElement.class", "name": "androidx/room/coroutines/ConnectionElement.class", "size": 3842, "crc": 1454602921}, {"key": "androidx/room/coroutines/ConnectionPool$RollbackException.class", "name": "androidx/room/coroutines/ConnectionPool$RollbackException.class", "size": 1052, "crc": 702106719}, {"key": "androidx/room/coroutines/ConnectionPool.class", "name": "androidx/room/coroutines/ConnectionPool.class", "size": 1414, "crc": 1770591974}, {"key": "androidx/room/coroutines/ConnectionPoolImpl$useConnection$1.class", "name": "androidx/room/coroutines/ConnectionPoolImpl$useConnection$1.class", "size": 2081, "crc": -161036675}, {"key": "androidx/room/coroutines/ConnectionPoolImpl$useConnection$2.class", "name": "androidx/room/coroutines/ConnectionPoolImpl$useConnection$2.class", "size": 3592, "crc": -2115796756}, {"key": "androidx/room/coroutines/ConnectionPoolImpl$useConnection$4.class", "name": "androidx/room/coroutines/ConnectionPoolImpl$useConnection$4.class", "size": 3803, "crc": -962909477}, {"key": "androidx/room/coroutines/ConnectionPoolImpl.class", "name": "androidx/room/coroutines/ConnectionPoolImpl.class", "size": 15158, "crc": 721833460}, {"key": "androidx/room/coroutines/ConnectionPoolKt.class", "name": "androidx/room/coroutines/ConnectionPoolKt.class", "size": 1611, "crc": 2043976664}, {"key": "androidx/room/coroutines/ConnectionWithLock.class", "name": "androidx/room/coroutines/ConnectionWithLock.class", "size": 6971, "crc": -493584118}, {"key": "androidx/room/coroutines/FlowUtil$createFlow$$inlined$map$1$2$1.class", "name": "androidx/room/coroutines/FlowUtil$createFlow$$inlined$map$1$2$1.class", "size": 1668, "crc": -2137037032}, {"key": "androidx/room/coroutines/FlowUtil$createFlow$$inlined$map$1$2.class", "name": "androidx/room/coroutines/FlowUtil$createFlow$$inlined$map$1$2.class", "size": 4043, "crc": 887261027}, {"key": "androidx/room/coroutines/FlowUtil$createFlow$$inlined$map$1.class", "name": "androidx/room/coroutines/FlowUtil$createFlow$$inlined$map$1.class", "size": 3489, "crc": 1580169547}, {"key": "androidx/room/coroutines/FlowUtil.class", "name": "androidx/room/coroutines/FlowUtil.class", "size": 3628, "crc": 551106440}, {"key": "androidx/room/coroutines/Pool$acquire$1.class", "name": "androidx/room/coroutines/Pool$acquire$1.class", "size": 1595, "crc": 13524089}, {"key": "androidx/room/coroutines/Pool$acquireWithTimeout$1.class", "name": "androidx/room/coroutines/Pool$acquireWithTimeout$1.class", "size": 1790, "crc": 1274731094}, {"key": "androidx/room/coroutines/Pool$acquireWithTimeout$2.class", "name": "androidx/room/coroutines/Pool$acquireWithTimeout$2.class", "size": 3716, "crc": 195797199}, {"key": "androidx/room/coroutines/Pool.class", "name": "androidx/room/coroutines/Pool.class", "size": 12101, "crc": -1017671203}, {"key": "androidx/room/coroutines/PooledConnectionImpl$StatementWrapper.class", "name": "androidx/room/coroutines/PooledConnectionImpl$StatementWrapper.class", "size": 9997, "crc": 479207910}, {"key": "androidx/room/coroutines/PooledConnectionImpl$TransactionImpl$rollback$1.class", "name": "androidx/room/coroutines/PooledConnectionImpl$TransactionImpl$rollback$1.class", "size": 2103, "crc": -1724507401}, {"key": "androidx/room/coroutines/PooledConnectionImpl$TransactionImpl.class", "name": "androidx/room/coroutines/PooledConnectionImpl$TransactionImpl.class", "size": 8537, "crc": -1493392823}, {"key": "androidx/room/coroutines/PooledConnectionImpl$TransactionItem.class", "name": "androidx/room/coroutines/PooledConnectionImpl$TransactionItem.class", "size": 1174, "crc": -1054559318}, {"key": "androidx/room/coroutines/PooledConnectionImpl$WhenMappings.class", "name": "androidx/room/coroutines/PooledConnectionImpl$WhenMappings.class", "size": 956, "crc": -561688273}, {"key": "androidx/room/coroutines/PooledConnectionImpl$beginTransaction$1.class", "name": "androidx/room/coroutines/PooledConnectionImpl$beginTransaction$1.class", "size": 2187, "crc": 379079336}, {"key": "androidx/room/coroutines/PooledConnectionImpl$endTransaction$1.class", "name": "androidx/room/coroutines/PooledConnectionImpl$endTransaction$1.class", "size": 1976, "crc": 1783783083}, {"key": "androidx/room/coroutines/PooledConnectionImpl$transaction$1.class", "name": "androidx/room/coroutines/PooledConnectionImpl$transaction$1.class", "size": 2351, "crc": -1381474523}, {"key": "androidx/room/coroutines/PooledConnectionImpl$usePrepared$1.class", "name": "androidx/room/coroutines/PooledConnectionImpl$usePrepared$1.class", "size": 1987, "crc": 1039208096}, {"key": "androidx/room/coroutines/PooledConnectionImpl.class", "name": "androidx/room/coroutines/PooledConnectionImpl.class", "size": 19370, "crc": 205185328}, {"key": "androidx/room/coroutines/RawConnectionAccessor.class", "name": "androidx/room/coroutines/RawConnectionAccessor.class", "size": 657, "crc": -1743655731}, {"key": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt$runBlockingUninterruptible$1$1.class", "name": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt$runBlockingUninterruptible$1$1.class", "size": 5217, "crc": -1242394268}, {"key": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt$runBlockingUninterruptible$1$2.class", "name": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt$runBlockingUninterruptible$1$2.class", "size": 3463, "crc": -1149524942}, {"key": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt$runBlockingUninterruptible$1.class", "name": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt$runBlockingUninterruptible$1.class", "size": 5585, "crc": -1539786747}, {"key": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt.class", "name": "androidx/room/coroutines/RunBlockingUninterruptible_androidKt.class", "size": 1729, "crc": -1201999433}, {"key": "androidx/room/driver/ResultCode.class", "name": "androidx/room/driver/ResultCode.class", "size": 850, "crc": 280533723}, {"key": "androidx/room/driver/SupportSQLiteConnection.class", "name": "androidx/room/driver/SupportSQLiteConnection.class", "size": 2410, "crc": 1998965759}, {"key": "androidx/room/driver/SupportSQLiteConnectionPool.class", "name": "androidx/room/driver/SupportSQLiteConnectionPool.class", "size": 3126, "crc": -1250970243}, {"key": "androidx/room/driver/SupportSQLiteDriver.class", "name": "androidx/room/driver/SupportSQLiteDriver.class", "size": 2176, "crc": 1750506150}, {"key": "androidx/room/driver/SupportSQLitePooledConnection$SupportSQLiteTransactor.class", "name": "androidx/room/driver/SupportSQLitePooledConnection$SupportSQLiteTransactor.class", "size": 4266, "crc": -443049787}, {"key": "androidx/room/driver/SupportSQLitePooledConnection$WhenMappings.class", "name": "androidx/room/driver/SupportSQLitePooledConnection$WhenMappings.class", "size": 983, "crc": -832246658}, {"key": "androidx/room/driver/SupportSQLitePooledConnection$transaction$1.class", "name": "androidx/room/driver/SupportSQLitePooledConnection$transaction$1.class", "size": 2315, "crc": 419268985}, {"key": "androidx/room/driver/SupportSQLitePooledConnection.class", "name": "androidx/room/driver/SupportSQLitePooledConnection.class", "size": 8712, "crc": -1208813281}, {"key": "androidx/room/driver/SupportSQLiteStatement$Companion.class", "name": "androidx/room/driver/SupportSQLiteStatement$Companion.class", "size": 3973, "crc": 145715868}, {"key": "androidx/room/driver/SupportSQLiteStatement$SupportAndroidSQLiteStatement$Companion.class", "name": "androidx/room/driver/SupportSQLiteStatement$SupportAndroidSQLiteStatement$Companion.class", "size": 2056, "crc": 250940369}, {"key": "androidx/room/driver/SupportSQLiteStatement$SupportAndroidSQLiteStatement$ensureCursor$1.class", "name": "androidx/room/driver/SupportSQLiteStatement$SupportAndroidSQLiteStatement$ensureCursor$1.class", "size": 2947, "crc": -1105510854}, {"key": "androidx/room/driver/SupportSQLiteStatement$SupportAndroidSQLiteStatement.class", "name": "androidx/room/driver/SupportSQLiteStatement$SupportAndroidSQLiteStatement.class", "size": 8597, "crc": 1515250129}, {"key": "androidx/room/driver/SupportSQLiteStatement$SupportOtherAndroidSQLiteStatement.class", "name": "androidx/room/driver/SupportSQLiteStatement$SupportOtherAndroidSQLiteStatement.class", "size": 4385, "crc": -1472924505}, {"key": "androidx/room/driver/SupportSQLiteStatement.class", "name": "androidx/room/driver/SupportSQLiteStatement.class", "size": 3107, "crc": 1698096700}, {"key": "androidx/room/driver/SupportSQLiteStatement_androidKt.class", "name": "androidx/room/driver/SupportSQLiteStatement_androidKt.class", "size": 430, "crc": 193623096}, {"key": "androidx/room/migration/AutoMigrationSpec.class", "name": "androidx/room/migration/AutoMigrationSpec.class", "size": 1294, "crc": 1964148291}, {"key": "androidx/room/migration/Migration.class", "name": "androidx/room/migration/Migration.class", "size": 1988, "crc": -812151258}, {"key": "androidx/room/migration/MigrationImpl.class", "name": "androidx/room/migration/MigrationImpl.class", "size": 1958, "crc": 553024460}, {"key": "androidx/room/migration/MigrationKt.class", "name": "androidx/room/migration/MigrationKt.class", "size": 1425, "crc": 530159934}, {"key": "androidx/room/paging/CursorSQLiteStatement$Companion.class", "name": "androidx/room/paging/CursorSQLiteStatement$Companion.class", "size": 1842, "crc": 1941052536}, {"key": "androidx/room/paging/CursorSQLiteStatement.class", "name": "androidx/room/paging/CursorSQLiteStatement.class", "size": 5108, "crc": -2106893186}, {"key": "androidx/room/support/AutoCloser$Companion.class", "name": "androidx/room/support/AutoCloser$Companion.class", "size": 864, "crc": 1032582029}, {"key": "androidx/room/support/AutoCloser$Watch.class", "name": "androidx/room/support/AutoCloser$Watch.class", "size": 534, "crc": 1930249851}, {"key": "androidx/room/support/AutoCloser$decrementCountAndScheduleClose$2.class", "name": "androidx/room/support/AutoCloser$decrementCountAndScheduleClose$2.class", "size": 3429, "crc": -642204195}, {"key": "androidx/room/support/AutoCloser.class", "name": "androidx/room/support/AutoCloser.class", "size": 10570, "crc": 1336056563}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$attachedDbs$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$attachedDbs$1.class", "size": 1276, "crc": 98622503}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$inTransaction$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$inTransaction$1.class", "size": 1820, "crc": 1785624855}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDatabaseIntegrityOk$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDatabaseIntegrityOk$1.class", "size": 1337, "crc": 1548431382}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDbLockedByCurrentThread$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isDbLockedByCurrentThread$1.class", "size": 1353, "crc": -1697667682}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isReadOnly$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isReadOnly$1.class", "size": 1293, "crc": -790011116}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isWriteAheadLoggingEnabled$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$isWriteAheadLoggingEnabled$1.class", "size": 1357, "crc": 1643570790}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$maximumSize$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$maximumSize$1.class", "size": 1311, "crc": 92128218}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pageSize$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$pageSize$1.class", "size": 1527, "crc": 253603085}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$path$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$path$1.class", "size": 1245, "crc": 403948889}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$version$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$version$1.class", "size": 1526, "crc": 1586627746}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$1.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$1.class", "size": 1856, "crc": -985339174}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$2.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase$yieldIfContendedSafely$2.class", "size": 1868, "crc": -1368228016}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteDatabase.class", "size": 18212, "crc": -540686217}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteStatement$Companion.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteStatement$Companion.class", "size": 1300, "crc": -743340773}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteStatement.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$AutoClosingSupportSQLiteStatement.class", "size": 8524, "crc": -583056294}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper$KeepAliveCursor.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper$KeepAliveCursor.class", "size": 7011, "crc": 331962468}, {"key": "androidx/room/support/AutoClosingRoomOpenHelper.class", "name": "androidx/room/support/AutoClosingRoomOpenHelper.class", "size": 3396, "crc": -740648758}, {"key": "androidx/room/support/AutoClosingRoomOpenHelperFactory.class", "name": "androidx/room/support/AutoClosingRoomOpenHelperFactory.class", "size": 2227, "crc": 2076354713}, {"key": "androidx/room/support/PrePackagedCopyOpenHelper$createFrameworkOpenHelper$configuration$1.class", "name": "androidx/room/support/PrePackagedCopyOpenHelper$createFrameworkOpenHelper$configuration$1.class", "size": 1899, "crc": 1137638393}, {"key": "androidx/room/support/PrePackagedCopyOpenHelper.class", "name": "androidx/room/support/PrePackagedCopyOpenHelper.class", "size": 11547, "crc": -1979058769}, {"key": "androidx/room/support/PrePackagedCopyOpenHelperFactory.class", "name": "androidx/room/support/PrePackagedCopyOpenHelperFactory.class", "size": 2971, "crc": 2022356822}, {"key": "androidx/room/support/QueryInterceptorDatabase$beginTransaction$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$beginTransaction$1.class", "size": 3552, "crc": -267166726}, {"key": "androidx/room/support/QueryInterceptorDatabase$beginTransactionNonExclusive$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$beginTransactionNonExclusive$1.class", "size": 3612, "crc": 1366392858}, {"key": "androidx/room/support/QueryInterceptorDatabase$beginTransactionWithListener$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$beginTransactionWithListener$1.class", "size": 3663, "crc": 1308282468}, {"key": "androidx/room/support/QueryInterceptorDatabase$beginTransactionWithListenerNonExclusive$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$beginTransactionWithListenerNonExclusive$1.class", "size": 3723, "crc": -317965714}, {"key": "androidx/room/support/QueryInterceptorDatabase$endTransaction$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$endTransaction$1.class", "size": 3526, "crc": 1093403206}, {"key": "androidx/room/support/QueryInterceptorDatabase$execSQL$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$execSQL$1.class", "size": 3567, "crc": 145790598}, {"key": "androidx/room/support/QueryInterceptorDatabase$execSQL$2.class", "name": "androidx/room/support/QueryInterceptorDatabase$execSQL$2.class", "size": 3699, "crc": -1320272694}, {"key": "androidx/room/support/QueryInterceptorDatabase$query$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$query$1.class", "size": 3607, "crc": 1205622022}, {"key": "androidx/room/support/QueryInterceptorDatabase$query$2.class", "name": "androidx/room/support/QueryInterceptorDatabase$query$2.class", "size": 3715, "crc": -1824991333}, {"key": "androidx/room/support/QueryInterceptorDatabase$query$3.class", "name": "androidx/room/support/QueryInterceptorDatabase$query$3.class", "size": 4039, "crc": 1922274665}, {"key": "androidx/room/support/QueryInterceptorDatabase$query$4.class", "name": "androidx/room/support/QueryInterceptorDatabase$query$4.class", "size": 4070, "crc": 707651315}, {"key": "androidx/room/support/QueryInterceptorDatabase$setTransactionSuccessful$1.class", "name": "androidx/room/support/QueryInterceptorDatabase$setTransactionSuccessful$1.class", "size": 3583, "crc": 1876872327}, {"key": "androidx/room/support/QueryInterceptorDatabase.class", "name": "androidx/room/support/QueryInterceptorDatabase.class", "size": 12217, "crc": 931408537}, {"key": "androidx/room/support/QueryInterceptorOpenHelper.class", "name": "androidx/room/support/QueryInterceptorOpenHelper.class", "size": 3008, "crc": -1253413097}, {"key": "androidx/room/support/QueryInterceptorOpenHelperFactory.class", "name": "androidx/room/support/QueryInterceptorOpenHelperFactory.class", "size": 2414, "crc": -184917831}, {"key": "androidx/room/support/QueryInterceptorProgram.class", "name": "androidx/room/support/QueryInterceptorProgram.class", "size": 2953, "crc": -1008696279}, {"key": "androidx/room/support/QueryInterceptorStatement$execute$1.class", "name": "androidx/room/support/QueryInterceptorStatement$execute$1.class", "size": 3682, "crc": -898889349}, {"key": "androidx/room/support/QueryInterceptorStatement$executeInsert$1.class", "name": "androidx/room/support/QueryInterceptorStatement$executeInsert$1.class", "size": 3712, "crc": 1115058081}, {"key": "androidx/room/support/QueryInterceptorStatement$executeUpdateDelete$1.class", "name": "androidx/room/support/QueryInterceptorStatement$executeUpdateDelete$1.class", "size": 3742, "crc": -941980933}, {"key": "androidx/room/support/QueryInterceptorStatement$simpleQueryForLong$1.class", "name": "androidx/room/support/QueryInterceptorStatement$simpleQueryForLong$1.class", "size": 3737, "crc": -1178179042}, {"key": "androidx/room/support/QueryInterceptorStatement$simpleQueryForString$1.class", "name": "androidx/room/support/QueryInterceptorStatement$simpleQueryForString$1.class", "size": 3764, "crc": 225841290}, {"key": "androidx/room/support/QueryInterceptorStatement.class", "name": "androidx/room/support/QueryInterceptorStatement.class", "size": 6547, "crc": -1967188244}, {"key": "androidx/room/util/ByteArrayWrapper.class", "name": "androidx/room/util/ByteArrayWrapper.class", "size": 1612, "crc": 389289369}, {"key": "androidx/room/util/CursorUtil$wrapMappedColumns$2.class", "name": "androidx/room/util/CursorUtil$wrapMappedColumns$2.class", "size": 2730, "crc": -1193311325}, {"key": "androidx/room/util/CursorUtil.class", "name": "androidx/room/util/CursorUtil.class", "size": 8050, "crc": -618086046}, {"key": "androidx/room/util/DBUtil.class", "name": "androidx/room/util/DBUtil.class", "size": 5737, "crc": 930299966}, {"key": "androidx/room/util/DBUtil__DBUtilKt$internalPerform$2$result$1.class", "name": "androidx/room/util/DBUtil__DBUtilKt$internalPerform$2$result$1.class", "size": 3505, "crc": -1877203988}, {"key": "androidx/room/util/DBUtil__DBUtilKt$internalPerform$2.class", "name": "androidx/room/util/DBUtil__DBUtilKt$internalPerform$2.class", "size": 5849, "crc": 1591037231}, {"key": "androidx/room/util/DBUtil__DBUtilKt.class", "name": "androidx/room/util/DBUtil__DBUtilKt.class", "size": 8027, "crc": 349159248}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$compatCoroutineExecute$2.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$compatCoroutineExecute$2.class", "size": 3566, "crc": 1161367341}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performBlocking$1$invokeSuspend$$inlined$internalPerform$1$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performBlocking$1$invokeSuspend$$inlined$internalPerform$1$1.class", "size": 4414, "crc": 1326471528}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performBlocking$1$invokeSuspend$$inlined$internalPerform$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performBlocking$1$invokeSuspend$$inlined$internalPerform$1.class", "size": 6388, "crc": -764381565}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performBlocking$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performBlocking$1.class", "size": 5341, "crc": -1995248140}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$2$invokeSuspend$$inlined$internalPerform$1$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$2$invokeSuspend$$inlined$internalPerform$1$1.class", "size": 4251, "crc": -615364692}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$2$invokeSuspend$$inlined$internalPerform$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$2$invokeSuspend$$inlined$internalPerform$1.class", "size": 6199, "crc": -860302632}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$2.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$2.class", "size": 4765, "crc": -1951964405}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$3$invokeSuspend$$inlined$internalPerform$1$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$3$invokeSuspend$$inlined$internalPerform$1$1.class", "size": 4251, "crc": 934074092}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$3$invokeSuspend$$inlined$internalPerform$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$3$invokeSuspend$$inlined$internalPerform$1.class", "size": 6199, "crc": -133734789}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$3.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performInTransactionSuspending$3.class", "size": 5057, "crc": 1404100362}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$$inlined$compatCoroutineExecute$DBUtil__DBUtil_androidKt$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$$inlined$compatCoroutineExecute$DBUtil__DBUtil_androidKt$1.class", "size": 5328, "crc": 152159042}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$1.class", "size": 1831, "crc": 848966119}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$lambda$1$$inlined$internalPerform$1$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$lambda$1$$inlined$internalPerform$1$1.class", "size": 4360, "crc": -776403931}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$lambda$1$$inlined$internalPerform$1.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt$performSuspending$lambda$1$$inlined$internalPerform$1.class", "size": 6458, "crc": -316908999}, {"key": "androidx/room/util/DBUtil__DBUtil_androidKt.class", "name": "androidx/room/util/DBUtil__DBUtil_androidKt.class", "size": 15036, "crc": -1716282777}, {"key": "androidx/room/util/FileUtil.class", "name": "androidx/room/util/FileUtil.class", "size": 2976, "crc": -1173155704}, {"key": "androidx/room/util/ForeignKeyWithSequence.class", "name": "androidx/room/util/ForeignKeyWithSequence.class", "size": 2130, "crc": 48725424}, {"key": "androidx/room/util/FtsTableInfo$Companion.class", "name": "androidx/room/util/FtsTableInfo$Companion.class", "size": 2304, "crc": -1584955126}, {"key": "androidx/room/util/FtsTableInfo.class", "name": "androidx/room/util/FtsTableInfo.class", "size": 3696, "crc": -309736409}, {"key": "androidx/room/util/FtsTableInfoKt.class", "name": "androidx/room/util/FtsTableInfoKt.class", "size": 2634, "crc": -2046172319}, {"key": "androidx/room/util/KClassUtil.class", "name": "androidx/room/util/KClassUtil.class", "size": 3956, "crc": -42831774}, {"key": "androidx/room/util/MappedColumnsSQLiteStatementWrapper.class", "name": "androidx/room/util/MappedColumnsSQLiteStatementWrapper.class", "size": 7694, "crc": -1209904471}, {"key": "androidx/room/util/MigrationUtil.class", "name": "androidx/room/util/MigrationUtil.class", "size": 4871, "crc": 1317780188}, {"key": "androidx/room/util/RelationUtil.class", "name": "androidx/room/util/RelationUtil.class", "size": 2542, "crc": -1924378353}, {"key": "androidx/room/util/RelationUtil__RelationUtilKt.class", "name": "androidx/room/util/RelationUtil__RelationUtilKt.class", "size": 3495, "crc": 361337454}, {"key": "androidx/room/util/RelationUtil__RelationUtil_androidKt.class", "name": "androidx/room/util/RelationUtil__RelationUtil_androidKt.class", "size": 3526, "crc": 701844006}, {"key": "androidx/room/util/SQLiteConnectionUtil.class", "name": "androidx/room/util/SQLiteConnectionUtil.class", "size": 2338, "crc": -186081787}, {"key": "androidx/room/util/SQLiteResultCode.class", "name": "androidx/room/util/SQLiteResultCode.class", "size": 893, "crc": 2115934562}, {"key": "androidx/room/util/SQLiteStatementUtil.class", "name": "androidx/room/util/SQLiteStatementUtil.class", "size": 1755, "crc": 1009886052}, {"key": "androidx/room/util/SQLiteStatementUtil__StatementUtilKt.class", "name": "androidx/room/util/SQLiteStatementUtil__StatementUtilKt.class", "size": 4107, "crc": -204348002}, {"key": "androidx/room/util/SQLiteStatementUtil__StatementUtil_androidKt.class", "name": "androidx/room/util/SQLiteStatementUtil__StatementUtil_androidKt.class", "size": 2404, "crc": -510889183}, {"key": "androidx/room/util/SchemaInfoUtilKt$readIndex$lambda$13$$inlined$sortedBy$1.class", "name": "androidx/room/util/SchemaInfoUtilKt$readIndex$lambda$13$$inlined$sortedBy$1.class", "size": 2089, "crc": -560810584}, {"key": "androidx/room/util/SchemaInfoUtilKt$readIndex$lambda$13$$inlined$sortedBy$2.class", "name": "androidx/room/util/SchemaInfoUtilKt$readIndex$lambda$13$$inlined$sortedBy$2.class", "size": 2088, "crc": 245309381}, {"key": "androidx/room/util/SchemaInfoUtilKt.class", "name": "androidx/room/util/SchemaInfoUtilKt.class", "size": 21393, "crc": -104630543}, {"key": "androidx/room/util/StringUtil.class", "name": "androidx/room/util/StringUtil.class", "size": 5273, "crc": 834535086}, {"key": "androidx/room/util/TableInfo$Column$Companion.class", "name": "androidx/room/util/TableInfo$Column$Companion.class", "size": 1488, "crc": 496883099}, {"key": "androidx/room/util/TableInfo$Column.class", "name": "androidx/room/util/TableInfo$Column.class", "size": 3917, "crc": 156630504}, {"key": "androidx/room/util/TableInfo$Companion.class", "name": "androidx/room/util/TableInfo$Companion.class", "size": 2276, "crc": -61146474}, {"key": "androidx/room/util/TableInfo$CreatedFrom.class", "name": "androidx/room/util/TableInfo$CreatedFrom.class", "size": 699, "crc": 1768445105}, {"key": "androidx/room/util/TableInfo$ForeignKey.class", "name": "androidx/room/util/TableInfo$ForeignKey.class", "size": 2799, "crc": -1291788147}, {"key": "androidx/room/util/TableInfo$Index$Companion.class", "name": "androidx/room/util/TableInfo$Index$Companion.class", "size": 925, "crc": -1052736837}, {"key": "androidx/room/util/TableInfo$Index.class", "name": "androidx/room/util/TableInfo$Index.class", "size": 4471, "crc": 1223962906}, {"key": "androidx/room/util/TableInfo.class", "name": "androidx/room/util/TableInfo.class", "size": 5021, "crc": 1173848289}, {"key": "androidx/room/util/TableInfoKt$toStringCommon$$inlined$sortedBy$1.class", "name": "androidx/room/util/TableInfoKt$toStringCommon$$inlined$sortedBy$1.class", "size": 1993, "crc": -736916228}, {"key": "androidx/room/util/TableInfoKt$toStringCommon$$inlined$sortedBy$2.class", "name": "androidx/room/util/TableInfoKt$toStringCommon$$inlined$sortedBy$2.class", "size": 1991, "crc": 441240810}, {"key": "androidx/room/util/TableInfoKt.class", "name": "androidx/room/util/TableInfoKt.class", "size": 11556, "crc": 1285282941}, {"key": "androidx/room/util/UUIDUtil.class", "name": "androidx/room/util/UUIDUtil.class", "size": 1783, "crc": -1452075602}, {"key": "androidx/room/util/ViewInfo$Companion.class", "name": "androidx/room/util/ViewInfo$Companion.class", "size": 2121, "crc": -1189064974}, {"key": "androidx/room/util/ViewInfo.class", "name": "androidx/room/util/ViewInfo.class", "size": 2934, "crc": 1531885672}, {"key": "androidx/room/util/ViewInfoKt.class", "name": "androidx/room/util/ViewInfoKt.class", "size": 2221, "crc": 1663914426}, {"key": "androidx/room/IMultiInstanceInvalidationCallback$Default.class", "name": "androidx/room/IMultiInstanceInvalidationCallback$Default.class", "size": 757, "crc": -2074588616}, {"key": "androidx/room/IMultiInstanceInvalidationCallback$Stub$Proxy.class", "name": "androidx/room/IMultiInstanceInvalidationCallback$Stub$Proxy.class", "size": 1567, "crc": -28184919}, {"key": "androidx/room/IMultiInstanceInvalidationCallback$Stub.class", "name": "androidx/room/IMultiInstanceInvalidationCallback$Stub.class", "size": 2031, "crc": -799856325}, {"key": "androidx/room/IMultiInstanceInvalidationCallback.class", "name": "androidx/room/IMultiInstanceInvalidationCallback.class", "size": 980, "crc": 2132674421}, {"key": "androidx/room/IMultiInstanceInvalidationService$Default.class", "name": "androidx/room/IMultiInstanceInvalidationService$Default.class", "size": 1224, "crc": 262936090}, {"key": "androidx/room/IMultiInstanceInvalidationService$Stub$Proxy.class", "name": "androidx/room/IMultiInstanceInvalidationService$Stub$Proxy.class", "size": 2766, "crc": -1253781928}, {"key": "androidx/room/IMultiInstanceInvalidationService$Stub.class", "name": "androidx/room/IMultiInstanceInvalidationService$Stub.class", "size": 2966, "crc": 911920060}, {"key": "androidx/room/IMultiInstanceInvalidationService.class", "name": "androidx/room/IMultiInstanceInvalidationService.class", "size": 1190, "crc": 1842413980}, {"key": "androidx/room/paging/LimitOffsetDataSource$1.class", "name": "androidx/room/paging/LimitOffsetDataSource$1.class", "size": 1292, "crc": -2004631912}, {"key": "androidx/room/paging/LimitOffsetDataSource.class", "name": "androidx/room/paging/LimitOffsetDataSource.class", "size": 9192, "crc": -2013525028}, {"key": "META-INF/androidx.room_room-runtime.version", "name": "META-INF/androidx.room_room-runtime.version", "size": 6, "crc": -737443587}, {"key": "META-INF/room-runtime_release.kotlin_module", "name": "META-INF/room-runtime_release.kotlin_module", "size": 972, "crc": 1039864303}]