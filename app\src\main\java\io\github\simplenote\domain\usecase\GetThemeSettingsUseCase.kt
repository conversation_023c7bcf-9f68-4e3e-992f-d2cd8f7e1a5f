package io.github.simplenote.domain.usecase

import io.github.simplenote.data.preferences.ThemePreferences
import io.github.simplenote.domain.model.ThemeSettings
import kotlinx.coroutines.flow.Flow

/**
 * Use case for getting theme settings.
 */
class GetThemeSettingsUseCase(
    private val themePreferences: ThemePreferences
) {
    
    /**
     * Get theme settings as a Flow
     */
    operator fun invoke(): Flow<ThemeSettings> {
        return themePreferences.themeSettings
    }
}
