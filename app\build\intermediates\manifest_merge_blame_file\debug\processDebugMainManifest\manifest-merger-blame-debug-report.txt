1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.github.simplenote"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="30"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
12        android:name="io.github.simplenote.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="io.github.simplenote.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:5:5-26:19
18        android:name="io.github.simplenote.SimpleNoteApplication"
18-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:6:9-46
19        android:allowBackup="true"
19-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:7:9-35
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8734d346b2e96ba5091fe03752072eab\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
21        android:dataExtractionRules="@xml/data_extraction_rules"
21-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:8:9-65
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:fullBackupContent="@xml/backup_rules"
24-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:9:9-54
25        android:icon="@mipmap/ic_launcher"
25-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:10:9-43
26        android:label="@string/app_name"
26-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:11:9-41
27        android:roundIcon="@mipmap/ic_launcher_round"
27-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:12:9-54
28        android:supportsRtl="true"
28-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:13:9-35
29        android:theme="@style/Theme.SimpleNOTE" >
29-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:14:9-48
30        <activity
30-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:15:9-25:20
31            android:name="io.github.simplenote.MainActivity"
31-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true"
32-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:17:13-36
33            android:label="@string/app_name"
33-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:18:13-45
34            android:theme="@style/Theme.SimpleNOTE" >
34-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:19:13-52
35            <intent-filter>
35-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:20:13-24:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:21:17-69
36-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:23:17-77
38-->C:\Users\<USER>\STUDIO\SimpleNOTE\app\src\main\AndroidManifest.xml:23:27-74
39            </intent-filter>
40        </activity>
41        <activity
41-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
42            android:name="androidx.compose.ui.tooling.PreviewActivity"
42-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
43            android:exported="true" />
43-->[androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fb9eb848c6406db46c405c970e0ebf0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
44
45        <provider
45-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
47            android:authorities="io.github.simplenote.androidx-startup"
47-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\119fd154df6e719216dad2725071985e\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7f33709b810836e551decc7afb4035a\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <service
60-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
61            android:name="androidx.room.MultiInstanceInvalidationService"
61-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
62            android:directBootAware="true"
62-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
63            android:exported="false" />
63-->[androidx.room:room-runtime-android:2.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9de00b0a8f98c04b3d873de1b4cd7a6b\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
64
65        <receiver
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
66            android:name="androidx.profileinstaller.ProfileInstallReceiver"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
67            android:directBootAware="false"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
68            android:enabled="true"
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
69            android:exported="true"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
70            android:permission="android.permission.DUMP" >
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
72                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
75                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
78                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
81                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f3b4ccf6a4f65ed47738ac64958db1d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
82            </intent-filter>
83        </receiver>
84    </application>
85
86</manifest>
