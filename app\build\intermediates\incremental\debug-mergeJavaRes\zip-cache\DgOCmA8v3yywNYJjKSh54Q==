[{"key": "org/koin/androidx/compose/KoinAndroidContextKt.class", "name": "org/koin/androidx/compose/KoinAndroidContextKt.class", "size": 3524, "crc": 1220865105}, {"key": "org/koin/androidx/compose/ViewModelKt.class", "name": "org/koin/androidx/compose/ViewModelKt.class", "size": 3746, "crc": -1877390843}, {"key": "org/koin/androidx/compose/scope/KoinAndroidScopeKt$KoinActivityScope$1.class", "name": "org/koin/androidx/compose/scope/KoinAndroidScopeKt$KoinActivityScope$1.class", "size": 2322, "crc": 825943641}, {"key": "org/koin/androidx/compose/scope/KoinAndroidScopeKt$KoinFragmentScope$1.class", "name": "org/koin/androidx/compose/scope/KoinAndroidScopeKt$KoinFragmentScope$1.class", "size": 2322, "crc": 403173740}, {"key": "org/koin/androidx/compose/scope/KoinAndroidScopeKt.class", "name": "org/koin/androidx/compose/scope/KoinAndroidScopeKt.class", "size": 7556, "crc": -1578927619}, {"key": "META-INF/koin-androidx-compose_release.kotlin_module", "name": "META-INF/koin-androidx-compose_release.kotlin_module", "size": 143, "crc": 2058187681}]