package io.github.simplenote.domain.model

import androidx.compose.ui.graphics.Color

/**
 * Enum representing the 24 predefined colors for notes.
 * Each color has a unique ID and Color value for UI display.
 */
enum class NoteColor(
    val id: Int,
    val color: Color,
    val displayName: String
) {
    // Warm Colors
    RED(1, Color(0xFFEF5350), "Red"),
    PINK(2, Color(0xFFEC407A), "Pink"),
    PURPLE(3, Color(0xFFAB47BC), "Purple"),
    DEEP_PURPLE(4, Color(0xFF7E57C2), "Deep Purple"),
    INDIGO(5, Color(0xFF5C6BC0), "Indigo"),
    BLUE(6, Color(0xFF42A5F5), "Blue"),
    
    // Cool Colors
    LIGHT_BLUE(7, Color(0xFF29B6F6), "Light Blue"),
    CYAN(8, Color(0xFF26C6DA), "Cyan"),
    TEAL(9, Color(0xFF26A69A), "Teal"),
    GRE<PERSON>(10, Color(0xFF66BB6A), "Green"),
    LIGHT_GREEN(11, Color(0xFF9CCC65), "Light Green"),
    LIME(12, Color(0xFFD4E157), "Lime"),
    
    // Warm Neutrals
    YELLOW(13, Color(0xFFFFEE58), "Yellow"),
    AMBER(14, Color(0xFFFFCA28), "Amber"),
    ORANGE(15, Color(0xFFFF9800), "Orange"),
    DEEP_ORANGE(16, Color(0xFFFF7043), "Deep Orange"),
    BROWN(17, Color(0xFF8D6E63), "Brown"),
    GREY(18, Color(0xFF78909C), "Grey"),
    
    // Pastels
    PASTEL_PINK(19, Color(0xFFF8BBD9), "Pastel Pink"),
    PASTEL_BLUE(20, Color(0xFFB3E5FC), "Pastel Blue"),
    PASTEL_GREEN(21, Color(0xFFC8E6C9), "Pastel Green"),
    PASTEL_YELLOW(22, Color(0xFFFFF9C4), "Pastel Yellow"),
    PASTEL_ORANGE(23, Color(0xFFFFE0B2), "Pastel Orange"),
    PASTEL_PURPLE(24, Color(0xFFE1BEE7), "Pastel Purple");

    companion object {
        /**
         * Get NoteColor by ID, defaults to YELLOW if not found
         */
        fun fromId(id: Int): NoteColor = entries.find { it.id == id } ?: YELLOW
        
        /**
         * Get all colors as a list for UI display
         */
        fun getAllColors(): List<NoteColor> = entries.toList()
        
        /**
         * Default color for new notes
         */
        val DEFAULT = YELLOW
    }
}
