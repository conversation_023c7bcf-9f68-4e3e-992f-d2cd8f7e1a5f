---
type: "always_apply"
---

# Performance Optimization Guidelines

## Database Optimization
- Add **proper indices** to frequently queried columns
- Use **pagination** for large datasets
- Implement **database migrations** carefully
- Use **transactions** for multiple operations
- Profile **query performance** regularly

## Compose Performance
```

// Use keys in lazy lists
LazyColumn {
items(messages, key = { it.id }) { message ->
MessageItem(message)
}
}

// Use remember for expensive calculations
val expensiveValue = remember(dependency) {
performExpensiveCalculation(dependency)
}

```

## Network Optimization
- Implement **response caching**
- Use **request deduplication**
- Apply **timeout configurations**
- Implement **retry mechanisms**
- Use **efficient serialization**

## Memory Management
- Avoid **memory leaks** in ViewModels
- Use **weak references** when appropriate
- Profile **memory usage** regularly
- Implement **lazy initialization**
- Clear **disposables** properly
