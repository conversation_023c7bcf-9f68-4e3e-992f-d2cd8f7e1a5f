[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 46, "crc": 1570773839}, {"key": "META-INF/kotlinx-serialization-json.kotlin_module", "name": "META-INF/kotlinx-serialization-json.kotlin_module", "size": 532, "crc": -1227064250}, {"key": "kotlinx/serialization/json/ClassDiscriminatorMode.class", "name": "kotlinx/serialization/json/ClassDiscriminatorMode.class", "size": 1988, "crc": 93648633}, {"key": "kotlinx/serialization/json/DecodeSequenceMode.class", "name": "kotlinx/serialization/json/DecodeSequenceMode.class", "size": 2019, "crc": 1641952998}, {"key": "kotlinx/serialization/json/Json$Default.class", "name": "kotlinx/serialization/json/Json$Default.class", "size": 1418, "crc": 2004564675}, {"key": "kotlinx/serialization/json/Json.class", "name": "kotlinx/serialization/json/Json.class", "size": 8932, "crc": 1722543347}, {"key": "kotlinx/serialization/json/JsonArray$Companion.class", "name": "kotlinx/serialization/json/JsonArray$Companion.class", "size": 1392, "crc": -1909466400}, {"key": "kotlinx/serialization/json/JsonArray.class", "name": "kotlinx/serialization/json/JsonArray.class", "size": 7770, "crc": 1317642655}, {"key": "kotlinx/serialization/json/JsonArrayBuilder.class", "name": "kotlinx/serialization/json/JsonArrayBuilder.class", "size": 2072, "crc": 1787037587}, {"key": "kotlinx/serialization/json/JsonArraySerializer$JsonArrayDescriptor.class", "name": "kotlinx/serialization/json/JsonArraySerializer$JsonArrayDescriptor.class", "size": 3770, "crc": -1885217278}, {"key": "kotlinx/serialization/json/JsonArraySerializer.class", "name": "kotlinx/serialization/json/JsonArraySerializer.class", "size": 3320, "crc": -1396125872}, {"key": "kotlinx/serialization/json/JsonBuilder.class", "name": "kotlinx/serialization/json/JsonBuilder.class", "size": 10352, "crc": 1964971528}, {"key": "kotlinx/serialization/json/JsonClassDiscriminator$Impl.class", "name": "kotlinx/serialization/json/JsonClassDiscriminator$Impl.class", "size": 906, "crc": -1540873911}, {"key": "kotlinx/serialization/json/JsonClassDiscriminator.class", "name": "kotlinx/serialization/json/JsonClassDiscriminator.class", "size": 1091, "crc": 603086697}, {"key": "kotlinx/serialization/json/JsonConfiguration.class", "name": "kotlinx/serialization/json/JsonConfiguration.class", "size": 7780, "crc": 1597990862}, {"key": "kotlinx/serialization/json/JsonContentPolymorphicSerializer.class", "name": "kotlinx/serialization/json/JsonContentPolymorphicSerializer.class", "size": 6388, "crc": -600074987}, {"key": "kotlinx/serialization/json/JsonDecoder$DefaultImpls.class", "name": "kotlinx/serialization/json/JsonDecoder$DefaultImpls.class", "size": 2102, "crc": -2061520786}, {"key": "kotlinx/serialization/json/JsonDecoder.class", "name": "kotlinx/serialization/json/JsonDecoder.class", "size": 2322, "crc": -893048373}, {"key": "kotlinx/serialization/json/JsonDslMarker.class", "name": "kotlinx/serialization/json/JsonDslMarker.class", "size": 607, "crc": 722592444}, {"key": "kotlinx/serialization/json/JsonElement$Companion.class", "name": "kotlinx/serialization/json/JsonElement$Companion.class", "size": 1406, "crc": 532290420}, {"key": "kotlinx/serialization/json/JsonElement.class", "name": "kotlinx/serialization/json/JsonElement.class", "size": 1311, "crc": -535570835}, {"key": "kotlinx/serialization/json/JsonElementBuildersKt.class", "name": "kotlinx/serialization/json/JsonElementBuildersKt.class", "size": 10976, "crc": 1266296113}, {"key": "kotlinx/serialization/json/JsonElementKt.class", "name": "kotlinx/serialization/json/JsonElementKt.class", "size": 13553, "crc": -1808582196}, {"key": "kotlinx/serialization/json/JsonElementSerializer.class", "name": "kotlinx/serialization/json/JsonElementSerializer.class", "size": 6639, "crc": 1369401616}, {"key": "kotlinx/serialization/json/JsonElementSerializersKt$defer$1.class", "name": "kotlinx/serialization/json/JsonElementSerializersKt$defer$1.class", "size": 3507, "crc": -1113138956}, {"key": "kotlinx/serialization/json/JsonElementSerializersKt.class", "name": "kotlinx/serialization/json/JsonElementSerializersKt.class", "size": 3499, "crc": -429193043}, {"key": "kotlinx/serialization/json/JsonEncoder$DefaultImpls.class", "name": "kotlinx/serialization/json/JsonEncoder$DefaultImpls.class", "size": 2547, "crc": 311615215}, {"key": "kotlinx/serialization/json/JsonEncoder.class", "name": "kotlinx/serialization/json/JsonEncoder.class", "size": 2887, "crc": -145056671}, {"key": "kotlinx/serialization/json/JsonIgnoreUnknownKeys$Impl.class", "name": "kotlinx/serialization/json/JsonIgnoreUnknownKeys$Impl.class", "size": 526, "crc": 125077817}, {"key": "kotlinx/serialization/json/JsonIgnoreUnknownKeys.class", "name": "kotlinx/serialization/json/JsonIgnoreUnknownKeys.class", "size": 988, "crc": 724634551}, {"key": "kotlinx/serialization/json/JsonImpl.class", "name": "kotlinx/serialization/json/JsonImpl.class", "size": 2205, "crc": -789049056}, {"key": "kotlinx/serialization/json/JsonKt.class", "name": "kotlinx/serialization/json/JsonKt.class", "size": 4289, "crc": 236719057}, {"key": "kotlinx/serialization/json/JsonLiteral.class", "name": "kotlinx/serialization/json/JsonLiteral.class", "size": 3911, "crc": -1813678095}, {"key": "kotlinx/serialization/json/JsonLiteralSerializer.class", "name": "kotlinx/serialization/json/JsonLiteralSerializer.class", "size": 6454, "crc": 2048447146}, {"key": "kotlinx/serialization/json/JsonNames$Impl.class", "name": "kotlinx/serialization/json/JsonNames$Impl.class", "size": 862, "crc": 856041835}, {"key": "kotlinx/serialization/json/JsonNames.class", "name": "kotlinx/serialization/json/JsonNames.class", "size": 1015, "crc": -1369900125}, {"key": "kotlinx/serialization/json/JsonNamingStrategy$Builtins$KebabCase$1.class", "name": "kotlinx/serialization/json/JsonNamingStrategy$Builtins$KebabCase$1.class", "size": 1750, "crc": 1010023942}, {"key": "kotlinx/serialization/json/JsonNamingStrategy$Builtins$SnakeCase$1.class", "name": "kotlinx/serialization/json/JsonNamingStrategy$Builtins$SnakeCase$1.class", "size": 1750, "crc": 393668389}, {"key": "kotlinx/serialization/json/JsonNamingStrategy$Builtins.class", "name": "kotlinx/serialization/json/JsonNamingStrategy$Builtins.class", "size": 4686, "crc": -1346236811}, {"key": "kotlinx/serialization/json/JsonNamingStrategy.class", "name": "kotlinx/serialization/json/JsonNamingStrategy.class", "size": 1186, "crc": 951725698}, {"key": "kotlinx/serialization/json/JsonNull.class", "name": "kotlinx/serialization/json/JsonNull.class", "size": 1685, "crc": 291289298}, {"key": "kotlinx/serialization/json/JsonNullSerializer.class", "name": "kotlinx/serialization/json/JsonNullSerializer.class", "size": 3697, "crc": -1904563734}, {"key": "kotlinx/serialization/json/JsonObject$Companion.class", "name": "kotlinx/serialization/json/JsonObject$Companion.class", "size": 1399, "crc": 999460099}, {"key": "kotlinx/serialization/json/JsonObject.class", "name": "kotlinx/serialization/json/JsonObject.class", "size": 10123, "crc": -238217419}, {"key": "kotlinx/serialization/json/JsonObjectBuilder.class", "name": "kotlinx/serialization/json/JsonObjectBuilder.class", "size": 1951, "crc": 1155816690}, {"key": "kotlinx/serialization/json/JsonObjectSerializer$JsonObjectDescriptor.class", "name": "kotlinx/serialization/json/JsonObjectSerializer$JsonObjectDescriptor.class", "size": 4026, "crc": 1393678920}, {"key": "kotlinx/serialization/json/JsonObjectSerializer.class", "name": "kotlinx/serialization/json/JsonObjectSerializer.class", "size": 3585, "crc": 1057503128}, {"key": "kotlinx/serialization/json/JsonPrimitive$Companion.class", "name": "kotlinx/serialization/json/JsonPrimitive$Companion.class", "size": 1420, "crc": -1299053242}, {"key": "kotlinx/serialization/json/JsonPrimitive.class", "name": "kotlinx/serialization/json/JsonPrimitive.class", "size": 1616, "crc": 1479147962}, {"key": "kotlinx/serialization/json/JsonPrimitiveSerializer.class", "name": "kotlinx/serialization/json/JsonPrimitiveSerializer.class", "size": 5044, "crc": 318112704}, {"key": "kotlinx/serialization/json/JsonSchemaCacheKt.class", "name": "kotlinx/serialization/json/JsonSchemaCacheKt.class", "size": 1290, "crc": -130333508}, {"key": "kotlinx/serialization/json/JsonTransformingSerializer.class", "name": "kotlinx/serialization/json/JsonTransformingSerializer.class", "size": 4055, "crc": 1905811511}, {"key": "kotlinx/serialization/json/JvmStreamsKt.class", "name": "kotlinx/serialization/json/JvmStreamsKt.class", "size": 6981, "crc": -867397868}, {"key": "kotlinx/serialization/json/internal/AbstractJsonLexer$fail$1.class", "name": "kotlinx/serialization/json/internal/AbstractJsonLexer$fail$1.class", "size": 2192, "crc": 459779986}, {"key": "kotlinx/serialization/json/internal/AbstractJsonLexer.class", "name": "kotlinx/serialization/json/internal/AbstractJsonLexer.class", "size": 24950, "crc": 1230911961}, {"key": "kotlinx/serialization/json/internal/AbstractJsonLexerKt.class", "name": "kotlinx/serialization/json/internal/AbstractJsonLexerKt.class", "size": 3876, "crc": -1670206943}, {"key": "kotlinx/serialization/json/internal/AbstractJsonTreeDecoder.class", "name": "kotlinx/serialization/json/internal/AbstractJsonTreeDecoder.class", "size": 37084, "crc": -1990875162}, {"key": "kotlinx/serialization/json/internal/AbstractJsonTreeEncoder$inlineUnquotedLiteralEncoder$1.class", "name": "kotlinx/serialization/json/internal/AbstractJsonTreeEncoder$inlineUnquotedLiteralEncoder$1.class", "size": 2398, "crc": -2013817153}, {"key": "kotlinx/serialization/json/internal/AbstractJsonTreeEncoder$inlineUnsignedNumberEncoder$1.class", "name": "kotlinx/serialization/json/internal/AbstractJsonTreeEncoder$inlineUnsignedNumberEncoder$1.class", "size": 3202, "crc": -1538989444}, {"key": "kotlinx/serialization/json/internal/AbstractJsonTreeEncoder.class", "name": "kotlinx/serialization/json/internal/AbstractJsonTreeEncoder.class", "size": 23206, "crc": -181605848}, {"key": "kotlinx/serialization/json/internal/ArrayAsSequence.class", "name": "kotlinx/serialization/json/internal/ArrayAsSequence.class", "size": 2474, "crc": -960736732}, {"key": "kotlinx/serialization/json/internal/ArrayPoolsKt.class", "name": "kotlinx/serialization/json/internal/ArrayPoolsKt.class", "size": 1431, "crc": 2109896359}, {"key": "kotlinx/serialization/json/internal/ByteArrayPool.class", "name": "kotlinx/serialization/json/internal/ByteArrayPool.class", "size": 1283, "crc": 1669046269}, {"key": "kotlinx/serialization/json/internal/ByteArrayPool8k.class", "name": "kotlinx/serialization/json/internal/ByteArrayPool8k.class", "size": 1287, "crc": -972171732}, {"key": "kotlinx/serialization/json/internal/ByteArrayPoolBase.class", "name": "kotlinx/serialization/json/internal/ByteArrayPoolBase.class", "size": 2703, "crc": -790590904}, {"key": "kotlinx/serialization/json/internal/CharArrayPool.class", "name": "kotlinx/serialization/json/internal/CharArrayPool.class", "size": 1283, "crc": 1296610842}, {"key": "kotlinx/serialization/json/internal/CharArrayPoolBase.class", "name": "kotlinx/serialization/json/internal/CharArrayPoolBase.class", "size": 2699, "crc": 246680497}, {"key": "kotlinx/serialization/json/internal/CharArrayPoolBatchSize.class", "name": "kotlinx/serialization/json/internal/CharArrayPoolBatchSize.class", "size": 2270, "crc": -1390661809}, {"key": "kotlinx/serialization/json/internal/CharMappings.class", "name": "kotlinx/serialization/json/internal/CharMappings.class", "size": 2199, "crc": -777215481}, {"key": "kotlinx/serialization/json/internal/CharsetReader.class", "name": "kotlinx/serialization/json/internal/CharsetReader.class", "size": 5701, "crc": -203330013}, {"key": "kotlinx/serialization/json/internal/Composer.class", "name": "kotlinx/serialization/json/internal/Composer.class", "size": 3304, "crc": -1905771952}, {"key": "kotlinx/serialization/json/internal/ComposerForUnquotedLiterals.class", "name": "kotlinx/serialization/json/internal/ComposerForUnquotedLiterals.class", "size": 1570, "crc": 2135300436}, {"key": "kotlinx/serialization/json/internal/ComposerForUnsignedNumbers.class", "name": "kotlinx/serialization/json/internal/ComposerForUnsignedNumbers.class", "size": 2396, "crc": 223808069}, {"key": "kotlinx/serialization/json/internal/ComposerWithPrettyPrint.class", "name": "kotlinx/serialization/json/internal/ComposerWithPrettyPrint.class", "size": 2883, "crc": 1125390049}, {"key": "kotlinx/serialization/json/internal/ComposersKt.class", "name": "kotlinx/serialization/json/internal/ComposersKt.class", "size": 1634, "crc": -2094546082}, {"key": "kotlinx/serialization/json/internal/CreateMapForCacheKt.class", "name": "kotlinx/serialization/json/internal/CreateMapForCacheKt.class", "size": 858, "crc": 1559763041}, {"key": "kotlinx/serialization/json/internal/DescriptorSchemaCache$Key.class", "name": "kotlinx/serialization/json/internal/DescriptorSchemaCache$Key.class", "size": 768, "crc": 1693264587}, {"key": "kotlinx/serialization/json/internal/DescriptorSchemaCache.class", "name": "kotlinx/serialization/json/internal/DescriptorSchemaCache.class", "size": 4998, "crc": 592297702}, {"key": "kotlinx/serialization/json/internal/FormatLanguageKt.class", "name": "kotlinx/serialization/json/internal/FormatLanguageKt.class", "size": 576, "crc": -726635864}, {"key": "kotlinx/serialization/json/internal/InternalJsonReader.class", "name": "kotlinx/serialization/json/internal/InternalJsonReader.class", "size": 702, "crc": -26758297}, {"key": "kotlinx/serialization/json/internal/InternalJsonReaderCodePointImpl.class", "name": "kotlinx/serialization/json/internal/InternalJsonReaderCodePointImpl.class", "size": 2013, "crc": 1804180153}, {"key": "kotlinx/serialization/json/internal/InternalJsonWriter$Companion.class", "name": "kotlinx/serialization/json/internal/InternalJsonWriter$Companion.class", "size": 2441, "crc": 367539569}, {"key": "kotlinx/serialization/json/internal/InternalJsonWriter.class", "name": "kotlinx/serialization/json/internal/InternalJsonWriter.class", "size": 1222, "crc": -405143445}, {"key": "kotlinx/serialization/json/internal/JavaStreamSerialReader.class", "name": "kotlinx/serialization/json/internal/JavaStreamSerialReader.class", "size": 1742, "crc": 527670699}, {"key": "kotlinx/serialization/json/internal/JsonDecoderForUnsignedTypes.class", "name": "kotlinx/serialization/json/internal/JsonDecoderForUnsignedTypes.class", "size": 5827, "crc": -268504215}, {"key": "kotlinx/serialization/json/internal/JsonDecodingException.class", "name": "kotlinx/serialization/json/internal/JsonDecodingException.class", "size": 960, "crc": 2066314824}, {"key": "kotlinx/serialization/json/internal/JsonElementMarker$origin$1.class", "name": "kotlinx/serialization/json/internal/JsonElementMarker$origin$1.class", "size": 2007, "crc": 1356331719}, {"key": "kotlinx/serialization/json/internal/JsonElementMarker.class", "name": "kotlinx/serialization/json/internal/JsonElementMarker.class", "size": 2747, "crc": 1231876547}, {"key": "kotlinx/serialization/json/internal/JsonEncodingException.class", "name": "kotlinx/serialization/json/internal/JsonEncodingException.class", "size": 960, "crc": 439766258}, {"key": "kotlinx/serialization/json/internal/JsonException.class", "name": "kotlinx/serialization/json/internal/JsonException.class", "size": 933, "crc": 1765168621}, {"key": "kotlinx/serialization/json/internal/JsonExceptionsKt.class", "name": "kotlinx/serialization/json/internal/JsonExceptionsKt.class", "size": 7199, "crc": 2096589537}, {"key": "kotlinx/serialization/json/internal/JsonFriendModuleApi.class", "name": "kotlinx/serialization/json/internal/JsonFriendModuleApi.class", "size": 784, "crc": -1291120639}, {"key": "kotlinx/serialization/json/internal/JsonIteratorArrayWrapped.class", "name": "kotlinx/serialization/json/internal/JsonIteratorArrayWrapped.class", "size": 6499, "crc": 289516040}, {"key": "kotlinx/serialization/json/internal/JsonIteratorKt$WhenMappings.class", "name": "kotlinx/serialization/json/internal/JsonIteratorKt$WhenMappings.class", "size": 913, "crc": -1326988503}, {"key": "kotlinx/serialization/json/internal/JsonIteratorKt.class", "name": "kotlinx/serialization/json/internal/JsonIteratorKt.class", "size": 5799, "crc": 1344408281}, {"key": "kotlinx/serialization/json/internal/JsonIteratorWsSeparated.class", "name": "kotlinx/serialization/json/internal/JsonIteratorWsSeparated.class", "size": 3340, "crc": -1746325676}, {"key": "kotlinx/serialization/json/internal/JsonNamesMapKt$tryCoerceValue$1.class", "name": "kotlinx/serialization/json/internal/JsonNamesMapKt$tryCoerceValue$1.class", "size": 1666, "crc": -1999976855}, {"key": "kotlinx/serialization/json/internal/JsonNamesMapKt.class", "name": "kotlinx/serialization/json/internal/JsonNamesMapKt.class", "size": 17608, "crc": 490955327}, {"key": "kotlinx/serialization/json/internal/JsonPath$Tombstone.class", "name": "kotlinx/serialization/json/internal/JsonPath$Tombstone.class", "size": 828, "crc": -295018499}, {"key": "kotlinx/serialization/json/internal/JsonPath.class", "name": "kotlinx/serialization/json/internal/JsonPath.class", "size": 4683, "crc": 1044534547}, {"key": "kotlinx/serialization/json/internal/JsonPrimitiveDecoder.class", "name": "kotlinx/serialization/json/internal/JsonPrimitiveDecoder.class", "size": 3301, "crc": -996595504}, {"key": "kotlinx/serialization/json/internal/JsonPrimitiveEncoder.class", "name": "kotlinx/serialization/json/internal/JsonPrimitiveEncoder.class", "size": 3591, "crc": 1986774744}, {"key": "kotlinx/serialization/json/internal/JsonSerializersModuleValidator.class", "name": "kotlinx/serialization/json/internal/JsonSerializersModuleValidator.class", "size": 8038, "crc": 1502621510}, {"key": "kotlinx/serialization/json/internal/JsonStreamsKt$decodeToSequenceByReader$$inlined$Sequence$1.class", "name": "kotlinx/serialization/json/internal/JsonStreamsKt$decodeToSequenceByReader$$inlined$Sequence$1.class", "size": 2191, "crc": -16431471}, {"key": "kotlinx/serialization/json/internal/JsonStreamsKt.class", "name": "kotlinx/serialization/json/internal/JsonStreamsKt.class", "size": 8448, "crc": 1245994394}, {"key": "kotlinx/serialization/json/internal/JsonToJavaStreamWriter.class", "name": "kotlinx/serialization/json/internal/JsonToJavaStreamWriter.class", "size": 12937, "crc": -287352083}, {"key": "kotlinx/serialization/json/internal/JsonToStringWriter.class", "name": "kotlinx/serialization/json/internal/JsonToStringWriter.class", "size": 4141, "crc": -1927509697}, {"key": "kotlinx/serialization/json/internal/JsonTreeDecoder.class", "name": "kotlinx/serialization/json/internal/JsonTreeDecoder.class", "size": 13358, "crc": 1126948507}, {"key": "kotlinx/serialization/json/internal/JsonTreeEncoder.class", "name": "kotlinx/serialization/json/internal/JsonTreeEncoder.class", "size": 3641, "crc": 840970717}, {"key": "kotlinx/serialization/json/internal/JsonTreeListDecoder.class", "name": "kotlinx/serialization/json/internal/JsonTreeListDecoder.class", "size": 2966, "crc": -1175782858}, {"key": "kotlinx/serialization/json/internal/JsonTreeListEncoder.class", "name": "kotlinx/serialization/json/internal/JsonTreeListEncoder.class", "size": 2933, "crc": 618032761}, {"key": "kotlinx/serialization/json/internal/JsonTreeMapDecoder.class", "name": "kotlinx/serialization/json/internal/JsonTreeMapDecoder.class", "size": 3725, "crc": -1267705620}, {"key": "kotlinx/serialization/json/internal/JsonTreeMapEncoder.class", "name": "kotlinx/serialization/json/internal/JsonTreeMapEncoder.class", "size": 3298, "crc": -356416772}, {"key": "kotlinx/serialization/json/internal/JsonTreeReader$readDeepRecursive$1.class", "name": "kotlinx/serialization/json/internal/JsonTreeReader$readDeepRecursive$1.class", "size": 4622, "crc": 738089123}, {"key": "kotlinx/serialization/json/internal/JsonTreeReader$readObject$2.class", "name": "kotlinx/serialization/json/internal/JsonTreeReader$readObject$2.class", "size": 2239, "crc": 1533652966}, {"key": "kotlinx/serialization/json/internal/JsonTreeReader.class", "name": "kotlinx/serialization/json/internal/JsonTreeReader.class", "size": 11392, "crc": 220625960}, {"key": "kotlinx/serialization/json/internal/PolymorphicKt$WhenMappings.class", "name": "kotlinx/serialization/json/internal/PolymorphicKt$WhenMappings.class", "size": 909, "crc": -1800056317}, {"key": "kotlinx/serialization/json/internal/PolymorphicKt.class", "name": "kotlinx/serialization/json/internal/PolymorphicKt.class", "size": 13722, "crc": -1684361951}, {"key": "kotlinx/serialization/json/internal/ReaderJsonLexer.class", "name": "kotlinx/serialization/json/internal/ReaderJsonLexer.class", "size": 9166, "crc": -809867921}, {"key": "kotlinx/serialization/json/internal/ReaderJsonLexerKt.class", "name": "kotlinx/serialization/json/internal/ReaderJsonLexerKt.class", "size": 2255, "crc": -1587029465}, {"key": "kotlinx/serialization/json/internal/ReaderJsonLexerWithComments.class", "name": "kotlinx/serialization/json/internal/ReaderJsonLexerWithComments.class", "size": 5813, "crc": 1564416702}, {"key": "kotlinx/serialization/json/internal/SchemaCacheKt.class", "name": "kotlinx/serialization/json/internal/SchemaCacheKt.class", "size": 492, "crc": -623846852}, {"key": "kotlinx/serialization/json/internal/StreamingJsonDecoder$DiscriminatorHolder.class", "name": "kotlinx/serialization/json/internal/StreamingJsonDecoder$DiscriminatorHolder.class", "size": 1050, "crc": -1708751769}, {"key": "kotlinx/serialization/json/internal/StreamingJsonDecoder$WhenMappings.class", "name": "kotlinx/serialization/json/internal/StreamingJsonDecoder$WhenMappings.class", "size": 947, "crc": 1424184214}, {"key": "kotlinx/serialization/json/internal/StreamingJsonDecoder.class", "name": "kotlinx/serialization/json/internal/StreamingJsonDecoder.class", "size": 25602, "crc": -576813913}, {"key": "kotlinx/serialization/json/internal/StreamingJsonDecoderKt.class", "name": "kotlinx/serialization/json/internal/StreamingJsonDecoderKt.class", "size": 4399, "crc": 1465178604}, {"key": "kotlinx/serialization/json/internal/StreamingJsonEncoder$WhenMappings.class", "name": "kotlinx/serialization/json/internal/StreamingJsonEncoder$WhenMappings.class", "size": 904, "crc": 895564916}, {"key": "kotlinx/serialization/json/internal/StreamingJsonEncoder.class", "name": "kotlinx/serialization/json/internal/StreamingJsonEncoder.class", "size": 18489, "crc": 386669992}, {"key": "kotlinx/serialization/json/internal/StreamingJsonEncoderKt.class", "name": "kotlinx/serialization/json/internal/StreamingJsonEncoderKt.class", "size": 2783, "crc": -1924578016}, {"key": "kotlinx/serialization/json/internal/StringJsonLexer.class", "name": "kotlinx/serialization/json/internal/StringJsonLexer.class", "size": 8215, "crc": -2129170710}, {"key": "kotlinx/serialization/json/internal/StringJsonLexerKt.class", "name": "kotlinx/serialization/json/internal/StringJsonLexerKt.class", "size": 1472, "crc": -17931883}, {"key": "kotlinx/serialization/json/internal/StringJsonLexerWithComments.class", "name": "kotlinx/serialization/json/internal/StringJsonLexerWithComments.class", "size": 4187, "crc": -1757703510}, {"key": "kotlinx/serialization/json/internal/StringOpsKt.class", "name": "kotlinx/serialization/json/internal/StringOpsKt.class", "size": 3633, "crc": 1395730684}, {"key": "kotlinx/serialization/json/internal/SuppressAnimalSniffer.class", "name": "kotlinx/serialization/json/internal/SuppressAnimalSniffer.class", "size": 891, "crc": -1635836366}, {"key": "kotlinx/serialization/json/internal/TreeJsonDecoderKt.class", "name": "kotlinx/serialization/json/internal/TreeJsonDecoderKt.class", "size": 3899, "crc": 923687363}, {"key": "kotlinx/serialization/json/internal/TreeJsonEncoderKt.class", "name": "kotlinx/serialization/json/internal/TreeJsonEncoderKt.class", "size": 5495, "crc": -772266386}, {"key": "kotlinx/serialization/json/internal/WriteMode.class", "name": "kotlinx/serialization/json/internal/WriteMode.class", "size": 2256, "crc": 658077078}, {"key": "kotlinx/serialization/json/internal/WriteModeKt.class", "name": "kotlinx/serialization/json/internal/WriteModeKt.class", "size": 5863, "crc": -1547758839}, {"key": "META-INF/versions/9/module-info.class", "name": "META-INF/versions/9/module-info.class", "size": 266, "crc": -1715260864}]