[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/room-common.kotlin_module", "name": "META-INF/room-common.kotlin_module", "size": 24, "crc": -813369971}, {"key": "androidx/room/AmbiguousColumnResolver$Match.class", "name": "androidx/room/AmbiguousColumnResolver$Match.class", "size": 1641, "crc": -1835412274}, {"key": "androidx/room/AmbiguousColumnResolver$ResultColumn.class", "name": "androidx/room/AmbiguousColumnResolver$ResultColumn.class", "size": 2849, "crc": 869855646}, {"key": "androidx/room/AmbiguousColumnResolver$Solution$Companion.class", "name": "androidx/room/AmbiguousColumnResolver$Solution$Companion.class", "size": 5437, "crc": -415451008}, {"key": "androidx/room/AmbiguousColumnResolver$Solution.class", "name": "androidx/room/AmbiguousColumnResolver$Solution.class", "size": 3005, "crc": 186984774}, {"key": "androidx/room/AmbiguousColumnResolver.class", "name": "androidx/room/AmbiguousColumnResolver.class", "size": 17176, "crc": -1729860602}, {"key": "androidx/room/AutoMigration.class", "name": "androidx/room/AutoMigration.class", "size": 1094, "crc": -1514753881}, {"key": "androidx/room/BuiltInTypeConverters$State.class", "name": "androidx/room/BuiltInTypeConverters$State.class", "size": 2030, "crc": -1645162964}, {"key": "androidx/room/BuiltInTypeConverters.class", "name": "androidx/room/BuiltInTypeConverters.class", "size": 1118, "crc": 227333134}, {"key": "androidx/room/ColumnInfo$Collate.class", "name": "androidx/room/ColumnInfo$Collate.class", "size": 755, "crc": -226298827}, {"key": "androidx/room/ColumnInfo$Companion.class", "name": "androidx/room/ColumnInfo$Companion.class", "size": 1621, "crc": 2068370285}, {"key": "androidx/room/ColumnInfo$SQLiteTypeAffinity.class", "name": "androidx/room/ColumnInfo$SQLiteTypeAffinity.class", "size": 700, "crc": 889733333}, {"key": "androidx/room/ColumnInfo.class", "name": "androidx/room/ColumnInfo.class", "size": 2300, "crc": -492529876}, {"key": "androidx/room/ConstructedBy.class", "name": "androidx/room/ConstructedBy.class", "size": 933, "crc": -127171178}, {"key": "androidx/room/Dao.class", "name": "androidx/room/Dao.class", "size": 748, "crc": -323960033}, {"key": "androidx/room/Database.class", "name": "androidx/room/Database.class", "size": 1367, "crc": -1975738911}, {"key": "androidx/room/DatabaseView.class", "name": "androidx/room/DatabaseView.class", "size": 938, "crc": 742037037}, {"key": "androidx/room/Delete.class", "name": "androidx/room/Delete.class", "size": 982, "crc": 573996333}, {"key": "androidx/room/DeleteColumn$Entries.class", "name": "androidx/room/DeleteColumn$Entries.class", "size": 1010, "crc": 2086322129}, {"key": "androidx/room/DeleteColumn.class", "name": "androidx/room/DeleteColumn.class", "size": 1085, "crc": 1773358918}, {"key": "androidx/room/DeleteTable$Entries.class", "name": "androidx/room/DeleteTable$Entries.class", "size": 1004, "crc": -614680838}, {"key": "androidx/room/DeleteTable.class", "name": "androidx/room/DeleteTable.class", "size": 1030, "crc": 857417100}, {"key": "androidx/room/Embedded.class", "name": "androidx/room/Embedded.class", "size": 907, "crc": 1265749089}, {"key": "androidx/room/Entity.class", "name": "androidx/room/Entity.class", "size": 1430, "crc": -760602906}, {"key": "androidx/room/ForeignKey$Action.class", "name": "androidx/room/ForeignKey$Action.class", "size": 664, "crc": -1774601151}, {"key": "androidx/room/ForeignKey$Companion.class", "name": "androidx/room/ForeignKey$Companion.class", "size": 971, "crc": 853962088}, {"key": "androidx/room/ForeignKey.class", "name": "androidx/room/ForeignKey.class", "size": 1849, "crc": -82137568}, {"key": "androidx/room/Fts3.class", "name": "androidx/room/Fts3.class", "size": 989, "crc": 1714929145}, {"key": "androidx/room/Fts4.class", "name": "androidx/room/Fts4.class", "size": 1877, "crc": 1729119232}, {"key": "androidx/room/FtsOptions$MatchInfo.class", "name": "androidx/room/FtsOptions$MatchInfo.class", "size": 1893, "crc": -1441719849}, {"key": "androidx/room/FtsOptions$Order.class", "name": "androidx/room/FtsOptions$Order.class", "size": 1860, "crc": 2049579169}, {"key": "androidx/room/FtsOptions.class", "name": "androidx/room/FtsOptions.class", "size": 1239, "crc": -1728351933}, {"key": "androidx/room/Ignore.class", "name": "androidx/room/Ignore.class", "size": 835, "crc": 1956676276}, {"key": "androidx/room/Index$Order.class", "name": "androidx/room/Index$Order.class", "size": 1815, "crc": 1854698527}, {"key": "androidx/room/Index.class", "name": "androidx/room/Index.class", "size": 1161, "crc": -1146956698}, {"key": "androidx/room/Insert.class", "name": "androidx/room/Insert.class", "size": 1148, "crc": 1848896742}, {"key": "androidx/room/Junction.class", "name": "androidx/room/Junction.class", "size": 1003, "crc": 1242849360}, {"key": "androidx/room/MapColumn.class", "name": "androidx/room/MapColumn.class", "size": 943, "crc": -769910386}, {"key": "androidx/room/MapInfo.class", "name": "androidx/room/MapInfo.class", "size": 1157, "crc": 820326802}, {"key": "androidx/room/OnConflictStrategy$Companion.class", "name": "androidx/room/OnConflictStrategy$Companion.class", "size": 1283, "crc": 554844720}, {"key": "androidx/room/OnConflictStrategy.class", "name": "androidx/room/OnConflictStrategy.class", "size": 1137, "crc": 2145575467}, {"key": "androidx/room/PrimaryKey.class", "name": "androidx/room/PrimaryKey.class", "size": 902, "crc": -133277680}, {"key": "androidx/room/ProvidedAutoMigrationSpec.class", "name": "androidx/room/ProvidedAutoMigrationSpec.class", "size": 814, "crc": -134312246}, {"key": "androidx/room/ProvidedTypeConverter.class", "name": "androidx/room/ProvidedTypeConverter.class", "size": 802, "crc": -2016619797}, {"key": "androidx/room/Query.class", "name": "androidx/room/Query.class", "size": 863, "crc": -742413816}, {"key": "androidx/room/RawQuery.class", "name": "androidx/room/RawQuery.class", "size": 998, "crc": 968980663}, {"key": "androidx/room/Relation.class", "name": "androidx/room/Relation.class", "size": 1395, "crc": 329451482}, {"key": "androidx/room/RenameColumn$Entries.class", "name": "androidx/room/RenameColumn$Entries.class", "size": 1010, "crc": 817160692}, {"key": "androidx/room/RenameColumn.class", "name": "androidx/room/RenameColumn.class", "size": 1141, "crc": -1048282339}, {"key": "androidx/room/RenameTable$Entries.class", "name": "androidx/room/RenameTable$Entries.class", "size": 1004, "crc": -1411358716}, {"key": "androidx/room/RenameTable.class", "name": "androidx/room/RenameTable.class", "size": 1085, "crc": 442177666}, {"key": "androidx/room/RewriteQueriesToDropUnusedColumns.class", "name": "androidx/room/RewriteQueriesToDropUnusedColumns.class", "size": 868, "crc": 351936745}, {"key": "androidx/room/RoomMasterTable.class", "name": "androidx/room/RoomMasterTable.class", "size": 2282, "crc": 1914998609}, {"key": "androidx/room/RoomWarnings$Companion.class", "name": "androidx/room/RoomWarnings$Companion.class", "size": 2093, "crc": 519382005}, {"key": "androidx/room/RoomWarnings.class", "name": "androidx/room/RoomWarnings.class", "size": 2918, "crc": -939968823}, {"key": "androidx/room/SkipQueryVerification.class", "name": "androidx/room/SkipQueryVerification.class", "size": 832, "crc": 1742148554}, {"key": "androidx/room/Transaction.class", "name": "androidx/room/Transaction.class", "size": 785, "crc": 1546423922}, {"key": "androidx/room/TypeConverter.class", "name": "androidx/room/TypeConverter.class", "size": 791, "crc": -101101503}, {"key": "androidx/room/TypeConverters.class", "name": "androidx/room/TypeConverters.class", "size": 1253, "crc": 585162924}, {"key": "androidx/room/Update.class", "name": "androidx/room/Update.class", "size": 1148, "crc": **********}, {"key": "androidx/room/Upsert.class", "name": "androidx/room/Upsert.class", "size": 982, "crc": **********}, {"key": "META-INF/androidx/room/room-common/LICENSE.txt", "name": "META-INF/androidx/room/room-common/LICENSE.txt", "size": 10175, "crc": -106424664}]