package io.github.simplenote.domain.usecase

import io.github.simplenote.data.preferences.ThemePreferences
import io.github.simplenote.domain.model.ThemeMode
import io.github.simplenote.domain.model.ThemeSettings

/**
 * Use case for updating theme settings.
 */
class UpdateThemeSettingsUseCase(
    private val themePreferences: ThemePreferences
) {
    
    /**
     * Update theme mode
     */
    suspend fun updateThemeMode(themeMode: ThemeMode) {
        themePreferences.updateThemeMode(themeMode)
    }
    
    /**
     * Update dynamic theme setting
     */
    suspend fun updateUseDynamicTheme(useDynamicTheme: Boolean) {
        themePreferences.updateUseDynamicTheme(useDynamicTheme)
    }
    
    /**
     * Update all theme settings
     */
    suspend fun updateThemeSettings(themeSettings: ThemeSettings) {
        themePreferences.updateThemeSettings(themeSettings)
    }
}
