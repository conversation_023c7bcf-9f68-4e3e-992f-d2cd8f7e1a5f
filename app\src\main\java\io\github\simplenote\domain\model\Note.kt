package io.github.simplenote.domain.model

import kotlinx.datetime.Instant

/**
 * Domain model representing a Note.
 * This is the core business entity used throughout the application.
 */
data class Note(
    val id: Long = 0L,
    val title: String,
    val content: String,
    val color: NoteColor = NoteColor.DEFAULT,
    val createdAt: Instant,
    val updatedAt: Instant
) {
    companion object {
        /**
         * Create a new note with current timestamp
         */
        fun create(
            title: String,
            content: String,
            color: NoteColor = NoteColor.DEFAULT,
            timestamp: Instant
        ): Note = Note(
            title = title,
            content = content,
            color = color,
            createdAt = timestamp,
            updatedAt = timestamp
        )
    }
    
    /**
     * Update the note with new values and current timestamp
     */
    fun update(
        title: String = this.title,
        content: String = this.content,
        color: NoteColor = this.color,
        timestamp: Instant
    ): Note = copy(
        title = title,
        content = content,
        color = color,
        updatedAt = timestamp
    )
    
    /**
     * Check if the note is empty (both title and content are blank)
     */
    val isEmpty: Boolean
        get() = title.isBlank() && content.isBlank()
    
    /**
     * Get a preview of the note content (first 100 characters)
     */
    val preview: String
        get() = content.take(100).let { preview ->
            if (content.length > 100) "$preview..." else preview
        }
}
