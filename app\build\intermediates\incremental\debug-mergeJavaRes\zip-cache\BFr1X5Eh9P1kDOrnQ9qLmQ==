[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 160, "crc": -835500566}, {"key": "META-INF/kotlin-android-extensions-runtime.kotlin_module", "name": "META-INF/kotlin-android-extensions-runtime.kotlin_module", "size": 24, "crc": -1111743755}, {"key": "kotlinx/android/extensions/CacheImplementation$Companion.class", "name": "kotlinx/android/extensions/CacheImplementation$Companion.class", "size": 1213, "crc": 140420271}, {"key": "kotlinx/android/extensions/CacheImplementation.class", "name": "kotlinx/android/extensions/CacheImplementation.class", "size": 2416, "crc": 347669202}, {"key": "kotlinx/android/extensions/ContainerOptions.class", "name": "kotlinx/android/extensions/ContainerOptions.class", "size": 760, "crc": 2087980103}, {"key": "kotlinx/android/extensions/LayoutContainer.class", "name": "kotlinx/android/extensions/LayoutContainer.class", "size": 617, "crc": -1755010923}, {"key": "kotlinx/android/parcel/IgnoredOnParcel.class", "name": "kotlinx/android/parcel/IgnoredOnParcel.class", "size": 778, "crc": 314959443}, {"key": "kotlinx/android/parcel/Parceler$DefaultImpls.class", "name": "kotlinx/android/parcel/Parceler$DefaultImpls.class", "size": 933, "crc": 1163865465}, {"key": "kotlinx/android/parcel/Parceler.class", "name": "kotlinx/android/parcel/Parceler.class", "size": 1162, "crc": -532188771}, {"key": "kotlinx/android/parcel/Parcelize.class", "name": "kotlinx/android/parcel/Parcelize.class", "size": 806, "crc": -844911176}, {"key": "kotlinx/android/parcel/RawValue.class", "name": "kotlinx/android/parcel/RawValue.class", "size": 814, "crc": 1186390907}, {"key": "kotlinx/android/parcel/TypeParceler$Container.class", "name": "kotlinx/android/parcel/TypeParceler$Container.class", "size": 883, "crc": 2072132443}, {"key": "kotlinx/android/parcel/TypeParceler.class", "name": "kotlinx/android/parcel/TypeParceler.class", "size": 1282, "crc": 1342335667}, {"key": "kotlinx/android/parcel/WriteWith.class", "name": "kotlinx/android/parcel/WriteWith.class", "size": 992, "crc": 61107936}]