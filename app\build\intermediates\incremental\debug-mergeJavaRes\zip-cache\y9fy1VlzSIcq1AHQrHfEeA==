[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/stately-concurrent-collections.kotlin_module", "name": "META-INF/stately-concurrent-collections.kotlin_module", "size": 24, "crc": -1111743755}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$add$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$add$1.class", "size": 1844, "crc": -1965289560}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$addAll$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$addAll$1.class", "size": 1915, "crc": -269669990}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$blockCollection$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$blockCollection$1.class", "size": 2168, "crc": 56140270}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$clear$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$clear$1.class", "size": 1599, "crc": -243880004}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$contains$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$contains$1.class", "size": 1859, "crc": -231038515}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$containsAll$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$containsAll$1.class", "size": 1930, "crc": -764750289}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$isEmpty$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$isEmpty$1.class", "size": 1734, "crc": 552540746}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$iterator$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$iterator$1.class", "size": 2095, "crc": -1960222114}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$remove$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$remove$1.class", "size": 1853, "crc": -1047861900}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$removeAll$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$removeAll$1.class", "size": 1924, "crc": 791024901}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$retainAll$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$retainAll$1.class", "size": 1924, "crc": -138689233}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection$size$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection$size$1.class", "size": 1740, "crc": -840032787}, {"key": "co/touchlab/stately/collections/ConcurrentMutableCollection.class", "name": "co/touchlab/stately/collections/ConcurrentMutableCollection.class", "size": 9644, "crc": 1101561981}, {"key": "co/touchlab/stately/collections/ConcurrentMutableIterator$hasNext$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableIterator$hasNext$1.class", "size": 1714, "crc": 1217420358}, {"key": "co/touchlab/stately/collections/ConcurrentMutableIterator$next$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableIterator$next$1.class", "size": 1440, "crc": 2050601975}, {"key": "co/touchlab/stately/collections/ConcurrentMutableIterator$remove$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableIterator$remove$1.class", "size": 1582, "crc": -2096967659}, {"key": "co/touchlab/stately/collections/ConcurrentMutableIterator.class", "name": "co/touchlab/stately/collections/ConcurrentMutableIterator.class", "size": 3981, "crc": -2071362571}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$add$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$add$1.class", "size": 1708, "crc": 1892718820}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$addAll$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$addAll$1.class", "size": 1902, "crc": 528990079}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$block$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$block$1.class", "size": 2121, "crc": -728072156}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$get$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$get$1.class", "size": 1467, "crc": -430066435}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$indexOf$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$indexOf$1.class", "size": 1790, "crc": 935525361}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$lastIndexOf$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$lastIndexOf$1.class", "size": 1802, "crc": 1545938631}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$listIterator$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$listIterator$1.class", "size": 2009, "crc": 1133860703}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$listIterator$2.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$listIterator$2.class", "size": 2062, "crc": -1843145886}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$removeAt$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$removeAt$1.class", "size": 1496, "crc": 1360353632}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$set$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$set$1.class", "size": 1589, "crc": 2122985295}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList$subList$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList$subList$1.class", "size": 1929, "crc": -29421536}, {"key": "co/touchlab/stately/collections/ConcurrentMutableList.class", "name": "co/touchlab/stately/collections/ConcurrentMutableList.class", "size": 9179, "crc": -834318511}, {"key": "co/touchlab/stately/collections/ConcurrentMutableListIterator$add$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableListIterator$add$1.class", "size": 1734, "crc": 855644265}, {"key": "co/touchlab/stately/collections/ConcurrentMutableListIterator$hasPrevious$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableListIterator$hasPrevious$1.class", "size": 1759, "crc": 1442260218}, {"key": "co/touchlab/stately/collections/ConcurrentMutableListIterator$nextIndex$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableListIterator$nextIndex$1.class", "size": 1753, "crc": -1699210477}, {"key": "co/touchlab/stately/collections/ConcurrentMutableListIterator$previous$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableListIterator$previous$1.class", "size": 1485, "crc": -265864124}, {"key": "co/touchlab/stately/collections/ConcurrentMutableListIterator$previousIndex$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableListIterator$previousIndex$1.class", "size": 1765, "crc": -830256742}, {"key": "co/touchlab/stately/collections/ConcurrentMutableListIterator$set$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableListIterator$set$1.class", "size": 1734, "crc": -1446378546}, {"key": "co/touchlab/stately/collections/ConcurrentMutableListIterator.class", "name": "co/touchlab/stately/collections/ConcurrentMutableListIterator.class", "size": 5667, "crc": 250775431}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$block$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$block$1.class", "size": 2133, "crc": -1187606165}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$clear$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$clear$1.class", "size": 1541, "crc": -1869125252}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$computeIfAbsent$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$computeIfAbsent$1.class", "size": 2155, "crc": 677160111}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$containsKey$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$containsKey$1.class", "size": 1806, "crc": 183907673}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$containsValue$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$containsValue$1.class", "size": 1814, "crc": -88590018}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$entries$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$entries$1.class", "size": 2073, "crc": -360563186}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$get$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$get$1.class", "size": 1623, "crc": 621804606}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$isEmpty$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$isEmpty$1.class", "size": 1676, "crc": -1010965045}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$keys$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$keys$1.class", "size": 1947, "crc": -1033959430}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$put$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$put$1.class", "size": 1722, "crc": -1999548493}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$putAll$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$putAll$1.class", "size": 1703, "crc": 2135026253}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$remove$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$remove$1.class", "size": 1632, "crc": 924560585}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$size$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$size$1.class", "size": 1682, "crc": -879376933}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap$values$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap$values$1.class", "size": 2002, "crc": 336442672}, {"key": "co/touchlab/stately/collections/ConcurrentMutableMap.class", "name": "co/touchlab/stately/collections/ConcurrentMutableMap.class", "size": 10857, "crc": -1161080619}, {"key": "co/touchlab/stately/collections/ConcurrentMutableSet$block$1.class", "name": "co/touchlab/stately/collections/ConcurrentMutableSet$block$1.class", "size": 2108, "crc": 707808950}, {"key": "co/touchlab/stately/collections/ConcurrentMutableSet.class", "name": "co/touchlab/stately/collections/ConcurrentMutableSet.class", "size": 3847, "crc": 1320609607}, {"key": "co/touchlab/stately/collections/MutableCollectionWrapper.class", "name": "co/touchlab/stately/collections/MutableCollectionWrapper.class", "size": 4160, "crc": -1062349862}, {"key": "co/touchlab/stately/collections/MutableListWrapper.class", "name": "co/touchlab/stately/collections/MutableListWrapper.class", "size": 3884, "crc": 15610012}, {"key": "co/touchlab/stately/collections/MutableMapWrapper.class", "name": "co/touchlab/stately/collections/MutableMapWrapper.class", "size": 4234, "crc": 1401941042}, {"key": "co/touchlab/stately/collections/MutableSetWrapper.class", "name": "co/touchlab/stately/collections/MutableSetWrapper.class", "size": 1779, "crc": 1891678526}]