[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 144, "crc": -1450413433}, {"key": "META-INF/parcelize-runtime.kotlin_module", "name": "META-INF/parcelize-runtime.kotlin_module", "size": 66, "crc": 908591982}, {"key": "kotlinx/parcelize/IgnoredOnParcel.class", "name": "kotlinx/parcelize/IgnoredOnParcel.class", "size": 752, "crc": 1973514986}, {"key": "kotlinx/parcelize/ParcelableCreatorKt.class", "name": "kotlinx/parcelize/ParcelableCreatorKt.class", "size": 1705, "crc": 293845526}, {"key": "kotlinx/parcelize/Parceler$DefaultImpls.class", "name": "kotlinx/parcelize/Parceler$DefaultImpls.class", "size": 908, "crc": -475168365}, {"key": "kotlinx/parcelize/Parceler.class", "name": "kotlinx/parcelize/Parceler.class", "size": 1131, "crc": 1780545470}, {"key": "kotlinx/parcelize/Parcelize.class", "name": "kotlinx/parcelize/Parcelize.class", "size": 780, "crc": 1290817084}, {"key": "kotlinx/parcelize/RawValue.class", "name": "kotlinx/parcelize/RawValue.class", "size": 788, "crc": -987545025}, {"key": "kotlinx/parcelize/TypeParceler$Container.class", "name": "kotlinx/parcelize/TypeParceler$Container.class", "size": 868, "crc": -2136077714}, {"key": "kotlinx/parcelize/TypeParceler.class", "name": "kotlinx/parcelize/TypeParceler.class", "size": 1236, "crc": 1412709860}, {"key": "kotlinx/parcelize/WriteWith.class", "name": "kotlinx/parcelize/WriteWith.class", "size": 956, "crc": 1384587109}]