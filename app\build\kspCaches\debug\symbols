{"src\\main\\java\\io\\github\\simplenote\\presentation\\components\\ErrorState.kt": ["ErrorStatePreview:io.github.simplenote.presentation.components", "getErrorTitle:io.github.simplenote.presentation.components", "ErrorState:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\domain\\model\\ThemeSettings.kt": ["useDynamicTheme:io.github.simplenote.domain.model.ThemeSettings", "component2:io.github.simplenote.domain.model.ThemeSettings", "themeMode:io.github.simplenote.domain.model.ThemeSettings", "DEFAULT:io.github.simplenote.domain.model.ThemeSettings.Companion", "copy:io.github.simplenote.domain.model.ThemeSettings", "ThemeSettings:io.github.simplenote.domain.model", "component1:io.github.simplenote.domain.model.ThemeSettings", "Companion:io.github.simplenote.domain.model.ThemeSettings"], "src\\main\\java\\io\\github\\simplenote\\presentation\\screen\\NotesScreen.kt": ["NotesScreen:io.github.simplenote.presentation.screen", "NotesScreenPreview:io.github.simplenote.presentation.screen"], "src\\main\\java\\io\\github\\simplenote\\presentation\\screen\\NoteEditorScreen.kt": ["formatDate:io.github.simplenote.presentation.screen", "NoteEditorScreenPreview:io.github.simplenote.presentation.screen", "NoteEditorScreen:io.github.simplenote.presentation.screen"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\ScrollableSearchBarLayout.kt": ["ScrollableSearchBarTopAppBar:io.github.simplenote.presentation.components", "SimpleScrollableSearchBarLayout:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\domain\\usecase\\CreateNoteUseCase.kt": ["validateNoteInput:io.github.simplenote.domain.usecase.CreateNoteUseCase", "CreateNoteUseCase:io.github.simplenote.domain.usecase", "repository:io.github.simplenote.domain.usecase.CreateNoteUseCase", "invoke:io.github.simplenote.domain.usecase.CreateNoteUseCase", "createFromExisting:io.github.simplenote.domain.usecase.CreateNoteUseCase"], "src\\main\\java\\io\\github\\simplenote\\data\\util\\SampleDataGenerator.kt": ["SampleDataGenerator:io.github.simplenote.data.util", "sampleContents:io.github.simplenote.data.util.SampleDataGenerator", "sampleTitles:io.github.simplenote.data.util.SampleDataGenerator", "generateSampleNotes:io.github.simplenote.data.util.SampleDataGenerator"], "src\\main\\java\\io\\github\\simplenote\\domain\\usecase\\GetThemeSettingsUseCase.kt": ["GetThemeSettingsUseCase:io.github.simplenote.domain.usecase", "invoke:io.github.simplenote.domain.usecase.GetThemeSettingsUseCase", "themePreferences:io.github.simplenote.domain.usecase.GetThemeSettingsUseCase"], "src\\main\\java\\io\\github\\simplenote\\presentation\\screen\\SettingsScreen.kt": ["SettingsScreenPreview:io.github.simplenote.presentation.screen", "SettingsScreen:io.github.simplenote.presentation.screen"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\ColorBottomSheet.kt": ["ColorBottomSheetPreview:io.github.simplenote.presentation.components", "ColorBottomSheet:io.github.simplenote.presentation.components", "SampleKeepBottomSheetRight:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\domain\\usecase\\UpdateNoteUseCase.kt": ["updateContent:io.github.simplenote.domain.usecase.UpdateNoteUseCase", "repository:io.github.simplenote.domain.usecase.UpdateNoteUseCase", "UpdateNoteUseCase:io.github.simplenote.domain.usecase", "updateTitle:io.github.simplenote.domain.usecase.UpdateNoteUseCase", "invoke:io.github.simplenote.domain.usecase.UpdateNoteUseCase", "validateNoteInput:io.github.simplenote.domain.usecase.UpdateNoteUseCase", "updateColor:io.github.simplenote.domain.usecase.UpdateNoteUseCase"], "src\\main\\java\\io\\github\\simplenote\\domain\\model\\ThemeMode.kt": ["values:io.github.simplenote.domain.model.ThemeMode", "Companion:io.github.simplenote.domain.model.ThemeMode", "displayName:io.github.simplenote.domain.model.ThemeMode", "LIGHT:io.github.simplenote.domain.model.ThemeMode", "fromString:io.github.simplenote.domain.model.ThemeMode.Companion", "SYSTEM:io.github.simplenote.domain.model.ThemeMode", "entries:io.github.simplenote.domain.model.ThemeMode", "valueOf:io.github.simplenote.domain.model.ThemeMode", "DARK:io.github.simplenote.domain.model.ThemeMode", "ThemeMode:io.github.simplenote.domain.model", "DEFAULT:io.github.simplenote.domain.model.ThemeMode.Companion"], "src\\main\\java\\io\\github\\simplenote\\di\\PreferencesModule.kt": ["preferencesModule:io.github.simplenote.di", "preferencesDataStore:io.github.simplenote.di"], "src\\main\\java\\io\\github\\simplenote\\data\\preferences\\ThemePreferences.kt": ["dataStore:io.github.simplenote.data.preferences.ThemePreferences", "THEME_MODE_KEY:io.github.simplenote.data.preferences.ThemePreferences.Companion", "updateThemeSettings:io.github.simplenote.data.preferences.ThemePreferences", "USE_DYNAMIC_THEME_KEY:io.github.simplenote.data.preferences.ThemePreferences.Companion", "Companion:io.github.simplenote.data.preferences.ThemePreferences", "updateThemeMode:io.github.simplenote.data.preferences.ThemePreferences", "themeSettings:io.github.simplenote.data.preferences.ThemePreferences", "updateUseDynamicTheme:io.github.simplenote.data.preferences.ThemePreferences", "ThemePreferences:io.github.simplenote.data.preferences"], "src\\main\\java\\io\\github\\simplenote\\domain\\model\\Note.kt": ["createdAt:io.github.simplenote.domain.model.Note", "isEmpty:io.github.simplenote.domain.model.Note", "id:io.github.simplenote.domain.model.Note", "copy:io.github.simplenote.domain.model.Note", "content:io.github.simplenote.domain.model.Note", "title:io.github.simplenote.domain.model.Note", "create:io.github.simplenote.domain.model.Note.Companion", "color:io.github.simplenote.domain.model.Note", "updatedAt:io.github.simplenote.domain.model.Note", "component2:io.github.simplenote.domain.model.Note", "update:io.github.simplenote.domain.model.Note", "component3:io.github.simplenote.domain.model.Note", "Note:io.github.simplenote.domain.model", "component1:io.github.simplenote.domain.model.Note", "Companion:io.github.simplenote.domain.model.Note", "component6:io.github.simplenote.domain.model.Note", "component4:io.github.simplenote.domain.model.Note", "component5:io.github.simplenote.domain.model.Note", "preview:io.github.simplenote.domain.model.Note"], "src\\main\\java\\io\\github\\simplenote\\di\\RepositoryModule.kt": ["repositoryModule:io.github.simplenote.di"], "src\\main\\java\\io\\github\\simplenote\\presentation\\viewmodel\\SettingsViewModel.kt": ["updateThemeSettingsUseCase:io.github.simplenote.presentation.viewmodel.SettingsViewModel", "updateThemeMode:io.github.simplenote.presentation.viewmodel.SettingsViewModel", "getThemeSettingsUseCase:io.github.simplenote.presentation.viewmodel.SettingsViewModel", "SettingsViewModel:io.github.simplenote.presentation.viewmodel", "themeSettings:io.github.simplenote.presentation.viewmodel.SettingsViewModel", "updateUseDynamicTheme:io.github.simplenote.presentation.viewmodel.SettingsViewModel"], "src\\main\\java\\io\\github\\simplenote\\presentation\\model\\UiState.kt": ["copy:io.github.simplenote.presentation.model.NotesUiState", "note:io.github.simplenote.presentation.model.NavigationEvent.ShowDeleteConfirmation", "component1:io.github.simplenote.presentation.model.NoteEditorUiEvent.LoadNote", "color:io.github.simplenote.presentation.model.NoteEditorUiEvent.ChangeColor", "valueOf:io.github.simplenote.presentation.model.NotesUiState.SortBy", "component1:io.github.simplenote.presentation.model.NotesUiEvent.SearchNotes", "isLoading:io.github.simplenote.presentation.model.NoteEditorUiState", "title:io.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateTitle", "NoteEditorUiState:io.github.simplenote.presentation.model", "component2:io.github.simplenote.presentation.model.NotesUiState", "component4:io.github.simplenote.presentation.model.NoteEditorUiState", "ShowColorBottomSheet:io.github.simplenote.presentation.model.NotesUiEvent", "copy:io.github.simplenote.presentation.model.NoteEditorUiEvent.LoadNote", "ShowError:io.github.simplenote.presentation.model.NavigationEvent", "error:io.github.simplenote.presentation.model.NavigationEvent.ShowError", "content:io.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateContent", "ChangeNoteColor:io.github.simplenote.presentation.model.NotesUiEvent", "UPDATED_DATE:io.github.simplenote.presentation.model.NotesUiState.SortBy", "note:io.github.simplenote.presentation.model.NotesUiEvent.DeleteNote", "ShowDeleteConfirmation:io.github.simplenote.presentation.model.NavigationEvent", "ClearError:io.github.simplenote.presentation.model.NotesUiEvent", "UpdateTitle:io.github.simplenote.presentation.model.NoteEditorUiEvent", "component1:io.github.simplenote.presentation.model.NavigationEvent.NavigateToNoteEditor", "DeleteNote:io.github.simplenote.presentation.model.NotesUiEvent", "title:io.github.simplenote.presentation.model.NoteEditorUiState", "selectedNoteId:io.github.simplenote.presentation.model.NotesUiState", "component3:io.github.simplenote.presentation.model.NotesUiState", "ChangeSortOrder:io.github.simplenote.presentation.model.NotesUiEvent", "component3:io.github.simplenote.presentation.model.NoteEditorUiState", "copy:io.github.simplenote.presentation.model.NotesUiEvent.SearchNotes", "SaveNote:io.github.simplenote.presentation.model.NoteEditorUiEvent", "component1:io.github.simplenote.presentation.model.NavigationEvent.ShowError", "isColorBottomSheetVisible:io.github.simplenote.presentation.model.NotesUiState", "isLoading:io.github.simplenote.presentation.model.NotesUiState", "note:io.github.simplenote.presentation.model.NoteEditorUiState", "NavigateBack:io.github.simplenote.presentation.model.NavigationEvent", "noteId:io.github.simplenote.presentation.model.NotesUiEvent.SelectNote", "NavigateBack:io.github.simplenote.presentation.model.NoteEditorUiEvent", "CreateNewNote:io.github.simplenote.presentation.model.NotesUiEvent", "component1:io.github.simplenote.presentation.model.NotesUiEvent.ChangeSortOrder", "component4:io.github.simplenote.presentation.model.NotesUiState", "copy:io.github.simplenote.presentation.model.NotesUiEvent.SelectNote", "copy:io.github.simplenote.presentation.model.NavigationEvent.ShowError", "component6:io.github.simplenote.presentation.model.NoteEditorUiState", "color:io.github.simplenote.presentation.model.NotesUiEvent.ChangeNoteColor", "UpdateContent:io.github.simplenote.presentation.model.NoteEditorUiEvent", "query:io.github.simplenote.presentation.model.NotesUiEvent.SearchNotes", "SortBy:io.github.simplenote.presentation.model.NotesUiState", "LoadNotes:io.github.simplenote.presentation.model.NotesUiEvent", "HideColorBottomSheet:io.github.simplenote.presentation.model.NotesUiEvent", "copy:io.github.simplenote.presentation.model.NotesUiEvent.ChangeSortOrder", "component1:io.github.simplenote.presentation.model.NotesUiEvent.SelectNote", "notes:io.github.simplenote.presentation.model.NotesUiState", "component5:io.github.simplenote.presentation.model.NotesUiState", "component5:io.github.simplenote.presentation.model.NoteEditorUiState", "sortBy:io.github.simplenote.presentation.model.NotesUiState", "component1:io.github.simplenote.presentation.model.NotesUiEvent.DeleteNote", "copy:io.github.simplenote.presentation.model.NavigationEvent.ShowDeleteConfirmation", "values:io.github.simplenote.presentation.model.NotesUiState.SortBy", "copy:io.github.simplenote.presentation.model.NotesUiEvent.DeleteNote", "sortBy:io.github.simplenote.presentation.model.NotesUiEvent.ChangeSortOrder", "entries:io.github.simplenote.presentation.model.NotesUiState.SortBy", "component6:io.github.simplenote.presentation.model.NotesUiState", "component8:io.github.simplenote.presentation.model.NoteEditorUiState", "component1:io.github.simplenote.presentation.model.NavigationEvent.ShowDeleteConfirmation", "LoadNote:io.github.simplenote.presentation.model.NoteEditorUiEvent", "NavigateToNoteEditor:io.github.simplenote.presentation.model.NavigationEvent", "TITLE:io.github.simplenote.presentation.model.NotesUiState.SortBy", "SearchNotes:io.github.simplenote.presentation.model.NotesUiEvent", "noteId:io.github.simplenote.presentation.model.NoteEditorUiEvent.LoadNote", "ClearError:io.github.simplenote.presentation.model.NoteEditorUiEvent", "NotesUiEvent:io.github.simplenote.presentation.model", "component2:io.github.simplenote.presentation.model.NotesUiEvent.ChangeNoteColor", "NavigationEvent:io.github.simplenote.presentation.model", "SelectNote:io.github.simplenote.presentation.model.NotesUiEvent", "component7:io.github.simplenote.presentation.model.NotesUiState", "hasUnsavedChanges:io.github.simplenote.presentation.model.NoteEditorUiState", "component7:io.github.simplenote.presentation.model.NoteEditorUiState", "DeleteNote:io.github.simplenote.presentation.model.NoteEditorUiEvent", "copy:io.github.simplenote.presentation.model.NoteEditorUiEvent.ChangeColor", "ChangeColor:io.github.simplenote.presentation.model.NoteEditorUiEvent", "CREATED_DATE:io.github.simplenote.presentation.model.NotesUiState.SortBy", "isNewNote:io.github.simplenote.presentation.model.NoteEditorUiState", "copy:io.github.simplenote.presentation.model.NoteEditorUiState", "searchQuery:io.github.simplenote.presentation.model.NotesUiState", "noteId:io.github.simplenote.presentation.model.NavigationEvent.NavigateToNoteEditor", "component1:io.github.simplenote.presentation.model.NotesUiEvent.ChangeNoteColor", "component2:io.github.simplenote.presentation.model.NoteEditorUiState", "component1:io.github.simplenote.presentation.model.NoteEditorUiEvent.ChangeColor", "copy:io.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateContent", "copy:io.github.simplenote.presentation.model.NavigationEvent.NavigateToNoteEditor", "error:io.github.simplenote.presentation.model.NoteEditorUiState", "copy:io.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateTitle", "selectedColor:io.github.simplenote.presentation.model.NoteEditorUiState", "canSave:io.github.simplenote.presentation.model.NoteEditorUiState", "NotesUiState:io.github.simplenote.presentation.model", "isSaving:io.github.simplenote.presentation.model.NoteEditorUiState", "component1:io.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateContent", "error:io.github.simplenote.presentation.model.NotesUiState", "noteId:io.github.simplenote.presentation.model.NotesUiEvent.ChangeNoteColor", "NoteEditorUiEvent:io.github.simplenote.presentation.model", "content:io.github.simplenote.presentation.model.NoteEditorUiState", "component1:io.github.simplenote.presentation.model.NotesUiState", "component1:io.github.simplenote.presentation.model.NoteEditorUiState", "component1:io.github.simplenote.presentation.model.NoteEditorUiEvent.UpdateTitle", "component9:io.github.simplenote.presentation.model.NoteEditorUiState", "copy:io.github.simplenote.presentation.model.NotesUiEvent.ChangeNoteColor"], "src\\main\\java\\io\\github\\simplenote\\data\\local\\dao\\NoteDao.kt": ["NoteDao:io.github.simplenote.data.local.dao", "getAllNotesByTitle:io.github.simplenote.data.local.dao.NoteDao", "updateNoteColor:io.github.simplenote.data.local.dao.NoteDao", "deleteNote:io.github.simplenote.data.local.dao.NoteDao", "getAllNotes:io.github.simplenote.data.local.dao.NoteDao", "deleteAllNotes:io.github.simplenote.data.local.dao.NoteDao", "getNotesByColor:io.github.simplenote.data.local.dao.NoteDao", "searchNotes:io.github.simplenote.data.local.dao.NoteDao", "getNoteById:io.github.simplenote.data.local.dao.NoteDao", "updateNote:io.github.simplenote.data.local.dao.NoteDao", "getAllNotesByCreatedDate:io.github.simplenote.data.local.dao.NoteDao", "deleteNoteById:io.github.simplenote.data.local.dao.NoteDao", "insertNote:io.github.simplenote.data.local.dao.NoteDao", "insertNotes:io.github.simplenote.data.local.dao.NoteDao", "getNotesCount:io.github.simplenote.data.local.dao.NoteDao"], "src\\main\\java\\io\\github\\simplenote\\di\\ViewModelModule.kt": ["viewModelModule:io.github.simplenote.di"], "src\\main\\java\\io\\github\\simplenote\\data\\local\\entity\\NoteEntity.kt": ["copy:io.github.simplenote.data.local.entity.NoteEntity", "NoteEntity:io.github.simplenote.data.local.entity", "createdAt:io.github.simplenote.data.local.entity.NoteEntity", "id:io.github.simplenote.data.local.entity.NoteEntity", "component6:io.github.simplenote.data.local.entity.NoteEntity", "component5:io.github.simplenote.data.local.entity.NoteEntity", "component4:io.github.simplenote.data.local.entity.NoteEntity", "component3:io.github.simplenote.data.local.entity.NoteEntity", "colorId:io.github.simplenote.data.local.entity.NoteEntity", "component2:io.github.simplenote.data.local.entity.NoteEntity", "component1:io.github.simplenote.data.local.entity.NoteEntity", "content:io.github.simplenote.data.local.entity.NoteEntity", "updatedAt:io.github.simplenote.data.local.entity.NoteEntity", "toEntity:io.github.simplenote.data.local.entity", "title:io.github.simplenote.data.local.entity.NoteEntity", "toDomain:io.github.simplenote.data.local.entity"], "src\\main\\java\\io\\github\\simplenote\\domain\\repository\\NoteRepository.kt": ["createNote:io.github.simplenote.domain.repository.NoteRepository", "getNotesByColor:io.github.simplenote.domain.repository.NoteRepository", "updateNote:io.github.simplenote.domain.repository.NoteRepository", "getNoteById:io.github.simplenote.domain.repository.NoteRepository", "searchNotes:io.github.simplenote.domain.repository.NoteRepository", "getAllNotesByCreatedDate:io.github.simplenote.domain.repository.NoteRepository", "NoteRepository:io.github.simplenote.domain.repository", "getNotesCount:io.github.simplenote.domain.repository.NoteRepository", "getAllNotes:io.github.simplenote.domain.repository.NoteRepository", "deleteNoteById:io.github.simplenote.domain.repository.NoteRepository", "updateNoteColor:io.github.simplenote.domain.repository.NoteRepository", "deleteAllNotes:io.github.simplenote.domain.repository.NoteRepository", "getAllNotesByTitle:io.github.simplenote.domain.repository.NoteRepository", "deleteNote:io.github.simplenote.domain.repository.NoteRepository"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\LoadingState.kt": ["LoadingStatePreview:io.github.simplenote.presentation.components", "LoadingState:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\ui\\theme\\Type.kt": ["Typography:io.github.simplenote.ui.theme"], "src\\main\\java\\io\\github\\simplenote\\di\\UseCaseModule.kt": ["useCaseModule:io.github.simplenote.di"], "src\\main\\java\\io\\github\\simplenote\\domain\\usecase\\PopulateSampleDataUseCase.kt": ["PopulateSampleDataUseCase:io.github.simplenote.domain.usecase", "repository:io.github.simplenote.domain.usecase.PopulateSampleDataUseCase", "invoke:io.github.simplenote.domain.usecase.PopulateSampleDataUseCase"], "src\\main\\java\\io\\github\\simplenote\\navigation\\AppNavigation.kt": ["AppNavigation:io.github.simplenote.navigation"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\NoteItem.kt": ["NoteItemPreview:io.github.simplenote.presentation.components", "NoteItem:io.github.simplenote.presentation.components", "NoteItemPreview2:io.github.simplenote.presentation.components", "formatDate:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\navigation\\AppDestination.kt": ["noteId:io.github.simplenote.navigation.AppDestination.NoteEditor", "NoteEditor:io.github.simplenote.navigation.AppDestination", "copy:io.github.simplenote.navigation.AppDestination.NoteEditor", "Settings:io.github.simplenote.navigation.AppDestination", "component1:io.github.simplenote.navigation.AppDestination.NoteEditor", "AppDestination:io.github.simplenote.navigation", "Notes:io.github.simplenote.navigation.AppDestination"], "src\\main\\java\\io\\github\\simplenote\\domain\\usecase\\DeleteNoteUseCase.kt": ["getNotesCount:io.github.simplenote.domain.usecase.DeleteNoteUseCase", "DeleteNoteUseCase:io.github.simplenote.domain.usecase", "repository:io.github.simplenote.domain.usecase.DeleteNoteUseCase", "invoke:io.github.simplenote.domain.usecase.DeleteNoteUseCase", "deleteById:io.github.simplenote.domain.usecase.DeleteNoteUseCase", "deleteAll:io.github.simplenote.domain.usecase.DeleteNoteUseCase"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\DeleteConfirmationDialog.kt": ["DeleteConfirmationDialog:io.github.simplenote.presentation.components", "DeleteConfirmationDialogPreview:io.github.simplenote.presentation.components"], "build\\generated\\ksp\\debug\\kotlin\\io\\github\\simplenote\\data\\local\\dao\\NoteDao_Impl.kt": ["NoteDao_Impl:io.github.simplenote.data.local.dao", "getAllNotesByTitle:io.github.simplenote.data.local.dao.NoteDao_Impl", "__db:io.github.simplenote.data.local.dao.NoteDao_Impl", "__deleteAdapterOfNoteEntity:io.github.simplenote.data.local.dao.NoteDao_Impl", "insertNotes:io.github.simplenote.data.local.dao.NoteDao_Impl", "getNotesCount:io.github.simplenote.data.local.dao.NoteDao_Impl", "insertNote:io.github.simplenote.data.local.dao.NoteDao_Impl", "searchNotes:io.github.simplenote.data.local.dao.NoteDao_Impl", "getNoteById:io.github.simplenote.data.local.dao.NoteDao_Impl", "deleteNoteById:io.github.simplenote.data.local.dao.NoteDao_Impl", "__insertAdapterOfNoteEntity:io.github.simplenote.data.local.dao.NoteDao_Impl", "getAllNotesByCreatedDate:io.github.simplenote.data.local.dao.NoteDao_Impl", "updateNote:io.github.simplenote.data.local.dao.NoteDao_Impl", "__updateAdapterOfNoteEntity:io.github.simplenote.data.local.dao.NoteDao_Impl", "getRequiredConverters:io.github.simplenote.data.local.dao.NoteDao_Impl.Companion", "updateNoteColor:io.github.simplenote.data.local.dao.NoteDao_Impl", "Companion:io.github.simplenote.data.local.dao.NoteDao_Impl", "getNotesByColor:io.github.simplenote.data.local.dao.NoteDao_Impl", "deleteAllNotes:io.github.simplenote.data.local.dao.NoteDao_Impl", "deleteNote:io.github.simplenote.data.local.dao.NoteDao_Impl", "getAllNotes:io.github.simplenote.data.local.dao.NoteDao_Impl"], "src\\main\\java\\io\\github\\simplenote\\SimpleNoteApplication.kt": ["onCreate:io.github.simplenote.SimpleNoteApplication", "SimpleNoteApplication:io.github.simplenote"], "src\\main\\java\\io\\github\\simplenote\\domain\\usecase\\UpdateThemeSettingsUseCase.kt": ["updateThemeMode:io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase", "updateUseDynamicTheme:io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase", "UpdateThemeSettingsUseCase:io.github.simplenote.domain.usecase", "themePreferences:io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase", "updateThemeSettings:io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase"], "src\\main\\java\\io\\github\\simplenote\\domain\\usecase\\GetNotesUseCase.kt": ["repository:io.github.simplenote.domain.usecase.GetNotesUseCase", "SortBy:io.github.simplenote.domain.usecase.GetNotesUseCase", "GetNotesUseCase:io.github.simplenote.domain.usecase", "valueOf:io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy", "values:io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy", "TITLE:io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy", "searchNotes:io.github.simplenote.domain.usecase.GetNotesUseCase", "entries:io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy", "getNoteById:io.github.simplenote.domain.usecase.GetNotesUseCase", "CREATED_DATE:io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy", "invoke:io.github.simplenote.domain.usecase.GetNotesUseCase", "getNotesByColor:io.github.simplenote.domain.usecase.GetNotesUseCase", "UPDATED_DATE:io.github.simplenote.domain.usecase.GetNotesUseCase.SortBy"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\ColorPalette.kt": ["getContrastColor:io.github.simplenote.presentation.components", "ColorPalette:io.github.simplenote.presentation.components", "ColorPalettePreview:io.github.simplenote.presentation.components", "ColorItem:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\domain\\model\\NoteError.kt": ["DeleteFailed:io.github.simplenote.domain.model.NoteError.DatabaseError", "EmptyTitle:io.github.simplenote.domain.model.NoteError.ValidationError", "TitleTooLong:io.github.simplenote.domain.model.NoteError.ValidationError", "copy:io.github.simplenote.domain.model.NoteError.DatabaseError.InsertFailed", "NotFound:io.github.simplenote.domain.model.NoteError.DatabaseError", "InsertFailed:io.github.simplenote.domain.model.NoteError.DatabaseError", "MAX_CONTENT_LENGTH:io.github.simplenote.domain.model.NoteValidation", "copy:io.github.simplenote.domain.model.NoteError.DatabaseError.QueryFailed", "ValidationError:io.github.simplenote.domain.model.NoteError", "copy:io.github.simplenote.domain.model.NoteError.AppError.Unknown", "NetworkUnavailable:io.github.simplenote.domain.model.NoteError.AppError", "message:io.github.simplenote.domain.model.NoteError", "component1:io.github.simplenote.domain.model.NoteError.DatabaseError.InsertFailed", "copy:io.github.simplenote.domain.model.NoteError.DatabaseError.UpdateFailed", "cause:io.github.simplenote.domain.model.NoteError.DatabaseError.DeleteFailed", "EmptyContent:io.github.simplenote.domain.model.NoteError.ValidationError", "component1:io.github.simplenote.domain.model.NoteError.AppError.Unknown", "MAX_TITLE_LENGTH:io.github.simplenote.domain.model.NoteValidation", "QueryFailed:io.github.simplenote.domain.model.NoteError.DatabaseError", "UpdateFailed:io.github.simplenote.domain.model.NoteError.DatabaseError", "StorageUnavailable:io.github.simplenote.domain.model.NoteError.AppError", "component1:io.github.simplenote.domain.model.NoteError.DatabaseError.DeleteFailed", "cause:io.github.simplenote.domain.model.NoteError.DatabaseError.InsertFailed", "ContentTooLong:io.github.simplenote.domain.model.NoteError.ValidationError", "component1:io.github.simplenote.domain.model.NoteError.DatabaseError.UpdateFailed", "Unknown:io.github.simplenote.domain.model.NoteError.AppError", "cause:io.github.simplenote.domain.model.NoteError.DatabaseError.QueryFailed", "cause:io.github.simplenote.domain.model.NoteError.AppError.Unknown", "NoteValidation:io.github.simplenote.domain.model", "InvalidColor:io.github.simplenote.domain.model.NoteError.ValidationError", "AppError:io.github.simplenote.domain.model.NoteError", "NoteError:io.github.simplenote.domain.model", "cause:io.github.simplenote.domain.model.NoteError.DatabaseError.UpdateFailed", "DatabaseError:io.github.simplenote.domain.model.NoteError", "copy:io.github.simplenote.domain.model.NoteError.DatabaseError.DeleteFailed", "component1:io.github.simplenote.domain.model.NoteError.DatabaseError.QueryFailed"], "src\\main\\java\\io\\github\\simplenote\\domain\\model\\NoteColor.kt": ["PINK:io.github.simplenote.domain.model.NoteColor", "NoteColor:io.github.simplenote.domain.model", "id:io.github.simplenote.domain.model.NoteColor", "DEEP_ORANGE:io.github.simplenote.domain.model.NoteColor", "PASTEL_YELLOW:io.github.simplenote.domain.model.NoteColor", "RED:io.github.simplenote.domain.model.NoteColor", "PASTEL_ORANGE:io.github.simplenote.domain.model.NoteColor", "color:io.github.simplenote.domain.model.NoteColor", "entries:io.github.simplenote.domain.model.NoteColor", "LIME:io.github.simplenote.domain.model.NoteColor", "PASTEL_GREEN:io.github.simplenote.domain.model.NoteColor", "GREY:io.github.simplenote.domain.model.NoteColor", "GREEN:io.github.simplenote.domain.model.NoteColor", "PASTEL_PURPLE:io.github.simplenote.domain.model.NoteColor", "Companion:io.github.simplenote.domain.model.NoteColor", "LIGHT_GREEN:io.github.simplenote.domain.model.NoteColor", "INDIGO:io.github.simplenote.domain.model.NoteColor", "DEFAULT:io.github.simplenote.domain.model.NoteColor.Companion", "valueOf:io.github.simplenote.domain.model.NoteColor", "PASTEL_PINK:io.github.simplenote.domain.model.NoteColor", "BLUE:io.github.simplenote.domain.model.NoteColor", "BROWN:io.github.simplenote.domain.model.NoteColor", "fromId:io.github.simplenote.domain.model.NoteColor.Companion", "displayName:io.github.simplenote.domain.model.NoteColor", "TEAL:io.github.simplenote.domain.model.NoteColor", "CYAN:io.github.simplenote.domain.model.NoteColor", "PASTEL_BLUE:io.github.simplenote.domain.model.NoteColor", "ORANGE:io.github.simplenote.domain.model.NoteColor", "YELLOW:io.github.simplenote.domain.model.NoteColor", "values:io.github.simplenote.domain.model.NoteColor", "AMBER:io.github.simplenote.domain.model.NoteColor", "DEEP_PURPLE:io.github.simplenote.domain.model.NoteColor", "LIGHT_BLUE:io.github.simplenote.domain.model.NoteColor", "PURPLE:io.github.simplenote.domain.model.NoteColor", "getAllColors:io.github.simplenote.domain.model.NoteColor.Companion"], "src\\main\\java\\io\\github\\simplenote\\MainActivity.kt": ["populateSampleDataUseCase:io.github.simplenote.MainActivity", "onCreate:io.github.simplenote.MainActivity", "MainActivity:io.github.simplenote", "getThemeSettingsUseCase:io.github.simplenote.MainActivity"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\EmptyState.kt": ["EmptyState:io.github.simplenote.presentation.components", "EmptyStatePreview:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\presentation\\viewmodel\\NoteEditorViewModel.kt": ["deleteNote:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "_navigationEvents:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "confirmDelete:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "navigateBack:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "clearError:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "createNoteUseCase:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "NoteEditorViewModel:io.github.simplenote.presentation.viewmodel", "onEvent:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "updateTitle:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "getNotesUseCase:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "deleteNoteUseCase:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "updateContent:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "changeColor:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "updateNoteUseCase:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "loadNote:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "saveNote:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "uiState:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "_uiState:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel", "navigationEvents:io.github.simplenote.presentation.viewmodel.NoteEditorViewModel"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\IntegratedSearchBar.kt": ["IntegratedSearchBarPreview:io.github.simplenote.presentation.components", "IntegratedSearchBar:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\ui\\theme\\Theme.kt": ["SimpleNOTETheme:io.github.simplenote.ui.theme", "LightColorScheme:io.github.simplenote.ui.theme", "DarkColorScheme:io.github.simplenote.ui.theme"], "src\\main\\java\\io\\github\\simplenote\\di\\DatabaseModule.kt": ["databaseModule:io.github.simplenote.di"], "src\\main\\java\\io\\github\\simplenote\\data\\local\\database\\NoteDatabase.kt": ["DATABASE_NAME:io.github.simplenote.data.local.database.NoteDatabase.Companion", "MIGRATION_1_2:io.github.simplenote.data.local.database.NoteDatabase.Companion", "getAllMigrations:io.github.simplenote.data.local.database.NoteDatabase.Companion", "create:io.github.simplenote.data.local.database.NoteDatabase.Companion", "NoteDatabase:io.github.simplenote.data.local.database", "noteDao:io.github.simplenote.data.local.database.NoteDatabase", "Companion:io.github.simplenote.data.local.database.NoteDatabase"], "src\\main\\java\\io\\github\\simplenote\\di\\AppModule.kt": ["appModules:io.github.simplenote.di"], "build\\generated\\ksp\\debug\\kotlin\\io\\github\\simplenote\\data\\local\\database\\NoteDatabase_Impl.kt": ["createInvalidationTracker:io.github.simplenote.data.local.database.NoteDatabase_Impl", "_noteDao:io.github.simplenote.data.local.database.NoteDatabase_Impl", "getRequiredTypeConverterClasses:io.github.simplenote.data.local.database.NoteDatabase_Impl", "getRequiredAutoMigrationSpecClasses:io.github.simplenote.data.local.database.NoteDatabase_Impl", "clearAllTables:io.github.simplenote.data.local.database.NoteDatabase_Impl", "createAutoMigrations:io.github.simplenote.data.local.database.NoteDatabase_Impl", "noteDao:io.github.simplenote.data.local.database.NoteDatabase_Impl", "NoteDatabase_Impl:io.github.simplenote.data.local.database", "createOpenDelegate:io.github.simplenote.data.local.database.NoteDatabase_Impl"], "src\\main\\java\\io\\github\\simplenote\\presentation\\components\\EnhancedSearchBar.kt": ["EnhancedSearchBarPreview:io.github.simplenote.presentation.components", "EnhancedSearchBar:io.github.simplenote.presentation.components"], "src\\main\\java\\io\\github\\simplenote\\data\\repository\\NoteRepositoryImpl.kt": ["deleteNoteById:io.github.simplenote.data.repository.NoteRepositoryImpl", "getAllNotes:io.github.simplenote.data.repository.NoteRepositoryImpl", "updateNoteColor:io.github.simplenote.data.repository.NoteRepositoryImpl", "getAllNotesByCreatedDate:io.github.simplenote.data.repository.NoteRepositoryImpl", "getNoteById:io.github.simplenote.data.repository.NoteRepositoryImpl", "searchNotes:io.github.simplenote.data.repository.NoteRepositoryImpl", "updateNote:io.github.simplenote.data.repository.NoteRepositoryImpl", "deleteNote:io.github.simplenote.data.repository.NoteRepositoryImpl", "getNotesByColor:io.github.simplenote.data.repository.NoteRepositoryImpl", "deleteAllNotes:io.github.simplenote.data.repository.NoteRepositoryImpl", "getNotesCount:io.github.simplenote.data.repository.NoteRepositoryImpl", "createNote:io.github.simplenote.data.repository.NoteRepositoryImpl", "NoteRepositoryImpl:io.github.simplenote.data.repository", "getAllNotesByTitle:io.github.simplenote.data.repository.NoteRepositoryImpl", "noteDao:io.github.simplenote.data.repository.NoteRepositoryImpl"], "src\\main\\java\\io\\github\\simplenote\\presentation\\viewmodel\\NotesViewModel.kt": ["getNotesUseCase:io.github.simplenote.presentation.viewmodel.NotesViewModel", "deleteNoteUseCase:io.github.simplenote.presentation.viewmodel.NotesViewModel", "selectNoteForColorChange:io.github.simplenote.presentation.viewmodel.NotesViewModel", "clearError:io.github.simplenote.presentation.viewmodel.NotesViewModel", "changeNoteColor:io.github.simplenote.presentation.viewmodel.NotesViewModel", "onEvent:io.github.simplenote.presentation.viewmodel.NotesViewModel", "navigateToNoteEditor:io.github.simplenote.presentation.viewmodel.NotesViewModel", "loadNotes:io.github.simplenote.presentation.viewmodel.NotesViewModel", "changeSortOrder:io.github.simplenote.presentation.viewmodel.NotesViewModel", "showColorBottomSheet:io.github.simplenote.presentation.viewmodel.NotesViewModel", "_navigationEvents:io.github.simplenote.presentation.viewmodel.NotesViewModel", "showDeleteConfirmation:io.github.simplenote.presentation.viewmodel.NotesViewModel", "_uiState:io.github.simplenote.presentation.viewmodel.NotesViewModel", "deleteNote:io.github.simplenote.presentation.viewmodel.NotesViewModel", "navigationEvents:io.github.simplenote.presentation.viewmodel.NotesViewModel", "uiState:io.github.simplenote.presentation.viewmodel.NotesViewModel", "searchJob:io.github.simplenote.presentation.viewmodel.NotesViewModel", "searchNotes:io.github.simplenote.presentation.viewmodel.NotesViewModel", "NotesViewModel:io.github.simplenote.presentation.viewmodel", "updateNoteUseCase:io.github.simplenote.presentation.viewmodel.NotesViewModel", "hideColorBottomSheet:io.github.simplenote.presentation.viewmodel.NotesViewModel"], "src\\main\\java\\io\\github\\simplenote\\ui\\theme\\Color.kt": ["Purple40:io.github.simplenote.ui.theme", "Pink80:io.github.simplenote.ui.theme", "PurpleGrey40:io.github.simplenote.ui.theme", "Pink40:io.github.simplenote.ui.theme", "PurpleGrey80:io.github.simplenote.ui.theme", "Purple80:io.github.simplenote.ui.theme"]}