package io.github.simplenote.domain.usecase

import arrow.core.Either
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteError
import io.github.simplenote.domain.repository.NoteRepository

/**
 * Use case for deleting notes.
 */
class DeleteNoteUseCase(
    private val repository: NoteRepository
) {
    
    /**
     * Delete a note by Note object
     */
    suspend operator fun invoke(note: Note): Either<NoteError, Unit> {
        return repository.deleteNote(note)
    }
    
    /**
     * Delete a note by ID
     */
    suspend fun deleteById(noteId: Long): Either<NoteError, Unit> {
        return if (noteId <= 0) {
            Either.Left(NoteError.ValidationError.InvalidColor)
        } else {
            repository.deleteNoteById(noteId)
        }
    }
    
    /**
     * Delete all notes (with confirmation)
     */
    suspend fun deleteAll(): Either<NoteError, Unit> {
        return repository.deleteAllNotes()
    }
    
    /**
     * Get notes count before deletion (for confirmation)
     */
    suspend fun getNotesCount(): Either<NoteError, Int> {
        return repository.getNotesCount()
    }
}
