package io.github.simplenote.presentation.components

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.ui.theme.SimpleNOTETheme
import kotlinx.datetime.Clock

/**
 * Confirmation dialog for deleting notes.
 */
@Composable
fun DeleteConfirmationDialog(
    note: Note,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Delete Note",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.SemiBold
            )
        },
        text = {
            Text(
                text = if (note.title.isNotBlank()) {
                    "Are you sure you want to delete \"${note.title}\"? This action cannot be undone."
                } else {
                    "Are you sure you want to delete this note? This action cannot be undone."
                },
                style = MaterialTheme.typography.bodyMedium
            )
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onConfirm()
                    onDismiss()
                }
            ) {
                Text(
                    text = "Delete",
                    color = MaterialTheme.colorScheme.error,
                    fontWeight = FontWeight.SemiBold
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(
                    text = "Cancel",
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun DeleteConfirmationDialogPreview() {
    SimpleNOTETheme {
        DeleteConfirmationDialog(
            note = Note.create(
                title = "Sample Note",
                content = "This is a sample note content.",
                color = NoteColor.BLUE,
                timestamp = Clock.System.now()
            ),
            onConfirm = {},
            onDismiss = {}
        )
    }
}
