package io.github.simplenote.presentation.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawing
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.outlined.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.outlined.Archive
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.material.icons.outlined.Menu
import androidx.compose.material.icons.outlined.Notifications
import androidx.compose.material.icons.outlined.Palette
import androidx.compose.material.icons.outlined.PushPin
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import io.github.simplenote.domain.model.Note
import io.github.simplenote.presentation.components.ColorBottomSheet
import io.github.simplenote.presentation.components.DeleteConfirmationDialog
import io.github.simplenote.presentation.components.SampleKeepBottomSheetRight
import io.github.simplenote.presentation.model.NavigationEvent
import io.github.simplenote.presentation.model.NoteEditorUiEvent
import io.github.simplenote.presentation.viewmodel.NoteEditorViewModel
import io.github.simplenote.ui.theme.SimpleNOTETheme
import org.koin.androidx.compose.koinViewModel

/**
 * Note editor screen for creating and editing notes.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NoteEditorScreen(
    noteId: Long?,
    onNavigateBack: () -> Unit,
    viewModel: NoteEditorViewModel = koinViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val snackbarHostState = remember { SnackbarHostState() }
    var showColorBottomSheet by remember { mutableStateOf(false) }
    var showRightBottomSheet by remember { mutableStateOf(false) }
    var noteToDelete by remember { mutableStateOf<Note?>(null) }

    // Load note on first composition
    LaunchedEffect(noteId) {
        if (noteId != null && noteId > 0) {
            viewModel.onEvent(NoteEditorUiEvent.LoadNote(noteId))
        }
    }

    // Handle navigation events
    LaunchedEffect(viewModel.navigationEvents) {
        viewModel.navigationEvents.collect { event ->
            when (event) {
                is NavigationEvent.NavigateBack -> {
                    onNavigateBack()
                }

                is NavigationEvent.ShowDeleteConfirmation -> {
                    noteToDelete = event.note
                }

                is NavigationEvent.ShowError -> {
                    snackbarHostState.showSnackbar(event.error.message)
                }

                else -> {}
            }
        }
    }

    // Handle errors
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(error.message)
            viewModel.onEvent(NoteEditorUiEvent.ClearError)
        }
    }

    val containerColor = uiState.note?.color?.color?.copy(alpha = 0.2f)
        ?: MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.2f)



    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = containerColor,
        contentColor = MaterialTheme.colorScheme.onSurface,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = if (uiState.isNewNote) "New Note" else "Edit Note",
                        fontWeight = FontWeight.SemiBold
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = {
                            viewModel.onEvent(NoteEditorUiEvent.NavigateBack)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Outlined.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    // Archive button
                    IconButton(
                        onClick = {
                            // TODO: Implement archive functionality
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.Archive,
                            contentDescription = "Archive note",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    // Pin button
                    IconButton(
                        onClick = {
                            // TODO: Implement pin functionality
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.PushPin,
                            contentDescription = "Pin note",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    // Reminder button
                    IconButton(
                        onClick = {
                            // TODO: Implement reminder functionality
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.Notifications,
                            contentDescription = "Set reminder",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }

                    // Delete button (only for existing notes)
                    if (!uiState.isNewNote) {
                        IconButton(
                            onClick = {
                                viewModel.onEvent(NoteEditorUiEvent.DeleteNote)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.Delete,
                                contentDescription = "Delete note",
                                tint = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.Transparent
                )
            )
        },
        floatingActionButton = {
            if (uiState.canSave) {
                FloatingActionButton(
                    onClick = {
                        viewModel.onEvent(NoteEditorUiEvent.SaveNote)
                    },
                    containerColor = MaterialTheme.colorScheme.primary
                ) {
                    if (uiState.isSaving) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = MaterialTheme.colorScheme.onPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "Save note"
                        )
                    }
                }
            }
        },
        bottomBar = {
            BottomAppBar(
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.onSurface
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Color palette button Left
                    IconButton(
                        onClick = { showColorBottomSheet = true }
                    ) {
                        Box(
                            modifier = Modifier
                                .size(33.dp)
                                .clip(CircleShape)
                                .background(uiState.selectedColor.color),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.Palette,
                                contentDescription = "Change color",
                                tint = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.size(22.dp)
                            )
                        }
                    }

                    // Last updated date (center)
                    if (!uiState.isNewNote) {
                        val note = uiState.note
                        if (note != null) {
                            Text(
                                text = "Updated ${formatDate(note.updatedAt.toString())}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }


                    // Menu button Right
                    IconButton(
                        onClick = { showRightBottomSheet = true }
                    ) {
                        Box(
                            modifier = Modifier
                                .size(33.dp)
                                .clip(CircleShape),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.Menu,
                                contentDescription = "Change color",
                                tint = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.size(22.dp)
                            )
                        }
                    }
                }


            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) },
        contentWindowInsets = WindowInsets(0.dp, 0.dp, 0.dp, 0.dp)
    ) { paddingValues ->
        if (uiState.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(containerColor)
                    .padding(paddingValues)
                    .padding(16.dp)
            ) {
                // Title field
                OutlinedTextField(
                    value = uiState.title,
                    onValueChange = { title ->
                        viewModel.onEvent(NoteEditorUiEvent.UpdateTitle(title))
                    },
                    placeholder = { Text("Note title...") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        disabledBorderColor = Color.Transparent,
                        errorBorderColor = Color.Transparent,
                        focusedContainerColor = Color.Transparent,
                        unfocusedContainerColor = Color.Transparent
                    )
                )

                // Content field
                OutlinedTextField(
                    value = uiState.content,
                    onValueChange = { content ->
                        viewModel.onEvent(NoteEditorUiEvent.UpdateContent(content))
                    },
                    placeholder = { Text("Start writing...") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(top = 16.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        disabledBorderColor = Color.Transparent,
                        errorBorderColor = Color.Transparent
                    )
                )
            }
        }
    }

    // Color bottom sheet
    ColorBottomSheet(
        isVisible = showColorBottomSheet,
        selectedColor = uiState.selectedColor,
        onColorSelected = { color ->
            viewModel.onEvent(NoteEditorUiEvent.ChangeColor(color))
        },
        onDismiss = {
            showColorBottomSheet = false
        }
    )

    SampleKeepBottomSheetRight(
        isVisible = showRightBottomSheet,
        items = listOf("Item 1", "Item 2", "Item 3"),
        onItemSelected = { item ->
            // Handle item selection
        },
        onDismiss = {
            showRightBottomSheet = false
        }
    )

    // Delete confirmation dialog
    noteToDelete?.let { note ->
        DeleteConfirmationDialog(
            note = note,
            onConfirm = {
                viewModel.confirmDelete()
                noteToDelete = null
            },
            onDismiss = {
                noteToDelete = null
            }
        )
    }
}


/**
 * Simple date formatting (can be improved with proper date formatting)
 */
private fun formatDate(dateString: String): String {
    return try {
        // Simple formatting - in a real app, use proper date formatting
        dateString.substringBefore('T')
    } catch (e: Exception) {
        "Unknown date"
    }
}

@Preview(showBackground = true)
@Composable
fun NoteEditorScreenPreview() {
    SimpleNOTETheme {
        NoteEditorScreen(
            noteId = null,
            onNavigateBack = {}
        )
    }
}
