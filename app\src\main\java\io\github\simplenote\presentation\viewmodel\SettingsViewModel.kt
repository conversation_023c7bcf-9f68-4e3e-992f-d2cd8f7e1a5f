package io.github.simplenote.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.github.simplenote.domain.model.ThemeMode
import io.github.simplenote.domain.model.ThemeSettings
import io.github.simplenote.domain.usecase.GetThemeSettingsUseCase
import io.github.simplenote.domain.usecase.UpdateThemeSettingsUseCase
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

/**
 * ViewModel for the settings screen.
 * Manages theme settings and preferences.
 */
class SettingsViewModel(
    private val getThemeSettingsUseCase: GetThemeSettingsUseCase,
    private val updateThemeSettingsUseCase: UpdateThemeSettingsUseCase
) : ViewModel() {
    
    /**
     * Current theme settings as StateFlow
     */
    val themeSettings: StateFlow<ThemeSettings> = getThemeSettingsUseCase()
        .stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = ThemeSettings.DEFAULT
        )
    
    /**
     * Update theme mode
     */
    fun updateThemeMode(themeMode: ThemeMode) {
        viewModelScope.launch {
            updateThemeSettingsUseCase.updateThemeMode(themeMode)
        }
    }
    
    /**
     * Update dynamic theme setting
     */
    fun updateUseDynamicTheme(useDynamicTheme: Boolean) {
        viewModelScope.launch {
            updateThemeSettingsUseCase.updateUseDynamicTheme(useDynamicTheme)
        }
    }
}
