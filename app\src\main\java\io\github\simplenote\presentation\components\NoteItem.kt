package io.github.simplenote.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewLightDark
import androidx.compose.ui.unit.dp
import io.github.simplenote.domain.model.Note
import io.github.simplenote.domain.model.NoteColor
import io.github.simplenote.ui.theme.SimpleNOTETheme
import kotlinx.datetime.Clock

/**
 * Composable for displaying a note item in the list.
 */
@Composable
fun NoteItem(
    note: Note,
    onNoteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onNoteClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (note.color == NoteColor.DEFAULT) {
                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            } else {
                note.color.color.copy(alpha = 0.3f)
            }
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            // Color indicator
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .clip(CircleShape)
                    .background(note.color.color)
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Title
            if (note.title.isNotBlank()) {
                Text(
                    text = note.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(4.dp))
            }

            // Content preview
            if (note.content.isNotBlank()) {
                Text(
                    text = note.preview,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 14,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Updated date
            Text(
                text = formatDate(note.updatedAt.toString()),
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Simple date formatting (can be improved with proper date formatting)
 */
private fun formatDate(dateString: String): String {
    return try {
        // Simple formatting - in a real app, use proper date formatting
        dateString.substringBefore('T')
    } catch (e: Exception) {
        "Unknown date"
    }
}

@PreviewLightDark
@Composable
fun NoteItemPreview() {
    SimpleNOTETheme {
        NoteItem(
            note = Note.create(
                title = "Sample Note Title",
                content = "This is a sample note content that shows how the note item will look in the list. It can be quite long and will be truncated appropriately.This is a sample note content that shows how the note item will look in the list. It can be quite long and will be truncated appropriately",
                color = NoteColor.BLUE,
                timestamp = Clock.System.now()
            ),
            onNoteClick = {}
        )
    }
}


@PreviewLightDark
@Composable
fun NoteItemPreview2() {
    SimpleNOTETheme {
        NoteItem(
            note = Note.create(
                title = "Sample Note Title",
                content = "This is a sample note content that shows how the note item will look in the list. It can be quite long and will be truncated appropriately.",
                color = NoteColor.DEFAULT,
                timestamp = Clock.System.now()
            ),
            onNoteClick = {}
        )
    }
}



