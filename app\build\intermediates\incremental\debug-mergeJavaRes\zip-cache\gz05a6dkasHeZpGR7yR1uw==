[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "androidx/datastore/preferences/protobuf/AbstractMessageLite$Builder$LimitedInputStream.class", "name": "androidx/datastore/preferences/protobuf/AbstractMessageLite$Builder$LimitedInputStream.class", "size": 1630, "crc": -84591669}, {"key": "androidx/datastore/preferences/protobuf/AbstractMessageLite$Builder.class", "name": "androidx/datastore/preferences/protobuf/AbstractMessageLite$Builder.class", "size": 14729, "crc": -1194949456}, {"key": "androidx/datastore/preferences/protobuf/AbstractMessageLite$InternalOneOfEnum.class", "name": "androidx/datastore/preferences/protobuf/AbstractMessageLite$InternalOneOfEnum.class", "size": 319, "crc": 2045368956}, {"key": "androidx/datastore/preferences/protobuf/AbstractMessageLite.class", "name": "androidx/datastore/preferences/protobuf/AbstractMessageLite.class", "size": 5714, "crc": -2056908968}, {"key": "androidx/datastore/preferences/protobuf/AbstractParser.class", "name": "androidx/datastore/preferences/protobuf/AbstractParser.class", "size": 14529, "crc": -1855095758}, {"key": "androidx/datastore/preferences/protobuf/AbstractProtobufList.class", "name": "androidx/datastore/preferences/protobuf/AbstractProtobufList.class", "size": 4127, "crc": -1725281378}, {"key": "androidx/datastore/preferences/protobuf/AllocatedBuffer$1.class", "name": "androidx/datastore/preferences/protobuf/AllocatedBuffer$1.class", "size": 1644, "crc": 1510126536}, {"key": "androidx/datastore/preferences/protobuf/AllocatedBuffer$2.class", "name": "androidx/datastore/preferences/protobuf/AllocatedBuffer$2.class", "size": 1903, "crc": 1989259707}, {"key": "androidx/datastore/preferences/protobuf/AllocatedBuffer.class", "name": "androidx/datastore/preferences/protobuf/AllocatedBuffer.class", "size": 2284, "crc": 315469519}, {"key": "androidx/datastore/preferences/protobuf/Android.class", "name": "androidx/datastore/preferences/protobuf/Android.class", "size": 1286, "crc": -745544695}, {"key": "androidx/datastore/preferences/protobuf/Any$1.class", "name": "androidx/datastore/preferences/protobuf/Any$1.class", "size": 1277, "crc": 27122751}, {"key": "androidx/datastore/preferences/protobuf/Any$Builder.class", "name": "androidx/datastore/preferences/protobuf/Any$Builder.class", "size": 2933, "crc": -1413290073}, {"key": "androidx/datastore/preferences/protobuf/Any.class", "name": "androidx/datastore/preferences/protobuf/Any.class", "size": 10969, "crc": -1730726542}, {"key": "androidx/datastore/preferences/protobuf/AnyOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/AnyOrBuilder.class", "size": 359, "crc": 1084331463}, {"key": "androidx/datastore/preferences/protobuf/AnyProto.class", "name": "androidx/datastore/preferences/protobuf/AnyProto.class", "size": 636, "crc": -111473407}, {"key": "androidx/datastore/preferences/protobuf/Api$1.class", "name": "androidx/datastore/preferences/protobuf/Api$1.class", "size": 1277, "crc": -1748759031}, {"key": "androidx/datastore/preferences/protobuf/Api$Builder.class", "name": "androidx/datastore/preferences/protobuf/Api$Builder.class", "size": 14232, "crc": 1911640844}, {"key": "androidx/datastore/preferences/protobuf/Api.class", "name": "androidx/datastore/preferences/protobuf/Api.class", "size": 24532, "crc": 1134243792}, {"key": "androidx/datastore/preferences/protobuf/ApiOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/ApiOrBuilder.class", "size": 1308, "crc": 270227616}, {"key": "androidx/datastore/preferences/protobuf/ApiProto.class", "name": "androidx/datastore/preferences/protobuf/ApiProto.class", "size": 636, "crc": 767245631}, {"key": "androidx/datastore/preferences/protobuf/ArrayDecoders$1.class", "name": "androidx/datastore/preferences/protobuf/ArrayDecoders$1.class", "size": 1667, "crc": -1752580051}, {"key": "androidx/datastore/preferences/protobuf/ArrayDecoders$Registers.class", "name": "androidx/datastore/preferences/protobuf/ArrayDecoders$Registers.class", "size": 1133, "crc": 1739000083}, {"key": "androidx/datastore/preferences/protobuf/ArrayDecoders.class", "name": "androidx/datastore/preferences/protobuf/ArrayDecoders.class", "size": 28892, "crc": 374192893}, {"key": "androidx/datastore/preferences/protobuf/BinaryReader$1.class", "name": "androidx/datastore/preferences/protobuf/BinaryReader$1.class", "size": 1617, "crc": 771230436}, {"key": "androidx/datastore/preferences/protobuf/BinaryReader$SafeHeapReader.class", "name": "androidx/datastore/preferences/protobuf/BinaryReader$SafeHeapReader.class", "size": 30181, "crc": 941492285}, {"key": "androidx/datastore/preferences/protobuf/BinaryReader.class", "name": "androidx/datastore/preferences/protobuf/BinaryReader.class", "size": 1632, "crc": 570584899}, {"key": "androidx/datastore/preferences/protobuf/BinaryWriter$1.class", "name": "androidx/datastore/preferences/protobuf/BinaryWriter$1.class", "size": 1617, "crc": -423823387}, {"key": "androidx/datastore/preferences/protobuf/BinaryWriter$SafeDirectWriter.class", "name": "androidx/datastore/preferences/protobuf/BinaryWriter$SafeDirectWriter.class", "size": 13986, "crc": -2101084982}, {"key": "androidx/datastore/preferences/protobuf/BinaryWriter$SafeHeapWriter.class", "name": "androidx/datastore/preferences/protobuf/BinaryWriter$SafeHeapWriter.class", "size": 14743, "crc": -1720801243}, {"key": "androidx/datastore/preferences/protobuf/BinaryWriter$UnsafeDirectWriter.class", "name": "androidx/datastore/preferences/protobuf/BinaryWriter$UnsafeDirectWriter.class", "size": 15012, "crc": -4314612}, {"key": "androidx/datastore/preferences/protobuf/BinaryWriter$UnsafeHeapWriter.class", "name": "androidx/datastore/preferences/protobuf/BinaryWriter$UnsafeHeapWriter.class", "size": 15363, "crc": 630654484}, {"key": "androidx/datastore/preferences/protobuf/BinaryWriter.class", "name": "androidx/datastore/preferences/protobuf/BinaryWriter.class", "size": 25647, "crc": 1085010578}, {"key": "androidx/datastore/preferences/protobuf/BoolValue$1.class", "name": "androidx/datastore/preferences/protobuf/BoolValue$1.class", "size": 1295, "crc": 323833859}, {"key": "androidx/datastore/preferences/protobuf/BoolValue$Builder.class", "name": "androidx/datastore/preferences/protobuf/BoolValue$Builder.class", "size": 2052, "crc": 1351786404}, {"key": "androidx/datastore/preferences/protobuf/BoolValue.class", "name": "androidx/datastore/preferences/protobuf/BoolValue.class", "size": 9900, "crc": -962522387}, {"key": "androidx/datastore/preferences/protobuf/BoolValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/BoolValueOrBuilder.class", "size": 250, "crc": 713687021}, {"key": "androidx/datastore/preferences/protobuf/BooleanArrayList.class", "name": "androidx/datastore/preferences/protobuf/BooleanArrayList.class", "size": 7034, "crc": 268228979}, {"key": "androidx/datastore/preferences/protobuf/BufferAllocator$1.class", "name": "androidx/datastore/preferences/protobuf/BufferAllocator$1.class", "size": 1076, "crc": -336035242}, {"key": "androidx/datastore/preferences/protobuf/BufferAllocator.class", "name": "androidx/datastore/preferences/protobuf/BufferAllocator.class", "size": 929, "crc": -2073763142}, {"key": "androidx/datastore/preferences/protobuf/ByteBufferWriter.class", "name": "androidx/datastore/preferences/protobuf/ByteBufferWriter.class", "size": 4387, "crc": -770225543}, {"key": "androidx/datastore/preferences/protobuf/ByteOutput.class", "name": "androidx/datastore/preferences/protobuf/ByteOutput.class", "size": 643, "crc": -1140474524}, {"key": "androidx/datastore/preferences/protobuf/ByteString$1.class", "name": "androidx/datastore/preferences/protobuf/ByteString$1.class", "size": 1363, "crc": -848726658}, {"key": "androidx/datastore/preferences/protobuf/ByteString$2.class", "name": "androidx/datastore/preferences/protobuf/ByteString$2.class", "size": 1741, "crc": -1682169277}, {"key": "androidx/datastore/preferences/protobuf/ByteString$AbstractByteIterator.class", "name": "androidx/datastore/preferences/protobuf/ByteString$AbstractByteIterator.class", "size": 979, "crc": -1276407560}, {"key": "androidx/datastore/preferences/protobuf/ByteString$ArraysByteArrayCopier.class", "name": "androidx/datastore/preferences/protobuf/ByteString$ArraysByteArrayCopier.class", "size": 1088, "crc": -830347317}, {"key": "androidx/datastore/preferences/protobuf/ByteString$BoundedByteString.class", "name": "androidx/datastore/preferences/protobuf/ByteString$BoundedByteString.class", "size": 2200, "crc": -757182890}, {"key": "androidx/datastore/preferences/protobuf/ByteString$ByteArrayCopier.class", "name": "androidx/datastore/preferences/protobuf/ByteString$ByteArrayCopier.class", "size": 354, "crc": 1228509570}, {"key": "androidx/datastore/preferences/protobuf/ByteString$ByteIterator.class", "name": "androidx/datastore/preferences/protobuf/ByteString$ByteIterator.class", "size": 386, "crc": -730150903}, {"key": "androidx/datastore/preferences/protobuf/ByteString$CodedBuilder.class", "name": "androidx/datastore/preferences/protobuf/ByteString$CodedBuilder.class", "size": 1546, "crc": 1519582227}, {"key": "androidx/datastore/preferences/protobuf/ByteString$LeafByteString.class", "name": "androidx/datastore/preferences/protobuf/ByteString$LeafByteString.class", "size": 1644, "crc": -2039939974}, {"key": "androidx/datastore/preferences/protobuf/ByteString$LiteralByteString.class", "name": "androidx/datastore/preferences/protobuf/ByteString$LiteralByteString.class", "size": 6383, "crc": 1943337289}, {"key": "androidx/datastore/preferences/protobuf/ByteString$NioByteString$1.class", "name": "androidx/datastore/preferences/protobuf/ByteString$NioByteString$1.class", "size": 2267, "crc": -1200259701}, {"key": "androidx/datastore/preferences/protobuf/ByteString$NioByteString.class", "name": "androidx/datastore/preferences/protobuf/ByteString$NioByteString.class", "size": 7711, "crc": 550118220}, {"key": "androidx/datastore/preferences/protobuf/ByteString$Output.class", "name": "androidx/datastore/preferences/protobuf/ByteString$Output.class", "size": 4036, "crc": 52400831}, {"key": "androidx/datastore/preferences/protobuf/ByteString$SystemByteArrayCopier.class", "name": "androidx/datastore/preferences/protobuf/ByteString$SystemByteArrayCopier.class", "size": 1164, "crc": -1501512446}, {"key": "androidx/datastore/preferences/protobuf/ByteString.class", "name": "androidx/datastore/preferences/protobuf/ByteString.class", "size": 16766, "crc": 1318712865}, {"key": "androidx/datastore/preferences/protobuf/BytesValue$1.class", "name": "androidx/datastore/preferences/protobuf/BytesValue$1.class", "size": 1298, "crc": -82654538}, {"key": "androidx/datastore/preferences/protobuf/BytesValue$Builder.class", "name": "androidx/datastore/preferences/protobuf/BytesValue$Builder.class", "size": 2272, "crc": 2112615267}, {"key": "androidx/datastore/preferences/protobuf/BytesValue.class", "name": "androidx/datastore/preferences/protobuf/BytesValue.class", "size": 10318, "crc": -1255085694}, {"key": "androidx/datastore/preferences/protobuf/BytesValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/BytesValueOrBuilder.class", "size": 303, "crc": 1867390807}, {"key": "androidx/datastore/preferences/protobuf/CanIgnoreReturnValue.class", "name": "androidx/datastore/preferences/protobuf/CanIgnoreReturnValue.class", "size": 471, "crc": 1890198469}, {"key": "androidx/datastore/preferences/protobuf/CheckReturnValue.class", "name": "androidx/datastore/preferences/protobuf/CheckReturnValue.class", "size": 511, "crc": 893551366}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream$1.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream$1.class", "size": 276, "crc": 1013006689}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream$ArrayDecoder.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream$ArrayDecoder.class", "size": 14795, "crc": 1960898744}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream$IterableDirectByteBufferDecoder.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream$IterableDirectByteBufferDecoder.class", "size": 18453, "crc": 200241282}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream$StreamDecoder$RefillCallback.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream$StreamDecoder$RefillCallback.class", "size": 417, "crc": 263433291}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream$StreamDecoder$SkippedDataSink.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream$StreamDecoder$SkippedDataSink.class", "size": 1838, "crc": 1445405123}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream$StreamDecoder.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream$StreamDecoder.class", "size": 21324, "crc": 1781563049}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream$UnsafeDirectNioDecoder.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream$UnsafeDirectNioDecoder.class", "size": 16253, "crc": 1465867007}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStream.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStream.class", "size": 11349, "crc": 8655599}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStreamReader$1.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStreamReader$1.class", "size": 1647, "crc": 1904878892}, {"key": "androidx/datastore/preferences/protobuf/CodedInputStreamReader.class", "name": "androidx/datastore/preferences/protobuf/CodedInputStreamReader.class", "size": 26869, "crc": 949825713}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$1.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$1.class", "size": 279, "crc": 525017340}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$AbstractBufferedEncoder.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$AbstractBufferedEncoder.class", "size": 3433, "crc": -1757670981}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$ArrayEncoder.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$ArrayEncoder.class", "size": 10994, "crc": 428161149}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$ByteOutputEncoder.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$ByteOutputEncoder.class", "size": 9882, "crc": -2142894174}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$HeapNioEncoder.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$HeapNioEncoder.class", "size": 1143, "crc": 474357286}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$OutOfSpaceException.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$OutOfSpaceException.class", "size": 1432, "crc": 778935072}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$OutputStreamEncoder.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$OutputStreamEncoder.class", "size": 10381, "crc": -374025519}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$SafeDirectNioEncoder.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$SafeDirectNioEncoder.class", "size": 10216, "crc": -920215509}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream$UnsafeDirectNioEncoder.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream$UnsafeDirectNioEncoder.class", "size": 11376, "crc": 1552647163}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStream.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStream.class", "size": 21653, "crc": -646406223}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStreamWriter$1.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStreamWriter$1.class", "size": 1413, "crc": -1323195762}, {"key": "androidx/datastore/preferences/protobuf/CodedOutputStreamWriter.class", "name": "androidx/datastore/preferences/protobuf/CodedOutputStreamWriter.class", "size": 31642, "crc": -373890387}, {"key": "androidx/datastore/preferences/protobuf/CompileTimeConstant.class", "name": "androidx/datastore/preferences/protobuf/CompileTimeConstant.class", "size": 485, "crc": 735700465}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$1.class", "size": 1316, "crc": 383580787}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$Builder.class", "size": 29397, "crc": 539398076}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ExtensionRange$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ExtensionRange$Builder.class", "size": 5092, "crc": 1230202966}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ExtensionRange.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ExtensionRange.class", "size": 13766, "crc": -79252161}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ExtensionRangeOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ExtensionRangeOrBuilder.class", "size": 811, "crc": 1212112348}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ReservedRange$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ReservedRange$Builder.class", "size": 3266, "crc": -1555612425}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ReservedRange.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ReservedRange.class", "size": 11540, "crc": -15007798}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ReservedRangeOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto$ReservedRangeOrBuilder.class", "size": 566, "crc": 1673514788}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProto.class", "size": 40940, "crc": 894344529}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$DescriptorProtoOrBuilder.class", "size": 3590, "crc": -1996880673}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$Edition$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$Edition$1.class", "size": 1355, "crc": 1488258667}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$Edition$EditionVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$Edition$EditionVerifier.class", "size": 1168, "crc": 584904148}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$Edition.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$Edition.class", "size": 4722, "crc": 1745280074}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$Builder.class", "size": 14563, "crc": -297862404}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$EnumReservedRange$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$EnumReservedRange$Builder.class", "size": 3383, "crc": -1522602933}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$EnumReservedRange.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$EnumReservedRange.class", "size": 11761, "crc": 1561504006}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$EnumReservedRangeOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto$EnumReservedRangeOrBuilder.class", "size": 586, "crc": 1375051324}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProto.class", "size": 24715, "crc": 2000910880}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumDescriptorProtoOrBuilder.class", "size": 2041, "crc": -1366044263}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumOptions$Builder.class", "size": 9542, "crc": -307220069}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumOptions.class", "size": 19001, "crc": -803808863}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumOptionsOrBuilder.class", "size": 1954, "crc": 860898763}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueDescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueDescriptorProto$Builder.class", "size": 5789, "crc": 872845732}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueDescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueDescriptorProto.class", "size": 14521, "crc": -885307414}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueDescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueDescriptorProtoOrBuilder.class", "size": 803, "crc": -2004183549}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueOptions$Builder.class", "size": 10849, "crc": 1770113995}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueOptions.class", "size": 20309, "crc": -1361038671}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$EnumValueOptionsOrBuilder.class", "size": 2153, "crc": -951298343}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$Builder.class", "size": 12894, "crc": 1702965612}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$Declaration$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$Declaration$Builder.class", "size": 6054, "crc": -589032885}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$Declaration.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$Declaration.class", "size": 15189, "crc": 1762927723}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$DeclarationOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$DeclarationOrBuilder.class", "size": 846, "crc": -415222049}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$VerificationState$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$VerificationState$1.class", "size": 1641, "crc": -1393859972}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$VerificationState$VerificationStateVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$VerificationState$VerificationStateVerifier.class", "size": 1452, "crc": -473434205}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$VerificationState.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions$VerificationState.class", "size": 3885, "crc": -931408019}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptions.class", "size": 22576, "crc": -1234346935}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ExtensionRangeOptionsOrBuilder.class", "size": 2449, "crc": 828976883}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Builder.class", "size": 8540, "crc": 1625680198}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$EnumType$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$EnumType$1.class", "size": 1510, "crc": 285776330}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$EnumType$EnumTypeVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$EnumType$EnumTypeVerifier.class", "size": 1314, "crc": 1211784361}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$EnumType.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$EnumType.class", "size": 3715, "crc": -1362632366}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$FieldPresence$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$FieldPresence$1.class", "size": 1540, "crc": -1401086276}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$FieldPresence$FieldPresenceVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$FieldPresence$FieldPresenceVerifier.class", "size": 1354, "crc": 382187842}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$FieldPresence.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$FieldPresence.class", "size": 3928, "crc": -1032882158}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$JsonFormat$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$JsonFormat$1.class", "size": 1522, "crc": 935076304}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$JsonFormat$JsonFormatVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$JsonFormat$JsonFormatVerifier.class", "size": 1330, "crc": 1927170026}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$JsonFormat.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$JsonFormat.class", "size": 3773, "crc": -1543468812}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$MessageEncoding$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$MessageEncoding$1.class", "size": 1552, "crc": 155754972}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$MessageEncoding$MessageEncodingVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$MessageEncoding$MessageEncodingVerifier.class", "size": 1370, "crc": -1711091843}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$MessageEncoding.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$MessageEncoding.class", "size": 3855, "crc": 282804903}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$RepeatedFieldEncoding$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$RepeatedFieldEncoding$1.class", "size": 1588, "crc": 824963140}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$RepeatedFieldEncoding$RepeatedFieldEncodingVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$RepeatedFieldEncoding$RepeatedFieldEncodingVerifier.class", "size": 1418, "crc": -1124503440}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$RepeatedFieldEncoding.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$RepeatedFieldEncoding.class", "size": 3933, "crc": -144314423}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Utf8Validation$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Utf8Validation$1.class", "size": 1546, "crc": -842300365}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Utf8Validation$Utf8ValidationVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Utf8Validation$Utf8ValidationVerifier.class", "size": 1362, "crc": 2010189379}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Utf8Validation.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet$Utf8Validation.class", "size": 3815, "crc": -341812899}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSet.class", "size": 18721, "crc": 284824404}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$Builder.class", "size": 7934, "crc": -678665515}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$FeatureSetEditionDefault$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$FeatureSetEditionDefault$Builder.class", "size": 5934, "crc": -229131506}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$FeatureSetEditionDefault.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$FeatureSetEditionDefault.class", "size": 15345, "crc": -509351521}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$FeatureSetEditionDefaultOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults$FeatureSetEditionDefaultOrBuilder.class", "size": 1008, "crc": 1186019595}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaults.class", "size": 17387, "crc": -1816742711}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaultsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetDefaultsOrBuilder.class", "size": 1293, "crc": -2107823895}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FeatureSetOrBuilder.class", "size": 2496, "crc": -895126650}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Builder.class", "size": 11654, "crc": -1230045396}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Label$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Label$1.class", "size": 1562, "crc": -389822519}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Label$LabelVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Label$LabelVerifier.class", "size": 1350, "crc": -638150899}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Label.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Label.class", "size": 3833, "crc": 132580920}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Type$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Type$1.class", "size": 1556, "crc": 1937726209}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Type$TypeVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Type$TypeVerifier.class", "size": 1342, "crc": -373808748}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Type.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto$Type.class", "size": 5532, "crc": 1509106010}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProto.class", "size": 23013, "crc": 1313943806}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldDescriptorProtoOrBuilder.class", "size": 1758, "crc": 1421872370}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$1.class", "size": 1722, "crc": -1708534342}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$Builder.class", "size": 21346, "crc": -377772177}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$CType$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$CType$1.class", "size": 1506, "crc": -1369515900}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$CType$CTypeVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$CType$CTypeVerifier.class", "size": 1302, "crc": 1139527274}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$CType.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$CType.class", "size": 3689, "crc": 124198593}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$EditionDefault$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$EditionDefault$Builder.class", "size": 4521, "crc": -1771979482}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$EditionDefault.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$EditionDefault.class", "size": 13401, "crc": -845501972}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$EditionDefaultOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$EditionDefaultOrBuilder.class", "size": 824, "crc": 1348332144}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$FeatureSupport$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$FeatureSupport$Builder.class", "size": 5587, "crc": -756904912}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$FeatureSupport.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$FeatureSupport.class", "size": 15094, "crc": -526457592}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$FeatureSupportOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$FeatureSupportOrBuilder.class", "size": 1001, "crc": 1975327873}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$JSType$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$JSType$1.class", "size": 1512, "crc": -1256804912}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$JSType$JSTypeVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$JSType$JSTypeVerifier.class", "size": 1310, "crc": -51807329}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$JSType.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$JSType.class", "size": 3713, "crc": -853132478}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionRetention$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionRetention$1.class", "size": 1566, "crc": 650021910}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionRetention$OptionRetentionVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionRetention$OptionRetentionVerifier.class", "size": 1382, "crc": -2135419176}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionRetention.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionRetention.class", "size": 3885, "crc": -1347924903}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionTargetType$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionTargetType$1.class", "size": 1572, "crc": 122564022}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionTargetType$OptionTargetTypeVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionTargetType$OptionTargetTypeVerifier.class", "size": 1390, "crc": 1006431388}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionTargetType.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions$OptionTargetType.class", "size": 4829, "crc": 720412486}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptions.class", "size": 35080, "crc": -318126907}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FieldOptionsOrBuilder.class", "size": 3735, "crc": 1264350874}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorProto$Builder.class", "size": 27386, "crc": -1243829831}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorProto.class", "size": 40623, "crc": 113286185}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorProtoOrBuilder.class", "size": 3483, "crc": 1284242844}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorSet$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorSet$Builder.class", "size": 6049, "crc": -1490818808}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorSet.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorSet.class", "size": 14603, "crc": 1211608290}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorSetOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileDescriptorSetOrBuilder.class", "size": 815, "crc": 1434217216}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$Builder.class", "size": 20813, "crc": 1383102281}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$OptimizeMode$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$OptimizeMode$1.class", "size": 1541, "crc": 422622712}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$OptimizeMode$OptimizeModeVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$OptimizeMode$OptimizeModeVerifier.class", "size": 1352, "crc": 1180873513}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$OptimizeMode.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions$OptimizeMode.class", "size": 3782, "crc": 1977058378}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptions.class", "size": 35639, "crc": 1331932066}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$FileOptionsOrBuilder.class", "size": 3468, "crc": -1542233485}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Builder.class", "size": 7513, "crc": 17285819}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Semantic$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Semantic$1.class", "size": 1726, "crc": 7286775}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Semantic$SemanticVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Semantic$SemanticVerifier.class", "size": 1512, "crc": 366686614}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Semantic.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation$Semantic.class", "size": 4009, "crc": -803312261}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Annotation.class", "size": 17481, "crc": 1771918145}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$AnnotationOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$AnnotationOrBuilder.class", "size": 1290, "crc": -1756581603}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo$Builder.class", "size": 6228, "crc": 1268513899}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfo.class", "size": 14700, "crc": 1138750356}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$GeneratedCodeInfoOrBuilder.class", "size": 959, "crc": 1214508337}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MessageOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MessageOptions$Builder.class", "size": 10634, "crc": 1977785967}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MessageOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MessageOptions.class", "size": 20599, "crc": 917650154}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MessageOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MessageOptionsOrBuilder.class", "size": 2123, "crc": -576316323}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodDescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodDescriptorProto$Builder.class", "size": 7591, "crc": 548317794}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodDescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodDescriptorProto.class", "size": 17243, "crc": 1262310466}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodDescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodDescriptorProtoOrBuilder.class", "size": 1009, "crc": -1739318703}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$Builder.class", "size": 9583, "crc": -1938780040}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$IdempotencyLevel$1.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$IdempotencyLevel$1.class", "size": 1579, "crc": 1602287884}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$IdempotencyLevel$IdempotencyLevelVerifier.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$IdempotencyLevel$IdempotencyLevelVerifier.class", "size": 1396, "crc": -419042714}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$IdempotencyLevel.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions$IdempotencyLevel.class", "size": 3900, "crc": 2033188303}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptions.class", "size": 19167, "crc": 1829523165}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$MethodOptionsOrBuilder.class", "size": 1996, "crc": -1426930861}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofDescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofDescriptorProto$Builder.class", "size": 5044, "crc": 823285397}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofDescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofDescriptorProto.class", "size": 13630, "crc": 1655357151}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofDescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofDescriptorProtoOrBuilder.class", "size": 737, "crc": 1838567539}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofOptions$Builder.class", "size": 7813, "crc": -78837574}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofOptions.class", "size": 16673, "crc": -751418004}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$OneofOptionsOrBuilder.class", "size": 1667, "crc": 218951581}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceDescriptorProto$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceDescriptorProto$Builder.class", "size": 9180, "crc": 387120658}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceDescriptorProto.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceDescriptorProto.class", "size": 18501, "crc": -1537356705}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceDescriptorProtoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceDescriptorProtoOrBuilder.class", "size": 1206, "crc": 1297480696}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceOptions$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceOptions$Builder.class", "size": 8488, "crc": -1657469093}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceOptions.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceOptions.class", "size": 17493, "crc": 2028628465}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceOptionsOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$ServiceOptionsOrBuilder.class", "size": 1729, "crc": -820652182}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$Builder.class", "size": 6071, "crc": -532897852}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$Location$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$Location$Builder.class", "size": 8873, "crc": 1830222122}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$Location.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$Location.class", "size": 19901, "crc": -438205330}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$LocationOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo$LocationOrBuilder.class", "size": 1336, "crc": 1654946518}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfo.class", "size": 14510, "crc": 1957732125}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfoOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$SourceCodeInfoOrBuilder.class", "size": 924, "crc": -1792832223}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$Builder.class", "size": 10564, "crc": -880276110}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$NamePart$Builder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$NamePart$Builder.class", "size": 4219, "crc": -1626573744}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$NamePart.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$NamePart.class", "size": 12771, "crc": -1170107909}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$NamePartOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption$NamePartOrBuilder.class", "size": 691, "crc": -1653585729}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOption.class", "size": 20678, "crc": -2068078141}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOptionOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos$UninterpretedOptionOrBuilder.class", "size": 1446, "crc": -2129190111}, {"key": "androidx/datastore/preferences/protobuf/DescriptorProtos.class", "name": "androidx/datastore/preferences/protobuf/DescriptorProtos.class", "size": 6177, "crc": -1206807975}, {"key": "androidx/datastore/preferences/protobuf/DoubleArrayList.class", "name": "androidx/datastore/preferences/protobuf/DoubleArrayList.class", "size": 7102, "crc": -2058988933}, {"key": "androidx/datastore/preferences/protobuf/DoubleValue$1.class", "name": "androidx/datastore/preferences/protobuf/DoubleValue$1.class", "size": 1301, "crc": -1328442610}, {"key": "androidx/datastore/preferences/protobuf/DoubleValue$Builder.class", "name": "androidx/datastore/preferences/protobuf/DoubleValue$Builder.class", "size": 2084, "crc": 650687657}, {"key": "androidx/datastore/preferences/protobuf/DoubleValue.class", "name": "androidx/datastore/preferences/protobuf/DoubleValue.class", "size": 9961, "crc": -1087156857}, {"key": "androidx/datastore/preferences/protobuf/DoubleValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DoubleValueOrBuilder.class", "size": 254, "crc": -1582606657}, {"key": "androidx/datastore/preferences/protobuf/Duration$1.class", "name": "androidx/datastore/preferences/protobuf/Duration$1.class", "size": 1292, "crc": 1444507936}, {"key": "androidx/datastore/preferences/protobuf/Duration$Builder.class", "name": "androidx/datastore/preferences/protobuf/Duration$Builder.class", "size": 2513, "crc": -1813732365}, {"key": "androidx/datastore/preferences/protobuf/Duration.class", "name": "androidx/datastore/preferences/protobuf/Duration.class", "size": 10140, "crc": -2022880475}, {"key": "androidx/datastore/preferences/protobuf/DurationOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/DurationOrBuilder.class", "size": 275, "crc": -154178367}, {"key": "androidx/datastore/preferences/protobuf/DurationProto.class", "name": "androidx/datastore/preferences/protobuf/DurationProto.class", "size": 651, "crc": -954503989}, {"key": "androidx/datastore/preferences/protobuf/Empty$1.class", "name": "androidx/datastore/preferences/protobuf/Empty$1.class", "size": 1283, "crc": 885632919}, {"key": "androidx/datastore/preferences/protobuf/Empty$Builder.class", "name": "androidx/datastore/preferences/protobuf/Empty$Builder.class", "size": 1273, "crc": -859214036}, {"key": "androidx/datastore/preferences/protobuf/Empty.class", "name": "androidx/datastore/preferences/protobuf/Empty.class", "size": 8787, "crc": -1809266437}, {"key": "androidx/datastore/preferences/protobuf/EmptyOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/EmptyOrBuilder.class", "size": 217, "crc": 2044208214}, {"key": "androidx/datastore/preferences/protobuf/EmptyProto.class", "name": "androidx/datastore/preferences/protobuf/EmptyProto.class", "size": 642, "crc": -1976616202}, {"key": "androidx/datastore/preferences/protobuf/Enum$1.class", "name": "androidx/datastore/preferences/protobuf/Enum$1.class", "size": 1280, "crc": 413052981}, {"key": "androidx/datastore/preferences/protobuf/Enum$Builder.class", "name": "androidx/datastore/preferences/protobuf/Enum$Builder.class", "size": 11596, "crc": -615040009}, {"key": "androidx/datastore/preferences/protobuf/Enum.class", "name": "androidx/datastore/preferences/protobuf/Enum.class", "size": 21579, "crc": -367893720}, {"key": "androidx/datastore/preferences/protobuf/EnumOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/EnumOrBuilder.class", "size": 1111, "crc": 1359617841}, {"key": "androidx/datastore/preferences/protobuf/EnumValue$1.class", "name": "androidx/datastore/preferences/protobuf/EnumValue$1.class", "size": 1295, "crc": -2085749184}, {"key": "androidx/datastore/preferences/protobuf/EnumValue$Builder.class", "name": "androidx/datastore/preferences/protobuf/EnumValue$Builder.class", "size": 6395, "crc": 1143148822}, {"key": "androidx/datastore/preferences/protobuf/EnumValue.class", "name": "androidx/datastore/preferences/protobuf/EnumValue.class", "size": 15206, "crc": 1173447229}, {"key": "androidx/datastore/preferences/protobuf/EnumValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/EnumValueOrBuilder.class", "size": 648, "crc": 1281877759}, {"key": "androidx/datastore/preferences/protobuf/ExperimentalApi.class", "name": "androidx/datastore/preferences/protobuf/ExperimentalApi.class", "size": 607, "crc": 548712438}, {"key": "androidx/datastore/preferences/protobuf/ExtensionLite.class", "name": "androidx/datastore/preferences/protobuf/ExtensionLite.class", "size": 1158, "crc": 1315817588}, {"key": "androidx/datastore/preferences/protobuf/ExtensionRegistryFactory.class", "name": "androidx/datastore/preferences/protobuf/ExtensionRegistryFactory.class", "size": 2488, "crc": 1312246745}, {"key": "androidx/datastore/preferences/protobuf/ExtensionRegistryLite$ExtensionClassHolder.class", "name": "androidx/datastore/preferences/protobuf/ExtensionRegistryLite$ExtensionClassHolder.class", "size": 1092, "crc": -737069389}, {"key": "androidx/datastore/preferences/protobuf/ExtensionRegistryLite$ObjectIntPair.class", "name": "androidx/datastore/preferences/protobuf/ExtensionRegistryLite$ObjectIntPair.class", "size": 1080, "crc": -422379269}, {"key": "androidx/datastore/preferences/protobuf/ExtensionRegistryLite.class", "name": "androidx/datastore/preferences/protobuf/ExtensionRegistryLite.class", "size": 5585, "crc": -1065260111}, {"key": "androidx/datastore/preferences/protobuf/ExtensionSchema.class", "name": "androidx/datastore/preferences/protobuf/ExtensionSchema.class", "size": 3879, "crc": 229273921}, {"key": "androidx/datastore/preferences/protobuf/ExtensionSchemaLite$1.class", "name": "androidx/datastore/preferences/protobuf/ExtensionSchemaLite$1.class", "size": 1685, "crc": -2041418256}, {"key": "androidx/datastore/preferences/protobuf/ExtensionSchemaLite.class", "name": "androidx/datastore/preferences/protobuf/ExtensionSchemaLite.class", "size": 18675, "crc": -1842694319}, {"key": "androidx/datastore/preferences/protobuf/ExtensionSchemas.class", "name": "androidx/datastore/preferences/protobuf/ExtensionSchemas.class", "size": 2037, "crc": -915631512}, {"key": "androidx/datastore/preferences/protobuf/Field$1.class", "name": "androidx/datastore/preferences/protobuf/Field$1.class", "size": 1283, "crc": -1951758688}, {"key": "androidx/datastore/preferences/protobuf/Field$Builder.class", "name": "androidx/datastore/preferences/protobuf/Field$Builder.class", "size": 10918, "crc": -256634175}, {"key": "androidx/datastore/preferences/protobuf/Field$Cardinality$1.class", "name": "androidx/datastore/preferences/protobuf/Field$Cardinality$1.class", "size": 1302, "crc": 666710652}, {"key": "androidx/datastore/preferences/protobuf/Field$Cardinality$CardinalityVerifier.class", "name": "androidx/datastore/preferences/protobuf/Field$Cardinality$CardinalityVerifier.class", "size": 1134, "crc": 1441082680}, {"key": "androidx/datastore/preferences/protobuf/Field$Cardinality.class", "name": "androidx/datastore/preferences/protobuf/Field$Cardinality.class", "size": 3814, "crc": 1579839160}, {"key": "androidx/datastore/preferences/protobuf/Field$Kind$1.class", "name": "androidx/datastore/preferences/protobuf/Field$Kind$1.class", "size": 1260, "crc": -435793683}, {"key": "androidx/datastore/preferences/protobuf/Field$Kind$KindVerifier.class", "name": "androidx/datastore/preferences/protobuf/Field$Kind$KindVerifier.class", "size": 1078, "crc": 59878969}, {"key": "androidx/datastore/preferences/protobuf/Field$Kind.class", "name": "androidx/datastore/preferences/protobuf/Field$Kind.class", "size": 5383, "crc": -835583009}, {"key": "androidx/datastore/preferences/protobuf/Field.class", "name": "androidx/datastore/preferences/protobuf/Field.class", "size": 21529, "crc": -82147663}, {"key": "androidx/datastore/preferences/protobuf/FieldInfo$1.class", "name": "androidx/datastore/preferences/protobuf/FieldInfo$1.class", "size": 876, "crc": 2096615140}, {"key": "androidx/datastore/preferences/protobuf/FieldInfo$Builder.class", "name": "androidx/datastore/preferences/protobuf/FieldInfo$Builder.class", "size": 6009, "crc": 643313164}, {"key": "androidx/datastore/preferences/protobuf/FieldInfo.class", "name": "androidx/datastore/preferences/protobuf/FieldInfo.class", "size": 9852, "crc": 1130583365}, {"key": "androidx/datastore/preferences/protobuf/FieldMask$1.class", "name": "androidx/datastore/preferences/protobuf/FieldMask$1.class", "size": 1295, "crc": -582539892}, {"key": "androidx/datastore/preferences/protobuf/FieldMask$Builder.class", "name": "androidx/datastore/preferences/protobuf/FieldMask$Builder.class", "size": 3954, "crc": 1558680436}, {"key": "androidx/datastore/preferences/protobuf/FieldMask.class", "name": "androidx/datastore/preferences/protobuf/FieldMask.class", "size": 12768, "crc": -828422974}, {"key": "androidx/datastore/preferences/protobuf/FieldMaskOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/FieldMaskOrBuilder.class", "size": 534, "crc": -326434281}, {"key": "androidx/datastore/preferences/protobuf/FieldMaskProto.class", "name": "androidx/datastore/preferences/protobuf/FieldMaskProto.class", "size": 654, "crc": -1550421284}, {"key": "androidx/datastore/preferences/protobuf/FieldOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/FieldOrBuilder.class", "size": 1290, "crc": 357538965}, {"key": "androidx/datastore/preferences/protobuf/FieldSet$1.class", "name": "androidx/datastore/preferences/protobuf/FieldSet$1.class", "size": 2349, "crc": 911466786}, {"key": "androidx/datastore/preferences/protobuf/FieldSet$Builder.class", "name": "androidx/datastore/preferences/protobuf/FieldSet$Builder.class", "size": 15221, "crc": 1939326066}, {"key": "androidx/datastore/preferences/protobuf/FieldSet$FieldDescriptorLite.class", "name": "androidx/datastore/preferences/protobuf/FieldSet$FieldDescriptorLite.class", "size": 1622, "crc": -715634393}, {"key": "androidx/datastore/preferences/protobuf/FieldSet.class", "name": "androidx/datastore/preferences/protobuf/FieldSet.class", "size": 27449, "crc": 1503951616}, {"key": "androidx/datastore/preferences/protobuf/FieldType$1.class", "name": "androidx/datastore/preferences/protobuf/FieldType$1.class", "size": 1279, "crc": 1237221273}, {"key": "androidx/datastore/preferences/protobuf/FieldType$Collection.class", "name": "androidx/datastore/preferences/protobuf/FieldType$Collection.class", "size": 1626, "crc": -270139719}, {"key": "androidx/datastore/preferences/protobuf/FieldType.class", "name": "androidx/datastore/preferences/protobuf/FieldType.class", "size": 10583, "crc": 1945751941}, {"key": "androidx/datastore/preferences/protobuf/FloatArrayList.class", "name": "androidx/datastore/preferences/protobuf/FloatArrayList.class", "size": 7009, "crc": 1804906159}, {"key": "androidx/datastore/preferences/protobuf/FloatValue$1.class", "name": "androidx/datastore/preferences/protobuf/FloatValue$1.class", "size": 1298, "crc": 645533566}, {"key": "androidx/datastore/preferences/protobuf/FloatValue$Builder.class", "name": "androidx/datastore/preferences/protobuf/FloatValue$Builder.class", "size": 2068, "crc": -186196025}, {"key": "androidx/datastore/preferences/protobuf/FloatValue.class", "name": "androidx/datastore/preferences/protobuf/FloatValue.class", "size": 9930, "crc": -15766483}, {"key": "androidx/datastore/preferences/protobuf/FloatValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/FloatValueOrBuilder.class", "size": 252, "crc": 1698880438}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageInfoFactory.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageInfoFactory.class", "size": 2251, "crc": 475614889}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$1.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$1.class", "size": 919, "crc": 1095198131}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$Builder.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$Builder.class", "size": 10339, "crc": -288053846}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$DefaultInstanceBasedParser.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$DefaultInstanceBasedParser.class", "size": 3461, "crc": 1081748548}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableBuilder.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableBuilder.class", "size": 9559, "crc": -1950070008}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter.class", "size": 4035, "crc": 1039048203}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableMessage.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableMessage.class", "size": 17647, "crc": 925188694}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableMessageOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtendableMessageOrBuilder.class", "size": 1906, "crc": -547631703}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtensionDescriptor.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$ExtensionDescriptor.class", "size": 3934, "crc": 1342176322}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$GeneratedExtension.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$GeneratedExtension.class", "size": 5436, "crc": 961635285}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$MethodToInvoke.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$MethodToInvoke.class", "size": 1824, "crc": 1358861798}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$SerializedForm.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite$SerializedForm.class", "size": 3732, "crc": -909823872}, {"key": "androidx/datastore/preferences/protobuf/GeneratedMessageLite.class", "name": "androidx/datastore/preferences/protobuf/GeneratedMessageLite.class", "size": 37116, "crc": 2014145054}, {"key": "androidx/datastore/preferences/protobuf/InlineMe.class", "name": "androidx/datastore/preferences/protobuf/InlineMe.class", "size": 520, "crc": 743536198}, {"key": "androidx/datastore/preferences/protobuf/Int32Value$1.class", "name": "androidx/datastore/preferences/protobuf/Int32Value$1.class", "size": 1298, "crc": 68891463}, {"key": "androidx/datastore/preferences/protobuf/Int32Value$Builder.class", "name": "androidx/datastore/preferences/protobuf/Int32Value$Builder.class", "size": 2068, "crc": -1431600088}, {"key": "androidx/datastore/preferences/protobuf/Int32Value.class", "name": "androidx/datastore/preferences/protobuf/Int32Value.class", "size": 9920, "crc": -1156821161}, {"key": "androidx/datastore/preferences/protobuf/Int32ValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/Int32ValueOrBuilder.class", "size": 252, "crc": -2014625134}, {"key": "androidx/datastore/preferences/protobuf/Int64Value$1.class", "name": "androidx/datastore/preferences/protobuf/Int64Value$1.class", "size": 1298, "crc": -2140329596}, {"key": "androidx/datastore/preferences/protobuf/Int64Value$Builder.class", "name": "androidx/datastore/preferences/protobuf/Int64Value$Builder.class", "size": 2068, "crc": -499121299}, {"key": "androidx/datastore/preferences/protobuf/Int64Value.class", "name": "androidx/datastore/preferences/protobuf/Int64Value.class", "size": 9930, "crc": 1648587857}, {"key": "androidx/datastore/preferences/protobuf/Int64ValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/Int64ValueOrBuilder.class", "size": 252, "crc": 528054473}, {"key": "androidx/datastore/preferences/protobuf/IntArrayList.class", "name": "androidx/datastore/preferences/protobuf/IntArrayList.class", "size": 6913, "crc": -176407620}, {"key": "androidx/datastore/preferences/protobuf/Internal$BooleanList.class", "name": "androidx/datastore/preferences/protobuf/Internal$BooleanList.class", "size": 1127, "crc": -378414364}, {"key": "androidx/datastore/preferences/protobuf/Internal$DoubleList.class", "name": "androidx/datastore/preferences/protobuf/Internal$DoubleList.class", "size": 1119, "crc": 1079363171}, {"key": "androidx/datastore/preferences/protobuf/Internal$EnumLite.class", "name": "androidx/datastore/preferences/protobuf/Internal$EnumLite.class", "size": 268, "crc": 1646836030}, {"key": "androidx/datastore/preferences/protobuf/Internal$EnumLiteMap.class", "name": "androidx/datastore/preferences/protobuf/Internal$EnumLiteMap.class", "size": 584, "crc": -1827017058}, {"key": "androidx/datastore/preferences/protobuf/Internal$EnumVerifier.class", "name": "androidx/datastore/preferences/protobuf/Internal$EnumVerifier.class", "size": 316, "crc": -571573603}, {"key": "androidx/datastore/preferences/protobuf/Internal$FloatList.class", "name": "androidx/datastore/preferences/protobuf/Internal$FloatList.class", "size": 1111, "crc": 1005770800}, {"key": "androidx/datastore/preferences/protobuf/Internal$IntList.class", "name": "androidx/datastore/preferences/protobuf/Internal$IntList.class", "size": 1099, "crc": -1936988198}, {"key": "androidx/datastore/preferences/protobuf/Internal$IntListAdapter$IntConverter.class", "name": "androidx/datastore/preferences/protobuf/Internal$IntListAdapter$IntConverter.class", "size": 518, "crc": -1553885927}, {"key": "androidx/datastore/preferences/protobuf/Internal$IntListAdapter.class", "name": "androidx/datastore/preferences/protobuf/Internal$IntListAdapter.class", "size": 1847, "crc": -994742552}, {"key": "androidx/datastore/preferences/protobuf/Internal$ListAdapter$Converter.class", "name": "androidx/datastore/preferences/protobuf/Internal$ListAdapter$Converter.class", "size": 542, "crc": -1030203379}, {"key": "androidx/datastore/preferences/protobuf/Internal$ListAdapter.class", "name": "androidx/datastore/preferences/protobuf/Internal$ListAdapter.class", "size": 1717, "crc": 1562201881}, {"key": "androidx/datastore/preferences/protobuf/Internal$LongList.class", "name": "androidx/datastore/preferences/protobuf/Internal$LongList.class", "size": 1103, "crc": 1527197628}, {"key": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$1.class", "name": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$1.class", "size": 2581, "crc": -308893031}, {"key": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$Converter.class", "name": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$Converter.class", "size": 594, "crc": 1262206385}, {"key": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$EntryAdapter.class", "name": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$EntryAdapter.class", "size": 2578, "crc": 1316725191}, {"key": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$IteratorAdapter.class", "name": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$IteratorAdapter.class", "size": 2052, "crc": -1985526202}, {"key": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$SetAdapter.class", "name": "androidx/datastore/preferences/protobuf/Internal$MapAdapter$SetAdapter.class", "size": 1795, "crc": -619793325}, {"key": "androidx/datastore/preferences/protobuf/Internal$MapAdapter.class", "name": "androidx/datastore/preferences/protobuf/Internal$MapAdapter.class", "size": 4205, "crc": -312856661}, {"key": "androidx/datastore/preferences/protobuf/Internal$ProtobufList.class", "name": "androidx/datastore/preferences/protobuf/Internal$ProtobufList.class", "size": 695, "crc": -1087783330}, {"key": "androidx/datastore/preferences/protobuf/Internal.class", "name": "androidx/datastore/preferences/protobuf/Internal.class", "size": 10350, "crc": -564269711}, {"key": "androidx/datastore/preferences/protobuf/InvalidProtocolBufferException$InvalidWireTypeException.class", "name": "androidx/datastore/preferences/protobuf/InvalidProtocolBufferException$InvalidWireTypeException.class", "size": 719, "crc": -766276020}, {"key": "androidx/datastore/preferences/protobuf/InvalidProtocolBufferException.class", "name": "androidx/datastore/preferences/protobuf/InvalidProtocolBufferException.class", "size": 4102, "crc": 654688409}, {"key": "androidx/datastore/preferences/protobuf/IterableByteBufferInputStream.class", "name": "androidx/datastore/preferences/protobuf/IterableByteBufferInputStream.class", "size": 3089, "crc": -2139241292}, {"key": "androidx/datastore/preferences/protobuf/Java8Compatibility.class", "name": "androidx/datastore/preferences/protobuf/Java8Compatibility.class", "size": 1120, "crc": 841939443}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$1.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$1.class", "size": 1319, "crc": -429041947}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Builder.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Builder.class", "size": 3685, "crc": -148111383}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Utf8Validation$1.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Utf8Validation$1.class", "size": 1568, "crc": -12331584}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Utf8Validation$Utf8ValidationVerifier.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Utf8Validation$Utf8ValidationVerifier.class", "size": 1381, "crc": 1169143517}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Utf8Validation.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures$Utf8Validation.class", "size": 3857, "crc": -1671487412}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeatures.class", "size": 12214, "crc": -1501747973}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeaturesOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto$JavaFeaturesOrBuilder.class", "size": 792, "crc": -964143004}, {"key": "androidx/datastore/preferences/protobuf/JavaFeaturesProto.class", "name": "androidx/datastore/preferences/protobuf/JavaFeaturesProto.class", "size": 2905, "crc": 970501565}, {"key": "androidx/datastore/preferences/protobuf/JavaType.class", "name": "androidx/datastore/preferences/protobuf/JavaType.class", "size": 3159, "crc": 2078358351}, {"key": "androidx/datastore/preferences/protobuf/LazyField$1.class", "name": "androidx/datastore/preferences/protobuf/LazyField$1.class", "size": 255, "crc": -556300852}, {"key": "androidx/datastore/preferences/protobuf/LazyField$LazyEntry.class", "name": "androidx/datastore/preferences/protobuf/LazyField$LazyEntry.class", "size": 2558, "crc": 58284166}, {"key": "androidx/datastore/preferences/protobuf/LazyField$LazyIterator.class", "name": "androidx/datastore/preferences/protobuf/LazyField$LazyIterator.class", "size": 2012, "crc": 2078876180}, {"key": "androidx/datastore/preferences/protobuf/LazyField.class", "name": "androidx/datastore/preferences/protobuf/LazyField.class", "size": 2056, "crc": 1471269964}, {"key": "androidx/datastore/preferences/protobuf/LazyFieldLite.class", "name": "androidx/datastore/preferences/protobuf/LazyFieldLite.class", "size": 7476, "crc": -2040754883}, {"key": "androidx/datastore/preferences/protobuf/LazyStringArrayList$ByteArrayListView.class", "name": "androidx/datastore/preferences/protobuf/LazyStringArrayList$ByteArrayListView.class", "size": 2252, "crc": 645045719}, {"key": "androidx/datastore/preferences/protobuf/LazyStringArrayList$ByteStringListView.class", "name": "androidx/datastore/preferences/protobuf/LazyStringArrayList$ByteStringListView.class", "size": 2759, "crc": 294043374}, {"key": "androidx/datastore/preferences/protobuf/LazyStringArrayList.class", "name": "androidx/datastore/preferences/protobuf/LazyStringArrayList.class", "size": 12100, "crc": 1847391706}, {"key": "androidx/datastore/preferences/protobuf/LazyStringList.class", "name": "androidx/datastore/preferences/protobuf/LazyStringList.class", "size": 1255, "crc": 728615150}, {"key": "androidx/datastore/preferences/protobuf/ListFieldSchema.class", "name": "androidx/datastore/preferences/protobuf/ListFieldSchema.class", "size": 691, "crc": -1290279126}, {"key": "androidx/datastore/preferences/protobuf/ListFieldSchemaLite.class", "name": "androidx/datastore/preferences/protobuf/ListFieldSchemaLite.class", "size": 2884, "crc": -1476415846}, {"key": "androidx/datastore/preferences/protobuf/ListFieldSchemas.class", "name": "androidx/datastore/preferences/protobuf/ListFieldSchemas.class", "size": 1706, "crc": -162535452}, {"key": "androidx/datastore/preferences/protobuf/ListValue$1.class", "name": "androidx/datastore/preferences/protobuf/ListValue$1.class", "size": 1295, "crc": 1276341161}, {"key": "androidx/datastore/preferences/protobuf/ListValue$Builder.class", "name": "androidx/datastore/preferences/protobuf/ListValue$Builder.class", "size": 4921, "crc": -1319884182}, {"key": "androidx/datastore/preferences/protobuf/ListValue.class", "name": "androidx/datastore/preferences/protobuf/ListValue.class", "size": 13176, "crc": 102134709}, {"key": "androidx/datastore/preferences/protobuf/ListValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/ListValueOrBuilder.class", "size": 502, "crc": 1614253706}, {"key": "androidx/datastore/preferences/protobuf/LongArrayList.class", "name": "androidx/datastore/preferences/protobuf/LongArrayList.class", "size": 6984, "crc": -913810997}, {"key": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory$1.class", "name": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory$1.class", "size": 1227, "crc": 785005517}, {"key": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory$2.class", "name": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory$2.class", "size": 769, "crc": 1423167371}, {"key": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory$CompositeMessageInfoFactory.class", "name": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory$CompositeMessageInfoFactory.class", "size": 2003, "crc": 577375139}, {"key": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory.class", "name": "androidx/datastore/preferences/protobuf/ManifestSchemaFactory.class", "size": 6877, "crc": -68979705}, {"key": "androidx/datastore/preferences/protobuf/MapEntryLite$1.class", "name": "androidx/datastore/preferences/protobuf/MapEntryLite$1.class", "size": 946, "crc": -1150708829}, {"key": "androidx/datastore/preferences/protobuf/MapEntryLite$Metadata.class", "name": "androidx/datastore/preferences/protobuf/MapEntryLite$Metadata.class", "size": 1490, "crc": -609273058}, {"key": "androidx/datastore/preferences/protobuf/MapEntryLite.class", "name": "androidx/datastore/preferences/protobuf/MapEntryLite.class", "size": 10830, "crc": 2091647541}, {"key": "androidx/datastore/preferences/protobuf/MapFieldLite.class", "name": "androidx/datastore/preferences/protobuf/MapFieldLite.class", "size": 6518, "crc": 498691825}, {"key": "androidx/datastore/preferences/protobuf/MapFieldSchema.class", "name": "androidx/datastore/preferences/protobuf/MapFieldSchema.class", "size": 1362, "crc": 1533636462}, {"key": "androidx/datastore/preferences/protobuf/MapFieldSchemaLite.class", "name": "androidx/datastore/preferences/protobuf/MapFieldSchemaLite.class", "size": 4204, "crc": -1372779004}, {"key": "androidx/datastore/preferences/protobuf/MapFieldSchemas.class", "name": "androidx/datastore/preferences/protobuf/MapFieldSchemas.class", "size": 1698, "crc": 1321872989}, {"key": "androidx/datastore/preferences/protobuf/MessageInfo.class", "name": "androidx/datastore/preferences/protobuf/MessageInfo.class", "size": 448, "crc": -1629803086}, {"key": "androidx/datastore/preferences/protobuf/MessageInfoFactory.class", "name": "androidx/datastore/preferences/protobuf/MessageInfoFactory.class", "size": 584, "crc": -1370148594}, {"key": "androidx/datastore/preferences/protobuf/MessageLite$Builder.class", "name": "androidx/datastore/preferences/protobuf/MessageLite$Builder.class", "size": 2891, "crc": -1206287201}, {"key": "androidx/datastore/preferences/protobuf/MessageLite.class", "name": "androidx/datastore/preferences/protobuf/MessageLite.class", "size": 1159, "crc": -1003830648}, {"key": "androidx/datastore/preferences/protobuf/MessageLiteOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/MessageLiteOrBuilder.class", "size": 386, "crc": -180290799}, {"key": "androidx/datastore/preferences/protobuf/MessageLiteToString.class", "name": "androidx/datastore/preferences/protobuf/MessageLiteToString.class", "size": 8862, "crc": 2116615568}, {"key": "androidx/datastore/preferences/protobuf/MessageSchema$1.class", "name": "androidx/datastore/preferences/protobuf/MessageSchema$1.class", "size": 1620, "crc": -1211852097}, {"key": "androidx/datastore/preferences/protobuf/MessageSchema.class", "name": "androidx/datastore/preferences/protobuf/MessageSchema.class", "size": 94262, "crc": 1888288142}, {"key": "androidx/datastore/preferences/protobuf/MessageSetSchema.class", "name": "androidx/datastore/preferences/protobuf/MessageSetSchema.class", "size": 17656, "crc": -976404226}, {"key": "androidx/datastore/preferences/protobuf/Method$1.class", "name": "androidx/datastore/preferences/protobuf/Method$1.class", "size": 1286, "crc": -1798591964}, {"key": "androidx/datastore/preferences/protobuf/Method$Builder.class", "name": "androidx/datastore/preferences/protobuf/Method$Builder.class", "size": 8989, "crc": -1803427089}, {"key": "androidx/datastore/preferences/protobuf/Method.class", "name": "androidx/datastore/preferences/protobuf/Method.class", "size": 18950, "crc": 1040867142}, {"key": "androidx/datastore/preferences/protobuf/MethodOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/MethodOrBuilder.class", "size": 911, "crc": 1806725711}, {"key": "androidx/datastore/preferences/protobuf/Mixin$1.class", "name": "androidx/datastore/preferences/protobuf/Mixin$1.class", "size": 1283, "crc": -1666468525}, {"key": "androidx/datastore/preferences/protobuf/Mixin$Builder.class", "name": "androidx/datastore/preferences/protobuf/Mixin$Builder.class", "size": 3183, "crc": 1259369792}, {"key": "androidx/datastore/preferences/protobuf/Mixin.class", "name": "androidx/datastore/preferences/protobuf/Mixin.class", "size": 11269, "crc": -436444764}, {"key": "androidx/datastore/preferences/protobuf/MixinOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/MixinOrBuilder.class", "size": 379, "crc": -1989084465}, {"key": "androidx/datastore/preferences/protobuf/MutabilityOracle$1.class", "name": "androidx/datastore/preferences/protobuf/MutabilityOracle$1.class", "size": 610, "crc": -224287283}, {"key": "androidx/datastore/preferences/protobuf/MutabilityOracle.class", "name": "androidx/datastore/preferences/protobuf/MutabilityOracle.class", "size": 473, "crc": 1081098085}, {"key": "androidx/datastore/preferences/protobuf/NewInstanceSchema.class", "name": "androidx/datastore/preferences/protobuf/NewInstanceSchema.class", "size": 367, "crc": 451668862}, {"key": "androidx/datastore/preferences/protobuf/NewInstanceSchemaLite.class", "name": "androidx/datastore/preferences/protobuf/NewInstanceSchemaLite.class", "size": 896, "crc": 1648029295}, {"key": "androidx/datastore/preferences/protobuf/NewInstanceSchemas.class", "name": "androidx/datastore/preferences/protobuf/NewInstanceSchemas.class", "size": 1722, "crc": 285387922}, {"key": "androidx/datastore/preferences/protobuf/NullValue$1.class", "name": "androidx/datastore/preferences/protobuf/NullValue$1.class", "size": 1193, "crc": 1975470269}, {"key": "androidx/datastore/preferences/protobuf/NullValue$NullValueVerifier.class", "name": "androidx/datastore/preferences/protobuf/NullValue$NullValueVerifier.class", "size": 1027, "crc": 1770149508}, {"key": "androidx/datastore/preferences/protobuf/NullValue.class", "name": "androidx/datastore/preferences/protobuf/NullValue.class", "size": 3242, "crc": -1587340840}, {"key": "androidx/datastore/preferences/protobuf/OneofInfo.class", "name": "androidx/datastore/preferences/protobuf/OneofInfo.class", "size": 963, "crc": 280024748}, {"key": "androidx/datastore/preferences/protobuf/Option$1.class", "name": "androidx/datastore/preferences/protobuf/Option$1.class", "size": 1286, "crc": 1825913038}, {"key": "androidx/datastore/preferences/protobuf/Option$Builder.class", "name": "androidx/datastore/preferences/protobuf/Option$Builder.class", "size": 4002, "crc": 1715313380}, {"key": "androidx/datastore/preferences/protobuf/Option.class", "name": "androidx/datastore/preferences/protobuf/Option.class", "size": 12045, "crc": 2038355729}, {"key": "androidx/datastore/preferences/protobuf/OptionOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/OptionOrBuilder.class", "size": 434, "crc": 1271130029}, {"key": "androidx/datastore/preferences/protobuf/Parser.class", "name": "androidx/datastore/preferences/protobuf/Parser.class", "size": 3367, "crc": 1046935405}, {"key": "androidx/datastore/preferences/protobuf/PrimitiveNonBoxingCollection.class", "name": "androidx/datastore/preferences/protobuf/PrimitiveNonBoxingCollection.class", "size": 177, "crc": -2027098037}, {"key": "androidx/datastore/preferences/protobuf/ProtoSyntax.class", "name": "androidx/datastore/preferences/protobuf/ProtoSyntax.class", "size": 1253, "crc": -2051146459}, {"key": "androidx/datastore/preferences/protobuf/Protobuf.class", "name": "androidx/datastore/preferences/protobuf/Protobuf.class", "size": 5850, "crc": -*********}, {"key": "androidx/datastore/preferences/protobuf/ProtobufArrayList.class", "name": "androidx/datastore/preferences/protobuf/ProtobufArrayList.class", "size": 4791, "crc": -*********}, {"key": "androidx/datastore/preferences/protobuf/ProtocolStringList.class", "name": "androidx/datastore/preferences/protobuf/ProtocolStringList.class", "size": 387, "crc": -*********}, {"key": "androidx/datastore/preferences/protobuf/RawMessageInfo.class", "name": "androidx/datastore/preferences/protobuf/RawMessageInfo.class", "size": 2192, "crc": *********}, {"key": "androidx/datastore/preferences/protobuf/Reader.class", "name": "androidx/datastore/preferences/protobuf/Reader.class", "size": 5099, "crc": 2069218187}, {"key": "androidx/datastore/preferences/protobuf/RopeByteString$1.class", "name": "androidx/datastore/preferences/protobuf/RopeByteString$1.class", "size": 2092, "crc": 1107270199}, {"key": "androidx/datastore/preferences/protobuf/RopeByteString$Balancer.class", "name": "androidx/datastore/preferences/protobuf/RopeByteString$Balancer.class", "size": 3983, "crc": 110021683}, {"key": "androidx/datastore/preferences/protobuf/RopeByteString$PieceIterator.class", "name": "androidx/datastore/preferences/protobuf/RopeByteString$PieceIterator.class", "size": 3133, "crc": -297515228}, {"key": "androidx/datastore/preferences/protobuf/RopeByteString$RopeInputStream.class", "name": "androidx/datastore/preferences/protobuf/RopeByteString$RopeInputStream.class", "size": 3680, "crc": -871298774}, {"key": "androidx/datastore/preferences/protobuf/RopeByteString.class", "name": "androidx/datastore/preferences/protobuf/RopeByteString.class", "size": 11874, "crc": 681721937}, {"key": "androidx/datastore/preferences/protobuf/RuntimeVersion$ProtobufRuntimeVersionException.class", "name": "androidx/datastore/preferences/protobuf/RuntimeVersion$ProtobufRuntimeVersionException.class", "size": 640, "crc": -1363809261}, {"key": "androidx/datastore/preferences/protobuf/RuntimeVersion$RuntimeDomain.class", "name": "androidx/datastore/preferences/protobuf/RuntimeVersion$RuntimeDomain.class", "size": 1419, "crc": 1079999753}, {"key": "androidx/datastore/preferences/protobuf/RuntimeVersion.class", "name": "androidx/datastore/preferences/protobuf/RuntimeVersion.class", "size": 4309, "crc": 1307261317}, {"key": "androidx/datastore/preferences/protobuf/Schema.class", "name": "androidx/datastore/preferences/protobuf/Schema.class", "size": 1845, "crc": 295666046}, {"key": "androidx/datastore/preferences/protobuf/SchemaFactory.class", "name": "androidx/datastore/preferences/protobuf/SchemaFactory.class", "size": 507, "crc": -322392365}, {"key": "androidx/datastore/preferences/protobuf/SchemaUtil.class", "name": "androidx/datastore/preferences/protobuf/SchemaUtil.class", "size": 33259, "crc": 1105401308}, {"key": "androidx/datastore/preferences/protobuf/SmallSortedMap$1.class", "name": "androidx/datastore/preferences/protobuf/SmallSortedMap$1.class", "size": 2366, "crc": -989093768}, {"key": "androidx/datastore/preferences/protobuf/SmallSortedMap$DescendingEntryIterator.class", "name": "androidx/datastore/preferences/protobuf/SmallSortedMap$DescendingEntryIterator.class", "size": 2814, "crc": -114086833}, {"key": "androidx/datastore/preferences/protobuf/SmallSortedMap$DescendingEntrySet.class", "name": "androidx/datastore/preferences/protobuf/SmallSortedMap$DescendingEntrySet.class", "size": 1708, "crc": -1657502273}, {"key": "androidx/datastore/preferences/protobuf/SmallSortedMap$Entry.class", "name": "androidx/datastore/preferences/protobuf/SmallSortedMap$Entry.class", "size": 3710, "crc": -361649617}, {"key": "androidx/datastore/preferences/protobuf/SmallSortedMap$EntryIterator.class", "name": "androidx/datastore/preferences/protobuf/SmallSortedMap$EntryIterator.class", "size": 3170, "crc": -341248894}, {"key": "androidx/datastore/preferences/protobuf/SmallSortedMap$EntrySet.class", "name": "androidx/datastore/preferences/protobuf/SmallSortedMap$EntrySet.class", "size": 3005, "crc": -745238723}, {"key": "androidx/datastore/preferences/protobuf/SmallSortedMap.class", "name": "androidx/datastore/preferences/protobuf/SmallSortedMap.class", "size": 10434, "crc": -561401320}, {"key": "androidx/datastore/preferences/protobuf/SourceContext$1.class", "name": "androidx/datastore/preferences/protobuf/SourceContext$1.class", "size": 1307, "crc": -1077082042}, {"key": "androidx/datastore/preferences/protobuf/SourceContext$Builder.class", "name": "androidx/datastore/preferences/protobuf/SourceContext$Builder.class", "size": 2778, "crc": 1256474614}, {"key": "androidx/datastore/preferences/protobuf/SourceContext.class", "name": "androidx/datastore/preferences/protobuf/SourceContext.class", "size": 10678, "crc": 36957988}, {"key": "androidx/datastore/preferences/protobuf/SourceContextOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/SourceContextOrBuilder.class", "size": 362, "crc": -169311331}, {"key": "androidx/datastore/preferences/protobuf/SourceContextProto.class", "name": "androidx/datastore/preferences/protobuf/SourceContextProto.class", "size": 666, "crc": 2033668747}, {"key": "androidx/datastore/preferences/protobuf/StringValue$1.class", "name": "androidx/datastore/preferences/protobuf/StringValue$1.class", "size": 1301, "crc": 1336555101}, {"key": "androidx/datastore/preferences/protobuf/StringValue$Builder.class", "name": "androidx/datastore/preferences/protobuf/StringValue$Builder.class", "size": 2727, "crc": 1676389698}, {"key": "androidx/datastore/preferences/protobuf/StringValue.class", "name": "androidx/datastore/preferences/protobuf/StringValue.class", "size": 10949, "crc": -422702452}, {"key": "androidx/datastore/preferences/protobuf/StringValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/StringValueOrBuilder.class", "size": 352, "crc": 1525940396}, {"key": "androidx/datastore/preferences/protobuf/Struct$1.class", "name": "androidx/datastore/preferences/protobuf/Struct$1.class", "size": 1286, "crc": -829839305}, {"key": "androidx/datastore/preferences/protobuf/Struct$Builder.class", "name": "androidx/datastore/preferences/protobuf/Struct$Builder.class", "size": 4795, "crc": -1425816513}, {"key": "androidx/datastore/preferences/protobuf/Struct$FieldsDefaultEntryHolder.class", "name": "androidx/datastore/preferences/protobuf/Struct$FieldsDefaultEntryHolder.class", "size": 1490, "crc": 1738171032}, {"key": "androidx/datastore/preferences/protobuf/Struct.class", "name": "androidx/datastore/preferences/protobuf/Struct.class", "size": 12038, "crc": 1882111635}, {"key": "androidx/datastore/preferences/protobuf/StructOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/StructOrBuilder.class", "size": 881, "crc": -443985045}, {"key": "androidx/datastore/preferences/protobuf/StructProto.class", "name": "androidx/datastore/preferences/protobuf/StructProto.class", "size": 645, "crc": -*********}, {"key": "androidx/datastore/preferences/protobuf/StructuralMessageInfo$Builder.class", "name": "androidx/datastore/preferences/protobuf/StructuralMessageInfo$Builder.class", "size": 2959, "crc": 2115003116}, {"key": "androidx/datastore/preferences/protobuf/StructuralMessageInfo.class", "name": "androidx/datastore/preferences/protobuf/StructuralMessageInfo.class", "size": 2403, "crc": *********}, {"key": "androidx/datastore/preferences/protobuf/Syntax$1.class", "name": "androidx/datastore/preferences/protobuf/Syntax$1.class", "size": 1175, "crc": *********}, {"key": "androidx/datastore/preferences/protobuf/Syntax$SyntaxVerifier.class", "name": "androidx/datastore/preferences/protobuf/Syntax$SyntaxVerifier.class", "size": 1003, "crc": *********}, {"key": "androidx/datastore/preferences/protobuf/Syntax.class", "name": "androidx/datastore/preferences/protobuf/Syntax.class", "size": 3444, "crc": -1137536463}, {"key": "androidx/datastore/preferences/protobuf/TextFormatEscaper$1.class", "name": "androidx/datastore/preferences/protobuf/TextFormatEscaper$1.class", "size": 1123, "crc": -*********}, {"key": "androidx/datastore/preferences/protobuf/TextFormatEscaper$2.class", "name": "androidx/datastore/preferences/protobuf/TextFormatEscaper$2.class", "size": 893, "crc": -*********}, {"key": "androidx/datastore/preferences/protobuf/TextFormatEscaper$ByteSequence.class", "name": "androidx/datastore/preferences/protobuf/TextFormatEscaper$ByteSequence.class", "size": 361, "crc": *********}, {"key": "androidx/datastore/preferences/protobuf/TextFormatEscaper.class", "name": "androidx/datastore/preferences/protobuf/TextFormatEscaper.class", "size": 2771, "crc": -2118333071}, {"key": "androidx/datastore/preferences/protobuf/Timestamp$1.class", "name": "androidx/datastore/preferences/protobuf/Timestamp$1.class", "size": 1295, "crc": *********}, {"key": "androidx/datastore/preferences/protobuf/Timestamp$Builder.class", "name": "androidx/datastore/preferences/protobuf/Timestamp$Builder.class", "size": 2531, "crc": 1507864119}, {"key": "androidx/datastore/preferences/protobuf/Timestamp.class", "name": "androidx/datastore/preferences/protobuf/Timestamp.class", "size": 10169, "crc": 1355828489}, {"key": "androidx/datastore/preferences/protobuf/TimestampOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/TimestampOrBuilder.class", "size": 277, "crc": 181160179}, {"key": "androidx/datastore/preferences/protobuf/TimestampProto.class", "name": "androidx/datastore/preferences/protobuf/TimestampProto.class", "size": 654, "crc": 2138732424}, {"key": "androidx/datastore/preferences/protobuf/Type$1.class", "name": "androidx/datastore/preferences/protobuf/Type$1.class", "size": 1280, "crc": 901816828}, {"key": "androidx/datastore/preferences/protobuf/Type$Builder.class", "name": "androidx/datastore/preferences/protobuf/Type$Builder.class", "size": 13082, "crc": -2118742569}, {"key": "androidx/datastore/preferences/protobuf/Type.class", "name": "androidx/datastore/preferences/protobuf/Type.class", "size": 23714, "crc": -1460548620}, {"key": "androidx/datastore/preferences/protobuf/TypeOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/TypeOrBuilder.class", "size": 1341, "crc": -2064385327}, {"key": "androidx/datastore/preferences/protobuf/TypeProto.class", "name": "androidx/datastore/preferences/protobuf/TypeProto.class", "size": 639, "crc": 1932840883}, {"key": "androidx/datastore/preferences/protobuf/UInt32Value$1.class", "name": "androidx/datastore/preferences/protobuf/UInt32Value$1.class", "size": 1301, "crc": -1588066037}, {"key": "androidx/datastore/preferences/protobuf/UInt32Value$Builder.class", "name": "androidx/datastore/preferences/protobuf/UInt32Value$Builder.class", "size": 2084, "crc": -1013952222}, {"key": "androidx/datastore/preferences/protobuf/UInt32Value.class", "name": "androidx/datastore/preferences/protobuf/UInt32Value.class", "size": 9950, "crc": -1063710689}, {"key": "androidx/datastore/preferences/protobuf/UInt32ValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/UInt32ValueOrBuilder.class", "size": 254, "crc": 2097630145}, {"key": "androidx/datastore/preferences/protobuf/UInt64Value$1.class", "name": "androidx/datastore/preferences/protobuf/UInt64Value$1.class", "size": 1301, "crc": -53165469}, {"key": "androidx/datastore/preferences/protobuf/UInt64Value$Builder.class", "name": "androidx/datastore/preferences/protobuf/UInt64Value$Builder.class", "size": 2084, "crc": 1762233271}, {"key": "androidx/datastore/preferences/protobuf/UInt64Value.class", "name": "androidx/datastore/preferences/protobuf/UInt64Value.class", "size": 9960, "crc": -1132252622}, {"key": "androidx/datastore/preferences/protobuf/UInt64ValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/UInt64ValueOrBuilder.class", "size": 254, "crc": -2082671248}, {"key": "androidx/datastore/preferences/protobuf/UninitializedMessageException.class", "name": "androidx/datastore/preferences/protobuf/UninitializedMessageException.class", "size": 2474, "crc": -612353693}, {"key": "androidx/datastore/preferences/protobuf/UnknownFieldSchema.class", "name": "androidx/datastore/preferences/protobuf/UnknownFieldSchema.class", "size": 4971, "crc": 1103598477}, {"key": "androidx/datastore/preferences/protobuf/UnknownFieldSetLite.class", "name": "androidx/datastore/preferences/protobuf/UnknownFieldSetLite.class", "size": 11158, "crc": 262004124}, {"key": "androidx/datastore/preferences/protobuf/UnknownFieldSetLiteSchema.class", "name": "androidx/datastore/preferences/protobuf/UnknownFieldSetLiteSchema.class", "size": 7108, "crc": -462822833}, {"key": "androidx/datastore/preferences/protobuf/UnmodifiableLazyStringList$1.class", "name": "androidx/datastore/preferences/protobuf/UnmodifiableLazyStringList$1.class", "size": 2596, "crc": 874210958}, {"key": "androidx/datastore/preferences/protobuf/UnmodifiableLazyStringList$2.class", "name": "androidx/datastore/preferences/protobuf/UnmodifiableLazyStringList$2.class", "size": 1659, "crc": 794550345}, {"key": "androidx/datastore/preferences/protobuf/UnmodifiableLazyStringList.class", "name": "androidx/datastore/preferences/protobuf/UnmodifiableLazyStringList.class", "size": 4578, "crc": 1936168197}, {"key": "androidx/datastore/preferences/protobuf/UnsafeByteOperations.class", "name": "androidx/datastore/preferences/protobuf/UnsafeByteOperations.class", "size": 1464, "crc": -2066042396}, {"key": "androidx/datastore/preferences/protobuf/UnsafeUtil$1.class", "name": "androidx/datastore/preferences/protobuf/UnsafeUtil$1.class", "size": 1507, "crc": -2090119059}, {"key": "androidx/datastore/preferences/protobuf/UnsafeUtil$Android32MemoryAccessor.class", "name": "androidx/datastore/preferences/protobuf/UnsafeUtil$Android32MemoryAccessor.class", "size": 4287, "crc": 511726498}, {"key": "androidx/datastore/preferences/protobuf/UnsafeUtil$Android64MemoryAccessor.class", "name": "androidx/datastore/preferences/protobuf/UnsafeUtil$Android64MemoryAccessor.class", "size": 4136, "crc": -2147409184}, {"key": "androidx/datastore/preferences/protobuf/UnsafeUtil$JvmMemoryAccessor.class", "name": "androidx/datastore/preferences/protobuf/UnsafeUtil$JvmMemoryAccessor.class", "size": 5030, "crc": -121440732}, {"key": "androidx/datastore/preferences/protobuf/UnsafeUtil$MemoryAccessor.class", "name": "androidx/datastore/preferences/protobuf/UnsafeUtil$MemoryAccessor.class", "size": 4340, "crc": -966334528}, {"key": "androidx/datastore/preferences/protobuf/UnsafeUtil.class", "name": "androidx/datastore/preferences/protobuf/UnsafeUtil.class", "size": 16014, "crc": -193511045}, {"key": "androidx/datastore/preferences/protobuf/Utf8$DecodeUtil.class", "name": "androidx/datastore/preferences/protobuf/Utf8$DecodeUtil.class", "size": 3255, "crc": 1041876772}, {"key": "androidx/datastore/preferences/protobuf/Utf8$Processor.class", "name": "androidx/datastore/preferences/protobuf/Utf8$Processor.class", "size": 7073, "crc": -137834228}, {"key": "androidx/datastore/preferences/protobuf/Utf8$SafeProcessor.class", "name": "androidx/datastore/preferences/protobuf/Utf8$SafeProcessor.class", "size": 5830, "crc": -1784097100}, {"key": "androidx/datastore/preferences/protobuf/Utf8$UnpairedSurrogateException.class", "name": "androidx/datastore/preferences/protobuf/Utf8$UnpairedSurrogateException.class", "size": 873, "crc": 2075573164}, {"key": "androidx/datastore/preferences/protobuf/Utf8$UnsafeProcessor.class", "name": "androidx/datastore/preferences/protobuf/Utf8$UnsafeProcessor.class", "size": 10558, "crc": 2034447649}, {"key": "androidx/datastore/preferences/protobuf/Utf8.class", "name": "androidx/datastore/preferences/protobuf/Utf8.class", "size": 6130, "crc": 1892705658}, {"key": "androidx/datastore/preferences/protobuf/Value$1.class", "name": "androidx/datastore/preferences/protobuf/Value$1.class", "size": 1283, "crc": -1467208546}, {"key": "androidx/datastore/preferences/protobuf/Value$Builder.class", "name": "androidx/datastore/preferences/protobuf/Value$Builder.class", "size": 8125, "crc": 505516504}, {"key": "androidx/datastore/preferences/protobuf/Value$KindCase.class", "name": "androidx/datastore/preferences/protobuf/Value$KindCase.class", "size": 2234, "crc": -676745330}, {"key": "androidx/datastore/preferences/protobuf/Value.class", "name": "androidx/datastore/preferences/protobuf/Value.class", "size": 17624, "crc": -1076042274}, {"key": "androidx/datastore/preferences/protobuf/ValueOrBuilder.class", "name": "androidx/datastore/preferences/protobuf/ValueOrBuilder.class", "size": 1062, "crc": 290969199}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$1.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$1.class", "size": 1602, "crc": 45164624}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$1.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$1.class", "size": 1174, "crc": -454982549}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$2.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$2.class", "size": 1174, "crc": 447355401}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$3.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$3.class", "size": 1174, "crc": 1265819790}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$4.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$FieldType$4.class", "size": 1174, "crc": -133402549}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$FieldType.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$FieldType.class", "size": 3869, "crc": -1440221375}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$JavaType.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$JavaType.class", "size": 2396, "crc": 1316804227}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation$1.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation$1.class", "size": 1176, "crc": -1274159613}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation$2.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation$2.class", "size": 1200, "crc": -601713605}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation$3.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation$3.class", "size": 1222, "crc": -1582767316}, {"key": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation.class", "name": "androidx/datastore/preferences/protobuf/WireFormat$Utf8Validation.class", "size": 2176, "crc": 339885785}, {"key": "androidx/datastore/preferences/protobuf/WireFormat.class", "name": "androidx/datastore/preferences/protobuf/WireFormat.class", "size": 4217, "crc": -1076347611}, {"key": "androidx/datastore/preferences/protobuf/WrappersProto.class", "name": "androidx/datastore/preferences/protobuf/WrappersProto.class", "size": 651, "crc": 455675650}, {"key": "androidx/datastore/preferences/protobuf/Writer$FieldOrder.class", "name": "androidx/datastore/preferences/protobuf/Writer$FieldOrder.class", "size": 1332, "crc": 2051703526}, {"key": "androidx/datastore/preferences/protobuf/Writer.class", "name": "androidx/datastore/preferences/protobuf/Writer.class", "size": 4183, "crc": -628539779}]