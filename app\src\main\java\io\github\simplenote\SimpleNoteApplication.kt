package io.github.simplenote

import android.app.Application
import io.github.simplenote.di.appModules
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import org.koin.core.logger.Level

/**
 * Application class for SimpleNote.
 * Initializes Koin dependency injection and other app-level configurations.
 */
class SimpleNoteApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize Koin
        startKoin {
            // Koin Android logger
            androidLogger(Level.ERROR)
            
            // Android context
            androidContext(this@SimpleNoteApplication)
            
            // Load modules
            modules(appModules)
        }
    }
}
