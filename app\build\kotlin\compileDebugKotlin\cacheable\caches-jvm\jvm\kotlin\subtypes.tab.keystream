+io.github.simplenote.data.local.dao.NoteDao5io.github.simplenote.data.local.database.NoteDatabase#androidx.activity.ComponentActivityandroid.app.Applicationandroidx.room.RoomDatabase5io.github.simplenote.domain.repository.NoteRepositorykotlin.Enum+io.github.simplenote.domain.model.NoteError9io.github.simplenote.domain.model.NoteError.DatabaseError;io.github.simplenote.domain.model.NoteError.ValidationError4io.github.simplenote.domain.model.NoteError.AppError.io.github.simplenote.navigation.AppDestination2kotlinx.serialization.internal.GeneratedSerializer4io.github.simplenote.presentation.model.NotesUiEvent9io.github.simplenote.presentation.model.NoteEditorUiEvent7io.github.simplenote.presentation.model.NavigationEventandroidx.lifecycle.ViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        